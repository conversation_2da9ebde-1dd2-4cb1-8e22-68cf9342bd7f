#import "UIImage+TColor.h"

@implementation UIImage (TColor)

- (instancetype)imageWithCornerRadius:(CGFloat)cornerRadius
                                 size:(CGSize)newSize {
  UIImage *originImage = [self scaleImage:newSize];

  if (newSize.width <= 0 || newSize.height <= 0) {
    return self;
  }
  CGRect bounds = CGRectMake(0, 0, newSize.width, newSize.height);
  UIGraphicsBeginImageContextWithOptions(newSize, NO,
                                         UIScreen.mainScreen.scale);
  CGContextRef context = UIGraphicsGetCurrentContext();
  UIBezierPath *path = [UIBezierPath bezierPathWithRoundedRect:bounds
                                                  cornerRadius:cornerRadius];
  CGContextAddPath(context, path.CGPath);
  CGContextClip(context);
  [originImage drawInRect:bounds];
  UIImage *image = UIGraphicsGetImageFromCurrentImageContext();
  UIGraphicsEndImageContext();
  return image;
}

- (UIImage *)scaleImage:(CGSize)newSize {
  CGFloat width = self.size.width;
  CGFloat height = self.size.height;

  CGFloat scale = newSize.width / newSize.height;
  CGFloat imageScale = width / height;

  if (imageScale > scale) {
    width = height * scale;
  } else if (imageScale < scale) {
    height = width / scale;
  } else {
  }

  CGRect frame = CGRectMake((self.size.width - width) * 0.5,
                            (self.size.height - height) * 0.5, width, height);

  CGImageRef imageRef = [self CGImage];
  imageRef = CGImageCreateWithImageInRect(imageRef, frame);
  UIImage *image = [UIImage imageWithCGImage:imageRef];

  return image;
}

@end
