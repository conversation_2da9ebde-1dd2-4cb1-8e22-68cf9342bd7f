#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, FTHZRadialMenuSelection) {
  FTHZRadialMenuSelectionNone = 0,
  FTHZRadialMenuSelectionDynamic = 1,
  FTHZRadialMenuSelectionSound = 2,
  FTHZRadialMenuSelectionMurmur = 3
};

@interface FTHZRadialMenuView : UIView

@property(nonatomic, copy) void (^onSelection)
    (FTHZRadialMenuSelection selection);
@property(nonatomic, assign) BOOL isRight;

- (void)showInView:(UIView *)parentView
         fromPoint:(CGPoint)origin
           isRight:(BOOL)isRight;
- (void)dismiss;
- (void)updateHighlightWithTouchPoint:(CGPoint)point;
- (FTHZRadialMenuSelection)currentSelection;

@end

NS_ASSUME_NONNULL_END