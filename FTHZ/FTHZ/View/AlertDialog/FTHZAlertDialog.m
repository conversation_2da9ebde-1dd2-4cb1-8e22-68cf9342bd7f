#import "FTHZAlertDialog.h"

NS_ASSUME_NONNULL_BEGIN

static const CGFloat kButtonHeight = 50;
static const CGFloat kDialogWidth = 270;
static const CGFloat kContentPaddingHorizontal = 21;
static const CGFloat kTitleLabelPaddingTop = 23;
static const CGFloat kContentLabelPaddingTop = 9;
static const CGFloat kContentLabelPaddingBottom = 15;
static const CGFloat kSeparateLineWidth = 1;

@implementation FTHZAlertDialogAction

+ (instancetype)actionWithTitle:(NSString *)title
                         action:(FTHZAlertDialogActionImp _Nullable)action
                          style:(FTHZAlertDialogActionStyle)style {
  FTHZAlertDialogAction *anAction = [[FTHZAlertDialogAction alloc] init];
  anAction.title = title;
  anAction.action = action;
  anAction.style = style;
  return anAction;
}

@end

@interface FTHZAlertDIalogButton : UIButton

@property(nonatomic, strong) FTHZAlertDialogAction *action;

@end

@implementation FTHZAlertDIalogButton

- (instancetype)initWithFrame:(CGRect)frame {
  self = [super initWithFrame:frame];
  if (self) {
    self.backgroundColor = KColor_White;
  }
  return self;
}

- (void)setAction:(FTHZAlertDialogAction *)action {
  _action = action;
  switch (action.style) {
  case FTHZAlertDialogActionStyleDefault:
    self.titleLabel.font = SourceHanSerifRegularFont(16);
    [self setTitleColor:[KColor_HighBlack colorWithAlphaComponent:0.85]
               forState:UIControlStateNormal];
    break;

  case FTHZAlertDialogActionStyleHighlighted:
    self.titleLabel.font = SourceHanSerifMediumFont(16);
    [self setTitleColor:[KColor_heziGreen colorWithAlphaComponent:0.85]
               forState:UIControlStateNormal];
    break;
  }
  [self setTitle:action.title forState:UIControlStateNormal];
}

@end

@interface FTHZAlertDialogController () <UIViewControllerTransitioningDelegate>

@property(nonatomic, strong, readonly) UIView *backgroundView;

@property(nonatomic, strong, readonly) UILabel *titleLabel;

@property(nonatomic, strong, readonly) UILabel *contentLabel;

@property(nonatomic, strong, readonly) UIView *customView;

@property(nonatomic, strong) NSMutableArray<FTHZAlertDIalogButton *> *buttons;

@end

@implementation FTHZAlertDialogController

+ (NSDictionary *)titleAttributes {
  NSMutableParagraphStyle *para = [[NSMutableParagraphStyle alloc] init];
  para.alignment = NSTextAlignmentCenter;
  return @{
    NSParagraphStyleAttributeName : [para copy],
    NSFontAttributeName : SourceHanSerifMediumFont(16),
    NSForegroundColorAttributeName : [UIColor whiteColor]
  };
}

+ (NSDictionary *)contentAttributes {
  NSMutableParagraphStyle *para = [[NSMutableParagraphStyle alloc] init];
  return @{
    NSParagraphStyleAttributeName : [para copy],
    NSFontAttributeName : SourceHanSerifRegularFont(14),
    NSForegroundColorAttributeName : [UIColor colorWithWhite:1.0 alpha:0.87]
  };
}

- (instancetype)initWithTitle:(NSString *_Nullable)title
                      message:(NSString *_Nullable)message {
  self = [super initWithNibName:nil bundle:nil];
  if (self) {
    self.modalPresentationStyle = UIModalPresentationCustom;
    self.transitioningDelegate = self;
    _backgroundView = [[UIView alloc] init];
    _backgroundView.backgroundColor = KColor_HighBlack;

    _titleLabel = [[UILabel alloc] init];
    _titleLabel.numberOfLines = 0;
    _titleLabel.attributedText = [[NSAttributedString alloc]
        initWithString:title ?: @""
            attributes:[[self class] titleAttributes]];
    _titleLabel.hidden = title == nil;

    _contentLabel = [[UILabel alloc] init];
    _contentLabel.numberOfLines = 0;
    _contentLabel.attributedText = [[NSAttributedString alloc]
        initWithString:message ?: @""
            attributes:[[self class] contentAttributes]];
    ;
    _contentLabel.hidden = message == nil;

    _buttons = [[NSMutableArray alloc] init];
  }
  return self;
}

- (instancetype)initWithCustomView:(UIView *)customView
                             title:(NSString *_Nullable)title {
  self = [super initWithNibName:nil bundle:nil];
  if (self) {
    self.modalPresentationStyle = UIModalPresentationCustom;
    self.transitioningDelegate = self;
    _backgroundView = [[UIView alloc] init];
    _backgroundView.backgroundColor = KColor_HighBlack;

    _titleLabel = [[UILabel alloc] init];
    _titleLabel.numberOfLines = 0;
    _titleLabel.attributedText = [[NSAttributedString alloc]
        initWithString:title ?: @""
            attributes:[[self class] titleAttributes]];
    _titleLabel.hidden = title == nil;

    _contentLabel = [[UILabel alloc] init];
    _contentLabel.numberOfLines = 0;
    _contentLabel.hidden = YES;

    _customView = [[UIView alloc] init];
    _customView = customView;

    _buttons = [[NSMutableArray alloc] init];
  }
  return self;
}

- (void)addAction:(FTHZAlertDialogAction *)action {
  FTHZAlertDIalogButton *button = [[FTHZAlertDIalogButton alloc] init];
  [button setAction:action];
  [button addTarget:self
                action:@selector(buttonClicked:)
      forControlEvents:UIControlEventTouchUpInside];
  if (self.isViewLoaded) {
    [self.view addSubview:button];
  }
  [self.buttons addObject:button];
}

- (void)viewDidLoad {
  [super viewDidLoad];
  self.view.layer.cornerRadius = 12;
  self.view.layer.masksToBounds = YES;
  self.view.backgroundColor = UIColorFromRGB(0xEBEDF5);
  [self.view addSubview:self.backgroundView];
  [self.view addSubview:self.titleLabel];
  [self.view addSubview:self.contentLabel];
  [self.view addSubview:self.customView];
  for (FTHZAlertDIalogButton *btn in self.buttons) {
    [self.view addSubview:btn];
  }
}

- (void)buttonClicked:(FTHZAlertDIalogButton *)btn {

  FTHZAlertDialogActionImp action = [btn.action.action copy];
  if (action) {
    action();
  }
  [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)viewDidLayoutSubviews {
  [super viewDidLayoutSubviews];
  CGFloat kContentWidth =
      CGRectGetWidth(self.view.bounds) - 2 * kContentPaddingHorizontal;

  CGFloat top = 0;
  if (!self.titleLabel.isHidden) {
    self.titleLabel.size =
        [self.titleLabel sizeThatFits:CGSizeMake(kContentWidth, CGFLOAT_MAX)];
    self.titleLabel.top = top + kTitleLabelPaddingTop;
    self.titleLabel.centerX = CGRectGetMidX(self.view.bounds);
    top = self.titleLabel.bottom;
  }
  if (!self.contentLabel.isHidden) {
    self.contentLabel.size =
        [self.contentLabel sizeThatFits:CGSizeMake(kContentWidth, CGFLOAT_MAX)];
    self.contentLabel.top = top + kContentLabelPaddingTop;
    self.contentLabel.centerX = CGRectGetMidX(self.view.bounds);
    top = self.contentLabel.bottom;
  }

  if (self.customView) {
    self.customView.top = top + kContentLabelPaddingTop;
    self.customView.centerX = CGRectGetMidX(self.view.bounds);
    self.customView.width = kContentWidth;
    top = self.customView.bottom;
  }

  NSInteger buttonCount = self.buttons.count;

  self.backgroundView.origin = CGPointZero;
  self.backgroundView.width = CGRectGetWidth(self.view.bounds);
  self.backgroundView.height =
      CGRectGetHeight(self.view.bounds) -
      (buttonCount > 0 ? (kButtonHeight + kSeparateLineWidth) : 0);

  if (buttonCount > 0) {
    const CGFloat buttonWidth = ceil(CGRectGetWidth(self.view.bounds) -
                                     (buttonCount - 1) * kSeparateLineWidth) /
                                buttonCount;
    for (NSInteger itr = 0; itr < buttonCount; ++itr) {
      FTHZAlertDIalogButton *aBtn = self.buttons[itr];
      aBtn.left = itr * (buttonWidth + kSeparateLineWidth) * itr;
      aBtn.width = buttonWidth;
      aBtn.height = kButtonHeight;
      aBtn.bottom = CGRectGetMaxY(self.view.bounds);
    }
  }
}

- (CGSize)preferredContentSize {
  CGFloat kContentWidth =
      kDialogWidth * kMainTemp - 2 * kContentPaddingHorizontal;
  CGFloat totalHeight = 0;
  if (!self.titleLabel.isHidden) {
    CGFloat titleHeight = [self.titleLabel.attributedText
        suggestHeightWithWidthLimit:kContentWidth];
    totalHeight += kTitleLabelPaddingTop + titleHeight;
  }
  if (!self.contentLabel.isHidden) {
    CGFloat contentHeight = [self.contentLabel.attributedText
        suggestHeightWithWidthLimit:kContentWidth];
    totalHeight += kContentLabelPaddingTop + contentHeight;
  }

  if (self.customView) {
    totalHeight +=
        kContentLabelPaddingTop + CGRectGetHeight(self.customView.frame);
  }

  totalHeight += kContentLabelPaddingBottom;
  if (self.buttons.count > 0) {
    totalHeight += kButtonHeight + kSeparateLineWidth;
  }
  return CGSizeMake(kDialogWidth * kMainTemp, totalHeight);
}

- (UIPresentationController *_Nullable)
    presentationControllerForPresentedViewController:
        (UIViewController *)presented
                            presentingViewController:
                                (UIViewController *_Nullable)presenting
                                sourceViewController:
                                    (UIViewController *)source {
  return [[FTHZDialogPresentationController alloc]
      initWithPresentedViewController:presented
             presentingViewController:presenting];
}

- (CGRect)frameInContainerView:(UIView *)containerView
        presentationController:
            (FTHZDialogPresentationController *)presentationController {
  const CGSize size = [self preferredContentSize];
  return CGRectMake(
      floor((CGRectGetWidth(containerView.bounds) - size.width) / 2),
      floor((CGRectGetHeight(containerView.bounds) - size.height) / 2),
      size.width, size.height);
}

@end

@implementation FTHZAlertDialogController (BannedDialog)

+ (void)showBannedTipWithMessage:(NSString *_Nullable)message {
  FTHZAlertDialogController *dialog =
      [[FTHZAlertDialogController alloc] initWithTitle:@"提示" message:message];
  [dialog addAction:[FTHZAlertDialogAction
                        actionWithTitle:@"好的，我知道了"
                                 action:nil
                                  style:FTHZAlertDialogActionStyleHighlighted]];
  [[UIViewController topViewController] presentViewController:dialog
                                                     animated:YES
                                                   completion:nil];
}

@end

NS_ASSUME_NONNULL_END
