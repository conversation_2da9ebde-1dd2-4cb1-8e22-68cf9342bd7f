#import "FTHZRadialMenuView.h"

static const NSInteger kMenuOptionCount = 3;
static const CGFloat kMenuTotalAngle = M_PI_2;
static const CGFloat kMenuSectorAngle = M_PI_2 / 3;
static const CGFloat kMenuInnerRadiusRatio = 0.35;

@interface FTHZRadialMenuView ()
@property(nonatomic, assign) FTHZRadialMenuSelection highlightedSelection;
@property(nonatomic, strong) UIView *maskView;
@property(nonatomic, strong) UIImageView *backgroundImageView;
@property(nonatomic, strong) NSDictionary<NSNumber *, UIImage *> *leftImages;
@property(nonatomic, strong) NSDictionary<NSNumber *, UIImage *> *rightImages;
@end

@implementation FTHZRadialMenuView

- (instancetype)initWithFrame:(CGRect)frame {
  self = [super initWithFrame:frame];
  if (self) {
    self.backgroundColor = [UIColor colorWithWhite:0 alpha:0.2];
    self.highlightedSelection = FTHZRadialMenuSelectionNone;
    self.backgroundImageView = [[UIImageView alloc] initWithFrame:self.bounds];
    self.backgroundImageView.contentMode = UIViewContentModeScaleAspectFill;
    [self addSubview:self.backgroundImageView];
    [self sendSubviewToBack:self.backgroundImageView];
    self.leftImages = @{
      @(FTHZRadialMenuSelectionNone) : [UIImage imageNamed:@"hx_l_k"],
      @(FTHZRadialMenuSelectionDynamic) : [UIImage imageNamed:@"hx_l_3"],
      @(FTHZRadialMenuSelectionSound) : [UIImage imageNamed:@"hx_l_2"],
      @(FTHZRadialMenuSelectionMurmur) : [UIImage imageNamed:@"hx_l_1"]
    };
    self.rightImages = @{
      @(FTHZRadialMenuSelectionNone) : [UIImage imageNamed:@"hx_r_k"],
      @(FTHZRadialMenuSelectionDynamic) : [UIImage imageNamed:@"hx_r_1"],
      @(FTHZRadialMenuSelectionSound) : [UIImage imageNamed:@"hx_r_2"],
      @(FTHZRadialMenuSelectionMurmur) : [UIImage imageNamed:@"hx_r_3"]
    };
  }
  return self;
}

- (void)showInView:(UIView *)parentView
         fromPoint:(CGPoint)origin
           isRight:(BOOL)isRight {
  self.isRight = isRight;
  UIView *containerView = parentView;
  CGFloat screenWidth = containerView.bounds.size.width;
  CGFloat menuRadius = screenWidth;
  CGFloat tabBarTop = origin.y;

  if (!self.maskView) {
    self.maskView = [[UIView alloc] initWithFrame:containerView.bounds];
    self.maskView.backgroundColor = [UIColor colorWithWhite:0 alpha:0.8];
    self.maskView.alpha = 0;
    UITapGestureRecognizer *tap =
        [[UITapGestureRecognizer alloc] initWithTarget:self
                                                action:@selector(dismiss)];
    [self.maskView addGestureRecognizer:tap];
  } else {
    self.maskView.frame = containerView.bounds;
  }
  [containerView addSubview:self.maskView];
  [UIView animateWithDuration:0.25
                   animations:^{
                     self.maskView.alpha = 1;
                   }];

  if (isRight) {
    self.frame = CGRectMake(screenWidth - menuRadius, tabBarTop - menuRadius,
                            menuRadius, menuRadius);
  } else {
    self.frame = CGRectMake(0, tabBarTop - menuRadius, menuRadius, menuRadius);
  }
  self.backgroundColor = [UIColor clearColor];
  self.alpha = 0;
  self.backgroundImageView.frame = CGRectInset(self.bounds, 12, 12);
  if (isRight) {
    self.backgroundImageView.image =
        self.rightImages[@(FTHZRadialMenuSelectionNone)];
  } else {
    self.backgroundImageView.image =
        self.leftImages[@(FTHZRadialMenuSelectionNone)];
  }
  [containerView addSubview:self];
  [UIView animateWithDuration:0.25
                   animations:^{
                     self.alpha = 1;
                   }];

  UITabBar *tabBar = nil;
  for (UIView *subview in containerView.subviews) {
    if ([subview isKindOfClass:[UITabBar class]]) {
      tabBar = (UITabBar *)subview;
      break;
    }
  }
  if (tabBar) {
    [containerView bringSubviewToFront:tabBar];
  }
}

- (void)dismiss {
  [UIView animateWithDuration:0.2
      animations:^{
        self.alpha = 0;
        self.maskView.alpha = 0;
      }
      completion:^(BOOL finished) {
        [self.maskView removeFromSuperview];
        [self removeFromSuperview];
      }];
}

- (void)updateHighlightWithTouchPoint:(CGPoint)point {
  CGFloat menuRadius = self.bounds.size.width;
  CGPoint local = [self convertPoint:point fromView:self.superview];
  CGFloat dx, dy;
  if (self.isRight) {
    dx = local.x - menuRadius;
    dy = local.y - menuRadius;
  } else {
    dx = local.x - 0;
    dy = local.y - menuRadius;
  }
  CGFloat distance = sqrt(dx * dx + dy * dy);
  CGFloat angleStart = self.isRight ? M_PI : 3 * M_PI_2;
  CGFloat sectorTotal = kMenuSectorAngle;
  if (distance < menuRadius * 0.35 || distance > menuRadius) {
    self.highlightedSelection = FTHZRadialMenuSelectionNone;
    if (self.isRight) {
      self.backgroundImageView.image =
          self.rightImages[@(FTHZRadialMenuSelectionNone)];
    } else {
      self.backgroundImageView.image =
          self.leftImages[@(FTHZRadialMenuSelectionNone)];
    }
    [self setNeedsDisplay];
    return;
  }
  CGFloat angle = atan2(dy, dx);
  if (angle < 0)
    angle += 2 * M_PI;
  if (self.isRight) {
    if (angle >= M_PI && angle <= M_PI + M_PI_2) {
      NSInteger idx = (NSInteger)((angle - angleStart) / sectorTotal);
      if (idx >= 0 && idx < kMenuOptionCount) {
        FTHZRadialMenuSelection sel = (FTHZRadialMenuSelection)(idx + 1);
        if (self.highlightedSelection != sel) {
          self.highlightedSelection = sel;
          self.backgroundImageView.image = self.rightImages[@(sel)];
          [self setNeedsDisplay];
          if (sel != FTHZRadialMenuSelectionNone) {
            if (@available(iOS 10.0, *)) {
              UIImpactFeedbackGenerator *generator =
                  [[UIImpactFeedbackGenerator alloc]
                      initWithStyle:UIImpactFeedbackStyleMedium];
              [generator impactOccurred];
            }
          }
        }
      }
    } else {
      self.highlightedSelection = FTHZRadialMenuSelectionNone;
      self.backgroundImageView.image =
          self.rightImages[@(FTHZRadialMenuSelectionNone)];
      [self setNeedsDisplay];
    }
  } else {
    if (angle >= 3 * M_PI_2 && angle <= 2 * M_PI) {
      NSInteger idx = (NSInteger)((angle - angleStart) / sectorTotal);
      if (idx >= 0 && idx < kMenuOptionCount) {
        FTHZRadialMenuSelection sel = (FTHZRadialMenuSelection)(idx + 1);
        if (self.highlightedSelection != sel) {
          self.highlightedSelection = sel;
          self.backgroundImageView.image = self.leftImages[@(sel)];
          [self setNeedsDisplay];
          if (sel != FTHZRadialMenuSelectionNone) {
            if (@available(iOS 10.0, *)) {
              UIImpactFeedbackGenerator *generator =
                  [[UIImpactFeedbackGenerator alloc]
                      initWithStyle:UIImpactFeedbackStyleMedium];
              [generator impactOccurred];
            }
          }
        }
      }
    } else {
      self.highlightedSelection = FTHZRadialMenuSelectionNone;
      self.backgroundImageView.image =
          self.leftImages[@(FTHZRadialMenuSelectionNone)];
      [self setNeedsDisplay];
    }
  }
}

- (FTHZRadialMenuSelection)currentSelection {
  return self.highlightedSelection;
}

- (void)layoutSubviews {
  [super layoutSubviews];
  self.backgroundImageView.frame = CGRectInset(self.bounds, 12, 12);
}

@end