#import "FTHZNetworkTask+UserInfo.h"
#import "GetUserinfoModel.h"

NS_ASSUME_NONNULL_BEGIN

NSString *kFTHZLogoutPath = @"/n/common/logout";

@implementation FTHZNetworkTask (Ocean)

+ (FTHZNetworkTask *)getCurrentUserInfo {
  return [[FTHZNetworkTask alloc]
      initWithMethod:kHZHTTPMethodGET
                 url:[baseURL() URLByAppendingPathComponent:@"/n/user/userinfo"]
           bizParser:[FTHZUniversalBizParser
                         parserWithDataParser:[FTHZObjectDataParser
                                                  dataParserWithObjectClass:
                                                      [UserPersonResult class]]
                                  flattenData:YES]];
}

+ (FTHZNetworkTask *)updateUserInfoWithName:(NSString *_Nullable)name
                                     gender:(NSString *_Nullable)gender
                                     avatar:(NSString *_Nullable)avatar
                                  signature:(NSString *_Nullable)sig
                                      brith:(NSString *_Nullable)birth {
  FTHZNetworkTask *task = [[FTHZNetworkTask alloc]
      initWithMethod:kHZHTTPMethodPOST
                 url:[baseURL() URLByAppendingPathComponent:@"/n/user/userinfo"]
           bizParser:[FTHZUniversalBizParser
                         parserWithDataParser:[FTHZObjectDataParser
                                                  dataParserWithObjectClass:
                                                      [UserPersonResult class]]
                                  flattenData:YES]];
  NSMutableDictionary *bodyJSON = [[NSMutableDictionary alloc] init];
  bodyJSON[@"birth"] = [birth copy];
  bodyJSON[@"nickname"] = [name copy];
  bodyJSON[@"gender"] = [gender copy];
  bodyJSON[@"avatar"] = [avatar copy];
  bodyJSON[@"signature"] = [sig copy];
  [task setJSONBody:[bodyJSON copy]];
  return task;
}

+ (FTHZNetworkTask *)updateUserToken {
  return [[FTHZNetworkTask alloc]
      initWithMethod:kHZHTTPMethodPOST
                 url:[baseURL()
                         URLByAppendingPathComponent:@"/n/common/updatessid"]
           bizParser:[FTHZUniversalBizParser
                         parserWithDataParser:[FTHZObjectDataParser
                                                  dataParserWithObjectClass:
                                                      [UserPersonResult class]]
                                  flattenData:YES]];
}

+ (FTHZNetworkTask *)loginWithAccount:(NSString *)account
                             password:(NSString *)password {
  NSURL *url = [baseURL() URLByAppendingPathComponent:KURLPostAccountLogin];

  FTHZNetworkTask *task = [[FTHZNetworkTask alloc]
      initWithMethod:kHZHTTPMethodPOST
                 url:[baseURL()
                         URLByAppendingPathComponent:KURLPostAccountLogin]
           bizParser:[FTHZUniversalBizParser
                         parserWithDataParser:[FTHZObjectDataParser
                                                  dataParserWithObjectClass:
                                                      [UserPersonResult class]]
                                  flattenData:YES]];

  NSMutableDictionary *bodyJSON = [[NSMutableDictionary alloc] init];
  bodyJSON[@"account"] = [account copy];
  bodyJSON[@"password"] = [password copy];

  [task setJSONBody:[bodyJSON copy]];
  return task;
}

+ (FTHZNetworkTask *)loginWithMobile:(NSString *)mobile code:(NSString *)code {
  FTHZNetworkTask *task = [[FTHZNetworkTask alloc]
      initWithMethod:kHZHTTPMethodPOST
                 url:[baseURL() URLByAppendingPathComponent:KURLValidate_code]
           bizParser:[FTHZUniversalBizParser
                         parserWithDataParser:[FTHZObjectDataParser
                                                  dataParserWithObjectClass:
                                                      [UserPersonResult class]]
                                  flattenData:YES]];
  NSMutableDictionary *bodyJSON = [[NSMutableDictionary alloc] init];

  bodyJSON[@"mobile"] = [mobile copy];
  bodyJSON[@"code"] = [code copy];

  [task setJSONBody:[bodyJSON copy]];
  return task;
}

+ (FTHZNetworkTask *)loginWithWechatCode:(NSString *)code {
  FTHZNetworkTask *task = [[FTHZNetworkTask alloc]
      initWithMethod:kHZHTTPMethodPOST
                 url:[baseURL() URLByAppendingPathComponent:KURLQWXLogin]
           bizParser:[FTHZUniversalBizParser
                         parserWithDataParser:[FTHZObjectDataParser
                                                  dataParserWithObjectClass:
                                                      [UserPersonResult class]]
                                  flattenData:YES]];
  NSMutableDictionary *bodyJSON = [[NSMutableDictionary alloc] init];

  bodyJSON[@"code"] = [code copy];

  [task setJSONBody:[bodyJSON copy]];
  return task;
}

+ (FTHZNetworkTask *)logout {
  FTHZNetworkTask *task = [[FTHZNetworkTask alloc]
      initWithMethod:kHZHTTPMethodGET
                 url:[baseURL() URLByAppendingPathComponent:kFTHZLogoutPath]
           bizParser:[FTHZUniversalBizParser
                         parserWithDataParser:[FTHZObjectDataParser
                                                  dataParserWithObjectClass:
                                                      [UserPersonResult class]]
                                  flattenData:YES]];
  return task;
}

@end

NS_ASSUME_NONNULL_END
