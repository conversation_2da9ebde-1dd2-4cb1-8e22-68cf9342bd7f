import Foundation
import SnapKit

private let straitCellID = "straitCellID"
class FZStraitVC: FZBaseVC {
  var collectionView: UICollectionView!
  var straitNavItems: [FTHZStraitModel]?
  let layout = StriatNavLayout()
  private lazy var tableView = UITableView.init()
  private let sortHotBt = UIButton.init()
  private let sortLastBt = UIButton.init()
  private var currentPage: Int = 1
  private var isFirstLoad = true
  private var currentSort: Int = 1
  private var titleL: UILabel!
  private var buttonContainer: UIView!
  private var titleTopConstraint: Constraint!
  private var isNotchScreen: Bool {
    if #available(iOS 11.0, *) {
      let window = UIApplication.shared.windows.first
      return window?.safeAreaInsets.top ?? 0 > 20
    }
    return false
  }
  var cellsData: [FTHZChannelModel]?
  private var topSpaceView: UIView!
  func loadStraitList() {
    FTHZStraitListModel.getStraitList({ [weak self] (res) in
      let member = FTHZStraitListModel.mj_object(withKeyValues: res)
      if member?.success.boolValue ?? false {
        let tempArr = NSMutableArray.init()
        for item in member!.data {
          if let model = FTHZStraitModel.mj_object(withKeyValues: item) {
            tempArr.add(model)
          }
        }
        self?.straitNavItems = tempArr.copy() as? [FTHZStraitModel]
        self?.collectionView.reloadData()
        self?.loadChannelList()
      } else {
        self?.showMsgFast(member?.msg ?? "请求数据发生错误，请稍后再试")

      }
    }) { [weak self] (err) in
      self?.showMsgFast("网络错误，请检查网络后重试")
    }
  }

  private func setUpUI() {
    let margin: CGFloat = kRealWidth(24)
    layout.itemSize = CGSize.init(width: kRealWidth(52), height: kRealWidth(122))
    layout.selectedSize = CGSize.init(width: kRealWidth(191), height: kRealWidth(122))
    layout.minimumLineSpacing = margin
    layout.minimumInteritemSpacing = kRealWidth(6)
    layout.sectionInset = UIEdgeInsets(top: 0, left: margin, bottom: 0, right: margin)
    layout.scrollDirection = .horizontal

    topSpaceView = UIView()
    topSpaceView.backgroundColor = .clear
    contentView.addSubview(topSpaceView)
    topSpaceView.snp.makeConstraints { (make) in
      make.top.equalToSuperview()
      make.left.right.equalToSuperview()
      make.height.equalTo(isNotchScreen ? kRealWidth(108) : kRealWidth(84))
    }

    collectionView = UICollectionView(frame: CGRect.zero, collectionViewLayout: layout)
    collectionView.backgroundColor = .white
    collectionView.dataSource = self
    collectionView.delegate = self
    collectionView.register(StraitNavCell.self, forCellWithReuseIdentifier: straitCellID)
    contentView.addSubview(collectionView)
    collectionView.showsHorizontalScrollIndicator = false
    collectionView.showsVerticalScrollIndicator = false
    // 限制collectionView的交互区域，防止与tableView冲突
    collectionView.clipsToBounds = true
    collectionView.isExclusiveTouch = true
    collectionView.snp.makeConstraints { (make) in
      make.top.equalTo(topSpaceView.snp.bottom)
      make.left.right.equalToSuperview()
      make.height.equalTo(kRealWidth(150))
    }

    // titleL = UILabel.init()
    // titleL.text = "排序"
    // titleL.font = .ft_SourceHanserifSC_Blod_10
    // titleL.textColor = .fz_DrawBlackColor
    // contentView.addSubview(titleL)
    // titleL.snp.makeConstraints { (make) in
    //   make.left.equalToSuperview().offset(kRealWidth(26))
    //   make.height.equalTo(kRealWidth(10))
    //   titleTopConstraint =
    //     make.top.equalTo(collectionView.snp_bottom).offset(kRealWidth(15)).constraint
    // }

    buttonContainer = UIView()
    buttonContainer.backgroundColor = .fz_lightgrayBlackColor
    buttonContainer.layer.cornerRadius = kRealWidth(12)
    buttonContainer.layer.masksToBounds = true
    contentView.addSubview(buttonContainer)
    buttonContainer.snp.makeConstraints { (make) in
      make.left.equalToSuperview().offset(kRealWidth(24))  // 原titleL的位置
      make.top.equalTo(collectionView.snp.bottom).offset(kRealWidth(15))
      make.height.equalTo(kRealWidth(24))
      make.width.equalTo(kRealWidth(64))
    }

    sortLastBt.setTitle("最新", for: .normal)
    sortLastBt.setTitleColor(.fz_HighBlackColor, for: .normal)
    sortLastBt.setTitleColor(.white, for: .selected)
    sortLastBt.titleLabel?.font = .ft_SourceHanserifSC_Blod_10
    sortLastBt.setBackgroundColor(
      color: USERINFO.gender == "1" ? .fz_skyBlueColor : .fz_femalePinkColor, forState: .selected)
    sortLastBt.setBackgroundColor(color: .fz_lightgrayBlackColor, forState: .normal)
    sortLastBt.isSelected = true
    buttonContainer.addSubview(sortLastBt)
    sortLastBt.snp.makeConstraints { (make) in
      make.left.equalToSuperview()
      make.centerY.equalToSuperview()
      make.size.equalTo(CGSize(width: kRealWidth(32), height: kRealWidth(24)))
    }

    sortLastBt.blockAction { [weak self] (button) in
      guard let self = self else { return }
      let isLastSort = button.isSelected
      button.isSelected = !isLastSort
      self.sortHotBt.isSelected = isLastSort
      if !isLastSort {
        self.currentSort = 1
        self.loadChannelList()
      }
    }

    sortHotBt.setTitle("最热", for: .normal)
    sortHotBt.setTitleColor(.fz_HighBlackColor, for: .normal)
    sortHotBt.setTitleColor(.white, for: .selected)
    sortHotBt.titleLabel?.font = .ft_SourceHanserifSC_Blod_10
    sortHotBt.setBackgroundColor(
      color: USERINFO.gender == "1" ? .fz_skyBlueColor : .fz_femalePinkColor, forState: .selected)
    sortHotBt.setBackgroundColor(color: .fz_lightgrayBlackColor, forState: .normal)
    buttonContainer.addSubview(sortHotBt)
    sortHotBt.snp.makeConstraints { (make) in
      make.left.equalTo(sortLastBt.snp.right)
      make.centerY.equalToSuperview()
      make.size.equalTo(CGSize(width: kRealWidth(32), height: kRealWidth(24)))
    }

    sortHotBt.blockAction { [weak self] (button) in
      guard let self = self else { return }
      let isHotSort = button.isSelected
      button.isSelected = !isHotSort
      self.sortLastBt.isSelected = isHotSort
      if !isHotSort {
        self.currentSort = 2
        self.loadChannelList()
      }
    }

    tableView.separatorStyle = .none
    tableView.delegate = self
    tableView.dataSource = self
    tableView.register(StraitCell.self, forCellReuseIdentifier: NSStringFromClass(StraitCell.self))
    tableView.rowHeight = kRealWidth(67)
    let header = MJChiBaoZiHeader(
      refreshingTarget: self, refreshingAction: #selector(loadChannelList))
    header?.lastUpdatedTimeLabel.isHidden = true
    header?.stateLabel.isHidden = true
    tableView.mj_header = header
    contentView.addSubview(tableView)
    // 设置tableView优先响应手势
    tableView.delaysContentTouches = false
    tableView.snp.makeConstraints { (make) in
      make.left.right.bottom.equalToSuperview()
      make.top.equalTo(buttonContainer.snp.bottom).offset(kRealWidth(15))
    }

    // createBt.frame = CGRect(
    //   x: FZSCREEN_W - kRealWidth(38),
    //   y: FZSCREEN_H * 0.7,
    //   width: kRealWidth(36),
    //   height: kRealWidth(36)
    // )
    // createBt.setImage(UIImage(named: POST_ICON), for: .normal)
    // createBt.blockAction { [weak self] (button) in
    //   self?.createAction()
    // }
    // view.addSubview(createBt)

    loadStraitList()

    let footer = MJChiBaoZiFooter(refreshingTarget: self, refreshingAction: #selector(loadMoreData))
    footer?.isRefreshingTitleHidden = true
    footer?.stateLabel.isHidden = true
    tableView.mj_footer = footer
  }

  @objc private func loadMoreData() {
    currentPage += 1
    loadChannelList(loadMore: true)
  }

  override func viewDidLoad() {
    super.viewDidLoad()
    setUpUI()
  }

  override func viewDidLayoutSubviews() {
    super.viewDidLayoutSubviews()
    // 确保视图层次正确，tableView不应被collectionView遮挡
    contentView.bringSubviewToFront(tableView)
  }

}

extension FZStraitVC: UICollectionViewDelegate, UICollectionViewDataSource {
  func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int)
    -> Int
  {
    return straitNavItems?.count ?? 0
  }

  func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath)
    -> UICollectionViewCell
  {
    let cell =
      collectionView.dequeueReusableCell(withReuseIdentifier: straitCellID, for: indexPath)
      as! StraitNavCell
    if let data = straitNavItems?[indexPath.row] {
      if indexPath.row == layout.currentIndex {
        cell.loadSelected(data)
      } else {
        cell.loadNormal(data)
      }
    }

    return cell
  }

  func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
    collectionView.deselectItem(at: indexPath, animated: true)
    layout.setCurrentIndex(indexPath.row, animated: true)
    currentPage = 1
    tableView.mj_footer?.resetNoMoreData()
    tableView.mj_header?.beginRefreshing()
  }
}

extension FZStraitVC: UITableViewDataSource, UITableViewDelegate {

  @objc func loadChannelList(loadMore: Bool = false) {
    if !loadMore {
      currentPage = 1
    }
    if let data = straitNavItems?[layout.currentIndex] {
      FTHZChannelListModel.getChannelList(
        by: data.islandId,
        sort: "\(currentSort)",
        page: "\(currentPage)",
        size: "20",
        succes: { [weak self] (res) in
          guard let self = self else { return }

          if loadMore {
            self.tableView.mj_footer?.endRefreshing()
          } else {
            self.tableView.mj_header?.endRefreshing()
          }

          let member = FTHZChannelListModel.mj_object(withKeyValues: res)
          if member?.success.boolValue ?? false {
            let tempArr = NSMutableArray.init()
            for item in member!.data.data {
              if let model = FTHZChannelModel.mj_object(withKeyValues: item) {
                tempArr.add(model)
              }
            }

            if loadMore {
              var currentData = self.cellsData ?? []
              currentData.append(contentsOf: tempArr as! [FTHZChannelModel])
              self.cellsData = currentData
            } else {
              self.cellsData = tempArr.copy() as? [FTHZChannelModel]
            }

            if let totalCount = member?.data.count.intValue,
              let currentCount = self.cellsData?.count
            {
              if currentCount >= totalCount {
                self.tableView.mj_footer?.endRefreshingWithNoMoreData()
              } else {
                self.tableView.mj_footer?.resetNoMoreData()
              }
            }

            self.isFirstLoad = false
            self.tableView.reloadData()
          } else {
            if loadMore {
              self.currentPage -= 1
            }
            self.showMsgFast(member?.msg ?? "请求数据发生错误，请稍后再试")
          }
        }
      ) { [weak self] (err) in
        guard let self = self else { return }
        if loadMore {
          self.tableView.mj_footer?.endRefreshing()
          self.currentPage -= 1
        } else {
          self.tableView.mj_header?.endRefreshing()
        }
        self.showMsgFast("网络错误，请检查网络后重试")
      }

    }
  }
  func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
    let baseCount = cellsData?.count ?? 0
    return baseCount + 1
  }

  func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
    if indexPath.row == (cellsData?.count ?? 0) {
      let cell = UITableViewCell()
      cell.backgroundColor = .clear
      cell.selectionStyle = .none
      return cell
    }

    let reuseIdentifier: String = NSStringFromClass(StraitCell.self)
    var cell =
      tableView.dequeueReusableCell(withIdentifier: reuseIdentifier, for: indexPath) as? StraitCell
    if cell == nil {
      cell = StraitCell.init(style: .default, reuseIdentifier: reuseIdentifier)
    }
    if let data = cellsData?[indexPath.row] {
      cell?.update(viewData: data)
    }
    return cell!
  }

  func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
    if indexPath.row == (cellsData?.count ?? 0) {
      return kRealWidth(49)
    }
    return kRealWidth(67)
  }

  func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
    tableView.deselectRow(at: indexPath, animated: true)
    if let data = cellsData?[indexPath.row] {
      let detailVC = FTHZChannelDetailVC.init()
      detailVC.discussId = data.discussId
      navigationController?.pushViewController(detailVC, animated: true)
    }
  }

}

extension FZStraitVC: UIScrollViewDelegate {

  @objc func createAction() {
    if let data = straitNavItems?[layout.currentIndex] {
      let crateVC = FTHZCreateChannelVC.init()
      crateVC.channelId = data.islandId
      crateVC.isVoiceType = (data.islandId.intValue == 1)
      crateVC.typeIconName = data.name
      crateVC.modalPresentationStyle = .fullScreen
      present(crateVC, animated: true, completion: nil)
    }

  }

  func scrollViewWillEndDragging(
    _ scrollView: UIScrollView,
    withVelocity velocity: CGPoint,
    targetContentOffset: UnsafeMutablePointer<CGPoint>
  ) {
    if scrollView == tableView {
      let pan = scrollView.panGestureRecognizer
      let veloc = pan.velocity(in: scrollView).y
      if veloc < -kRealWidth(15) {
        // createBt.isHidden = true
        UIView.animate(withDuration: 0.3) { [weak self] in
          guard let self = self else { return }
          self.topSpaceView.alpha = 0
          self.topSpaceView.snp_updateConstraints({ (make) in
            make.height.equalTo(0)
          })

          self.collectionView.alpha = 0
          self.collectionView.snp_updateConstraints({ (make) in
            make.height.equalTo(0)
          })

          self.buttonContainer.alpha = 0
          self.buttonContainer.snp_updateConstraints({ (make) in
            make.height.equalTo(0)
          })

          self.view.layoutIfNeeded()
        }
      } else if veloc > kRealWidth(15) {
        // createBt.isHidden = false
        UIView.animate(withDuration: 0.3) { [weak self] in
          guard let self = self else { return }
          self.topSpaceView.alpha = 1
          self.topSpaceView.snp_updateConstraints({ (make) in
            make.height.equalTo(self.isNotchScreen ? kRealWidth(108) : kRealWidth(84))
          })

          self.collectionView.alpha = 1
          self.collectionView.snp_updateConstraints({ (make) in
            make.height.equalTo(kRealWidth(150))
          })

          self.buttonContainer.alpha = 1
          self.buttonContainer.snp_updateConstraints({ (make) in
            make.height.equalTo(kRealWidth(24))
          })

          self.view.layoutIfNeeded()
        }
      }
    }
  }
}
