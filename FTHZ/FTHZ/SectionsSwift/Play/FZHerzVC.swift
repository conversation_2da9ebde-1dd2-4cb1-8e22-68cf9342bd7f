import Foundation

class FZHerzVC: FZBaseVC {
  var herzItems: [FTHZHeziRes]?
  private lazy var pageFlowView = HQFlowView.init()
  private lazy var scrollView = UIScrollView.init()
  private lazy var pageIndicatorView: UIView = {
    let view = UIView()
    view.backgroundColor = .clear
    view.clipsToBounds = false
    return view
  }()
  private var topSpaceView: UIView!
  private var indicatorDots: [UIView] = []
  private var bottomHeight: CGFloat = 0
  private var isViewVisible = false
  private var isNotchScreen: Bool {
    if #available(iOS 11.0, *) {
      let window = UIApplication.shared.windows.first
      return window?.safeAreaInsets.top ?? 0 > 20
    }
    return false
  }
  private func configurePageFlowView() {
    pageFlowView.delegate = self
    pageFlowView.dataSource = self
    pageFlowView.minimumPageAlpha = 0.5
    pageFlowView.leftRightMargin = kRealWidth(120)
    pageFlowView.topBottomMargin = 0
    pageFlowView.autoTime = 3.0
    pageFlowView.orientation = HQFlowViewOrientationHorizontal
    pageFlowView.isOpenAutoScroll = false
    pageFlowView.backgroundColor = .clear
  }

  @objc func loadHerzList() {
    FTHZHeziModel.getRecomenHezi({ [weak self] (res) in
      let member = FTHZHeziModel.mj_object(withKeyValues: res)
      if member?.success.boolValue ?? false {
        let tempArr = NSMutableArray.init()
        for item in member!.data {
          if let model = FTHZHeziRes.mj_object(withKeyValues: item) {
            tempArr.add(model)
          }
        }
        self?.herzItems = tempArr.copy() as? [FTHZHeziRes]

        DispatchQueue.main.async { [weak self] in
          guard let self = self else { return }

          self.pageFlowView.isOpenAutoScroll = false

          self.pageFlowView.orginPageCount = self.herzItems?.count ?? 0

          self.setupPageDots()

          self.updateBottom(0)

          self.pageFlowView.reloadData()

          DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
            guard let self = self else { return }
            if self.herzItems?.count ?? 0 > 0 {
              self.pageFlowView.adjustCenterSubview()
              self.pageFlowView.isOpenAutoScroll = true
            }
          }
        }
      } else {
        self?.showMsgFast(member?.msg ?? "请求数据发生错误，请稍后再试")
      }
    }) { [weak self] (err) in
      self?.showMsgFast("网络错误，请检查网络后重试")
    }
  }

  private func setUpUI() {
    topSpaceView = UIView()
    topSpaceView.backgroundColor = .clear
    contentView.addSubview(topSpaceView)
    topSpaceView.snp.makeConstraints { (make) in
        make.top.equalToSuperview()
        make.left.right.equalToSuperview()
        make.height.equalTo(isNotchScreen ? kRealWidth(108) : kRealWidth(84))
    }

    scrollView.backgroundColor = .clear
    scrollView.showsVerticalScrollIndicator = false
    scrollView.showsHorizontalScrollIndicator = false
    contentView.addSubview(scrollView)
    scrollView.snp.makeConstraints { (make) in
      make.top.equalTo(topSpaceView.snp.bottom).offset(kRealWidth(24))
      make.left.right.bottom.equalToSuperview()
    }

    configurePageFlowView()

    scrollView.addSubview(pageFlowView)
    pageFlowView.snp.makeConstraints { (make) in
      make.top.equalToSuperview()
      make.centerX.equalToSuperview()
      make.width.equalTo(view.frame.width)
      make.height.equalTo(kRealWidth(500))
    }

    contentView.addSubview(pageIndicatorView)
    pageIndicatorView.snp.makeConstraints { make in
      make.top.equalTo(pageFlowView.snp.bottom).offset(kRealWidth(20))
      make.centerX.equalToSuperview()
      make.height.equalTo(kRealWidth(10))
      make.width.equalTo(0)
    }

    loadHerzList()
  }

  private func setupPageDots() {
    indicatorDots.forEach { $0.removeFromSuperview() }
    indicatorDots.removeAll()

    let dotSize: CGFloat = kRealWidth(8)
    let dotSpacing = kRealWidth(12)
    let count = herzItems?.count ?? 0

    let totalWidth = (dotSize + dotSpacing) * CGFloat(count) - dotSpacing

    pageIndicatorView.backgroundColor = .clear
    pageIndicatorView.snp.updateConstraints { make in
      make.width.equalTo(totalWidth)
    }

    for i in 0..<count {
      let dotView = UIView(
        frame: CGRect(
          x: CGFloat(i) * (dotSize + dotSpacing),
          y: 0,
          width: dotSize,
          height: dotSize))

      let circleLayer = CAShapeLayer()
      let circlePath = UIBezierPath(
        roundedRect: CGRect(x: 0, y: 0, width: dotSize, height: dotSize),
        cornerRadius: dotSize / 2)

      circleLayer.path = circlePath.cgPath
      circleLayer.fillColor = UIColor.clear.cgColor
      circleLayer.strokeColor = UIColor.fz_lightBlackColor.cgColor
      circleLayer.lineWidth = 1

      dotView.layer.addSublayer(circleLayer)
      dotView.backgroundColor = .clear
      dotView.layer.setValue(circleLayer, forKey: "circleLayer")

      pageIndicatorView.addSubview(dotView)
      dotView.center.y = pageIndicatorView.bounds.height / 2

      indicatorDots.append(dotView)
    }

    if !indicatorDots.isEmpty {
      updateSelectedDot(pageFlowView.currentPageIndex)
    }
  }

  private func updateSelectedDot(_ index: Int) {
    guard index >= 0 && index < indicatorDots.count else { return }

    for (i, dot) in indicatorDots.enumerated() {
      if let circleLayer = dot.layer.value(forKey: "circleLayer") as? CAShapeLayer {
        CATransaction.begin()
        CATransaction.setDisableActions(true)

        if i == index {
          circleLayer.fillColor = UIColor.fz_HighBlackColor.cgColor
          circleLayer.strokeColor = UIColor.fz_HighBlackColor.cgColor
        } else {
          circleLayer.fillColor = UIColor.clear.cgColor
          circleLayer.strokeColor = UIColor.fz_lightBlackColor.cgColor
        }

        CATransaction.commit()
      }
    }
  }

  func updateBottom(_ page: Int) {
    updateSelectedDot(page)
  }

  override func viewDidLoad() {
    super.viewDidLoad()
    setUpUI()
  }

  override func viewDidAppear(_ animated: Bool) {
    super.viewDidAppear(animated)

    if herzItems?.count ?? 0 > 0 {
      DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) { [weak self] in
        guard let self = self else { return }
        self.pageFlowView.adjustCenterSubview()
        self.pageFlowView.isOpenAutoScroll = true
      }
    }
  }

  override func viewDidDisappear(_ animated: Bool) {
    super.viewDidDisappear(animated)
    pageFlowView.isOpenAutoScroll = false
  }

  override func viewWillAppear(_ animated: Bool) {
    super.viewWillAppear(animated)
  }

  override func viewDidLayoutSubviews() {
    super.viewDidLayoutSubviews()

    for dot in indicatorDots {
      dot.layer.cornerRadius = dot.bounds.height / 2
      dot.clipsToBounds = true
    }

    let contentRect: CGRect = scrollView.subviews.reduce(into: .zero) { rect, view in
      rect = rect.union(view.frame)
    }
    scrollView.contentSize = contentRect.size
  }
}

extension FZHerzVC: HQSubViewDelegate, HQFlowViewDelegate, HQFlowViewDataSource {
  func contentMore(_ data: DynamicModelResult!) {
  }

  func userMore(_ uid: String!) {
  }

  func numberOfPages(in flowView: HQFlowView!) -> Int {
    return herzItems?.count ?? 0
  }

  func flowView(_ flowView: HQFlowView!, cellForPageAt index: Int) -> HQIndexBannerSubview! {
    var bannerView = flowView.dequeueReusableCell()
    if bannerView == nil {
      bannerView = HerzCardView.init(frame: pageFlowView.bounds)
    }
    if let herzCardView = bannerView as? HerzCardView {
      herzCardView.parentVC = self
      herzCardView.data = herzItems?[index]
    }
    bannerView?.setupData(herzItems?[index], hasTop: 0)
    return bannerView
  }

  func sizeForPage(in flowView: HQFlowView!) -> CGSize {
    let width = kRealWidth(280)
    return CGSize(width: width, height: kRealWidth(500))
  }

  func didSelectCell(_ subView: HQIndexBannerSubview!, withSubViewIndex subIndex: Int) {
  }

  func willScroll(toPage pageNumber: Int, in flowView: HQFlowView!) {
    updateBottom(pageNumber)
    updateSelectedDot(pageNumber)
  }
}
