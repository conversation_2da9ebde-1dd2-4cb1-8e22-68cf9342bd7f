import Foundation

class TopicNavLayout: UICollectionViewFlowLayout {
  var currentIndex = 0
  var selectedSize: CGSize?
  
  private let animationDuration: TimeInterval = 0.4
  
  override var collectionViewContentSize: CGSize {
    return super.collectionViewContentSize
  }
  
  override func shouldInvalidateLayout(forBoundsChange newBounds: CGRect) -> Bool {
    return true
  }
  
  override func initialLayoutAttributesForAppearingItem(at itemIndexPath: IndexPath) -> UICollectionViewLayoutAttributes? {
    let attributes = super.initialLayoutAttributesForAppearingItem(at: itemIndexPath)
    attributes?.alpha = 0
    attributes?.transform = CGAffineTransform(scaleX: 0.9, y: 0.9)
    return attributes
  }
  
  override func finalLayoutAttributesForDisappearingItem(at itemIndexPath: IndexPath) -> UICollectionViewLayoutAttributes? {
    let attributes = super.finalLayoutAttributesForDisappearingItem(at: itemIndexPath)
    attributes?.alpha = 0
    attributes?.transform = CGAffineTransform(scaleX: 0.9, y: 0.9)
    return attributes
  }
  
  override func prepare(forCollectionViewUpdates updateItems: [UICollectionViewUpdateItem]) {
    super.prepare(forCollectionViewUpdates: updateItems)
  }
  
  override func prepare() {
    super.prepare()
  }
  
  override func layoutAttributesForElements(in rect: CGRect) -> [UICollectionViewLayoutAttributes]? {
    var attributesCopy: [UICollectionViewLayoutAttributes] = []
    if let attributes = super.layoutAttributesForElements(in: rect) {
      attributes.forEach({ attributesCopy.append($0.copy() as! UICollectionViewLayoutAttributes) })
    }
    
    for attributes in attributesCopy {
      attributes.alpha = 1.0
      attributes.transform = .identity
    }
    
    let midItem = Int(attributesCopy.count / 2)
    var preMoveDirec = 0
    if currentIndex == midItem, attributesCopy.count > 0 {
      let midAttri = attributesCopy[midItem]
      if midAttri.frame.minX < ((collectionView?.bounds.width ?? 0) / 2) {
        preMoveDirec = -1
      } else {
        preMoveDirec = 1
      }
    }
    
    for attributes in attributesCopy {
      if attributes.representedElementKind == nil {
        let row = attributes.indexPath.row
        let currentFrame = attributes.frame
        
        let addOffsetX = (selectedSize?.width ?? 0) - currentFrame.width
        if currentIndex == row {
          let addOffsetY = (selectedSize?.height ?? 0) - currentFrame.height
          var newFrame = CGRect.init(
            origin: CGPoint.init(
              x: currentFrame.origin.x, y: currentFrame.origin.y - addOffsetY / 2),
            size: selectedSize ?? currentFrame.size)
          if midItem == row, preMoveDirec > 0 {
            newFrame = CGRect.init(
              origin: CGPoint.init(
                x: minimumLineSpacing,
                y: currentFrame.origin.y + currentFrame.height + minimumLineSpacing),
              size: selectedSize ?? currentFrame.size)
          }
          attributes.frame = newFrame
          attributes.transform = CGAffineTransform(scaleX: 1.05, y: 1.05)
        } else if row < currentIndex {
          if preMoveDirec < 0 {
            let newX = currentFrame.origin.x - addOffsetX
            var newFrame = CGRect.init(
              origin: CGPoint.init(x: currentFrame.origin.x + addOffsetX, y: currentFrame.origin.y),
              size: currentFrame.size)
            if newX < 0 {
              newFrame = CGRect.init(
                origin: CGPoint.init(
                  x: minimumLineSpacing,
                  y: currentFrame.origin.y + currentFrame.height + minimumLineSpacing),
                size: currentFrame.size)
            }
            attributes.frame = newFrame
          }
        } else {
          let newX = currentFrame.origin.x + addOffsetX * (preMoveDirec > 0 ? 2 : 1)
          var newFrame = CGRect.init(
            origin: CGPoint.init(x: newX, y: currentFrame.origin.y), size: currentFrame.size)
          if newX > (collectionView?.bounds.width ?? 0) {
            newFrame = CGRect.init(
              origin: CGPoint.init(
                x: minimumLineSpacing,
                y: currentFrame.origin.y + currentFrame.height + minimumLineSpacing),
              size: currentFrame.size)
            if preMoveDirec > 0 {
              newFrame = CGRect.init(
                origin: CGPoint.init(
                  x: currentFrame.origin.x + addOffsetX,
                  y: currentFrame.origin.y - currentFrame.height - minimumLineSpacing),
                size: currentFrame.size)
            }
          }
          attributes.frame = newFrame
        }
      }
    }
    return attributesCopy
  }
}
