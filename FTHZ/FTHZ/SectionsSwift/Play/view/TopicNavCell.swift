import Foundation

class TopicNavCell: UICollectionViewCell {
  private lazy var nameL = UILabelPadding.init()
  private lazy var avatarIV = UIImageView.init(
    frame: CGRect.init(x: 0, y: 0, width: kReal<PERSON>idth(52), height: kReal<PERSON>idth(76)))
  private lazy var slogan: TTTAttributedLabel = TTTAttributedLabel(frame: CGRect.zero)

  override init(frame: CGRect) {
    super.init(frame: frame)

    nameL.textColor = .white
    nameL.font = .ft_SourceHanserifSC_Meium_14
    nameL.textColor = .fz_HighBlackColor

    slogan.textColor = .fz_titleGreyColor
    slogan.numberOfLines = 0
    slogan.textAlignment = .center
    slogan.font = .ft_SourceHanserifSC_Normal_10
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  func loadNormal(_ data: AffairTagmodelResult) {

    nameL.isHidden = true
    slogan.isHidden = true

    avatarIV.image = UIImage.init(named: data.name)
    addSubview(avatarIV)
    avatarIV.snp.remakeConstraints { (make) in
      make.center.equalToSuperview()
    }

  }

  func loadSelected(_ data: AffairTagmodelResult) {
    nameL.isHidden = false
    slogan.isHidden = false
    let img = UIImage.init(named: data.name + "_选中")
    avatarIV.image = img
    let imgWidth = img?.size.width ?? 0
    let imgHeight = img?.size.height ?? 0

    addSubview(avatarIV)
    avatarIV.snp.remakeConstraints { (make) in
      make.left.equalToSuperview().offset(kRealWidth((73.5 - imgWidth) / 2))
      make.top.equalToSuperview().offset(kRealWidth(6))
      make.width.equalTo(kRealWidth(imgWidth))
      make.height.equalTo(kRealWidth(imgHeight))
    }

    nameL.text = data.name
    addSubview(nameL)
    nameL.snp.remakeConstraints { (make) in
      make.left.equalTo(avatarIV.snp_right).offset(kRealWidth(12))
      make.top.equalTo(avatarIV.snp_top)
      make.height.equalTo(kRealWidth(20))
    }

    addSubview(slogan)
    slogan.snp.makeConstraints { (make) in
      make.centerX.equalTo(nameL)
      make.width.equalTo(kRealWidth(113))
      make.top.equalTo(nameL.snp_bottom).offset(kRealWidth(kRealWidth(2)))
    }

    slogan.text = "\"\(data.slogan)\""
  }

}
