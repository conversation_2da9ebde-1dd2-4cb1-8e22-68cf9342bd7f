import Foundation
import SnapKit

class StraitNavCell: UICollectionViewCell {
  lazy var nameL = UILabelPadding.init()
  lazy var avatarIV = UIImageView.init()
  lazy var slogan = UILabelPadding2.init()
  private lazy var icon = UIImageView.init()

  private let normalSize = CGSize(width: 52, height: kRealWidth(75))
  private let selectedSize = CGSize(width: kRealWidth(88), height: kRealWidth(122))

  private var currentData: FTHZStraitModel?
  private(set) var isCellSelected = false

  override init(frame: CGRect) {
    super.init(frame: frame)
    setupUI()
  }

  private func setupUI() {
    contentView.addSubview(icon)
    contentView.addSubview(avatarIV)
    contentView.addSubview(nameL)
    contentView.addSubview(slogan)

    nameL.textColor = .white
    nameL.backgroundColor = .fz_HighBlackColor
    nameL.layer.cornerRadius = 12
    nameL.layer.masksToBounds = true
    nameL.clipsToBounds = true
    nameL.font = .ft_SourceHanserifSC_Normal_12
    nameL.alpha = 0
    nameL.isHidden = true

    slogan.textColor = .white
    slogan.layer.cornerRadius = 10
    slogan.layer.backgroundColor = UIColor.fz_HighBlackColor2.cgColor
    slogan.font = .ft_SourceHanserifSC_Normal_10
    slogan.alpha = 0
    slogan.isHidden = true

    avatarIV.layer.cornerRadius = kRealWidth(12)
    avatarIV.layer.masksToBounds = true

    setupInitialConstraints()
  }

  private func setupInitialConstraints() {
    avatarIV.snp.makeConstraints { make in
      make.left.equalToSuperview()
      make.centerY.equalToSuperview()
      make.width.equalTo(normalSize.width)
      make.height.equalTo(normalSize.height)
    }

    icon.snp.makeConstraints { make in
      make.centerX.equalTo(avatarIV)
      make.top.equalToSuperview()
    }
  }

  override func prepareForReuse() {
    super.prepareForReuse()
    avatarIV.image = nil
    nameL.text = nil
    slogan.text = nil
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  func loadNormal(_ data: FTHZStraitModel) {
    isCellSelected = false

    icon.image = UIImage(named: data.name)
    avatarIV.netImg(data.picture, EMPTY_ICON)

    nameL.snp.removeConstraints()
    slogan.snp.removeConstraints()

    avatarIV.snp.remakeConstraints { make in
      make.left.equalToSuperview()
      make.centerY.equalToSuperview()
      make.width.equalTo(normalSize.width)
      make.height.equalTo(normalSize.height)
    }

    icon.snp.remakeConstraints { make in
      make.centerX.equalTo(avatarIV)
      make.top.equalToSuperview()
    }

    UIView.animate(
      withDuration: 0.2,
      animations: {
        self.nameL.alpha = 0
        self.slogan.alpha = 0
      }
    ) { _ in
      self.nameL.isHidden = true
      self.slogan.isHidden = true
      self.icon.isHidden = false
    }

    layoutIfNeeded()
  }

  func loadSelected(_ data: FTHZStraitModel) {
    isCellSelected = true

    avatarIV.snp.remakeConstraints { make in
      make.left.equalToSuperview()
      make.centerY.equalToSuperview()
      make.width.equalTo(selectedSize.width)
      make.height.equalTo(selectedSize.height)
    }

    avatarIV.netImg(data.picture, EMPTY_ICON)
    nameL.text = data.name
    nameL.font = .ft_SourceHanserifSC_Blod_14
    updateSloganAttributedText(data.slogan)

    nameL.snp.makeConstraints { make in
      make.left.equalTo(avatarIV.snp.right).offset(kRealWidth(16))
      make.top.equalTo(avatarIV).offset(kRealWidth(12))
      make.height.equalTo(kRealWidth(24))
    }

    slogan.snp.makeConstraints { make in
      make.left.equalTo(avatarIV.snp.right).offset(kRealWidth(16))
      make.width.equalTo(kRealWidth(74))
      make.top.equalTo(nameL.snp.bottom).offset(kRealWidth(12))
      make.bottom.lessThanOrEqualToSuperview().offset(-kRealWidth(8))
    }

    nameL.isHidden = false
    slogan.isHidden = false
    icon.isHidden = true
    nameL.alpha = 0
    slogan.alpha = 0

    UIView.animate(withDuration: 0.2) {
      self.nameL.alpha = 1
      self.slogan.alpha = 1
    }

    layoutIfNeeded()
  }

  func updateSloganAttributedText(_ text: String) {
    let paragraphStyle = NSMutableParagraphStyle()
    paragraphStyle.lineSpacing = kRealWidth(2)
    paragraphStyle.minimumLineHeight = kRealWidth(16)
    paragraphStyle.firstLineHeadIndent = kRealWidth(4)
    paragraphStyle.headIndent = kRealWidth(4)
    paragraphStyle.tailIndent = -kRealWidth(4)
    let attr: [NSAttributedString.Key: Any] = [
      .font: UIFont.ft_SourceHanserifSC_Normal_10,
      .foregroundColor: UIColor.white,
      .paragraphStyle: paragraphStyle,
    ]

    let attributedString = NSMutableAttributedString(string: text, attributes: attr)
    slogan.attributedText = attributedString
    slogan.numberOfLines = 0
  }

  func updateLayoutForAnimation(
    progress: CGFloat, isBecomingSelected: Bool, data: FTHZStraitModel? = nil
  ) {
    if let data = data {
      currentData = data
    }

    let easedProgress = easeInOutCubic(progress)
    let currentSize =
      isBecomingSelected
      ? interpolateSize(from: normalSize, to: selectedSize, progress: easedProgress)
      : interpolateSize(from: selectedSize, to: normalSize, progress: easedProgress)

    avatarIV.snp.remakeConstraints { make in
      make.left.equalToSuperview()
      make.centerY.equalToSuperview()
      make.width.equalTo(currentSize.width)
      make.height.equalTo(currentSize.height)
    }

    if (isBecomingSelected && easedProgress > 0.5) || (!isBecomingSelected && easedProgress < 0.5) {
      nameL.snp.remakeConstraints { make in
        make.left.equalTo(avatarIV.snp.right).offset(kRealWidth(16))
        make.top.equalTo(avatarIV).offset(kRealWidth(12))
        make.height.equalTo(kRealWidth(24))
      }

      slogan.snp.remakeConstraints { make in
        make.left.equalTo(avatarIV.snp.right).offset(kRealWidth(16))
        make.width.equalTo(kRealWidth(74))
        make.top.equalTo(nameL.snp.bottom).offset(kRealWidth(12))
        make.bottom.lessThanOrEqualToSuperview().offset(-kRealWidth(8))
      }

      nameL.isHidden = true
      icon.isHidden = true
      slogan.isHidden = true

      nameL.alpha = isBecomingSelected ? easedProgress : 1 - easedProgress
      icon.alpha = isBecomingSelected ? 1 - easedProgress : easedProgress
    } else {
      nameL.isHidden = true
      slogan.isHidden = true
      icon.isHidden = false
      icon.alpha = 1
    }

    layoutIfNeeded()
  }

  private func interpolateSize(from: CGSize, to: CGSize, progress: CGFloat) -> CGSize {
    let width = from.width + (to.width - from.width) * progress
    let height = from.height + (to.height - from.height) * progress
    return CGSize(width: width, height: height)
  }

  private func easeInOutCubic(_ t: CGFloat) -> CGFloat {
    if t < 0.5 {
      return 4 * t * t * t
    } else {
      return 1 - pow(-2 * t + 2, 3) / 2
    }
  }
}
