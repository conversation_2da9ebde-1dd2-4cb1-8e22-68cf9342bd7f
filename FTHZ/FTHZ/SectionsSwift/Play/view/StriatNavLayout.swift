class StriatNavLayout: UICollectionViewFlowLayout {
  var currentIndex = 0
  private var isAnimating = false
  private var oldIndex: Int = 0
  private var progress: CGFloat = 1.0
  private var contentWidth: CGFloat = 0
  
  private let normalSize = CGSize(width: kR<PERSON><PERSON>idth(52), height: kR<PERSON><PERSON>idth(122))
  var selectedSize = CGSize(width: kReal<PERSON>idth(190), height: kReal<PERSON>idth(122))
  
  override func prepare() {
    super.prepare()
    contentWidth = 0
  }

  func setCurrentIndex(_ index: Int, animated: Bool) {
    if animated && currentIndex != index {
      oldIndex = currentIndex
      currentIndex = index
      isAnimating = true
      progress = 0.0
      
      if let cell = collectionView?.cellForItem(at: IndexPath(item: currentIndex, section: 0)) as? StraitNavCell,
         let data = (collectionView?.dataSource as? FZStraitVC)?.straitNavItems?[currentIndex] {
        cell.loadSelected(data)
      }
      
      startAnimation()
    } else {
      currentIndex = index
      isAnimating = false
      progress = 1.0
    }
    invalidateLayout()
  }

  private func startAnimation() {
    progress = 0.0
    let displayLink = CADisplayLink(target: self, selector: #selector(animationStep))
    displayLink.preferredFramesPerSecond = 60
    displayLink.add(to: .main, forMode: .common)
  }

  @objc private func animationStep(_ displayLink: CADisplayLink) {
    progress += 0.05
    if progress >= 1.0 {
      progress = 1.0
      isAnimating = false
      displayLink.invalidate()
      
      if let cell = collectionView?.cellForItem(at: IndexPath(item: oldIndex, section: 0)) as? StraitNavCell,
         let data = (collectionView?.dataSource as? FZStraitVC)?.straitNavItems?[oldIndex] {
        cell.loadNormal(data)
      }
      
      if let cell = collectionView?.cellForItem(at: IndexPath(item: currentIndex, section: 0)) as? StraitNavCell,
         let data = (collectionView?.dataSource as? FZStraitVC)?.straitNavItems?[currentIndex] {
        cell.loadSelected(data)
      }
      
      // Force recalculation of content size after animation completes
      collectionView?.collectionViewLayout.invalidateLayout()
    }
    invalidateLayout()
  }

  override func layoutAttributesForElements(in rect: CGRect) -> [UICollectionViewLayoutAttributes]? {
    var attributesCopy: [UICollectionViewLayoutAttributes] = []
    if let attributes = super.layoutAttributesForElements(in: rect) {
      attributes.forEach {
        attributesCopy.append($0.copy() as! UICollectionViewLayoutAttributes)
      }
    }
    
    var currentX: CGFloat = sectionInset.left
    let collectionViewHeight = collectionView?.bounds.height ?? 0
    contentWidth = sectionInset.left
    
    // Count total number of items to prevent miscalculation when some cells are off-screen
    let totalItems = collectionView?.numberOfItems(inSection: 0) ?? 0
    
    // Calculate proper width for all items, not just visible ones
    for i in 0..<totalItems {
      let indexPath = IndexPath(item: i, section: 0)
      let isCurrentItem = i == currentIndex
      let isOldItem = isAnimating && i == oldIndex
      
      var itemWidth: CGFloat = 0
      
      if isAnimating {
        if i == currentIndex {
          itemWidth = normalSize.width + (selectedSize.width - normalSize.width) * progress
        } else if i == oldIndex {
          itemWidth = selectedSize.width + (normalSize.width - selectedSize.width) * progress
        } else {
          itemWidth = normalSize.width
        }
      } else {
        itemWidth = isCurrentItem ? selectedSize.width : normalSize.width
      }
      
      // Only add to contentWidth, don't modify attributes yet
      contentWidth += itemWidth
      if i < totalItems - 1 {
        contentWidth += minimumLineSpacing
      }
    }
    
    // Reset currentX for actual layout
    currentX = sectionInset.left
    
    for attributes in attributesCopy {
      if attributes.representedElementKind == nil {
        let row = attributes.indexPath.row
        
        if isAnimating && (row == currentIndex || row == oldIndex) {
          if row == currentIndex {
            let currentSize = interpolateSize(from: normalSize, to: selectedSize, progress: progress)
            let currentY = (collectionViewHeight - currentSize.height) / 2
            
            attributes.frame = CGRect(
              origin: CGPoint(x: currentX, y: currentY),
              size: currentSize
            )
            
            if let cell = collectionView?.cellForItem(at: attributes.indexPath) as? StraitNavCell {
              cell.updateLayoutForAnimation(progress: progress, isBecomingSelected: true)
            }
          } else if row == oldIndex {
            let currentSize = interpolateSize(from: selectedSize, to: normalSize, progress: progress)
            let currentY = (collectionViewHeight - currentSize.height) / 2
            
            attributes.frame = CGRect(
              origin: CGPoint(x: currentX, y: currentY),
              size: currentSize
            )
            
            if let cell = collectionView?.cellForItem(at: attributes.indexPath) as? StraitNavCell {
              cell.updateLayoutForAnimation(progress: progress, isBecomingSelected: false)
            }
          }
        } else if row == currentIndex {
          let currentY = (collectionViewHeight - selectedSize.height) / 2
          attributes.frame = CGRect(
            origin: CGPoint(x: currentX, y: currentY),
            size: selectedSize
          )
          
          if let cell = collectionView?.cellForItem(at: attributes.indexPath) as? StraitNavCell {
            if !cell.isCellSelected {
              if let data = (collectionView?.dataSource as? FZStraitVC)?.straitNavItems?[row] {
                cell.loadSelected(data)
              }
            }
          }
        } else {
          let currentY = (collectionViewHeight - normalSize.height) / 2
          attributes.frame = CGRect(
            origin: CGPoint(x: currentX, y: currentY),
            size: normalSize
          )
          
          if let cell = collectionView?.cellForItem(at: attributes.indexPath) as? StraitNavCell {
            if cell.isCellSelected {
              if let data = (collectionView?.dataSource as? FZStraitVC)?.straitNavItems?[row] {
                cell.loadNormal(data)
              }
            }
          }
        }
        
        currentX += attributes.frame.width + minimumLineSpacing
      }
    }
    
    return attributesCopy
  }
  
  private func interpolateSize(from: CGSize, to: CGSize, progress: CGFloat) -> CGSize {
    let width = from.width + (to.width - from.width) * progress
    let height = from.height + (to.height - from.height) * progress
    return CGSize(width: width, height: height)
  }

  override var collectionViewContentSize: CGSize {
    // Add right inset to content width
    let width = contentWidth + sectionInset.right
    // Use the collection view's visible height, not the cell height
    let height = collectionView?.bounds.height ?? selectedSize.height
    return CGSize(width: width, height: height)
  }

  override func shouldInvalidateLayout(forBoundsChange newBounds: CGRect) -> Bool {
    return true
  }
}
