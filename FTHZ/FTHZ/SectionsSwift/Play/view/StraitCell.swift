import Foundation

class StraitCell: UITableViewCell {

  private let titleL: UILabel
  private let contentL: UILabel
  private let repyCountL: UILabel

  override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
    titleL = UILabel.init()
    titleL.textColor = .fz_titleBlackColor
    titleL.font = .ft_SourceHanserifSC_Blod_14
    repyCountL = UILabel.init()
    repyCountL.textColor = .white
    repyCountL.textAlignment = .center
    repyCountL.layer.backgroundColor = UIColor.fz_HighBlackColor.cgColor
    repyCountL.layer.cornerRadius = 8
    repyCountL.font = .ft_Din_Bold_10

    contentL = UILabel.init()
    contentL.numberOfLines = 1
    contentL.textColor = .fz_titleGreyColor
    contentL.font = .ft_SourceHanserifSC_Meium_13

    super.init(style: style, reuseIdentifier: reuseIdentifier)

    addSubview(repyCountL)
    repyCountL.snp.makeConstraints { (make) in
      make.left.equalToSuperview().offset(kRealWidth(26))
      make.top.equalToSuperview().offset(kRealWidth(12))
      make.size.equalTo(CGSize(width: kRealWidth(32), height: kRealWidth(16)))
    }

    addSubview(titleL)
    titleL.snp.makeConstraints { (make) in
      make.left.equalTo(repyCountL.snp_right).offset(kRealWidth(10))
      make.right.equalToSuperview().offset(-kRealWidth(30))
      make.centerY.equalTo(repyCountL)
    }

    addSubview(contentL)
    contentL.snp.makeConstraints { (make) in
      make.left.right.equalTo(titleL)
      make.top.equalTo(titleL.snp_bottom).offset(kRealWidth(8))
    }

  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func layoutSubviews() {
    super.layoutSubviews()

  }

  func update(viewData: FTHZChannelModel) {
    titleL.text = viewData.title
    contentL.text = viewData.content
    let commentCount = viewData.commentNum.intValue
    repyCountL.text = commentCount > 999 ? "999+" : "\(commentCount)"
  }
}
