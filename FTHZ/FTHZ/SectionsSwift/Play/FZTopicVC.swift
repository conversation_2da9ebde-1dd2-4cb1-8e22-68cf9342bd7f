import Foundation
import SnapKit

private let topicNavCellID = "topicNavCellID"

class FZTopicVC: FZBaseVC {
  var collectionView: UICollectionView!
  let layout = TopicNavLayout()
  var topicNavItems: [AffairTagmodelResult]?
  private lazy var tableView = UITableView.init()
  private lazy var countLabel = UILabel.init()
  var cellsData: [DynamicModelResult]?
  private var isFirstLoad = true
  private var countLabelTopConstraint: Constraint!
  private var topSpaceView: UIView!
  private var isNotchScreen: Bool {
    if #available(iOS 11.0, *) {
      let window = UIApplication.shared.windows.first
      return window?.safeAreaInsets.top ?? 0 > 20
    }
    return false
  }
  var lastId: Int = 0

  var currentPage = 1
  func loadTopicNavList() {
    AffairTagmodel.getAffairTagmodel({ [weak self] (res) in
      let member = AffairTagmodel.mj_object(withKeyValues: res)
      if member?.success.boolValue ?? false {
        let tempArr = NSMutableArray.init()
        for item in member!.data {
          if let model = AffairTagmodelResult.mj_object(withKeyValues: item) {
            tempArr.add(model)
          }
        }
        if tempArr.count > 7 {
          self?.topicNavItems =
            tempArr.subarray(with: NSRange.init(location: 0, length: 7)) as? [AffairTagmodelResult]
        } else {
          self?.topicNavItems = tempArr.copy() as? [AffairTagmodelResult]
        }
        self?.collectionView.reloadData()
        self?.loadTopicDataList()
      } else {
        self?.showMsgFast(member?.msg ?? "请求数据发生错误，请稍后再试")
      }
    }) { [weak self] (err) in
      self?.showMsgFast("网络错误，请检查网络后重试")
    }
  }

  private func setUpUI() {
    let margin: CGFloat = kRealWidth(8)
    layout.itemSize = CGSize.init(width: kRealWidth(73.5), height: kRealWidth(64))
    layout.selectedSize = CGSize.init(width: kRealWidth(182), height: kRealWidth(64))
    layout.minimumLineSpacing = margin
    layout.minimumInteritemSpacing = kRealWidth(24)
    layout.sectionInset = UIEdgeInsets(top: 0, left: margin, bottom: 0, right: margin)
    
    topSpaceView = UIView()
    topSpaceView.backgroundColor = .clear
    contentView.addSubview(topSpaceView)
    topSpaceView.snp.makeConstraints { (make) in
      make.top.equalToSuperview()
      make.left.right.equalToSuperview()
      make.height.equalTo(isNotchScreen ? kRealWidth(108) : kRealWidth(84))
    }
    
    collectionView = UICollectionView(frame: CGRect.zero, collectionViewLayout: layout)
    collectionView.backgroundColor = .white
    collectionView.dataSource = self
    collectionView.delegate = self
    collectionView.register(TopicNavCell.self, forCellWithReuseIdentifier: topicNavCellID)
    contentView.addSubview(collectionView)
    collectionView.showsHorizontalScrollIndicator = false
    collectionView.showsVerticalScrollIndicator = false
    collectionView.snp.makeConstraints { (make) in
      make.top.equalTo(topSpaceView.snp.bottom).offset(kRealWidth(6))
      make.left.right.equalToSuperview()
      make.height.equalTo(kRealWidth(148))
    }

    countLabel.font = .ft_SourceHanserifSC_Blod_14
    countLabel.textColor = .fz_lightBlackColor
    contentView.addSubview(countLabel)
    countLabel.snp.makeConstraints { (make) in
      make.left.equalToSuperview().offset(kRealWidth(24))
      make.height.equalTo(kRealWidth(14))
      countLabelTopConstraint =
        make.top.equalTo(collectionView.snp_bottom).offset(kRealWidth(10)).constraint
    }

    tableView.separatorStyle = .none
    tableView.delegate = self
    tableView.dataSource = self
    tableView.estimatedRowHeight = kRealWidth(110)
    tableView.rowHeight = UITableView.automaticDimension
    tableView.register(
      AttentionTableViewCell.self,
      forCellReuseIdentifier: NSStringFromClass(AttentionTableViewCell.self))
    let header = MJChiBaoZiHeader(
      refreshingTarget: self, refreshingAction: #selector(loadTopicDataList))
    header?.lastUpdatedTimeLabel.isHidden = true
    header?.stateLabel.isHidden = true
    tableView.mj_header = header
    contentView.addSubview(tableView)
    tableView.snp.makeConstraints { (make) in
      make.left.right.bottom.equalToSuperview()
      make.top.equalTo(countLabel.snp_bottom).offset(kRealWidth(15))
    }

    // postBt.frame = CGRect(
    //   x: FZSCREEN_W - kRealWidth(38),
    //   y: FZSCREEN_H * 0.7,
    //   width: kRealWidth(36),
    //   height: kRealWidth(36)
    // )
    // postBt.setImage(UIImage.init(named: POST_ICON), for: .normal)
    // postBt.blockAction { [weak self] (button) in
    //   self?.postAction(button: button)
    // }
    // view.addSubview(postBt)

    loadTopicNavList()

    let footer = MJChiBaoZiFooter(refreshingTarget: self, refreshingAction: #selector(loadMoreData))
    footer?.isRefreshingTitleHidden = true
    footer?.stateLabel.isHidden = true
    tableView.mj_footer = footer
  }

  override func viewDidLoad() {
    super.viewDidLoad()
    setUpUI()
  }

  override func viewDidLayoutSubviews() {
    super.viewDidLayoutSubviews()
  }
}

extension FZTopicVC: UICollectionViewDelegate, UICollectionViewDataSource {
  func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int)
    -> Int
  {
    return topicNavItems?.count ?? 0
  }

  func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath)
    -> UICollectionViewCell
  {
    let cell =
      collectionView.dequeueReusableCell(withReuseIdentifier: topicNavCellID, for: indexPath)
      as! TopicNavCell
    if let data = topicNavItems?[indexPath.row] {
      if indexPath.row == layout.currentIndex {
        cell.loadSelected(data)
      } else {
        cell.loadNormal(data)
      }
    }
    return cell
  }

  func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
    collectionView.deselectItem(at: indexPath, animated: true)
    let oldIndex = layout.currentIndex
    layout.currentIndex = indexPath.row
    
    UIView.animate(withDuration: 0.4, delay: 0, options: [.curveEaseInOut, .allowUserInteraction]) {
      collectionView.performBatchUpdates({
        if oldIndex != indexPath.row {
          collectionView.reloadItems(at: [IndexPath(item: oldIndex, section: 0)])
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
          collectionView.reloadItems(at: [indexPath])
        }
      })
    }
    
    currentPage = 1
    tableView.mj_header.beginRefreshing()
    tableView.mj_footer?.resetNoMoreData()
  }
}
extension FZTopicVC: UITableViewDataSource, UITableViewDelegate {

  @objc func postAction(button: UIButton) {
    let postVC = ReleaseDynamicVC.init()
    if let tagid = topicNavItems?[layout.currentIndex].tagid {
      postVC.preTagId = tagid
    }

    postVC.modalPresentationStyle = .fullScreen
    present(postVC, animated: true) {
    }
  }

  func dolikeAction(_ data: DynamicModelResult, _ index: Int) {
    if data.affair.upvoted() {
      DoLikeModel.postUnlikeModel(
        data.user.uid, contentid: data.affair.aid,
        success: { [weak self] (res) in
          if let resObj = DoLikeModel.mj_object(withKeyValues: res) {
            self?.dealAfterLikeAction(resObj, data, index)
          } else {
            self?.showErr()
          }
        }
      ) { [weak self] (err) in
        self?.showErr()
      }
    } else {
      DoLikeModel.post(
        data.user.uid, contentid: data.affair.aid,
        success: { [weak self] (res) in
          if let resObj = DoLikeModel.mj_object(withKeyValues: res) {
            self?.dealAfterLikeAction(resObj, data, index)
          } else {
            self?.showErr()
          }
        }
      ) { [weak self] (err) in
        self?.showErr()
      }
    }
  }

  func dealAfterLikeAction(_ res: DoLikeModel, _ data: DynamicModelResult, _ index: Int) {
    if res.success.boolValue {
      let tempLike = data.affair.likeNum.toInt() ?? 0
      if data.affair.likeRs == "0" {
        data.affair.likeNum = "\(tempLike + 1)"
        data.affair.likeRs = "1"

      } else {
        data.affair.likeNum = "\(tempLike - 1)"
        data.affair.likeRs = "0"
      }
      let tempArr = NSMutableArray.init(array: cellsData ?? [])
      tempArr.replaceObject(at: index, with: data)
      cellsData = tempArr.copy() as? [DynamicModelResult]

      tableView.reloadRows(at: [IndexPath.init(row: index, section: 0)], with: .none)

    } else if res.code.intValue == 1004 {
      FTHZAlertDialogController.showBannedTip(withMessage: res.msg)
    } else {
      showErr()
    }
  }
  @objc func loadMoreData() {
    currentPage += 1
    loadTopicDataList(isLoadMore: true)
  }
  func gotoWhale(_ uid: String) {
    let whaleVC = WhaleDetailVC.init()
    whaleVC.uid = uid
    navigationController?.pushViewController(whaleVC, animated: true)
  }

  @objc func loadTopicDataList(isLoadMore: Bool = false) {
    if !isLoadMore {
      lastId = 0
    }

    if let data = topicNavItems?[layout.currentIndex] {
      AffairTagListModel.getAffairTagListModel(
        data.tagid,
        lastId: "\(lastId)",
        success: { [weak self] (res) in
          guard let self = self else { return }

          if isLoadMore {
            self.tableView.mj_footer?.endRefreshing()
          } else {
            self.tableView.mj_header?.endRefreshing()
          }

          let member = AffairListModel.mj_object(withKeyValues: res)
          if member?.success.boolValue ?? false,
            let listModel = AffairListModelResult.mj_object(withKeyValues: member?.data.first)
          {
            self.isFirstLoad = false
            if !isLoadMore {
              self.cellsData = []
              self.countLabel.text = "\(listModel.count)只鲸鱼发出声波"
            }

            let tempArr = NSMutableArray.init(array: self.cellsData ?? [])
            for item in listModel.data {
              if let model = DynamicModelResult.mj_object(withKeyValues: item) {
                tempArr.add(model)
              }
            }

            self.lastId = listModel.lastId

            if self.lastId == 0 {
              self.tableView.mj_footer?.endRefreshingWithNoMoreData()
            } else {
              self.tableView.mj_footer?.resetNoMoreData()
            }

            self.cellsData = tempArr.copy() as? [DynamicModelResult]
            self.tableView.reloadData()
          } else {
            if isLoadMore {
              self.currentPage -= 1
            }
            self.showMsgFast(member?.msg ?? "请求数据发生错误，请稍后再试")
          }
        }
      ) { [weak self] (err) in
        guard let self = self else { return }
        if isLoadMore {
          self.tableView.mj_footer?.endRefreshing()
        } else {
          self.tableView.mj_header?.endRefreshing()
        }
        self.showMsgFast("网络错误，请检查网络后重试")
      }
    }
  }

  func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
    let baseCount = cellsData?.count ?? 0
    // 始终添加一个底部空白cell
    return baseCount + 1
  }

  func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
    // 如果是最后一个cell，返回空白cell
    if indexPath.row == (cellsData?.count ?? 0) {
        let cell = UITableViewCell()
        cell.backgroundColor = .clear
        cell.selectionStyle = .none
        return cell
    }

    let reuseIdentifier: String = NSStringFromClass(AttentionTableViewCell.self)
    var cell = tableView.dequeueReusableCell(withIdentifier: reuseIdentifier, for: indexPath) as? AttentionTableViewCell
    if cell == nil {
        cell = AttentionTableViewCell.init(style: .default, reuseIdentifier: reuseIdentifier)
    }
    if let data = cellsData?[indexPath.row] {
        cell?.setDynamic(data, showTime: false)
        cell?.tagBtn.isHidden = true
        cell?.tapIconAction = { [weak self] (uid) in
            self?.gotoWhale(uid)
        }
        cell?.awesomeBtn.blockAction(action: { [weak self] (button) in
            self?.dolikeAction(data, indexPath.row)
        })

        cell?.commentBtn.blockAction(action: { [weak self] (button) in
            let detail = FZMomentVC.init()
            detail.momentId = "\(data.affair.aid)"
            detail.userId = data.user.uid
            detail.withComment = true
            self?.navigationController?.pushViewController(detail, animated: true)
        })
    }
    return cell!
  }

  func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
    // 如果是最后一个cell，返回底部导航栏的高度
    if indexPath.row == (cellsData?.count ?? 0) {
        return kRealWidth(49)
    }
    return UITableView.automaticDimension
  }

  func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
    tableView.deselectRow(at: indexPath, animated: true)
    if let data = cellsData?[indexPath.row] {
      let detail = FZMomentVC.init()
      detail.momentId = "\(data.affair.aid)"
      detail.userId = data.user.uid
      navigationController?.pushViewController(detail, animated: true)
    }
  }
  func showErr() {
    view.makeToast("数据有误，请检查网络后重试", duration: 1.0, position: CSToastPositionCenter)
  }

}

extension FZTopicVC: UIScrollViewDelegate {
  func scrollViewWillEndDragging(
    _ scrollView: UIScrollView,
    withVelocity velocity: CGPoint,
    targetContentOffset: UnsafeMutablePointer<CGPoint>
  ) {
    if scrollView == tableView {
      let pan = scrollView.panGestureRecognizer
      let veloc = pan.velocity(in: scrollView).y
      if veloc < -kRealWidth(15) {
        // postBt.isHidden = true
        UIView.animate(withDuration: 0.3) { [weak self] in
          guard let self = self else { return }
          self.topSpaceView.alpha = 0
          self.topSpaceView.snp_updateConstraints({ (make) in
            make.height.equalTo(0)
          })
          self.collectionView.alpha = 0
          self.collectionView.snp_updateConstraints({ (make) in
            make.height.equalTo(0)
          })
          self.countLabel.alpha = 0
          self.countLabel.snp_updateConstraints({ (make) in
            make.height.equalTo(0)
          })
          self.countLabelTopConstraint.update(offset: 0)
          self.tableView.snp_remakeConstraints({ (make) in
            make.left.right.bottom.equalToSuperview()
            make.top.equalToSuperview()
          })
          self.view.layoutIfNeeded()
        }
      } else if veloc > kRealWidth(15) {
        // postBt.isHidden = false
        UIView.animate(withDuration: 0.3) { [weak self] in
          guard let self = self else { return }
          self.topSpaceView.alpha = 1
          self.topSpaceView.snp_updateConstraints({ (make) in
            make.height.equalTo(self.isNotchScreen ? kRealWidth(108) : kRealWidth(84))
          })
          self.collectionView.alpha = 1
          self.collectionView.snp_updateConstraints({ (make) in
            make.height.equalTo(kRealWidth(148))
          })
          self.countLabel.alpha = 1
          self.countLabel.snp_updateConstraints({ (make) in
            make.height.equalTo(kRealWidth(14))
          })
          self.countLabelTopConstraint.update(offset: kRealWidth(10))
          self.tableView.snp_remakeConstraints({ (make) in
            make.left.right.bottom.equalToSuperview()
            make.top.equalTo(self.countLabel.snp_bottom).offset(kRealWidth(15))
          })
          self.view.layoutIfNeeded()
        }
      }
    }
  }
}
