import Foundation

class FZPlayVC: FZBaseVC, JXCategoryViewDelegate, UIScrollViewDelegate, contentScrollDelegate,
  UIGestureRecognizerDelegate
{
  private var categoryView: JXCategoryTitleView!
  private var scroll: UIScrollView!
  private var topTitleView: UIView!
  private var isNotchScreen: Bool {
    if #available(iOS 11.0, *) {
      let window = UIApplication.shared.windows.first
      return window?.safeAreaInsets.top ?? 0 > 20
    }
    return false
  }
  private func setUI() {
    categoryView = JXCategoryTitleView.init()
    categoryView.delegate = self

    scroll = UIScrollView.init(frame: CGRect.init())
    scroll.delegate = self
    scroll.isPagingEnabled = true
    scroll.showsHorizontalScrollIndicator = false
    scroll.bounces = false
    contentView.addSubview(scroll)
    scroll.snp.makeConstraints { (make) in
      make.top.left.right.bottom.equalToSuperview()
    }

    if #available(iOS 11, *) {
      scroll.contentInsetAdjustmentBehavior = .never
    } else {
      automaticallyAdjustsScrollViewInsets = false
    }

    loadChildVC()
    topTitleView = UIView.init()
    contentView.addSubview(topTitleView)
    topTitleView.snp.makeConstraints { (make) in
      make.left.right.top.equalToSuperview()
      make.height.equalTo(isNotchScreen ? kRealWidth(108) : kRealWidth(84))
    }
    let blurEffect = UIBlurEffect(style: .extraLight)
    let visualEffectView = UIVisualEffectView(effect: blurEffect)
    visualEffectView.frame = topTitleView.bounds
    visualEffectView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
    visualEffectView.layer.cornerRadius = 20.0
    visualEffectView.layer.maskedCorners = [.layerMinXMaxYCorner, .layerMaxXMaxYCorner]
    visualEffectView.clipsToBounds = true
    topTitleView.addSubview(visualEffectView)
    
    let backgroundView = UIView()
    backgroundView.backgroundColor = UIColor.white.withAlphaComponent(0.1)
    backgroundView.frame = visualEffectView.bounds
    backgroundView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
    visualEffectView.contentView.addSubview(backgroundView)

    let topBorder = CALayer()
    topBorder.frame = CGRect(x: 0, y: 0, width: topTitleView.frame.size.width, height: 0.5)
    topBorder.backgroundColor = UIColor(white: 0.0, alpha: 0.1).cgColor
    visualEffectView.layer.addSublayer(topBorder)

    topTitleView.layer.shadowColor = UIColor.black.cgColor
    topTitleView.layer.shadowOffset = CGSize(width: 0, height: 2.0)
    topTitleView.layer.shadowOpacity = 0.2
    topTitleView.layer.shadowRadius = 4.0
    topTitleView.layer.masksToBounds = false
    visualEffectView.layer.masksToBounds = true

    categoryView.titles = ["声波", "主题", "碎碎念", "赫兹"]
    categoryView.isAverageCellSpacingEnabled = false
    categoryView.titleLabelZoomScale = 1.5
    categoryView.isTitleLabelZoomEnabled = true
    categoryView.titleFont = UIFont.ft_SourceHanserifSC_Blod_18
    categoryView.isTitleLabelZoomScrollGradientEnabled = true
    categoryView.titleColor = UIColor.fz_lightBlackColor
    categoryView.titleSelectedColor = UIColor.fz_HighBlackColor
    categoryView.isCellWidthZoomEnabled = false
    categoryView.contentScrollView = scroll
    categoryView.cellWidth = JXCategoryViewAutomaticDimension
    categoryView.contentEdgeInsetLeft = 20
    categoryView.contentEdgeInsetRight = 0
    topTitleView.addSubview(categoryView)
    categoryView.snp.makeConstraints { (make) in
      make.left.equalToSuperview().offset(kRealWidth(12))
      make.right.equalToSuperview().offset(-kRealWidth(24))
      make.top.equalToSuperview().offset(isNotchScreen ? kRealWidth(50) : kRealWidth(26))
      make.height.equalTo(kRealWidth(40))
    }

  }

  private func loadChildVC() {

    let straitVC = FZStraitVC.init()
    addChild(straitVC)
    scroll.addSubview(straitVC.view)
    straitVC.view.snp.makeConstraints { (make) in
      make.left.top.equalToSuperview()
      make.width.equalTo(view.frame.width)
      make.bottom.equalTo(contentView)
    }

    let topicVC = FZTopicVC.init()
    addChild(topicVC)
    scroll.addSubview(topicVC.view)
    topicVC.view.snp.makeConstraints { (make) in
      make.top.equalToSuperview()
      make.left.equalTo(straitVC.view.snp.right)
      make.width.equalTo(view.frame.width)
      make.bottom.equalTo(contentView)
    }

    let shudongVC = ShudongDetailVC()
    shudongVC.mainTempScale = kRealWidth(1)
    addChild(shudongVC)
    scroll.addSubview(shudongVC.view)
    shudongVC.view.snp.makeConstraints { make in
      make.top.equalToSuperview()
      make.left.equalTo(topicVC.view.snp.right)
      make.width.equalTo(view.frame.width)
      make.bottom.equalTo(contentView)
    }

    let herzVC = FZHerzVC.init()
    addChild(herzVC)
    scroll.addSubview(herzVC.view)
    herzVC.view.snp.makeConstraints { (make) in
      make.top.equalToSuperview()
      make.left.equalTo(shudongVC.view.snp.right)
      make.width.equalTo(view.frame.width)
      make.bottom.equalTo(contentView)

    }
  }
  override func viewDidLoad() {
    super.viewDidLoad()
    navigationController?.navigationBar.isHidden = true
    navigationController?.interactivePopGestureRecognizer?.delegate = self
    setUI()
  }

  override func viewDidLayoutSubviews() {
    let contentRect: CGRect = scroll.subviews.reduce(into: .zero) { rect, view in
      rect = rect.union(view.frame)
    }
    scroll.contentSize = contentRect.size
  }
  func categoryView(_ categoryView: JXCategoryBaseView!, didSelectedItemAt index: Int) {

  }
}
