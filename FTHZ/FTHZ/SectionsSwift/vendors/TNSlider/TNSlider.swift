import UIKit

extension String {
  func height(withConstrainedWidth width: C<PERSON><PERSON>loat, font: UIFont) -> CGFloat {
    let constraintRect = CGSize(width: width, height: .greatestFiniteMagnitude)
    let boundingBox = self.boundingRect(
      with: constraintRect, options: .usesLineFragmentOrigin,
      attributes: [NSAttributedString.Key.font: font], context: nil)

    return boundingBox.height
  }

  func width(withConstraintedHeight height: CGFloat, font: UIFont) -> CGFloat {
    let constraintRect = CGSize(width: .greatestFiniteMagnitude, height: height)
    let boundingBox = self.boundingRect(
      with: constraintRect, options: .usesLineFragmentOrigin,
      attributes: [NSAttributedString.Key.font: font], context: nil)

    return boundingBox.width
  }
}

@objc public protocol TNSliderDelegate: class {
  func slider(_ slider: TNSlider, displayTextForValue value: Float) -> String
}

@IBDesignable
public class TNSlider: UIControl {

  @IBOutlet weak public var delegate: TNSliderDelegate? {
    didSet {
      thumbWidth = kRealWidth(45)
      thumbLayer.bounds = CGRect(x: 0, y: 0, width: thumbWidth, height: thumbHeight)
      thumbLayer.position = CGPoint(x: positionForValue(value: minimum), y: bounds.size.height / 2)
      thumbLayer.cornerRadius = thumbHeight / 2
      thumbLayer.shadowPath =
        UIBezierPath(roundedRect: thumbLayer.bounds, cornerRadius: thumbHeight / 2).cgPath

      updateThumbLayersText()
    }
  }

  func log(_ msg: String) {
  }

  @IBInspectable public var value: Float = 0 {
    didSet {
      if value < minimum {
        minimum = value
      }

      if value > maximum {
        maximum = value
      }

      reinitComponentValues()
      redrawLayers()
    }
  }

  @IBInspectable public var minimum: Float = 0 {
    didSet {
      log("Minimum didSet")
      if minimum > maximum {
        maximum = minimum
      }

      if value < minimum {
        value = minimum
      }
      log("Final minimum: \(minimum)")
      reinitComponentValues()
      redrawLayers()
    }
  }

  @IBInspectable public var maximum: Float = 1 {
    didSet {
      log("Maximum didSet")
      if maximum < minimum {
        minimum = maximum
      }

      if value > maximum {
        value = maximum
      }
      log("Final maximum: \(maximum)")
      reinitComponentValues()
      redrawLayers()
    }
  }

  @IBInspectable public var step: Float = 0 {
    didSet {
      log("Step didSet")
      if step < 0 {
        step = 0
      }
      if step > maximum - minimum {
        maximum = minimum + step
      }

      log("Final step \(step)")
    }
  }

  @IBInspectable public var trackMinColor: UIColor = TNConstants.trackMinColor {
    didSet {
      trackLayer.trackMinColor = trackMinColor
      redrawLayers()
    }
  }

  @IBInspectable public var trackMaxColor: UIColor = TNConstants.trackMaxColor {
    didSet {
      trackLayer.trackMaxColor = trackMaxColor
      redrawLayers()
    }
  }

  @IBInspectable public var thumbBackgroundColor: UIColor = TNConstants.thumbBackgroundColor {
    didSet {
      thumbLayer.backgroundColor = thumbBackgroundColor.cgColor
      redrawLayers()
    }
  }

  @IBInspectable public var thumbTextColor: UIColor = TNConstants.thumbTextColor {
    didSet {
      thumbLayer.foregroundColor = thumbTextColor.cgColor
      redrawLayers()
    }
  }

  @IBInspectable public var continuous: Bool = true

  private var trackLayer: TNTrackLayer
  private var thumbLayer: CATextLayer

  private var previousTouchPoint = CGPoint.zero
  private var usableTrackingLength: CGFloat = 0
  private var pointsPerValueScale: CGFloat = 1

  private let trackHeight: CGFloat = kRealWidth(3)
  private let trackInset: CGFloat = 0
  private let thumbHeight: CGFloat = kRealWidth(21)
  private var thumbWidth: CGFloat = kRealWidth(21)

  required public override init(frame: CGRect) {
    continuous = true

    trackLayer = TNTrackLayer()
    thumbLayer = TNTextLayer()

    super.init(frame: frame)
    translatesAutoresizingMaskIntoConstraints = false
    layer.addSublayer(trackLayer)
    layer.addSublayer(thumbLayer)

    initLayers()
    commonInit()
  }

  required public init?(coder aDecoder: NSCoder) {
    continuous = true
    trackLayer = TNTrackLayer()
    thumbLayer = TNTextLayer()

    super.init(coder: aDecoder)
    translatesAutoresizingMaskIntoConstraints = false
    layer.addSublayer(trackLayer)
    layer.addSublayer(thumbLayer)

    initLayers()
    commonInit()
  }

  func initLayers() {
    trackLayer.contentsScale = UIScreen.main.scale
    trackLayer.frame = trackRectForBound(bounds)
    trackLayer.setNeedsDisplay()

    initThumbLayer()
  }

  func initThumbLayer() {
    thumbLayer.anchorPoint = CGPoint(x: 0.5, y: 0.5)
    thumbWidth = kRealWidth(45)
    thumbLayer.bounds = CGRect(x: 0, y: 0, width: thumbWidth, height: thumbHeight)
    thumbLayer.position = CGPoint(x: positionForValue(value: minimum), y: bounds.size.height / 2)
    thumbLayer.foregroundColor = UIColor.black.cgColor
    thumbLayer.cornerRadius = thumbHeight / 2

    thumbLayer.fontSize = kRealWidth(12)
    thumbLayer.font = UIFont.ft_Din_Bold_12
    thumbLayer.backgroundColor = UIColor.white.cgColor
    thumbLayer.alignmentMode = CATextLayerAlignmentMode.center
    thumbLayer.contentsScale = UIScreen.main.scale

    thumbLayer.masksToBounds = false
    thumbLayer.shadowOffset = CGSize(width: 0, height: 0.5)
    thumbLayer.shadowColor = UIColor.black.cgColor
    thumbLayer.shadowRadius = 2
    thumbLayer.shadowOpacity = 0.125
    thumbLayer.shadowPath =
      UIBezierPath(roundedRect: thumbLayer.bounds, cornerRadius: thumbHeight / 2).cgPath

  }

  func commonInit() {
    usableTrackingLength = bounds.size.width - thumbWidth
    translatesAutoresizingMaskIntoConstraints = false
  }

  func reinitComponentValues() {
    trackLayer.minimumValue = minimum
    trackLayer.maximumValue = maximum
    trackLayer.value = value
    trackLayer.valuePositon = positionForValue(value: value)

    updateThumbLayersText()
    updateThumbLayersPosition()
  }

  func redrawLayers() {
    thumbLayer.setNeedsDisplay()
    trackLayer.setNeedsDisplay()
  }

  func updateThumbLayersText() {
    thumbLayer.string = textForValue(value)
  }

  func updateThumbLayersPosition() {
    CATransaction.begin()
    CATransaction.setDisableActions(true)
    CATransaction.setAnimationDuration(0)

    let thumbCenterX = positionForValue(value: value)
    thumbLayer.position = CGPoint(x: thumbCenterX, y: bounds.size.height / 2)
    CATransaction.commit()
  }

  func updateLayersValue() {
    updateThumbLayersText()
    trackLayer.value = value
    trackLayer.valuePositon = positionForValue(value: value)
  }

  func positionForValue(value: Float) -> CGFloat {
    if minimum == maximum {
      return thumbWidth / 2
    }

    return usableTrackingLength * CGFloat((value - minimum) / (maximum - minimum)) + thumbWidth / 2
  }

  public override func beginTracking(_ touch: UITouch, with event: UIEvent?) -> Bool {
    previousTouchPoint = touch.location(in: self)
    if thumbLayer.frame.contains(previousTouchPoint) {
      return true
    }
    return false
  }

  public override func continueTracking(_ touch: UITouch, with event: UIEvent?) -> Bool {

    let touchPoint = touch.location(in: self)
    let delta = touchPoint.x - previousTouchPoint.x
    let valueDelta = (maximum - minimum) * Float(delta / usableTrackingLength)

    var tempValue = value + valueDelta
    if tempValue > maximum {
      tempValue = maximum
    } else if tempValue < minimum {
      tempValue = minimum
    }

    if tempValue == value {
      return true
    }

    value = tempValue
    previousTouchPoint = touchPoint

    if continuous {
      sendActions(for: .valueChanged)
    }

    return true
  }

  public override func endTracking(_ touch: UITouch?, with event: UIEvent?) {
    super.endTracking(touch, with: event)

    if step > 0 {
      let noOfStep = (value / step).rounded(.toNearestOrEven)
      value = noOfStep * step
    }

    if !continuous {
      sendActions(for: .valueChanged)
    }
  }

  public override func layoutSubviews() {
    super.layoutSubviews()

    trackLayer.frame = trackRectForBound(bounds)
    commonInit()
    updateThumbLayersPosition()
    redrawLayers()
  }

  public override var intrinsicContentSize: CGSize {
    return CGSize(width: 118, height: 31)
  }

  public override func prepareForInterfaceBuilder() {
    trackLayer.frame = trackRectForBound(bounds)
    commonInit()
    updateThumbLayersPosition()
    redrawLayers()
  }

  func trackRectForBound(_ bound: CGRect) -> CGRect {
    return CGRect(
      x: trackInset, y: kRealWidth(2), width: bound.size.width - 2 * trackInset,
      height: bound.size.height - kRealWidth(4))
  }

  func textForValue(_ value: Float) -> String {
    if let delegate = delegate {
      return delegate.slider(self, displayTextForValue: value)
    } else {
      return String(format: "%.2f", value)
    }
  }

}
