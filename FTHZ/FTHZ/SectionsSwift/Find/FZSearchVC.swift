import Foundation

class FZSearchVC: FZBaseVC {

  private var logoView: UIView?
  private var listView: UIView?

  private lazy var searchBar = UIView.init()
  private lazy var searchInput = UITextField.init()
  private lazy var listVC: ConfigurableTableViewController = ConfigurableTableViewController()
  private var items: [SearchResModel]?
  private let topSearchBar = UIView.init()
  private let countTips = UILabel.init()
  
  private var recommendUsers: [RecommendSearchUser] = []
  private var recommendAvatarView: UIView?
  private var avatarScrollView: UIScrollView?
  private var animationTimer: Timer?
  private var displayLink: CADisplayLink?
  private var animationStartTime: CFTimeInterval = 0
  
  private var isNotchScreen: Bool {
    if #available(iOS 11.0, *) {
      let window = UIApplication.shared.windows.first
      return window?.safeAreaInsets.top ?? 0 > 20
    }
    return false
  }
  func setupInitUI() {
    listView?.isHidden = true
    searchInput.resignFirstResponder()

    if logoView == nil {
      logoView = UIView.init()
      contentView.addSubview(logoView!)
      logoView?.snp.makeConstraints { (make) in
        make.edges.equalToSuperview()
      }

      let leftIcon = UIView.init()
      leftIcon.backgroundColor = .fz_HighBlackColor
      leftIcon.layer.cornerRadius = kRealWidth(12)
      leftIcon.layer.masksToBounds = true
      logoView?.addSubview(leftIcon)
      leftIcon.snp.makeConstraints { (make) in
        make.left.equalToSuperview().offset(kRealWidth(145))
        make.top.equalToSuperview().offset(kRealWidth(160))
        make.size.equalTo(CGSize.init(width: kRealWidth(24), height: kRealWidth(48)))
      }

      let rightIcon = UIView.init()
      rightIcon.backgroundColor = .fz_HighBlackColor
      rightIcon.layer.cornerRadius = kRealWidth(12)
      rightIcon.layer.masksToBounds = true
      logoView?.addSubview(rightIcon)
      rightIcon.snp.makeConstraints { (make) in
        make.right.equalToSuperview().offset(-kRealWidth(145))
        make.top.equalTo(leftIcon)
        make.size.equalTo(CGSize.init(width: kRealWidth(24), height: kRealWidth(48)))
      }

      let hiLabel = UILabel.init()
      hiLabel.text = ""
      hiLabel.textColor = .fz_bigGreyColor
      hiLabel.font = .ft_SourceHanserifSC_Meium_24
      logoView?.addSubview(hiLabel)
      hiLabel.snp.makeConstraints { (make) in
        make.centerX.equalToSuperview()
        make.top.equalTo(leftIcon.snp_bottom).offset(kRealWidth(40))
      }

      logoView?.addSubview(searchBar)
      searchBar.backgroundColor = .fz_HighBlackColor
      searchBar.layer.cornerRadius = kRealWidth(20)
      searchBar.layer.masksToBounds = true
      searchBar.snp.makeConstraints { (make) in
        make.left.equalToSuperview().offset(kRealWidth(24))
        make.right.equalToSuperview().offset(-kRealWidth(24))
        make.top.equalTo(leftIcon.snp_bottom).offset(kRealWidth(80))
        make.height.equalTo(kRealWidth(40))
      }
      searchInput.placeholder = ""
      searchInput.textColor = .white
      searchInput.tintColor = .white
      searchInput.keyboardAppearance = .dark
      searchInput.returnKeyType = .done
      searchInput.delegate = self
      searchInput.font = .ft_SourceHanserifSC_Normal_16
      searchBar.addSubview(searchInput)
      searchInput.snp.makeConstraints { (make) in
        make.left.equalToSuperview().offset(kRealWidth(15))
        make.right.equalToSuperview().offset(-kRealWidth(45))
        make.centerY.equalToSuperview()
        make.height.equalTo(kRealWidth(30))
      }

      let searchBt = UIButton.init()
      searchBt.setImage(UIImage.init(named: SEARCH_WHITE_ICON), for: .normal)
      searchBt.addTarget(self, action: #selector(searchAction), for: .touchUpInside)
      searchBar.addSubview(searchBt)
      searchBt.snp.makeConstraints { (make) in
        make.right.equalToSuperview().offset(-kRealWidth(15))
        make.centerY.equalToSuperview()
        make.size.equalTo(CGSize.init(width: kRealWidth(20), height: kRealWidth(20)))
      }

      let backBt = UIButton.init()
      backBt.setImage(UIImage.init(named: "back"), for: .normal)
      backBt.addTarget(self, action: #selector(backAction), for: .touchUpInside)
      logoView?.addSubview(backBt)
      backBt.snp.makeConstraints { (make) in
        make.left.equalToSuperview().offset(kRealWidth(24))
        make.top.equalToSuperview().offset(isNotchScreen ? kRealWidth(58) : kRealWidth(34))
        make.size.equalTo(CGSize.init(width: kRealWidth(10), height: kRealWidth(15)))
      }
      
      setupRecommendAvatarView()
      
    } else {
      searchInput.removeFromSuperview()
      searchInput.textColor = .white
      searchInput.font = .ft_SourceHanserifSC_Normal_16
      searchInput.text = ""
      searchBar.addSubview(searchInput)
      searchInput.snp.makeConstraints { (make) in
        make.left.equalToSuperview().offset(kRealWidth(15))
        make.right.equalToSuperview().offset(-kRealWidth(45))
        make.centerY.equalToSuperview()
        make.height.equalTo(kRealWidth(30))
      }

      logoView?.snp.remakeConstraints { (make) in
        make.edges.equalToSuperview()
      }
      logoView?.isHidden = false
      
      recommendAvatarView?.isHidden = false
      if !recommendUsers.isEmpty {
        startAvatarAnimation()
      }
    }
  }

  func setupListView(keyWord: String) {
    logoView?.isHidden = true
    listView?.isHidden = false
    searchInput.resignFirstResponder()
    
    recommendAvatarView?.isHidden = true
    stopAvatarAnimation()

    if listView == nil {
      listView = UIView.init()
      contentView.addSubview(listView!)
      listView?.snp.makeConstraints({ (make) in
        make.edges.equalToSuperview()
      })

      topSearchBar.backgroundColor = .black
      topSearchBar.layer.cornerRadius = kRealWidth(20)
      topSearchBar.layer.masksToBounds = true
      listView?.addSubview(topSearchBar)
      topSearchBar.snp.makeConstraints { (make) in
        make.left.equalToSuperview().offset(kRealWidth(24))
        make.top.equalToSuperview().offset(isNotchScreen ? kRealWidth(58) : kRealWidth(34))
        make.right.equalToSuperview().offset(kRealWidth(-70))
        make.height.equalTo(kRealWidth(40))
      }

      let searchIcon = UIImageView.init(image: UIImage.init(named: SEARCH_WHITE_ICON))
      topSearchBar.addSubview(searchIcon)
      searchIcon.snp.makeConstraints { (make) in
        make.left.equalToSuperview().offset(kRealWidth(15))
        make.centerY.equalToSuperview()
        make.size.equalTo(CGSize.init(width: kRealWidth(16), height: kRealWidth(16)))
      }

      let leftIcon = UIView.init()
      leftIcon.backgroundColor = .white
      leftIcon.layer.cornerRadius = kRealWidth(2)
      leftIcon.layer.masksToBounds = true
      topSearchBar.addSubview(leftIcon)
      leftIcon.snp.makeConstraints { (make) in
        make.right.equalToSuperview().offset(-kRealWidth(19))
        make.centerY.equalToSuperview()
        make.size.equalTo(CGSize.init(width: kRealWidth(4), height: kRealWidth(8)))
      }

      let rightIcon = UIView.init()
      rightIcon.backgroundColor = .white
      rightIcon.layer.cornerRadius = kRealWidth(2)
      rightIcon.layer.masksToBounds = true
      topSearchBar.addSubview(rightIcon)
      rightIcon.snp.makeConstraints { (make) in
        make.right.equalToSuperview().offset(-kRealWidth(10))
        make.top.equalTo(leftIcon)
        make.size.equalTo(CGSize.init(width: kRealWidth(4), height: kRealWidth(8)))
      }

      let cancelBt = UIButton.init()
      cancelBt.setTitle("取消", for: .normal)
      cancelBt.setTitleColor(.fz_tipButtonColor, for: .normal)
      cancelBt.titleLabel?.font = .ft_SourceHanserifSC_Meium_14
      cancelBt.blockAction { [weak self] (button) in
        self?.setupInitUI()
      }
      listView?.addSubview(cancelBt)
      cancelBt.snp.makeConstraints { (make) in
        make.right.equalToSuperview().offset(-kRealWidth(24))
        make.centerY.equalTo(topSearchBar)
      }

      countTips.textColor = .fz_tipTitleColor
      countTips.font = .ft_SourceHanserifSC_Normal_14
      countTips.textAlignment = .center
      listView?.addSubview(countTips)
      countTips.snp.makeConstraints { (make) in
        make.centerX.equalToSuperview()
        make.top.equalTo(topSearchBar.snp_bottom).offset(kRealWidth(20))
        make.left.equalToSuperview().offset(kRealWidth(24))
        make.right.equalToSuperview().offset(-kRealWidth(24))
      }

      addChild(listVC)
      listView?.addSubview(listVC.view)
      listVC.view.snp.makeConstraints { (make) in
        make.left.right.bottom.equalToSuperview()
        make.top.equalTo(countTips.snp_bottom).offset(kRealWidth(20))
      }

    } else {
      listView?.isHidden = false
    }
    countTips.text = "• 发现了\(items?.count ?? 0)只相似的鱼 •"
    searchInput.removeFromSuperview()
    searchInput.textColor = .white
    searchInput.font = .ft_SourceHanserifSC_Normal_14
    topSearchBar.addSubview(searchInput)
    searchInput.snp.makeConstraints { (make) in
      make.left.equalToSuperview().offset(kRealWidth(40))
      make.centerY.equalToSuperview()
      make.right.equalToSuperview().offset(kRealWidth(-15))
      make.height.equalTo(kRealWidth(30))
    }
    var list: Array = Array<CellConfiguratorType>.init()
    items?.forEach { (m) in
      list.append(CellConfigurator<SearchCell>(viewData: m))
    }
    if list.count > 0 {
      listVC.items = list
      listVC.tableView.separatorStyle = .none
      listVC.tableView.delegate = self
    } else {
      listVC.items = []
    }
  }

  func searchContent(key: String) {
    NetworkManager<SearchResModel>().requestListModel(
      FindAPI.searchContent(key),
      completion: { [weak self] (response) in
        self?.items = response?.dataList
        DispatchQueue.main.async {
          HUD.dissmiss()
          self?.setupListView(keyWord: key)
        }
      }
    ) { [weak self] (error) in
      DispatchQueue.main.async {
        HUD.dissmiss()
        if let msg = error.message {
          self?.view.makeToast(msg)
        }
      }

    }
  }

  @objc func searchAction() {
    HUD.show()
    searchContent(key: searchInput.text ?? "")
  }

  @objc func backAction() {
    navigationController?.popViewController(animated: true)
  }

  @objc func keyboardChanged(_ noti: Notification) {
    guard listView?.isHidden == false else { return }
    let info = noti.userInfo
    let kbRect = (info?[UIResponder.keyboardFrameEndUserInfoKey] as! NSValue).cgRectValue
    let offsetY = kbRect.origin.y - UIScreen.main.bounds.height
    var keyboardHeight: CGFloat = 0
    if offsetY != 0 {
      keyboardHeight = 150
    }
    UIView.animate(withDuration: 0.3) { [weak self] in
      self?.logoView?.snp.updateConstraints { (make) in
        make.top.equalToSuperview().offset(kRealWidth(37) - keyboardHeight)
      }
      self?.view.layoutIfNeeded()
    }

  }

  override func viewDidLoad() {
    super.viewDidLoad()
    setupInitUI()
    navigationController?.interactivePopGestureRecognizer?.isEnabled = true
    NotificationCenter.default.addObserver(
      self, selector: #selector(keyboardChanged(_:)),
      name: UIResponder.keyboardWillChangeFrameNotification, object: nil)
    
    fetchRecommendSearchData()
  }
    
  private var avatarHorizontalSpacing: CGFloat { 24 }
  private var avatarVerticalSpacing: CGFloat { 48 }
  private var avatarPadding: CGFloat { 48 }
  private var avatarSize: CGFloat {
    return (UIScreen.main.bounds.width - 2 * 12 - 3.3 * avatarHorizontalSpacing) / 3.3
  }
  private var avatarViewHeight: CGFloat { avatarSize * 2 + avatarVerticalSpacing + avatarPadding * 2 }
  private var pixelsPerSecond: CGFloat { 25 }

  private func setupRecommendAvatarView() {
    recommendAvatarView = UIView()
    recommendAvatarView?.backgroundColor = .fz_HighBlackColor
    recommendAvatarView?.layer.cornerRadius = 20
    recommendAvatarView?.layer.masksToBounds = true
    logoView?.addSubview(recommendAvatarView!)
    recommendAvatarView?.snp.makeConstraints { (make) in
      make.left.equalToSuperview().offset(12)
      make.right.equalToSuperview().offset(-12)
      make.top.equalTo(searchBar.snp_bottom).offset(kRealWidth(80))
      make.height.equalTo(avatarViewHeight)
    }
    avatarScrollView = UIScrollView()
    avatarScrollView?.backgroundColor = .clear
    avatarScrollView?.showsHorizontalScrollIndicator = false
    avatarScrollView?.isUserInteractionEnabled = true
    recommendAvatarView?.addSubview(avatarScrollView!)
    avatarScrollView?.snp.makeConstraints { (make) in
      make.edges.equalToSuperview()
    }
  }

  private func fetchRecommendSearchData() {
    NetworkManager<RecommendSearchResponse>().requestModel(
      FindAPI.getRecommendSearch,
      completion: { [weak self] (response) in
        DispatchQueue.main.async {
          if let modelResponse = response {
            if let recommendResponse = modelResponse.data {
              self?.recommendUsers = recommendResponse
              self?.setupAvatarDisplay()
              self?.startAvatarAnimation()
            }
          }
        }
      }
    ) { [weak self] (error) in
      if let msg = error.message {
      }
    }
  }
  
  @objc private func avatarTapped(_ sender: UITapGestureRecognizer) {
    guard let view = sender.view else { return }
    let userId = view.tag
    let whaleVC = WhaleDetailVC()
    whaleVC.uid = "\(userId)"
    navigationController?.pushViewController(whaleVC, animated: true)
  }

  private func setupAvatarDisplay() {
    guard let scrollView = avatarScrollView, !recommendUsers.isEmpty else {
      return
    }
    scrollView.subviews.forEach { $0.removeFromSuperview() }
    let rowCount = 2
    let countPerRow = Int(ceil(Double(recommendUsers.count) / Double(rowCount)))
    let duplicatedUsers = recommendUsers + recommendUsers
    let singleGroupWidth = CGFloat(countPerRow) * (avatarSize + avatarHorizontalSpacing)
    scrollView.contentSize = CGSize(width: singleGroupWidth * 2, height: avatarViewHeight)
    for (index, user) in duplicatedUsers.enumerated() {
      let row = index % 2
      let col = index / 2
      let x = CGFloat(col) * (avatarSize + avatarHorizontalSpacing) - (row == 0 ? avatarSize / 2 : 0)
      let y = (row == 0)
        ? avatarPadding
        : (avatarSize + avatarVerticalSpacing + avatarPadding)
      let avatarFrame = CGRect(x: x, y: y, width: avatarSize, height: avatarSize)
      let shadowView = UIView(frame: avatarFrame)
      scrollView.addSubview(shadowView)
      let avatarImageView = UIImageView(frame: shadowView.bounds)
      avatarImageView.contentMode = .scaleAspectFill
      avatarImageView.layer.cornerRadius = 20
      avatarImageView.layer.masksToBounds = true
      avatarImageView.tio_imageUrl(
        user.avatar,
        placeHolderImageName: EMPTY_ICON,
        radius: 0
      )
      avatarImageView.isUserInteractionEnabled = true
      avatarImageView.tag = user.id
      let tap = UITapGestureRecognizer(target: self, action: #selector(avatarTapped(_:)))
      avatarImageView.addGestureRecognizer(tap)
      avatarImageView.applyshadowWithCorner(containerView: shadowView, cornerRadious: 20)
      shadowView.addSubview(avatarImageView)
      let nameLabel = UILabel()
      nameLabel.text = user.nickname
      nameLabel.textColor = .white
      nameLabel.font = .ft_SourceHanSerif_Blod_12
      nameLabel.textAlignment = .center
      nameLabel.numberOfLines = 2
      nameLabel.lineBreakMode = .byTruncatingTail
      nameLabel.frame = CGRect(x: x, y: y + avatarSize + 4, width: avatarSize, height: 32)
      scrollView.addSubview(nameLabel)
    }
  }
  
  private func startAvatarAnimation() {
    guard let scrollView = avatarScrollView, !recommendUsers.isEmpty else { 
      return 
    }
    
    stopAvatarAnimation()
    
    animationStartTime = CACurrentMediaTime()
    displayLink = CADisplayLink(target: self, selector: #selector(updateAvatarAnimation))
    displayLink?.add(to: .main, forMode: .common)
  }
  
  @objc private func updateAvatarAnimation() {
    guard let scrollView = avatarScrollView else { return }
    let rowCount = 2
    let countPerRow = Int(ceil(Double(recommendUsers.count) / Double(rowCount)))
    let currentTime = CACurrentMediaTime()
    let elapsedTime = currentTime - animationStartTime
    let currentOffset = CGFloat(elapsedTime) * pixelsPerSecond
    let singleGroupWidth = CGFloat(countPerRow) * (avatarSize + avatarHorizontalSpacing)
    if currentOffset >= singleGroupWidth {
      scrollView.contentOffset = CGPoint(x: currentOffset - singleGroupWidth, y: 0)
      animationStartTime = currentTime - (singleGroupWidth / pixelsPerSecond)
    } else {
      scrollView.contentOffset = CGPoint(x: currentOffset, y: 0)
    }
  }
  
  private func stopAvatarAnimation() {
    animationTimer?.invalidate()
    animationTimer = nil
    displayLink?.invalidate()
    displayLink = nil
  }
  
  override func viewWillDisappear(_ animated: Bool) {
    super.viewWillDisappear(animated)
    stopAvatarAnimation()
  }
  
  override func viewWillAppear(_ animated: Bool) {
    super.viewWillAppear(animated)
    if !recommendUsers.isEmpty {
      startAvatarAnimation()
    }
  }

  override var preferredStatusBarStyle: UIStatusBarStyle {
    return .lightContent
  }

}

extension FZSearchVC: UITableViewDelegate, UITextFieldDelegate, MomentDelegate {

  func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
    let dy = items?[indexPath.row]

    let tempSize = NemoUtil.calculateLabelHeight(
      byText: .ft_SourceHanserifSC_Normal_14, width: Float(kRealWidth(365)),
      heightMax: Float(kRealWidth(120)), content: dy?.content)

    return kRealWidth(60) + tempSize.height
  }

  func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
    tableView.deselectRow(at: indexPath, animated: true)
    let dy = items?[indexPath.row]
    if let userid = dy?.authorid {
      let whaleVC = WhaleDetailVC.init()
      whaleVC.uid = "\(userid)"
      navigationController?.pushViewController(whaleVC, animated: true)
    }

  }

  func textFieldShouldReturn(_ textField: UITextField) -> Bool {
    textField.resignFirstResponder()
    searchAction()

    return true
  }

  func needRefreshList() {
    searchAction()
  }

  func momentDeleted(index: NSIndexPath) {
  }

}
