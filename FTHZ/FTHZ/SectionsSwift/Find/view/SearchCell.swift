import Foundation

class SearchCell: UITableViewCell {

  private let nameL: UILabel
  private let herzL: UILabel
  private let contentL: UILabel
  private let avatarL: UIImageView

  override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
    avatarL = UIImageView.init(
      frame: CGRect.init(
        x: kRealWidth(24), y: kReal<PERSON>idth(24), width: kRealWidth(30), height: kRealWidth(30)))

    nameL = UILabel.init()
    nameL.textColor = .fz_grayBlueColor
    nameL.font = .ft_SourceHanSerif_Blod_14

    herzL = UILabelPadding.init()
    herzL.textColor = .fz_lightBlackColor
    herzL.font = .ft_Din_Bold_12

    contentL = UILabel.init()
    contentL.numberOfLines = 4
    contentL.textColor = .fz_titleBlackColor
    contentL.font = .ft_SourceHanserifSC_Normal_12

    super.init(style: style, reuseIdentifier: reuseIdentifier)

    addSubview(avatarL)
    avatarL.snp.makeConstraints { (make) in
      make.left.equalToSuperview().offset(kRealWidth(24))
      make.top.equalToSuperview().offset(kRealWidth(12))
      make.size.equalTo(CGSize.init(width: kRealWidth(40), height: kRealWidth(40)))
    }

    addSubview(nameL)
    nameL.snp.makeConstraints { (make) in
      make.left.equalTo(avatarL.snp_right).offset(kRealWidth(12))
      make.top.equalTo(avatarL)
    }

    addSubview(herzL)
    herzL.snp.makeConstraints { (make) in
      make.left.equalTo(nameL.snp_right).offset(kRealWidth(8))
      make.right.lessThanOrEqualToSuperview().offset(-kRealWidth(24))
      make.centerY.equalTo(nameL)
    }

    addSubview(contentL)
    contentL.snp.makeConstraints { (make) in
      make.left.equalTo(nameL)
      make.right.equalToSuperview().offset(-kRealWidth(24))
      make.top.equalTo(nameL.snp_bottom).offset(kRealWidth(8))
    }
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func layoutSubviews() {
    super.layoutSubviews()
  }

  func update(viewData: SearchResModel) {
    avatarL.tio_imageUrl(
      viewData.avatar ?? "", placeHolderImageName: EMPTY_ICON, radius: kRealWidth(24))
    nameL.text = viewData.nickName
    herzL.attributedText = NSAttributedString.init(string: "\(viewData.hertz ?? "10")Hz")
    herzL.changeFont(ofText: "Hz", with: .ft_Din_Bold_10)
    contentL.text = viewData.content?.count ?? 0 > 0 ? viewData.content : "分享图片"
  }
}

extension SearchCell: Updatable {
  typealias ViewData = SearchResModel
}
