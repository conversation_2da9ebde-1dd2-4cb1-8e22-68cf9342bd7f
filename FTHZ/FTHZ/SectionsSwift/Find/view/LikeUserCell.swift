import Foundation

class LikeUserCell: UITableViewCell {
  private let nameL: UILabel
  private let avatarIV: UIImageView
  private let signL: UILabel
  private let timeL: UILabel

  override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
    avatarIV = UIImageView.init(
      frame: CGRect.init(x: 0, y: 0, width: kRealWidth(48), height: kRealWidth(48)))

    avatarIV.layer.cornerRadius = kRealWidth(24)
    avatarIV.layer.masksToBounds = true
    avatarIV.contentMode = .scaleAspectFill

    nameL = UILabel.init()
    nameL.textColor = .fz_grayBlueColor
    nameL.font = .ft_SourceHanSerif_Blod_14

    timeL = UILabel.init()
    timeL.textColor = .fz_detailLightGray
    timeL.font = .ft_SourceHanserifSC_Normal_12

    signL = UILabel.init()
    signL.textColor = .fz_titleGreyColor
    signL.font = .ft_SourceHanserifSC_Normal_12
    super.init(style: style, reuseIdentifier: reuseIdentifier)

    addSubview(avatarIV)
    avatarIV.snp.makeConstraints { (make) in
      make.left.equalToSuperview().offset(kRealWidth(24))
      make.top.equalToSuperview().offset(kRealWidth(8))
      make.size.equalTo(CGSize.init(width: kRealWidth(48), height: kRealWidth(48)))
    }
    addSubview(nameL)
    nameL.snp.makeConstraints { (make) in
      make.left.equalTo(avatarIV.snp_right).offset(kRealWidth(12))
      make.top.equalTo(avatarIV.snp_top).offset(kRealWidth(4))
    }

    addSubview(timeL)
    timeL.snp.makeConstraints { (make) in
      make.left.equalTo(nameL.snp_right).offset(kRealWidth(12))
      make.centerY.equalTo(nameL)
    }

    addSubview(signL)
    signL.snp.makeConstraints { (make) in
      make.left.equalTo(avatarIV.snp_right).offset(kRealWidth(12))
      make.right.equalToSuperview().offset(-kRealWidth(18))
      make.bottom.equalTo(avatarIV.snp_bottom).offset(kRealWidth(-4))
    }
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func prepareForReuse() {
    super.prepareForReuse()

    nameL.text = nil
    timeL.text = nil
    signL.text = nil
    avatarIV.image = nil

    avatarIV.layer.cornerRadius = kRealWidth(24)
    avatarIV.layer.masksToBounds = true

    nameL.layer.sublayers?.forEach { layer in
      if layer.name == "gradientLayer" {
        layer.removeFromSuperlayer()
      }
    }

    nameL.textColor = .fz_grayBlueColor
  }

  func update(viewData: LikesUserModelResult) {
    nameL.text = viewData.nickname
    signL.text = viewData.signature

    if let timestamp = Double(viewData.create_time) {
      let date = Date(timeIntervalSince1970: timestamp)
      let formatter = DateFormatter()
      formatter.dateFormat = "yy年M月d日"
      timeL.text = formatter.string(from: date)
    }

    if let colorM = viewData.colorFont {
      nameL.applyGradient(type: Int(colorM.type), expireTime: Int64(colorM.expire_time))
    }

    avatarIV.tio_imageUrl(viewData.avatar, placeHolderImageName: "empty", radius: kRealWidth(24))

    avatarIV.layer.cornerRadius = kRealWidth(24)
    avatarIV.layer.masksToBounds = true
  }
}

extension LikeUserCell: Updatable {
  typealias ViewData = LikesUserModelResult
}
