import AVFoundation
import AVKit

class FTHZVideoPlayerView: UIView {

  private let coverImageView = UIImageView()

  var coverImageUrl: String? {
    didSet {
      if let urlString = coverImageUrl {
        coverImageView.tio_imageUrl(
          urlString,
          placeHolderImageName: "empty",
          radius: kRealWidth(10)
        )
      }
    }
  }

  var videoUrl: String?

  override init(frame: CGRect) {
    super.init(frame: frame)
    setupUI()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  private func setupUI() {
    addSubview(coverImageView)
    coverImageView.contentMode = .scaleAspectFill
    coverImageView.clipsToBounds = true
    coverImageView.layer.cornerRadius = kRealWidth(10)
    coverImageView.isUserInteractionEnabled = true

    coverImageView.snp.makeConstraints { make in
      make.edges.equalToSuperview()
    }

    let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap))
    coverImageView.addGestureRecognizer(tapGesture)
  }

  @objc private func handleTap() {
    guard let videoUrlString = videoUrl,
      let url = URL(string: videoUrlString)
    else { return }

    let playerVC = FTHZFullScreenVideoPlayerViewController(videoURL: url)

    playerVC.modalPresentationStyle = .overFullScreen

    if let viewController = parentContainerViewController {
      viewController.present(playerVC, animated: true)
    }
  }
}

class FTHZFullScreenVideoPlayerViewController: UIViewController, UIGestureRecognizerDelegate {

  private let player: AVPlayer
  private let playerLayer: AVPlayerLayer
  private var playPauseButton = UIButton(type: .system)
  private var progressView = UIProgressView()
  private var timeObserver: Any?
  private var isPlaying = false
  private var panGesture: UIPanGestureRecognizer!
  private var dragStartY: CGFloat = 0
  private var initialTouchPoint: CGPoint = .zero
  private var timeLabel = UILabel()

  init(videoURL: URL) {
    player = AVPlayer(url: videoURL)
    playerLayer = AVPlayerLayer(player: player)
    super.init(nibName: nil, bundle: nil)

    self.modalPresentationStyle = .overFullScreen
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func viewDidLoad() {
    super.viewDidLoad()
    setupUI()
    setupGestures()
  }

  override func viewWillDisappear(_ animated: Bool) {
    super.viewWillDisappear(animated)
    pauseVideo()
    removeProgressObserver()
  }

  private func setupUI() {
    view.backgroundColor = .black
    view.isOpaque = false
    playerLayer.videoGravity = .resizeAspect
    playerLayer.frame = view.bounds
    view.layer.addSublayer(playerLayer)

    setupPlayPauseButton()

    setupProgressView()
    setupTimeLabel()

    playVideo()
    addProgressObserver()

    NotificationCenter.default.addObserver(
      self,
      selector: #selector(playerItemDidReachEnd),
      name: .AVPlayerItemDidPlayToEndTime,
      object: player.currentItem
    )
  }

  private func setupTimeLabel() {
    timeLabel.textColor = .white
    timeLabel.font = UIFont.systemFont(ofSize: 12)
    timeLabel.textAlignment = .right
    timeLabel.text = "00:00"

    view.addSubview(timeLabel)
  }

  private func setupPlayPauseButton() {
    playPauseButton.tintColor = .white

    playPauseButton.setImage(UIImage(named: "pause"), for: .normal)

    playPauseButton.backgroundColor = UIColor(white: 0, alpha: 0.5)
    playPauseButton.layer.cornerRadius = 20
    playPauseButton.alpha = 0.8
    playPauseButton.addTarget(self, action: #selector(playPauseButtonTapped), for: .touchUpInside)

    view.addSubview(playPauseButton)
  }

  private func setupProgressView() {
    progressView.progressTintColor = .white
    progressView.trackTintColor = UIColor(white: 0.5, alpha: 0.5)

    view.addSubview(progressView)
  }

  private func setupGestures() {
    let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap))
    view.addGestureRecognizer(tapGesture)

    panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan))
    panGesture.delegate = self
    view.addGestureRecognizer(panGesture)
  }

  func gestureRecognizerShouldBegin(_ gestureRecognizer: UIGestureRecognizer) -> Bool {
    if gestureRecognizer == panGesture {
      let velocity = panGesture.velocity(in: view)
      return abs(velocity.y) > abs(velocity.x) && velocity.y > 0
    }
    return true
  }

  @objc private func playPauseButtonTapped() {
    if isPlaying {
      pauseVideo()
    } else {
      playVideo()
    }
  }

  @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
    let location = gesture.location(in: view)

    if playPauseButton.frame.contains(location) || progressView.frame.contains(location) {
      return
    }

    if isPlaying {
      pauseVideo()
    } else {
      playVideo()
    }
  }

  @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
    let translation = gesture.translation(in: view.superview)

    switch gesture.state {
    case .began:
      dragStartY = view.frame.origin.y
      initialTouchPoint = gesture.location(in: view.superview)

    case .changed:
      if translation.y > 0 {
        view.frame.origin.y = dragStartY + translation.y
      }

    case .ended, .cancelled:
      let distance = gesture.translation(in: view.superview).y

      if distance > UIScreen.main.bounds.height * 0.2 {
        exitPlayback()
      } else {
        UIView.animate(withDuration: 0.3) {
          self.view.frame.origin.y = self.dragStartY
        }
      }

    default:
      break
    }
  }

  @objc private func playerItemDidReachEnd(_ notification: Notification) {
    pauseVideo()

    player.seek(to: CMTime(seconds: 0, preferredTimescale: 1))
  }

  private func playVideo() {
    player.play()
    isPlaying = true

    playPauseButton.setImage(UIImage(named: "music_pause"), for: .normal)
  }

  private func pauseVideo() {
    player.pause()
    isPlaying = false

    playPauseButton.setImage(UIImage(named: "music_play"), for: .normal)
  }

  private func addProgressObserver() {
    timeObserver = player.addPeriodicTimeObserver(
      forInterval: CMTime(seconds: 1.0 / 60.0, preferredTimescale: CMTimeScale(NSEC_PER_SEC)),
      queue: DispatchQueue.main
    ) { [weak self] time in
      guard let self = self,
        let duration = self.player.currentItem?.duration,
        CMTimeGetSeconds(duration) > 0
      else { return }

      let progress = CMTimeGetSeconds(time) / CMTimeGetSeconds(duration)
      self.progressView.progress = Float(progress)

      let remainingSeconds = CMTimeGetSeconds(duration) - CMTimeGetSeconds(time)
      let minutes = Int(remainingSeconds) / 60
      let seconds = Int(remainingSeconds) % 60
      self.timeLabel.text = String(format: "%02d:%02d", minutes, seconds)
    }
  }

  private func removeProgressObserver() {
    if let observer = timeObserver {
      player.removeTimeObserver(observer)
      timeObserver = nil
    }
  }

  private func exitPlayback() {
    pauseVideo()

    removeProgressObserver()

    let screenHeight = UIScreen.main.bounds.height

    UIView.animate(
      withDuration: 0.3,
      animations: {
        self.view.frame.origin.y = screenHeight
      }
    ) { _ in
      self.player.replaceCurrentItem(with: nil)
      self.playerLayer.removeFromSuperlayer()

      NotificationCenter.default.removeObserver(
        self,
        name: .AVPlayerItemDidPlayToEndTime,
        object: self.player.currentItem
      )

      self.dismiss(animated: false)
    }
  }

  override func viewDidLayoutSubviews() {
    super.viewDidLayoutSubviews()
    playerLayer.frame = view.bounds

    let progressHeight: CGFloat = 4
    let progressWidth = view.bounds.width * 0.6
    let bottomMargin: CGFloat = 40

    let buttonSize: CGFloat = 40

    let timeLabelWidth: CGFloat = 50
    let timeLabelHeight: CGFloat = 20

    let availableWidth = view.bounds.width - (buttonSize + 20)
    let maxProgressWidth = availableWidth - timeLabelWidth - 30
    let actualProgressWidth = min(progressWidth, maxProgressWidth)

    let progressLeft = (view.bounds.width - actualProgressWidth - timeLabelWidth - 20) / 2 + 20
    let progressY = view.bounds.height - bottomMargin
    progressView.frame = CGRect(
      x: progressLeft, y: progressY, width: actualProgressWidth, height: progressHeight)

    let buttonX = progressLeft - buttonSize - 10
    let buttonY = progressY - buttonSize / 2 + progressHeight / 2
    playPauseButton.frame = CGRect(x: buttonX, y: buttonY, width: buttonSize, height: buttonSize)

    let timeLabelX = progressLeft + actualProgressWidth + 10
    let timeLabelY = progressY - timeLabelHeight / 2 + progressHeight / 2

    let maxTimeLabelX = view.bounds.width - timeLabelWidth - 10
    let finalTimeLabelX = min(timeLabelX, maxTimeLabelX)

    timeLabel.frame = CGRect(
      x: finalTimeLabelX, y: timeLabelY, width: timeLabelWidth, height: timeLabelHeight)
  }

  override var prefersStatusBarHidden: Bool {
    return true
  }

  override var shouldAutorotate: Bool {
    return false
  }

  override var supportedInterfaceOrientations: UIInterfaceOrientationMask {
    return .portrait
  }

  deinit {
    removeProgressObserver()
    NotificationCenter.default.removeObserver(self)
  }
}
