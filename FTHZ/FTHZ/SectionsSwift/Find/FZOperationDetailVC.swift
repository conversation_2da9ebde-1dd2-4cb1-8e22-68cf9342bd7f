class FZOperationDetailVC: FZBaseVC {
  @objc var operationId: NSString = ""
  var operationDetail: OperationItemResult?
  private var isNotchScreen: Bool {
    if #available(iOS 11.0, *) {
      let window = UIApplication.shared.windows.first
      return window?.safeAreaInsets.top ?? 0 > 20
    }
    return false
  }
  private let scrollView: UIScrollView = {
    let scrollView = UIScrollView()
    scrollView.showsVerticalScrollIndicator = false
    scrollView.showsHorizontalScrollIndicator = false
    if #available(iOS 11.0, *) {
      scrollView.contentInsetAdjustmentBehavior = .never
    }
    return scrollView
  }()

  private let detailView: OperationDetailView = {
    let view = OperationDetailView()
    view.backgroundColor = .white
    return view
  }()

  override func viewDidLoad() {
    super.viewDidLoad()
    setupUI()
    loadOperationDetail()
  }

  override func viewWillAppear(_ animated: <PERSON>ol) {
    super.viewWillAppear(animated)
    navigationController?.navigationBar.isHidden = true
  }

  private func setupUI() {
    view.backgroundColor = .white

    let backBt = UIButton()
    backBt.setImage(UIImage(named: "back"), for: .normal)
    backBt.addTarget(self, action: #selector(backAction), for: .touchUpInside)
    contentView.addSubview(backBt)
    backBt.snp.makeConstraints { make in
      make.left.equalToSuperview().offset(kRealWidth(24))
      make.top.equalToSuperview().offset(isNotchScreen ? kRealWidth(58) : kRealWidth(34))
      make.size.equalTo(CGSize(width: kRealWidth(10), height: kRealWidth(15)))
    }

    let titleLabel = UILabel()
    titleLabel.text = "资讯"
    titleLabel.textColor = .fz_HighBlackColor
    titleLabel.font = .ft_SourceHanserifSC_Blod_16
    contentView.addSubview(titleLabel)
    titleLabel.snp.makeConstraints { make in
      make.centerY.equalTo(backBt)
      make.centerX.equalToSuperview()
    }

    contentView.addSubview(scrollView)
    scrollView.snp.makeConstraints { make in
      make.top.equalTo(backBt.snp.bottom).offset(kRealWidth(20))
      make.left.right.bottom.equalToSuperview()
    }

    scrollView.addSubview(detailView)
    detailView.snp.makeConstraints { make in
      make.edges.equalToSuperview()
      make.width.equalTo(scrollView)
    }
  }

  private func loadOperationDetail() {
    guard !operationId.isEqual("") else {
      showMsgFast("参数错误")
      backAction()
      return
    }

    OperationsModel.getOperationDetail(
      operationId as String,
      success: { [weak self] (resObject) in
        guard let resDict = resObject,  // 先解包resObject
          let data = resDict["data"] as? [String: Any]
        else {
          self?.showMsgFast("请求数据发生错误，请稍后再试")
          self?.backAction()
          return
        }

        self?.operationDetail = OperationItemResult.mj_object(withKeyValues: data)

        if let detail = self?.operationDetail {
          self?.detailView.detailData = detail
        } else {
          self?.showMsgFast("数据解析错误")
          self?.backAction()
        }
      }
    ) { [weak self] (err) in
      self?.showMsgFast("网络错误，请检查网络后重试")
      self?.backAction()
    }
  }

  @objc func backAction() {
    navigationController?.popViewController(animated: true)
  }
}
