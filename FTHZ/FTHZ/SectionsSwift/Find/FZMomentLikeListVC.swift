import Foundation

class FZMomentLikeListVC: FZBaseVC, UITableViewDelegate {

  var contentId: String! {
    didSet {
      loadRequestData()
    }
  }

  private var isNotchScreen: Bool {
    if #available(iOS 11.0, *) {
      let window = UIApplication.shared.windows.first
      return window?.safeAreaInsets.top ?? 0 > 20
    }
    return false
  }
  
  private var pageIndex: Int = 1

  var items: [LikesUserModelResult]? {
    didSet {
      loadListData()
    }
  }
  lazy var listVC: ConfigurableTableViewController = ConfigurableTableViewController()
  func loadRequestData() {
    AffairLikesModel.getAffairLikesModel(
      contentId, page: NSNumber(value: pageIndex), size: "20",
      success: { [weak self] (resObject) in
        self?.listVC.tableView?.mj_footer?.endRefreshing()
        let member = AffairLikesModel.mj_object(withKeyValues: resObject)
        if let res = member?.success.boolValue,
          res == true
        {
          if let tempData = AffairLikesModelResult.mj_object(withKeyValues: member?.data.first) {
            let tempArr = NSMutableArray.init(array: self?.items ?? [])
            var replyCount: Int = self?.items?.count ?? 0
            for dic in tempData.data {
              if let dy = LikesUserModelResult.mj_object(withKeyValues: dic) {
                replyCount = replyCount + 1
                tempArr.add(dy)
              }
            }
            self?.items = tempArr.copy() as? [LikesUserModelResult]
            if tempData.count.toInt() ?? 0 > replyCount {
              if let footer = self?.listVC.tableView?.mj_footer {
                footer.removeFromSuperview()
                self?.listVC.tableView?.mj_footer = nil
              } else {
                let mjFooter = MJChiBaoZiFooter.init(
                  refreshingTarget: self, refreshingAction: #selector(self?.loadMoreData))
                mjFooter?.isRefreshingTitleHidden = true
                self?.listVC.tableView?.mj_footer = mjFooter
              }
            }

          }
        } else {
          self?.showErr()
        }
      }
    ) { [weak self] (Error) in
      self?.showErr()
    }
  }
  func setupUI() {
    view.backgroundColor = .white
    let backBt = UIButton()
    backBt.setImage(UIImage(named: "back"), for: .normal)
    backBt.addTarget(self, action: #selector(backAction), for: .touchUpInside)
    contentView.addSubview(backBt)
    backBt.snp.makeConstraints { make in
      make.left.equalToSuperview().offset(kRealWidth(24))
      make.top.equalToSuperview().offset(isNotchScreen ? kRealWidth(58) : kRealWidth(34))
      make.size.equalTo(CGSize(width: kRealWidth(10), height: kRealWidth(15)))
    }

    let title = UILabel.init()
    title.text = "点赞的鱼"
    title.textColor = .fz_HighBlackColor
    title.font = .ft_SourceHanserifSC_Meium_16
    contentView.addSubview(title)
    title.snp.makeConstraints { (make) in
      make.centerY.equalTo(backBt)
      make.centerX.equalToSuperview()
    }

    addChild(listVC)
    contentView.addSubview(listVC.view)
    listVC.view.snp.makeConstraints { (make) in
      make.left.right.bottom.equalToSuperview()
      make.top.equalTo(backBt.snp_bottom).offset(kRealWidth(36))
    }

  }

  func loadListData() {
    var list: Array = Array<CellConfiguratorType>.init()
    items?.forEach { (m) in
      list.append(CellConfigurator<LikeUserCell>(viewData: m))
    }

    if list.count > 0 {
      listVC.items = list
      listVC.tableView.separatorStyle = .none
      listVC.tableView.delegate = self
      listVC.tableView.rowHeight = kRealWidth(64)
    }

  }

  @objc func loadMoreData() {
    pageIndex += 1
    loadRequestData()
  }
  @objc func backAction() {
    navigationController?.popViewController(animated: true)
  }
  override func viewDidLoad() {
    super.viewDidLoad()
    setupUI()
  }
  func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
    tableView.deselectRow(at: indexPath, animated: true)
    if let data = items?[indexPath.row] {
      let whaleVC = WhaleDetailVC.init()
      whaleVC.uid = data.uid
      navigationController?.pushViewController(whaleVC, animated: true)
    }

  }

  func showErr() {
    view.makeToast("数据有误，请检查网络后重试", duration: 1.0, position: CSToastPositionCenter)
  }
}
