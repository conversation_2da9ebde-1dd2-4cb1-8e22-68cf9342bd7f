import SideMenu
import WebKit

class FZFindVC: FZBaseVC, JXCategoryViewDelegate, UIScrollViewDelegate, UIGestureRecognizerDelegate
{
  private var leftMenuNavVC: UISideMenuNavigationController!
  private var categoryView: JXCategoryTitleView!
  private var scroll: UIScrollView!
  private var topTitleView: UIView!
  private var bigCircle: UIView!
  private var dynamicTitle: String?
  private var dynamicLink: String?
  private var unreadBadge: UIView!
  private var hzPickerView: UIPickerView!
  private var hzPickerValues: [Int] = []

  private var isNotchScreen: Bool {
    if #available(iOS 11.0, *) {
      let window = UIApplication.shared.windows.first
      return window?.safeAreaInsets.top ?? 0 > 20
    }
    return false
  }

  private let playerBanner = FTHZFloatMusicPlayer.init(
    frame: CGRect(x: 0, y: FZSCREEN_H - 248, width: 52, height: 52))

  private let avatarIV = UIImageView()
  private lazy var titleTag: UILabel = UILabel.init()
  private lazy var dyVC = DynamicVC.init()

  private var deleteView: UIView?
  private var sideMenuPanGesture: UIPanGestureRecognizer?

  private func setUI() {
    categoryView = JXCategoryTitleView.init()
    categoryView.delegate = self

    scroll = UIScrollView.init(frame: CGRect.init())
    scroll.delegate = self
    scroll.isPagingEnabled = true
    scroll.showsHorizontalScrollIndicator = false
    scroll.bounces = false
    contentView.addSubview(scroll)
    scroll.snp.makeConstraints { (make) in
      make.top.left.right.bottom.equalToSuperview()
    }

    scroll.contentSize = CGSize(width: view.frame.width * 2, height: 0)

    let mineVC = FZMineVC.init()

    leftMenuNavVC = UISideMenuNavigationController.init(rootViewController: mineVC)
    leftMenuNavVC.menuWidth = view.frame.width * 0.70
    SideMenuManager.default.menuLeftNavigationController = leftMenuNavVC
    SideMenuManager.default.menuAnimationBackgroundColor = .white
    SideMenuManager.default.menuFadeStatusBar = false
    SideMenuManager.default.menuLeftNavigationController?.sideMenuDelegate = self

    if #available(iOS 11, *) {
      scroll.contentInsetAdjustmentBehavior = .never
    } else {
      automaticallyAdjustsScrollViewInsets = false
    }

    loadChildVC()
    topTitleView = UIView.init()
    contentView.addSubview(topTitleView)
    topTitleView.snp.makeConstraints { (make) in
      make.left.right.top.equalToSuperview()
      make.height.equalTo(isNotchScreen ? kRealWidth(108) : kRealWidth(84))
    }

    let blurEffect = UIBlurEffect(style: .extraLight)
    let visualEffectView = UIVisualEffectView(effect: blurEffect)
    visualEffectView.frame = topTitleView.bounds
    visualEffectView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
    visualEffectView.layer.cornerRadius = 20.0
    visualEffectView.layer.maskedCorners = [.layerMinXMaxYCorner, .layerMaxXMaxYCorner]
    visualEffectView.clipsToBounds = true
    topTitleView.addSubview(visualEffectView)
    visualEffectView.snp.makeConstraints { (make) in
      make.edges.equalToSuperview()
    }

    let backgroundView = UIView()
    backgroundView.backgroundColor = UIColor.white.withAlphaComponent(0.1)
    backgroundView.frame = visualEffectView.bounds
    backgroundView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
    visualEffectView.contentView.addSubview(backgroundView)

    let topBorder = CALayer()
    topBorder.frame = CGRect(x: 0, y: 0, width: topTitleView.frame.size.width, height: 0.5)
    topBorder.backgroundColor = UIColor(white: 0.0, alpha: 0.1).cgColor
    visualEffectView.layer.addSublayer(topBorder)

    topTitleView.layer.shadowColor = UIColor.black.cgColor
    topTitleView.layer.shadowOffset = CGSize(width: 0, height: 2.0)
    topTitleView.layer.shadowOpacity = 0.2
    topTitleView.layer.shadowRadius = 4.0
    topTitleView.layer.masksToBounds = false
    visualEffectView.layer.masksToBounds = true

    topTitleView.addSubview(avatarIV)
    avatarIV.snp.makeConstraints { (make) in
      make.left.equalToSuperview().offset(kRealWidth(24))
      make.top.equalToSuperview().offset(isNotchScreen ? kRealWidth(58) : kRealWidth(34))
      make.width.equalTo(kRealWidth(26))
      make.height.equalTo(kRealWidth(24))
    }
    avatarIV.addTapGestureRecognizer { [weak self] in
      if #available(iOS 10.0, *) {
        let generator = UIImpactFeedbackGenerator(style: .medium)
        generator.impactOccurred()
      }
      self?.navigationController?.present(
        self!.leftMenuNavVC, animated: true,
        completion: nil)
    }

    categoryView.titles = getDynamicTitles()
    categoryView.isAverageCellSpacingEnabled = false
    categoryView.cellSpacing = kRealWidth(25)
    categoryView.titleLabelZoomScale = 1.5
    categoryView.isTitleLabelZoomEnabled = true
    categoryView.titleFont = .ft_SourceHanserifSC_Blod_18
    categoryView.isTitleLabelZoomScrollGradientEnabled = true
    categoryView.titleColor = UIColor.fz_lightBlackColor
    categoryView.titleSelectedColor = UIColor.fz_HighBlackColor
    categoryView.isCellWidthZoomEnabled = false
    categoryView.contentScrollView = scroll
    categoryView.cellWidth = JXCategoryViewAutomaticDimension
    categoryView.contentEdgeInsetLeft = 20
    categoryView.contentEdgeInsetRight = 0
    topTitleView.addSubview(categoryView)
    categoryView.snp.makeConstraints { (make) in
      make.left.equalTo(avatarIV.snp.right).offset(kRealWidth(2))
      make.right.equalToSuperview().offset(-kRealWidth(72))
      make.top.equalToSuperview().offset(isNotchScreen ? kRealWidth(50) : kRealWidth(26))
      make.height.equalTo(kRealWidth(40))
    }

    let searchBt = UIButton.init()
    searchBt.setImage(UIImage.init(named: SEARCH_ICON), for: .normal)
    topTitleView.addSubview(searchBt)
    searchBt.snp.makeConstraints { (make) in
      make.right.equalToSuperview().offset(-kRealWidth(25))
      make.centerY.equalTo(avatarIV)
      make.width.height.equalTo(kRealWidth(16))
    }
    searchBt.addTarget(self, action: #selector(searchAction(button:)), for: .touchUpInside)

    setupHzPicker()

    self.tabBarController?.view.addSubview(playerBanner)

    playerBanner.canShow = false

    let dragPlayerGesture = UIPanGestureRecognizer.init(
      target: self, action: #selector(dragPlayerBt(recognizer:)))
    playerBanner.addGestureRecognizer(dragPlayerGesture)

    NotificationCenter.default.addObserver(
      forName: NSNotification.Name(
        rawValue: NSNotification.Name.FTHZMusicPlayerPlayStateWillChange.rawValue), object: nil,
      queue: .main
    ) { [weak self] noti in
      let isPlay = !FTHZMusicPlayer.shared().isPlaying
      self?.playerBanner.isSelected = isPlay

      if isPlay {

        self?.playerBanner.canShow = true
      } else {
        if let state = noti.object as? NSNumber,
          let playerState = FSAudioStreamState(rawValue: state.intValue)
        {
          if playerState != .fsAudioStreamPaused {
            self?.playerBanner.canShow = false
          }
        }
      }
    }

    NotificationCenter.default.addObserver(
      forName: NSNotification.Name(rawValue: "NotificationForOpenLeftMenu"), object: nil,
      queue: .main
    ) { [weak self] _ in
      self?.navigationController?.present(
        self!.leftMenuNavVC, animated: true,
        completion: nil)
    }

  }

  private func setupHzPicker() {
    hzPickerValues = stride(from: 9, through: 90, by: 1).map { $0 }

    hzPickerView = UIPickerView()
    hzPickerView.delegate = self
    hzPickerView.dataSource = self

    hzPickerView.backgroundColor = .clear
    hzPickerView.isHidden = categoryView.selectedIndex == 1
    hzPickerView.alpha = categoryView.selectedIndex == 1 ? 0 : 1

    topTitleView.addSubview(hzPickerView)
    hzPickerView.snp.makeConstraints { (make) in
      make.right.equalTo(topTitleView.snp.right).offset(-kRealWidth(50))
      make.centerY.equalTo(avatarIV)
      make.width.equalTo(kRealWidth(60))
      make.height.equalTo(kRealWidth(80))
    }

    let userHz = Int(round(Double(USERINFO.hertz) ?? 0))
    let initialIndex = hzPickerValues.firstIndex(of: userHz) ?? 0
    hzPickerView.selectRow(initialIndex, inComponent: 0, animated: true)
  }

  private func getDynamicTitles() -> [String] {
    var titles = ["海洋", "关注"]
    if let dynamicTitle = dynamicTitle {
      titles.insert(dynamicTitle, at: 2)
    }
    return titles
  }

  private func getDeleteView() -> UIView {
    if deleteView == nil {
      let view = UIView.init(
        frame: CGRect(x: 0, y: FZSCREEN_H - 144, width: FZSCREEN_W, height: 144))
      view.backgroundColor = .red
      let icon = UIImageView(image: UIImage(named: "垃圾桶"))
      view.addSubview(icon)
      icon.snp.makeConstraints { (make) in
        make.centerX.equalToSuperview()
        make.top.equalToSuperview().offset(42)
        make.size.equalTo(CGSize(width: 18, height: 20))
      }

      let tips = UILabel.init()
      tips.text = "播放器拖到此处删除"
      tips.textColor = .white
      tips.font = .ft_SourceHanserifSC_Normal_16
      tips.textAlignment = .center
      view.addSubview(tips)
      tips.snp.makeConstraints { (make) in
        make.centerX.equalToSuperview()
        make.top.equalTo(icon.snp_bottom).offset(12)
      }

      deleteView = view
    }

    return deleteView!
  }

  func updateDynamicTitle(_ title: String?) {
    dynamicTitle = title
    categoryView.titles = getDynamicTitles()
    categoryView.reloadData()
  }

  private func loadChildVC() {
    dyVC.mainTempScale = kRealWidth(1)
    addChild(dyVC)
    scroll.addSubview(dyVC.view)
    dyVC.view.snp.makeConstraints { (make) in
      make.left.top.bottom.equalToSuperview()
      make.width.equalTo(scroll.snp.width)
      make.height.equalTo(scroll.snp.height)
    }

    let attVC = AttentionVC.init()
    attVC.mainTempScale = kRealWidth(1)
    addChild(attVC)
    scroll.addSubview(attVC.view)
    attVC.view.snp.makeConstraints { (make) in
      make.top.bottom.equalToSuperview()
      make.left.equalTo(dyVC.view.snp.right)
      make.width.equalTo(scroll.snp.width)
      make.height.equalTo(scroll.snp.height)
    }

    scroll.contentSize = CGSize(width: view.frame.width * 2, height: 0)

    let gesView = UIView.init()
    contentView.addSubview(gesView)

    gesView.snp.makeConstraints { (make) in
      make.left.top.bottom.equalToSuperview()
      make.width.equalTo(kRealWidth(24))
    }

    sideMenuPanGesture = SideMenuManager.default.menuAddPanGestureToPresent(toView: gesView)
  }

  @objc func dragPostBt(recognizer: UIPanGestureRecognizer) {
    let location = recognizer.location(in: self.contentView)
    let selfLocation = recognizer.location(in: recognizer.view)
    let cSize = self.contentView.frame.size

    var adjustX = location.x
    if adjustX - selfLocation.x < 0 {
      adjustX = 0
    }
    if adjustX > cSize.width - kRealWidth(38) {
      adjustX = cSize.width - kRealWidth(38)
    }

    var adjustY = location.y
    if adjustY < kRealWidth(100) {
      adjustY = kRealWidth(100)
    }
    if adjustY > cSize.height - kRealWidth(36) {
      adjustY = cSize.height - kRealWidth(36)
    }

    if recognizer.state == .changed {
      if let target = recognizer.view {
        target.frame = CGRect(x: adjustX, y: adjustY, width: kRealWidth(36), height: kRealWidth(36))
      }
    } else if recognizer.state == .ended || recognizer.state == .cancelled {
      if let target = recognizer.view {
        UIView.animate(withDuration: 0.2) {
          target.frame = CGRect(
            x: cSize.width - kRealWidth(38),
            y: adjustY,
            width: kRealWidth(36),
            height: kRealWidth(36))
        }
        MomentPostPostion = CGPoint(x: cSize.width - kRealWidth(38), y: adjustY)
      }
    }
  }

  @objc func dragPlayerBt(recognizer: UIPanGestureRecognizer) {
    let location = recognizer.location(in: self.tabBarController?.view)
    let selfLocation = recognizer.location(in: recognizer.view)

    var adjustX = location.x
    if adjustX - selfLocation.x < 0 {
      adjustX = 0
    }

    if adjustX > FZSCREEN_W - 44 {
      adjustX = FZSCREEN_W - 44
    }

    var adjustY = location.y
    if adjustY < kRealWidth(100) {
      adjustY = kRealWidth(100)
    }

    if adjustY > FZSCREEN_H - 44 {
      adjustY = FZSCREEN_H - 44
    }

    if recognizer.state == .began {
      self.tabBarController?.view.addSubview(getDeleteView())
      self.tabBarController?.view.bringSubviewToFront(playerBanner)
    } else if recognizer.state == .changed {
      if let target = recognizer.view {

        if location.y > FZSCREEN_H - 110 {
          getDeleteView().removeFromSuperview()
          FTHZMusicPlayer.shared().stop()

        } else {
          target.frame = CGRect(x: adjustX, y: adjustY, width: 44, height: 44)

        }
      }

    } else if recognizer.state == .ended || recognizer.state == .cancelled {
      if location.y > FZSCREEN_H - 110 {
        adjustY = FZSCREEN_H - 248
        playerBanner.alpha = 0
      }
      if let target = recognizer.view {
        UIView.animate(withDuration: 0.2) {
          target.frame = CGRect(x: 0, y: adjustY, width: 44, height: 44)
        }
      }

      getDeleteView().removeFromSuperview()
    }
  }

  @objc func searchAction(button: UIButton) {
    button.startAnimatingPressActions()
    let searchVC = FZSearchVC.init()
    navigationController?.pushViewController(searchVC, animated: true)
  }

  @objc func postAction(button: UIButton) {
    let postVC = ReleaseDynamicVC.init()

    postVC.modalPresentationStyle = .fullScreen
    present(postVC, animated: true) {

    }
  }

  override func viewDidLoad() {
    super.viewDidLoad()
    navigationController?.interactivePopGestureRecognizer?.delegate = self
    setUI()
    fetchDynamicTitle()
  }

  private func fetchDynamicTitle() {
    DynamicTitleModel.getDynamicTitle({ [weak self] response in
      if let dict = response as? [String: Any],
        let dataArray = dict["data"] as? [[String: Any]],
        let firstItem = dataArray.first,
        let title = firstItem["title"] as? String
      {
        if let link = firstItem["link"] as? String {
          self?.dynamicLink = link
        }

        DispatchQueue.main.async {
          self?.updateDynamicTitle(title)
        }
      }
    }) { [weak self] error in
      self?.updateDynamicTitle(nil)
    }
  }

  override func viewWillAppear(_ animated: Bool) {
    super.viewWillAppear(animated)
    AppConfig.statusbarStyle(true)
    avatarIV.image = UIImage(named: "caidan")
  }

  override func viewDidLayoutSubviews() {
    super.viewDidLayoutSubviews()

    let contentWidth = view.frame.width * CGFloat(getDynamicTitles().count)
    scroll.contentSize = CGSize(width: contentWidth, height: 0)

    for (index, childVC) in children.enumerated() {
      if let childView = childVC.view {
        childView.frame = CGRect(
          x: CGFloat(index) * view.frame.width,
          y: 0,
          width: view.frame.width,
          height: scroll.frame.height)
      }
    }
  }

  func categoryView(_ categoryView: JXCategoryBaseView!, didSelectedItemAt index: Int) {
    if index == 2,
      let link = dynamicLink
    {
      let webVC = FZDynamicWebVC(urlString: link)
      navigationController?.pushViewController(webVC, animated: true)
    }

    if index == 1 {
      UIView.animate(withDuration: 0.3) { [weak self] in
        self?.hzPickerView.alpha = 0
      } completion: { [weak self] _ in
        self?.hzPickerView.isHidden = true
      }
    } else {
      self.hzPickerView.isHidden = false
      UIView.animate(withDuration: 0.3) { [weak self] in
        self?.hzPickerView.alpha = 1
      }
    }
  }

  func sliderValue(toHerz herz: Int32) {
    UIView.animate(
      withDuration: 1.0,
      animations: { [weak self] in
        if let index = self?.hzPickerValues.firstIndex(of: Int(herz)) {
          self?.hzPickerView.selectRow(index, inComponent: 0, animated: true)
        }
      })
  }
}

extension FZFindVC: UISideMenuNavigationControllerDelegate {
  func sideMenuWillAppear(menu: UISideMenuNavigationController, animated: Bool) {
    if #available(iOS 10.0, *) {
      let generator = UIImpactFeedbackGenerator(style: .medium)
      generator.impactOccurred()
    }
  }

  func sideMenuWillDisappear(menu: UISideMenuNavigationController, animated: Bool) {
    AppConfig.statusbarStyle(true)
  }
}

extension FZFindVC: UIPickerViewDelegate, UIPickerViewDataSource {
  func numberOfComponents(in pickerView: UIPickerView) -> Int {
    return 1
  }

  func pickerView(_ pickerView: UIPickerView, numberOfRowsInComponent component: Int) -> Int {
    return hzPickerValues.count
  }

  func pickerView(_ pickerView: UIPickerView, titleForRow row: Int, forComponent component: Int)
    -> String?
  {
    return "\(hzPickerValues[row])Hz"
  }

  func pickerView(_ pickerView: UIPickerView, didSelectRow row: Int, inComponent component: Int) {
    let selectedHerz = hzPickerValues[row]

    dyVC.refresh(byHerz: Int32(selectedHerz), after: 0.1)
    pickerView.reloadAllComponents()
  }

  func pickerView(
    _ pickerView: UIPickerView, viewForRow row: Int, forComponent component: Int,
    reusing view: UIView?
  ) -> UIView {
    let label = UILabel()
    if hzPickerValues[row] == 9 {
      label.text = "NEW"
    } else {
      label.text = "\(hzPickerValues[row])Hz"
    }
    label.font = .ft_SourceHanSerif_Blod_12
    let isSelected = pickerView.selectedRow(inComponent: component) == row
    label.textColor = .fz_HighBlackColor
    label.textAlignment = .center
    return label
  }

  func pickerView(_ pickerView: UIPickerView, rowHeightForComponent component: Int) -> CGFloat {
    return kRealWidth(24)
  }
}
