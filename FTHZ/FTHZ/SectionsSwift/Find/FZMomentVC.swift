import Foundation

@objc protocol MomentDelegate {
  @objc
  optional
    func needRefreshList()
  func momentDeleted(index: NSIndexPath)
}

class FZMomentVC: FZBaseVC, NicknameDelegate {
  @objc var userId: String!
  @objc var momentId: String!
  @objc var withComment: Bool = false
  @objc var indexIntable: NSIndexPath?
  @objc weak var delegate: MomentDelegate?
  var momentDetail: AffairResult?
  private lazy var listTable = UITableView.init()
  private lazy var bottomInput = UIView.init()
  var pageIndex: Int = 1
  lazy var cellData: NSArray = NSArray.init()
  private var selectedIndexPath: IndexPath?
  private var isRequesting: Bool = false
  private var isLoading: Bool = true
  private var isNotchScreen: Bool {
    if #available(iOS 11.0, *) {
      let window = UIApplication.shared.windows.first
      return window?.safeAreaInsets.top ?? 0 > 20
    }
    return false
  }
  func loadMoment() {
    AffairDetailModel.getAffairDetail(
      momentId, authorid: userId, from: "0",
      success: { [weak self] (resObject) in
        let member = AffairDetailModel.mj_object(withKeyValues: resObject)
        if let res = member?.success.boolValue,
          res == true
        {
          self?.momentDetail = AffairResult.mj_object(withKeyValues: member?.data.first)
          self?.setupHeadView()
          self?.loadComment()
        } else {
          self?.showMsgFast(member?.msg ?? "请求数据发生错误，请稍后再试")
          self?.backAction()
        }
      }
    ) { [weak self] (err) in
      self?.showMsgFast("网络错误，请检查网络后重试")
    }
  }

  func loadComment() {
    isLoading = true
    AffairCommentsModel.getAffairCommentsModel(
      momentId, authorid: userId, page: NSNumber(value: pageIndex), size: "50",
      success: { [weak self] (resObject) in
        self?.listTable.mj_footer?.endRefreshing()
        let member = AffairCommentsModel.mj_object(withKeyValues: resObject)
        if let res = member?.success.boolValue,
          res == true
        {

          let tempArray = NSMutableArray.init(array: self?.cellData ?? [])
          let tempMember: AffairCommentsModelResult = AffairCommentsModelResult.mj_object(
            withKeyValues: member?.data.first)
          var replyCount: Int = self?.cellData.count ?? 0
          for dic in tempMember.data {
            if let dy = AffairCommentsReplyResult.mj_object(withKeyValues: dic) {
              replyCount = replyCount + dy.data.count + 1
              tempArray.add(dy)
            }

          }
          if tempMember.count.toInt() ?? 0 > replyCount {
            if let footer = self?.listTable.mj_footer {
              footer.removeFromSuperview()
              self?.listTable.mj_footer = nil
            } else {
              let mjFooter = MJChiBaoZiFooter.init(
                refreshingTarget: self, refreshingAction: #selector(self?.loadMoreData))
              mjFooter?.isRefreshingTitleHidden = true
              self?.listTable.mj_footer = mjFooter
            }
          }
          self?.cellData = tempArray.copy() as! NSArray
          self?.isLoading = false
          self?.listTable.reloadData()
          if self?.withComment == true {
            self?.withComment = false
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
              self?.showXHInput(.default, indexPath: nil)
            }
          }

        } else {
          self?.isLoading = false
          self?.showMsgFast(member?.msg ?? "请求数据发生错误，请稍后再试")
        }

      }
    ) { [weak self] (err) in
      self?.isLoading = false
      self?.listTable.mj_footer?.endRefreshing()
      self?.showMsgFast("网络错误，请检查网络后重试")
    }
  }

  func setupHeadView() {
    let tempHeight = kRealWidth(100)
    let headView = momentDetailView.init(
      frame: CGRect.init(x: 0, y: 0, width: FZSCREEN_W, height: tempHeight))

    headView.detailData = momentDetail
    headView.onHeightChanged = { [weak self] in
      self?.updateHeaderViewHeight()
    }

    let longtouch = UILongPressGestureRecognizer.init(
      target: self, action: #selector(longPressHeaderAction(ges:)))
    headView.addGestureRecognizer(longtouch)

    listTable.delegate = self
    listTable.dataSource = self
    listTable.register(
      DyCommentTableViewCell.self,
      forCellReuseIdentifier: NSStringFromClass(DyCommentTableViewCell.self))

    let actualHeight = headView.getCurrentHeight()
    var frame = headView.frame
    frame.size.height = actualHeight
    headView.frame = frame

    listTable.tableHeaderView = headView
  }

  func setupUI() {
    let backBt = UIButton.init()
    backBt.setImage(UIImage.init(named: "back"), for: .normal)
    backBt.addTarget(self, action: #selector(backAction), for: .touchUpInside)
    contentView.addSubview(backBt)
    backBt.snp.makeConstraints { (make) in
      make.left.equalToSuperview().offset(kRealWidth(24))
      make.top.equalToSuperview().offset(isNotchScreen ? kRealWidth(58) : kRealWidth(34))
      make.size.equalTo(CGSize.init(width: kRealWidth(10), height: kRealWidth(15)))
    }

    let title = UILabel.init()
    title.text = "动态详情"
    title.textColor = .fz_HighBlackColor
    title.font = .ft_SourceHanserifSC_Meium_16
    contentView.addSubview(title)
    title.snp.makeConstraints { (make) in
      make.centerY.equalTo(backBt)
      make.centerX.equalToSuperview()
    }

    let settingBt = UIButton.init()
    settingBt.setImage(UIImage.init(named: MORE_BLACK_ICON), for: .normal)
    contentView.addSubview(settingBt)
    settingBt.snp.makeConstraints { (make) in
      make.right.equalToSuperview().offset(-kRealWidth(24))
      make.centerY.equalTo(backBt)
    }
    settingBt.blockAction { [weak self] (button) in
      let popover = PopoverView()
      popover.showShade = true
      popover.show(to: button, with: self?.popActions())
    }

    contentView.addSubview(listTable)
    listTable.contentInset = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
    listTable.rowHeight = UITableView.automaticDimension
    listTable.estimatedRowHeight = 100
    listTable.separatorStyle = .none
    listTable.snp.makeConstraints { make in
      make.left.right.equalToSuperview()
      make.top.equalTo(backBt.snp.bottom).offset(kRealWidth(20))
      make.bottom.equalToSuperview()
    }

    contentView.addSubview(bottomInput)
    bottomInput.backgroundColor = .fz_HighBlackColor
    bottomInput.layer.cornerRadius = kRealWidth(20)
    bottomInput.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
    bottomInput.layer.masksToBounds = true
    bottomInput.snp.makeConstraints { make in
      make.left.right.equalToSuperview()
      make.bottom.equalToSuperview()
      make.height.equalTo(kRealWidth(80))
    }

    let touchArea = UIButton()
    touchArea.addTarget(self, action: #selector(showKeyboard), for: .touchUpInside)
    bottomInput.addSubview(touchArea)
    touchArea.snp.makeConstraints { (make) in
      make.edges.equalToSuperview()
    }

    let tipLabel = UILabel()
    tipLabel.text = "评论"
    tipLabel.textColor = .white
    tipLabel.font = .ft_SourceHanserifSC_Blod_14
    bottomInput.addSubview(tipLabel)
    tipLabel.snp.makeConstraints { (make) in
      make.right.equalToSuperview().offset(-kRealWidth(24))
      make.centerY.equalToSuperview()
    }

    let replyImageView = UIImageView()
    replyImageView.image = UIImage(named: "回复w2")
    bottomInput.addSubview(replyImageView)
    replyImageView.snp.makeConstraints { (make) in
      make.right.equalTo(tipLabel.snp_left).offset(-kRealWidth(12))
      make.centerY.equalToSuperview()
    }
  }

  @objc private func showKeyboard() {
    DispatchQueue.main.async { [weak self] in
      self?.showXHInput(.default, indexPath: nil)
    }
  }

  @objc func longPressHeaderAction(ges: UILongPressGestureRecognizer) {
    if ges.state == .began {
      let popmenu = FTHZPopMenuView.init()
      let copyItem = FTHZPopMenuItem.menu(withTitle: "复制") { [weak self] in
        UIPasteboard.general.string = self?.momentDetail?.content
      }

      popmenu.setMenuItems([copyItem])
      if let headerview = self.listTable.tableHeaderView as? momentDetailView {
        popmenu.show(inTargetRect: headerview.contentL.frame, in: headerview) {

        }
      } else {
        let gesView = ges.view!
        popmenu.show(inTargetRect: gesView.frame, in: gesView, dismissHandler: nil)
      }

    }
  }

  @objc func loadMoreData() {
    pageIndex += 1
    loadComment()
  }

  private func updateHeaderViewHeight() {
    guard let headerView = listTable.tableHeaderView as? momentDetailView else { return }

    listTable.beginUpdates()

    let newHeight = headerView.getCurrentHeight()

    var frame = headerView.frame
    frame.size.height = newHeight
    headerView.frame = frame

    listTable.tableHeaderView = headerView

    listTable.endUpdates()
  }

  private func resizedImage(_ named: String, size: CGFloat = 18) -> UIImage? {
    guard let originalImage = UIImage(named: named) else { return nil }

    let size = CGSize(width: size, height: size)
    UIGraphicsBeginImageContextWithOptions(size, false, UIScreen.main.scale)
    originalImage.draw(in: CGRect(origin: .zero, size: size))
    let resizedImage = UIGraphicsGetImageFromCurrentImageContext()
    UIGraphicsEndImageContext()

    return resizedImage
  }

  @objc func popActions() -> [PopoverAction] {
    if USERINFO.userid != userId {
      let reportAction = PopoverAction(
        image: resizedImage("Report"),
        title: "举报"
      ) { [weak self] (action) in
        let reportVC = ReportVC.init()
        if let ws = self {
          reportVC.delegate = ws
          ws.navigationController?.pushViewController(reportVC, animated: true)
        }
      }

      let blockAction = PopoverAction(
        image: resizedImage("more-lahei"),
        title: "拉黑"
      ) { [weak self] (action) in
        let alert = UIAlertController.init(
          title: "确定拉黑该用户？",
          message: "拉黑后你们将互相看不到对方的动态。如需解除，请在设置-黑名单管理中移除黑名单。",
          preferredStyle: .alert)
        let cancelAction = UIAlertAction.init(title: "取消", style: .cancel) { (action) in }
        let okAction = UIAlertAction.init(title: "确定", style: .default) { (action) in
          self?.blockUser()
        }
        alert.addAction(cancelAction)
        alert.addAction(okAction)
        self?.present(alert, animated: true, completion: nil)
      }

      return [reportAction!, blockAction!]
    } else {
      var actions: [PopoverAction] = []

      let isTop = momentDetail?.top == "1"
      let topAction = PopoverAction(
        image: resizedImage("more-top"),
        title: isTop ? "取消" : "置顶"
      ) { [weak self] (action) in
        let message = isTop ? "取消置顶后该动态将恢复正常排序，确定取消置顶吗?" : "置顶后该动态将排在最前面，确定置顶吗?"
        let alert = UIAlertController(
          title: "提示",
          message: message,
          preferredStyle: .alert
        )
        let cancelAction = UIAlertAction(title: "取消", style: .cancel)
        let okAction = UIAlertAction(title: "确定", style: .default) { (action) in
          self?.doTopAction(setTop: !isTop)
        }
        alert.addAction(cancelAction)
        alert.addAction(okAction)
        self?.present(alert, animated: true)
      }
      actions.append(topAction!)

      if momentDetail?.status == "3" {
        let immediateAction = PopoverAction(
          image: resizedImage("more-send"),
          title: "发布"
        ) { [weak self] (action) in
          let alert = UIAlertController(
            title: "提示",
            message: "确定立即发布这条动态吗?",
            preferredStyle: .alert
          )
          let cancelAction = UIAlertAction(title: "取消", style: .cancel)
          let okAction = UIAlertAction(title: "确定", style: .default) { (action) in
            self?.doHideAction(immediately: true)
          }
          alert.addAction(cancelAction)
          alert.addAction(okAction)
          self?.present(alert, animated: true)
        }
        actions.append(immediateAction!)
      } else {
        let isHidden = momentDetail?.status == "2"
        let hideAction = PopoverAction(
          image: resizedImage("more-yincang"),
          title: isHidden ? "取消" : "隐藏"
        ) { [weak self] (action) in
          let message = isHidden ? "取消隐藏后其他用户将可以看到这条动态,确定取消隐藏吗?" : "隐藏后其他用户将看不到这条动态,确定隐藏吗?"

          let alert = UIAlertController(
            title: "提示",
            message: message,
            preferredStyle: .alert
          )
          let cancelAction = UIAlertAction(title: "取消", style: .cancel)
          let okAction = UIAlertAction(title: "确定", style: .default) { (action) in
            self?.doHideAction(immediately: false)
          }
          alert.addAction(cancelAction)
          alert.addAction(okAction)
          self?.present(alert, animated: true)
        }
        actions.append(hideAction!)
      }

      let delAction = PopoverAction(
        image: resizedImage("more-delete"),
        title: "删除"
      ) { [weak self] (action) in
        let alert = UIAlertController.init(
          title: "提示",
          message: "确定删除该动态?",
          preferredStyle: .alert)
        let cancelAction = UIAlertAction.init(title: "取消", style: .cancel) { (action) in }
        let okAction = UIAlertAction.init(title: "确定", style: .default) { (action) in
          self?.deleteMoment()
        }
        alert.addAction(cancelAction)
        alert.addAction(okAction)
        self?.present(alert, animated: true, completion: nil)
      }

      actions.append(delAction!)
      return actions
    }
  }

  @objc func backAction() {
    navigationController?.popViewController(animated: true)
  }

  private func doHideAction(immediately: Bool = false) {
    guard let cid = momentDetail?.contentid else { return }
    let isHidden = momentDetail?.status == "2"

    HUD.show()
    AffairDetailModel.toggleAffairHide(
      cid,
      immediately: immediately,
      success: { [weak self] (res) in
        HUD.dissmiss()
        let member = AffairDetailModel.mj_object(withKeyValues: res)
        if member?.success.boolValue ?? false {
          if immediately {
            self?.showMsgFast("已立即发布")
          } else {
            self?.showMsgFast(isHidden ? "取消隐藏成功" : "隐藏成功")
          }

          self?.momentDetail?.status = isHidden ? "1" : "2"

          if let index = self?.indexIntable {
            self?.delegate?.momentDeleted(index: index)
          } else {
            self?.delegate?.needRefreshList?()
          }

          DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self?.backAction()
          }
        } else {
          if immediately {
            self?.showMsgFast(member?.msg ?? "立即发布失败，请重试")
          } else {
            self?.showMsgFast(member?.msg ?? "\(isHidden ? "取消隐藏" : "隐藏")失败，请重试")
          }
        }
      },
      failure: { [weak self] (err) in
        HUD.dissmiss()
        if immediately {
          self?.showMsgFast("立即发布失败，请重试")
        } else {
          self?.showMsgFast("\(isHidden ? "取消隐藏" : "隐藏")失败，请重试")
        }
      }
    )
  }

  func blockUser() {
    if let uid = momentDetail?.authorid {
      BlackUserModel.post(
        uid,
        success: { [weak self] (res) in
          let member = BlackUserModel.mj_object(withKeyValues: res)
          if member?.success.boolValue ?? false {
            self?.showMsgFast("拉黑成功")
            self?.perform(#selector(self?.backAction), with: nil, afterDelay: 1.0)
          } else {
            self?.showMsgFast(member?.msg ?? "拉黑失败，请重试")
          }
        }
      ) { [weak self] (err) in
        self?.showMsgFast("拉黑失败，请重试")
      }
    }

  }

  func deleteMoment() {
    if let cid = momentDetail?.contentid {
      DeleteDynamicModel.post(
        cid,
        success: { [weak self] (res) in
          let member = BlackUserModel.mj_object(withKeyValues: res)
          if member?.success.boolValue ?? false {
            self?.showMsgFast("删除成功")
            self?.delegate?.needRefreshList?()
            if let index = self?.indexIntable {
              self?.delegate?.momentDeleted(index: index)
            }
            self?.perform(#selector(self?.backAction), with: nil, afterDelay: 1.0)
          } else {
            self?.showMsgFast(member?.msg ?? "删除失败，请重试")
          }
        }
      ) { [weak self] (err) in
        self?.showMsgFast("删除失败，请重试")
      }
    }
  }

  override func viewDidLoad() {
    super.viewDidLoad()
    setupUI()
    loadMoment()
  }

  override func viewWillAppear(_ animated: Bool) {
    super.viewWillAppear(animated)
    navigationController?.navigationBar.isHidden = true
  }

  func delegateGetReport(_ RelationshipType: Int, detail: String) {
    ReportModel.post(
      momentDetail?.authorid ?? "0",
      report_type: "\(RelationshipType)",
      type: "1",
      rid: momentDetail?.contentid ?? "0",
      detail: detail,
      success: { [weak self] (res) in
        let member = ReportModel.mj_object(withKeyValues: res)
        if member?.success.boolValue ?? false {
          self?.showMsgFast("举报成功")
        } else {
          self?.showMsgFast(member?.msg ?? "发生错误，请检查网络情况后重试")
        }

      }
    ) { [weak self] (err) in
      self?.showMsgFast("举报失败，请重试")
    }
  }

  private func doTopAction(setTop: Bool) {
    guard let cid = momentDetail?.contentid else { return }
    let topValue = setTop ? "1" : "0"
    HUD.show()
    AffairDetailModel.topAffair(
      cid,
      success: { [weak self] (res) in
        HUD.dissmiss()
        let member = AffairDetailModel.mj_object(withKeyValues: res)
        if member?.success.boolValue ?? false {
          self?.showMsgFast(setTop ? "置顶成功" : "取消置顶成功")
          self?.momentDetail?.top = topValue
          self?.delegate?.needRefreshList?()
          DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self?.backAction()
          }
        } else {
          self?.showMsgFast(member?.msg ?? (setTop ? "置顶失败，请重试" : "取消置顶失败，请重试"))
        }
      },
      failure: { [weak self] (err) in
        HUD.dissmiss()
        self?.showMsgFast(setTop ? "置顶失败，请重试" : "取消置顶失败，请重试")
      }
    )
  }
}

extension FZMomentVC: UITableViewDelegate, UITableViewDataSource, UIScrollViewDelegate {
  func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
    let noMoreData = listTable.mj_footer == nil
    if noMoreData && section == cellData.count {
      return 1
    }
    if cellData.count == 0 {
      return 0
    }
    if let dy = cellData[section] as? AffairCommentsReplyResult, dy.data.count > 0 {
      return dy.data.count + 1
    }
    return 1
  }

  func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
    if cellData.count == 0 && !isLoading {
      return kRealWidth(100)
    }

    return 0
  }

  func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
    return nil
  }

  func numberOfSections(in tableView: UITableView) -> Int {
    let noMoreData = listTable.mj_footer == nil
    if cellData.count == 0 {
      return 1
    }
    return cellData.count + (noMoreData ? 1 : 0)
  }

  func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
    let noMoreData = listTable.mj_footer == nil
    if noMoreData && indexPath.section == cellData.count {
      let cell = UITableViewCell(style: .default, reuseIdentifier: "blankCell")
      cell.backgroundColor = .clear
      cell.selectionStyle = .none
      return cell
    }
    let rpData: AffairCommentsReplyResult =
      cellData[indexPath.section] as! AffairCommentsReplyResult
    let reuseIdentifier: String = NSStringFromClass(DyCommentTableViewCell.self)
    let cell: DyCommentTableViewCell =
      tableView.dequeueReusableCell(withIdentifier: reuseIdentifier, for: indexPath)
      as! DyCommentTableViewCell

    cell.deletedComment = { [weak self] (cell) in
      self?.commentDeleted(cell: cell)
    }
    if rpData.data.count > 0 && indexPath.row > 0 {
      guard
        let toReply = AffairCommentsToReplyResult.mj_object(
          withKeyValues: rpData.data[indexPath.row - 1])
      else { return cell }
      cell.load(toCommentUser: toReply)
      cell.contentID = momentDetail?.contentid ?? ""
      cell.contentAuthorID = momentDetail?.authorid ?? ""
      cell.anameBtn.tag = toReply.userid.toInt() ?? 0
      cell.bnameBtn.tag = toReply.to_userid.toInt() ?? 0
      cell.anameBtn.blockAction { [weak self] (button) in
        self?.gotoWhale("\(button.tag)")
      }
      cell.bnameBtn.blockAction { [weak self] (button) in
        self?.gotoWhale("\(button.tag)")
      }

      return cell

    } else {
      cell.commentUser = rpData
      cell.contentID = momentDetail?.contentid ?? ""
      cell.contentAuthorID = momentDetail?.authorid ?? ""
      cell.tapIconAction = { [weak self] (uid) in
        self?.gotoWhale(uid)
      }

      return cell
    }

  }

  func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
    tableView.deselectRow(at: indexPath, animated: true)
    showXHInput(.default, indexPath: indexPath)
  }

  func gotoWhale(_ uid: String) {
    let whaleVC = WhaleDetailVC.init()
    whaleVC.uid = uid
    navigationController?.pushViewController(whaleVC, animated: true)
  }

  func scrollViewWillEndDragging(_ scrollView: UIScrollView, withVelocity velocity: CGPoint, targetContentOffset: UnsafeMutablePointer<CGPoint>) {
    if velocity.y > 0 {
      UIView.animate(withDuration: 0.25) {
        self.bottomInput.transform = CGAffineTransform(translationX: 0, y: self.bottomInput.frame.height)
      }
    } else if velocity.y < 0 {
      UIView.animate(withDuration: 0.25) {
        self.bottomInput.transform = .identity
      }
    }
  }

  func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
    let noMoreData = listTable.mj_footer == nil
    if noMoreData && indexPath.section == cellData.count {
      return kRealWidth(90)
    }
    return UITableView.automaticDimension
  }
}

extension FZMomentVC: XHInputViewDelagete {

  @objc func showXHInput(_ style: InputViewStyle, indexPath: IndexPath?) {
    selectedIndexPath = indexPath
    XHInputView.show(
      with: style,
      configurationBlock: { [weak self] (inputView) in
        inputView?.delegate = self
        inputView?.placeholder = (indexPath != nil ? "回应Ta" : "友好的沟通是成为朋友的第一步")
        inputView?.maxCount = 200
        inputView?.backgroundColor = .fz_keyboardBgColor
      }
    ) { [weak self] (text, sendIM) -> Bool in
      if text?.count ?? 0 > 0 {
        self?.doCommentAction(text!, isIM: sendIM)
        return true
      } else {
        return false
      }
    }
  }

  func commentDeleted(cell: DyCommentTableViewCell) {
    if let indexpath = listTable.indexPath(for: cell) {
      if indexpath.row == 0 && cellData.count == 1 {
        cellData = NSArray.init()
        listTable.reloadData()
        return
      }

      listTable.beginUpdates()
      if indexpath.row == 0 {
        let tempArr = NSMutableArray.init(array: cellData)
        tempArr.removeObject(at: indexpath.section)
        cellData = tempArr.copy() as! NSArray
        listTable.deleteSections([indexpath.section], with: .fade)
      } else {
        let rpData: AffairCommentsReplyResult =
          cellData[indexpath.section] as! AffairCommentsReplyResult
        rpData.data.remove(at: indexpath.row - 1)
        listTable.deleteRows(at: [indexpath], with: .fade)
      }
      listTable.endUpdates()
    }

  }

  func doCommentAction(_ text: String, isIM: Bool) {
    if momentDetail == nil {
      showMsgFast("数据有误，发送失败")
      return
    }

    if isRequesting {
      return
    }
    isRequesting = true

    HUD.show()
    var userId = momentDetail!.authorid
    var commentId = "0"
    var belongs = "0"
    if let iPath = selectedIndexPath,
      let rpData = cellData[iPath.section] as? AffairCommentsReplyResult
    {
      if rpData.data.count > 0 && iPath.row > 0,
        let torp = AffairCommentsToReplyResult.mj_object(withKeyValues: rpData.data[iPath.row - 1])
      {
        userId = torp.userid
        commentId = torp.commentid
      } else {
        userId = rpData.userid
        commentId = rpData.commentid
      }
      belongs = rpData.commentid
    }

    DoCommentModel.post(
      momentDetail!.authorid, contentid: momentDetail!.contentid, comment: text, to_userid: userId,
      to_commentid: commentId, belongs: belongs,
      success: { [weak self] (res) in
        HUD.dissmiss()
        self?.isRequesting = false
        let member = DoCommentModel.mj_object(withKeyValues: res)
        if member?.success.boolValue ?? false {
          AppConfig.removeContextOfHistory()
          self?.showMsgFast("评论成功")
          self?.cellData = NSArray.init()
          self?.loadComment()
          if self?.momentDetail!.authorid != USERINFO.userid && isIM {
            self?.sendToIM(text)
          }
        } else if member?.code.intValue == 1004 {
          FTHZAlertDialogController.showBannedTip(withMessage: member?.msg)
        } else {
          self?.showMsgFast(member?.msg ?? "发送失败")
        }
      }
    ) { [weak self] (err) in
      self?.isRequesting = false
      HUD.dissmiss()
      self?.showMsgFast("网络错误，请检查网络后重试")
    }
  }

  func sendToIM(_ sendTxt: String) {
    var imgStr = "null"
    var contentStr = "分享图片"
    if let model = momentDetail {
      if model.images.count > 0 {
        imgStr = model.images
      }
      if model.content.count > 0 {
        contentStr = model.content
      }
    }
  }
}
