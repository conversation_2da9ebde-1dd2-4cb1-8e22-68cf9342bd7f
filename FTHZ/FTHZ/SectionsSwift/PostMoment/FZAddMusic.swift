import Foundation

@objc class FZAddMusic: FZBaseVC {

  private lazy var linkLabel = UITextFieldPadding.init()
  private lazy var pastTipBg = UIView.init()
  private lazy var pastTipL = UILabel.init()
  private var isNotchScreen: Bool {
    if #available(iOS 11.0, *) {
      let window = UIApplication.shared.windows.first
      return window?.safeAreaInsets.top ?? 0 > 20
    }
    return false
  }
  typealias addComplete = (_ url: String) -> (Void)
  @objc var completion: addComplete?
  func setupUI() {
    let backBt = UIButton.init()
    backBt.setImage(UIImage.init(named: "back"), for: .normal)
    backBt.addTarget(self, action: #selector(backAction), for: .touchUpInside)
    contentView.addSubview(backBt)
    backBt.snp.makeConstraints { (make) in
      make.left.equalToSuperview().offset(kRealWidth(24))
      make.top.equalToSuperview().offset(isNotchScreen ? kRealWidth(58) : kRealWidth(34))
      make.size.equalTo(CGSize.init(width: kRealWidth(10), height: kRealWidth(15)))
    }

    let titleL = UILabel.init()
    titleL.text = "添加音乐"
    titleL.textColor = .fz_HighBlackColor
    titleL.font = .ft_SourceHanserifSC_Meium_16
    titleL.textAlignment = .center
    contentView.addSubview(titleL)
    titleL.snp.makeConstraints { (make) in
      make.centerX.equalToSuperview()
      make.centerY.equalTo(backBt)
    }

    let addBt = UIButton.init()
    addBt.setTitle("添加", for: .normal)
    addBt.setTitleColor(.fz_HighBlackColor, for: .normal)
    addBt.titleLabel?.font = .ft_SourceHanserifSC_Normal_14
    addBt.addTarget(self, action: #selector(addAction), for: .touchUpInside)
    contentView.addSubview(addBt)
    addBt.snp.makeConstraints { (make) in
      make.centerY.equalTo(titleL)
      make.right.equalToSuperview().offset(-kRealWidth(22))
    }

    linkLabel.backgroundColor = .fz_HighBlackColor
    linkLabel.font = .ft_SourceHanserifSC_Meium_12
    linkLabel.attributedPlaceholder = NSAttributedString(
      string: "粘贴音乐的分享链接在此处",
      attributes: [NSAttributedString.Key.foregroundColor: UIColor.fz_WiteColor_27])
    linkLabel.textColor = .white
    linkLabel.layer.cornerRadius = 8
    linkLabel.layer.masksToBounds = true
    contentView.addSubview(linkLabel)
    linkLabel.snp.makeConstraints { (make) in
      make.top.equalTo(titleL.snp_bottom).offset(kRealWidth(46))
      make.left.equalToSuperview().offset(kRealWidth(24))
      make.right.equalToSuperview().offset(-kRealWidth(24))
      make.height.equalTo(kRealWidth(51))

    }

    pastTipBg.backgroundColor = .fz_tipFillColor
    pastTipBg.isHidden = true 
    pastTipBg.layer.cornerRadius = 8
    pastTipBg.layer.masksToBounds = true
    contentView.addSubview(pastTipBg)
    pastTipBg.snp.makeConstraints { (make) in
      make.top.equalTo(linkLabel.snp_bottom).offset(kRealWidth(15))
      make.left.right.equalTo(linkLabel)
      make.height.equalTo(kRealWidth(27))
    }

    let pastTipBt = UIButton.init()
    pastTipBt.setTitle("粘贴", for: .normal)
    pastTipBt.setTitleColor(.fz_tipButtonColor, for: .normal)
    pastTipBt.titleLabel?.font = .ft_SourceHanserifSC_Meium_12
    pastTipBt.addTarget(self, action: #selector(pastAction), for: .touchUpInside)
    pastTipBg.addSubview(pastTipBt)
    pastTipBt.snp.makeConstraints { (make) in
      make.centerY.equalToSuperview()
      make.right.equalToSuperview().offset(-kRealWidth(6))
    }

    pastTipL.textColor = .fz_tipTxtColor
    pastTipL.font = .ft_SourceHanserifSC_Normal_12
    pastTipBg.addSubview(pastTipL)
    pastTipL.snp.makeConstraints { (make) in
      make.centerY.equalToSuperview()
      make.left.equalToSuperview().offset(kRealWidth(10))
      make.right.equalToSuperview().offset(-kRealWidth(56))
    }

  }

  @objc func backAction() {
    dismiss(animated: true) {}
  }

  @objc func addAction() {
    if completion != nil && linkLabel.text?.count ?? 0 > 0 {
      completion!(linkLabel.text!)
    }
    backAction()
  }

  @objc func pastAction() {
    linkLabel.text = pastTipL.text
  }

  func checkCopyboard() {
    if let copyString = UIPasteboard.general.string {
      pastTipBg.isHidden = false
      pastTipL.text = copyString

    } else {
      pastTipBg.isHidden = true
    }
  }
  override func viewDidLoad() {
    super.viewDidLoad()

    setupUI()
  }

  override func viewWillAppear(_ animated: Bool) {
    super.viewWillAppear(animated)

    checkCopyboard()
  }
}
