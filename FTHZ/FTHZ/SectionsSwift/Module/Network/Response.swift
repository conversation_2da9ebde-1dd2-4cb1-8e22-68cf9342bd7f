import Foundation

struct PageModel: Codable {
  var size: String?
  var page: String?
  var count: String?
  init(from decoder: Decoder) throws {
    size = try decoder.decode("size") ?? "0"
    page = try decoder.decode("page") ?? "0"
    count = try decoder.decodeIfPresent("count") ?? "0"
  }
}

class BaseResponse {
  var code: Int {
    guard let temp = json["code"] as? Int else {
      return -1
    }
    return temp
  }

  var message: String? {
    guard let temp = json["msg"] as? String else {
      return nil
    }
    return temp
  }

  var jsonData: Any? {
    guard let temp = json["data"] else {
      return nil
    }
    return temp
  }

  let json: [String: Any]

  init?(data: Any) {
    guard let temp = data as? [String: Any] else {
      return nil
    }
    self.json = temp
  }

  func json2Data(_ object: Any) -> Data? {
    return try? JSONSerialization.data(withJSONObject: object, options: [])
  }
}

class ListResponse<T>: BaseResponse where T: Codable {
  var totalCount: Int {
    guard let countStr = json["count"] as? String else {
      return 0
    }
    return Int(countStr) ?? 0
  }

  var dataList: [T]? {
    guard code == 0,
      let jsonData = jsonData as? [Any],
      let temp = json2Data(jsonData)
    else {
      return nil
    }
    return try? JSONDecoder().decode([T].self, from: temp)
  }

  var page: PageModel? {
    guard code == 0,
      let jsonData = jsonData as? [[String: Any]], jsonData.count > 0,
      let temp = json2Data(jsonData[0])
    else {
      return nil
    }

    return try? JSONDecoder().decode(PageModel.self, from: temp)

  }
}

class ModelResponse<T>: BaseResponse where T: Codable {
  var data: T? {
    guard code == 0,
      let tempJSONData = jsonData,
      let temp = json2Data(tempJSONData)
    else {
      return nil
    }

    return try? JSONDecoder().decode(T.self, from: temp)

  }
}
