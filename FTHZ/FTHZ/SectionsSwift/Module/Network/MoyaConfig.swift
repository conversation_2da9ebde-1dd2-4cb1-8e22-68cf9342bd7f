import Foundation
import Moya

let Moya_baseURL = "https://app.52hz.space"

let RESULT_CODE = "flag"
let RESULT_MESSAGE = "message"

protocol MoyaAddable {
  var cacheKey: String? { get }
  var isShowHud: Bool { get }
}

extension TargetType {
  public var baseURL: URL { return URL.init(string: (Moya_baseURL))! }

  public var headers: [String: String]? {
    var headers: [String: String] = [
      "Content-Type": "application/json",
      "App_Version": NemoUtil.getAppVersionint(),
      "App_Id": "1",
      "Channel_Name": "AppStore",
      "Resolution": "\(FZSCREEN_W)X\(FZSCREEN_H)",
      "Idfa": ASIdentifierManager.shared().advertisingIdentifier.uuidString,
      "Iphone_Type": NemoUtil.iphoneType(),
      "secretCon": "Hello, this is a secret message!",
      "secretRs":
        "975a461ad5e32f381e046679441aaedaa768456b47ba718701721aeb72caf92b0403fd4e9eda6868f52c7d5247c61ed3",
      "ssid": FTHZAccountManager.shared().token ?? "",
    ]

    let path = self.path
    let method = self.method.rawValue.lowercased()

    if let encryptedValue = FTHZRequestEncryptor.getEncryptedHeaderValue(
      [:], path: path, method: method)
    {
      headers["X-Encrypted-Data"] = encryptedValue
    }

    return headers
  }

  public var sampleData: Data {
    return Data()
  }
}
