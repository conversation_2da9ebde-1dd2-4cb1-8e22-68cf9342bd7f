import Foundation
import Moya

enum FindAPI {
  case searchContent(_ key: String)
  case getOceanList
  case getMylikeList(_ page: Int, size: Int)
  case getWeightRanking
  case cocreation
  case getRecommendSearch
}

extension FindAPI: TargetType, MoyaAddable {

  var path: String {
    switch self {
    case .searchContent:
      return "/n/content/search"
    case .getOceanList:
      return "/p/affair/followed/list"
    case .getMylikeList:
      return "/n/conntent/like/list"
    case .getWeightRanking:
      return "/n/socket/weight/statistics"
    case .cocreation:
      return "/n/user/cocreation"
    case .getRecommendSearch:
      return "/n/user/recommen/by/search"
    }
  }

  var method: Moya.Method {
    switch self {
    default:
      return .get
    }
  }

  var task: Task {
    var parameters: [String: Any] = [:]
    switch self {
    case let .searchContent(key):
      parameters = ["content": key]
    case let .getMylikeList(page, size):
      parameters = ["page": page, "size": size]
    case .getOceanList:
      ()
    case .getWeightRanking:
      ()
    case .cocreation:
      ()
    case .getRecommendSearch:
      ()
    }
    return .requestParameters(parameters: parameters, encoding: URLEncoding.default)
  }

  var cacheKey: String? {
    switch self {
    case .searchContent:
      return "mall home cache key"
    default:
      return nil
    }
  }

  var isShowHud: Bool {
    switch self {
    case .searchContent:
      return true
    default:
      return false
    }
  }
}
