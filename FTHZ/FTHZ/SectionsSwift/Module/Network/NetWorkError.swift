import Foundation

public struct ResponseCode {
  static let successResponseStatus = 0
  static let forceLogoutError = 1003
}

public enum NetworkError: Error {
  case jsonSerializationFailed(message: String)
  case jsonToDictionaryFailed(message: String)
  case loginStateIsexpired(message: String?)
  case serverResponse(message: String?, code: Int)
  case exception(message: String)
}

extension NetworkError {
  var message: String? {
    switch self {
    case let .serverResponse(msg, _):
      return msg
    default:
      return nil
    }
  }

  var code: Int {
    switch self {
    case let .serverResponse(_, code):
      return code
    default:
      return -1
    }
  }
}
