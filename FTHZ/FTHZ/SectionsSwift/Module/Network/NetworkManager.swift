import Foundation
import Moya

class NetworkManager<T> where T: Codable {

  @discardableResult
  func requestModel<R: TargetType & MoyaAddable>(
    _ type: R,
    test: Bool = false,
    progressBlock: ((Double) -> Void)? = nil,
    completion: @escaping ((ModelResponse<T>?) -> Void),
    error: @escaping (NetworkError) -> Void
  )
    -> Cancellable?
  {
    return request(
      type, test: test, progressBlock: progressBlock, modelComletion: completion, error: error)
  }

  @discardableResult
  func requestListModel<R: TargetType & MoyaAddable>(
    _ type: R,
    test: Bool = false,
    completion: @escaping ((ListResponse<T>?) -> Void),
    error: @escaping (NetworkError) -> Void
  )
    -> Cancellable?
  {
    return request(type, test: test, modelListComletion: completion, error: error)
  }

  private let barrierQueue = DispatchQueue(
    label: "com.hz-52.NetworkManager", attributes: .concurrent)
  private var fetchRequestKeys = [String]()
}

extension NetworkManager {
  private func request<R: TargetType & MoyaAddable>(
    _ type: R,
    test: Bool = false,
    progressBlock: ((Double) -> Void)? = nil,
    modelComletion: ((ModelResponse<T>?) -> Void)? = nil,
    modelListComletion: ((ListResponse<T>?) -> Void)? = nil,
    error: @escaping (NetworkError) -> Void
  )
    -> Cancellable?
  {
    if isSameRequest(type) {
      return nil
    }

    let provider = createProvider(type: type, test: test)
    let cancellable = provider.request(
      type, callbackQueue: DispatchQueue.global(),
      progress: { (progress) in
        DispatchQueue.main.async {
          progressBlock?(progress.progress)
        }
      }
    ) { (response) in
      let errorblock = { (e: NetworkError) in
        DispatchQueue.main.async {
          error(e)
        }
      }

      self.cleanRequest(type)

      switch response {
      case .success(let successResponse):
        if let temp = modelComletion {
          self.handleSuccessResponse(
            type, response: successResponse, modelComletion: temp, error: error)
        }
        if let temp = modelListComletion {
          self.handleSuccessResponse(
            type, response: successResponse, modelListComletion: temp, error: error)
        }
      case .failure:
        errorblock(NetworkError.exception(message: "未连接到服务器"))
      }
    }
    return cancellable
  }

  private func handleSuccessResponse<R: TargetType & MoyaAddable>(
    _ type: R,
    response: Response,
    modelComletion: ((ModelResponse<T>?) -> Void)? = nil,
    modelListComletion: ((ListResponse<T>?) -> Void)? = nil,
    error: @escaping (NetworkError) -> Void
  ) {
    switch type.task {
    case .uploadMultipart, .requestParameters:
      do {
        if let temp = modelComletion {
          let modelResponse = try handleResponseData(false, type: type, data: response.data)
          DispatchQueue.main.async {
            self.cacheData(type, modelComletion: temp, model: (modelResponse.0, nil))
            temp(modelResponse.0)
          }
        }

        if let temp = modelListComletion {
          let listResponse = try handleResponseData(true, type: type, data: response.data)
          DispatchQueue.main.async {
            self.cacheData(type, modelListComletion: temp, model: (nil, listResponse.1))
            temp(listResponse.1)
          }
        }
      } catch let NetworkError.serverResponse(message, code) {
        error(NetworkError.serverResponse(message: message, code: code))
      } catch let NetworkError.loginStateIsexpired(message) {
        error(NetworkError.loginStateIsexpired(message: message))
      } catch {
        #if Debug
          fatalError("未知错误")
        #endif
      }
    default:
      ()
    }
  }

  private func handleResponseData<R: TargetType & MoyaAddable>(_ isList: Bool, type: R, data: Data)
    throws -> (ModelResponse<T>?, ListResponse<T>?)
  {
    guard let jsonAny = try? JSONSerialization.jsonObject(with: data, options: []) else {
      throw NetworkError.jsonSerializationFailed(message: "JSON解析失败")
    }

    if isList {
      let listResponse: ListResponse<T>? = ListResponse(data: jsonAny)
      guard let temp = listResponse else {
        throw NetworkError.jsonToDictionaryFailed(message: "JSON转字典失败")
      }

      if temp.code != ResponseCode.successResponseStatus {
        try handleCode(responseCode: temp.code, message: temp.message)
      }

      return (nil, temp)
    } else {
      let response: ModelResponse<T>? = ModelResponse(data: jsonAny)
      guard let temp = response else {
        throw NetworkError.jsonToDictionaryFailed(message: "JSON转字典失败")
      }

      if temp.code != ResponseCode.successResponseStatus {
        try handleCode(responseCode: temp.code, message: temp.message)
      }

      return (temp, nil)
    }
  }

  private func handleCode(responseCode: Int, message: String?) throws {
    switch responseCode {
    case ResponseCode.forceLogoutError:
      throw NetworkError.loginStateIsexpired(message: message)
    default:
      throw NetworkError.serverResponse(message: message, code: responseCode)
    }
  }

  private func cacheData<R: TargetType & MoyaAddable>(
    _ type: R,
    modelComletion: ((ModelResponse<T>?) -> Void)? = nil,
    modelListComletion: ((ListResponse<T>?) -> Void)? = nil,
    model: (ModelResponse<T>?, ListResponse<T>?)
  ) {
    guard let cacheKey = type.cacheKey else {
      return
    }
    if modelComletion != nil, let temp = model.0 {
    }
    if modelListComletion != nil, let temp = model.1 {
    }
  }

  private func JSONResponseDataFormatter(_ data: Data) -> String {
    do {
      let dataAsJSON = try JSONSerialization.jsonObject(with: data)
      let prettyData = try JSONSerialization.data(
        withJSONObject: dataAsJSON, options: .prettyPrinted)
      return String(data: prettyData, encoding: .utf8) ?? ""
    } catch {
      if JSONSerialization.isValidJSONObject(data) {
        return String(data: data, encoding: .utf8) ?? ""
      }
      return ""
    }
  }

  private func createProvider<T: TargetType & MoyaAddable>(type: T, test: Bool) -> MoyaProvider<T> {
    let activityPlugin = NetworkActivityPlugin { (state, targetType) in
      switch state {
      case .began:
        DispatchQueue.main.async {
          if type.isShowHud {
          }
          self.startStatusNetworkActivity()
        }
      case .ended:
        DispatchQueue.main.async {
          if type.isShowHud {
          }
          self.stopStatusNetworkActivity()
        }
      }
    }

    let provider = MoyaProvider<T>(plugins: [activityPlugin])
    return provider
  }

  private func startStatusNetworkActivity() {
    UIApplication.shared.isNetworkActivityIndicatorVisible = true
  }

  private func stopStatusNetworkActivity() {
    UIApplication.shared.isNetworkActivityIndicatorVisible = false
  }
}

extension NetworkManager {
  private func isSameRequest<R: TargetType & MoyaAddable>(_ type: R) -> Bool {
    switch type.task {
    case let .requestParameters(parameters, _):
      let key = type.path + parameters.description
      var result: Bool!
      barrierQueue.sync(flags: .barrier) {
        result = fetchRequestKeys.contains(key)
        if !result {
          fetchRequestKeys.append(key)
        }
      }
      return result
    default:
      return false
    }
  }

  private func cleanRequest<R: TargetType & MoyaAddable>(_ type: R) {
    switch type.task {
    case let .requestParameters(parameters, _):
      let key = type.path + parameters.description
      barrierQueue.sync(flags: .barrier) {
        fetchRequestKeys.filter { $0 != key }

      }
    default:
      ()
    }
  }
}
