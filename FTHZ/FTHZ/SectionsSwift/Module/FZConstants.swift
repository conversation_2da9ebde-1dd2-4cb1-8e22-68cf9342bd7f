import Foundation

let SEARCH_ICON = "search_icon"
let SEARCH_WHITE_ICON = "search_white_icon"
let EDIT_ICON = "edit_icon"
let FOLLOWER_ICON = "follwers_icon"
let FOLLOWING_ICON = "following_icon"
let LIKE_ICON = "like_icon"
let CUSTOMERSERVICE_ICON = "customerService_icon"
let DRAFTBOX_ICON = "draftbox_icon"
let HOMEPAGE_ICON = "homePage_icon"
let INVIACTION_ICON = "invitaction_icon"
let SETTING_ICON = "setting_icon"
let COCREATION_ICON = "cocreation_icon"
let LIUYAN_ICON = "liuyan_icon"
let POST_ICON = "post_icon"
let DRAFT_EDIT_ICON = "编辑"
let EMPTY_ICON = "empty"

let MORE_BLACK_ICON = "more-black"
let MORE_WHITE_ICON = "more-white"

let USER_HOMEPAGE_ICON = "用户主页"
let CREATE_CHANNEL_ICON = "发布"
let NEWEST_SORT_ICON = "最新顺序"
let CHANNEL_REPY_COUNT_ICON = "声波回应"

let FZSCREEN_W = UIScreen.main.bounds.width
let FZSCREEN_H =
  UIScreen.main.bounds.height == 812
  ? UIScreen.main.bounds.height - 64.0 : UIScreen.main.bounds.height

let kPerWidth = FZSCREEN_W / 414.0
func kRealWidth(_ width: CGFloat) -> CGFloat { return kPerWidth * width }

var USERINFO: UserPersonResult! {
  return FTHZAccountManager.shared().user ?? UserPersonResult.init()
}

var MomentPostPostion: CGPoint {
  set {
    UserDefaults.standard.set(NSCoder.string(for: newValue), forKey: "52herz.momont.post.postion")
    UserDefaults.standard.synchronize()
  }

  get {
    guard let pointString = UserDefaults.standard.string(forKey: "52herz.momont.post.postion")
    else { return CGPoint.zero }
    return NSCoder.cgPoint(for: pointString)
  }
}

var StraitPostPostion: CGPoint {
  set {
    UserDefaults.standard.set(NSCoder.string(for: newValue), forKey: "52herz.strait.post.postion")
    UserDefaults.standard.synchronize()
  }

  get {
    guard let pointString = UserDefaults.standard.string(forKey: "52herz.strait.post.postion")
    else { return CGPoint.zero }
    return NSCoder.cgPoint(for: pointString)
  }
}

var TopicPostPostion: CGPoint {
  set {
    UserDefaults.standard.set(NSCoder.string(for: newValue), forKey: "52herz.topic.post.postion")
    UserDefaults.standard.synchronize()
  }

  get {
    guard let pointString = UserDefaults.standard.string(forKey: "52herz.topic.post.postion") else {
      return CGPoint.zero
    }
    return NSCoder.cgPoint(for: pointString)
  }
}
