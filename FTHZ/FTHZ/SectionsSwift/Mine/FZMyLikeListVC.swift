import Foundation

class FZMyLikeListVC: FZBaseVC, UITableViewDelegate {
  private var pageIndex: Int = 1
  lazy var listVC: ConfigurableTableViewController = ConfigurableTableViewController()
  private var isNotchScreen: Bool {
    if #available(iOS 11.0, *) {
      let window = UIApplication.shared.windows.first
      return window?.safeAreaInsets.top ?? 0 > 20
    }
    return false
  }
  var items: [MyLikedModel]? {
    didSet {
      loadListData()
    }
  }

  func loadRequestData() {
    NetworkManager<MyLikedModel>().requestListModel(
      FindAPI.getMylikeList(pageIndex, size: 20),
      completion: { [weak self] response in
        guard let self = self else { return }
        let items = response?.dataList ?? []

        DispatchQueue.main.async {
          HUD.dissmiss()

          if self.items != nil {
            self.items? += items
          } else {
            self.items = items
          }

          let totalCount = (self.items?.count ?? 0)
          if totalCount > 0 {
            if self.listVC.tableView?.mj_footer == nil {
              let mjFooter = MJChiBaoZiFooter(
                refreshingTarget: self,
                refreshingAction: #selector(self.loadMoreData))
              mjFooter?.isRefreshingTitleHidden = true
              mjFooter?.setTitle("", for: .idle)
              self.listVC.tableView?.mj_footer = mjFooter
            }
            if items.count < 20 {
              self.listVC.tableView?.mj_footer?.endRefreshingWithNoMoreData()
            } else {
              self.listVC.tableView?.mj_footer?.endRefreshing()
            }
          } else {
            self.listVC.tableView?.mj_footer = nil
          }

          self.listVC.tableView?.reloadData()
        }
      }
    ) { [weak self] error in
      DispatchQueue.main.async {
        guard let self = self else { return }
        HUD.dissmiss()

        self.pageIndex = max(1, self.pageIndex - 1)

        if let msg = error.message {
          self.view.makeToast(msg)
        }
        self.listVC.tableView?.mj_footer?.endRefreshing()
      }
    }
  }

  func setupUI() {
    view.backgroundColor = .white
    let backBt = UIButton()
    backBt.setImage(UIImage(named: "back"), for: .normal)
    backBt.addTarget(self, action: #selector(backAction), for: .touchUpInside)
    contentView.addSubview(backBt)
    backBt.snp.makeConstraints { make in
      make.left.equalToSuperview().offset(kRealWidth(24))
      make.top.equalToSuperview().offset(isNotchScreen ? kRealWidth(58) : kRealWidth(34))
      make.size.equalTo(CGSize(width: kRealWidth(10), height: kRealWidth(15)))
    }

    let title = UILabel()
    title.text = "点赞列表"
    title.textColor = .fz_HighBlackColor
    title.font = .ft_SourceHanserifSC_Meium_16
    contentView.addSubview(title)
    title.snp.makeConstraints { make in
      make.centerY.equalTo(backBt)
      make.centerX.equalToSuperview()
    }

    addChild(listVC)
    contentView.addSubview(listVC.view)
    listVC.view.snp.makeConstraints { make in
      make.left.right.bottom.equalToSuperview()
      make.top.equalTo(backBt.snp_bottom).offset(kRealWidth(30))
    }
  }

  func loadListData() {
    var list: Array = Array<CellConfiguratorType>.init()
    items?.forEach { m in
      list.append(CellConfigurator<MyLikedCell>(viewData: m))
    }

    if list.count > 0 {
      listVC.items = list
      listVC.tableView.separatorStyle = .none
      listVC.tableView.delegate = self
      listVC.tableView.rowHeight = kRealWidth(72)
    }
  }

  @objc func loadMoreData() {
    pageIndex += 1
    loadRequestData()
  }

  @objc func backAction() {
    navigationController?.popViewController(animated: true)
    NotificationCenter.default.post(
      name: NSNotification.Name(rawValue: "NotificationForOpenLeftMenu"), object: nil)
  }

  override func viewDidLoad() {
    super.viewDidLoad()
    setupUI()
    listVC.tableView.delegate = self
    let header = MJChiBaoZiHeader(refreshingTarget: self, refreshingAction: #selector(refreshData))
    header?.lastUpdatedTimeLabel.isHidden = true
    header?.stateLabel.isHidden = true
    listVC.tableView?.mj_header = header
    loadRequestData()
  }

  override func viewWillAppear(_ animated: Bool) {
    super.viewWillAppear(animated)
    AppConfig.statusbarStyle(true)
  }

  func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
    tableView.deselectRow(at: indexPath, animated: true)
    if let data = items?[indexPath.row],
      data.status == 0
    {
      let detail = FZMomentVC()
      detail.momentId = "\(data.contentid)"
      detail.userId = "\(data.userid ?? 0)"
      navigationController?.pushViewController(detail, animated: true)
    }
  }

  @objc func refreshData() {
    pageIndex = 1
    items = []
    loadRequestData()
    listVC.tableView?.mj_header?.endRefreshing()
  }
}
