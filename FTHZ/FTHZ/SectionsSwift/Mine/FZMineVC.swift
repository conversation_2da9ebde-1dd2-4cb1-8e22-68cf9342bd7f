import SideMenu

class FZMineVC: FZBaseVC {
  private var avatarIV: UIImageView!
  private lazy var userName = UILabel.init()
  private lazy var sologenLabel = UILabel.init()
  private lazy var hzLabel = UILabel.init()
  private lazy var followingLabel = UILabel.init()
  private lazy var followerLabel = UILabel.init()
  private lazy var likeLabel = UILabel.init()
  // private lazy var sexMark = UIView.init()
  private var homepageBadge: UIView!
  private lazy var homepageBt = TouchAreaButton.init()
  private lazy var draftBoxBt = TouchAreaButton.init()
  private lazy var messageBtn = TouchAreaButton.init()
  private lazy var settingBt = TouchAreaButton.init()
  private lazy var cocreationBt = TouchAreaButton.init()
  private var isNeedStatusBarWhite: Bool = false
  private var isNotchScreen: Bool {
    if #available(iOS 11.0, *) {
      let window = UIApplication.shared.windows.first
      return window?.safeAreaInsets.top ?? 0 > 20
    }
    return false
  }
  private func enterPersonalHomePage() {
    guard let userInfo = USERINFO else {
      return
    }

    let infoVC = WhaleDetailVC()
    infoVC.uid = userInfo.userid
    navigationController?.pushViewController(infoVC, animated: true)
  }
  private func setupTop() {
    let topView = UIView.init()
    contentView.addSubview(topView)
    topView.snp.makeConstraints { (make) in
      make.left.right.equalToSuperview()
      make.top.equalToSuperview().offset(isNotchScreen ? kRealWidth(24) : kRealWidth(0))
      make.height.equalTo(kRealWidth(250))
    }

    let avatarBgView = UIView.init()
    topView.addSubview(avatarBgView)
    avatarBgView.snp.makeConstraints { (make) in
      make.left.equalToSuperview().offset(kRealWidth(12))
      make.top.equalToSuperview().offset(kRealWidth(24))
      make.size.equalTo(CGSize(width: kRealWidth(48), height: kRealWidth(48)))
    }

    avatarIV = UIImageView.init()
    avatarIV.contentMode = .scaleAspectFill
    avatarIV.layer.cornerRadius = kRealWidth(24)
    avatarIV.layer.masksToBounds = true
    avatarBgView.addSubview(avatarIV)
    avatarIV.snp.makeConstraints { (make) in
      make.edges.equalToSuperview()
    }

    avatarIV.applyshadowWithCorner(containerView: avatarBgView, cornerRadious: kRealWidth(24))

    avatarIV.addTapGestureRecognizer { [weak self] in
      self?.enterPersonalHomePage()
    }

    userName = UILabel.init()
    userName.font = .ft_SourceHanSerif_Blod_16
    userName.textColor = .white
    topView.addSubview(userName)
    userName.snp.makeConstraints { (make) in
      make.left.equalTo(avatarIV.snp.right).offset(kRealWidth(12))
      make.bottom.equalTo(avatarIV.snp.centerY)
    }

    hzLabel.font = .ft_SourceHanSerif_Blod_16
    topView.addSubview(hzLabel)
    hzLabel.snp.makeConstraints { (make) in
      make.left.equalTo(userName.snp.right).offset(kRealWidth(12))
      make.centerY.equalTo(userName)
    }

    // sexMark = UIView.init()
    // sexMark.layer.cornerRadius = kRealWidth(6)
    // topView.addSubview(sexMark)
    // sexMark.snp.makeConstraints { (make) in
    //   make.left.equalTo(hzLabel.snp.right).offset(kRealWidth(12))
    //   make.centerY.equalTo(userName)
    //   make.size.equalTo(CGSize.init(width: kRealWidth(12), height: kRealWidth(12)))
    // }

    let editBt = TouchAreaButton.init()
    editBt.setImage(UIImage.init(named: EDIT_ICON), for: .normal)
    editBt.addTarget(self, action: #selector(editInfoAction), for: .touchUpInside)
    topView.addSubview(editBt)
    editBt.snp.makeConstraints { (make) in
      make.left.equalTo(hzLabel.snp.right).offset(kRealWidth(16))
      make.centerY.equalTo(userName)
      make.size.equalTo(CGSize.init(width: kRealWidth(14), height: kRealWidth(14)))
    }

    sologenLabel.textColor = .fz_lightWiteColor
    sologenLabel.font = .ft_SourceHanserifSC_Normal_12
    sologenLabel.numberOfLines = 1
    topView.addSubview(sologenLabel)
    sologenLabel.snp.makeConstraints { (make) in
      make.top.equalTo(userName.snp.bottom).offset(kRealWidth(4))
      make.left.equalTo(userName)
      make.right.equalToSuperview().offset(-kRealWidth(4))
    }

    let statsBackgroundView = UIView()
    statsBackgroundView.backgroundColor = .fz_HighBlackColor2
    statsBackgroundView.layer.cornerRadius = kRealWidth(12)
    statsBackgroundView.layer.shadowColor = UIColor.black.cgColor
    statsBackgroundView.layer.shadowOffset = CGSize(width: 0, height: 2)
    statsBackgroundView.layer.shadowOpacity = 0.3
    statsBackgroundView.layer.shadowRadius = kRealWidth(4)
    topView.addSubview(statsBackgroundView)
    statsBackgroundView.snp.makeConstraints { make in
      make.left.equalToSuperview().offset(kRealWidth(12))
      make.right.equalToSuperview().offset(-kRealWidth(12))
      make.top.equalTo(avatarIV.snp.bottom).offset(kRealWidth(32))
      make.height.equalTo(kRealWidth(60))
    }

    let followingBt = TouchAreaButton.init()
    followingBt.setImage(UIImage.init(named: FOLLOWING_ICON), for: .normal)
    followingBt.addTarget(self, action: #selector(followingAction), for: .touchUpInside)
    statsBackgroundView.addSubview(followingBt)
    followingBt.snp.makeConstraints { make in
      make.left.equalToSuperview()
      make.top.equalToSuperview().offset(kRealWidth(6))
      make.width.equalTo(statsBackgroundView.snp.width).multipliedBy(1.0 / 3.0)
    }

    followingLabel.textColor = .fz_WiteColor_27
    followingLabel.font = .ft_Din_Bold_16
    statsBackgroundView.addSubview(followingLabel)
    followingLabel.snp.makeConstraints { make in
      make.centerX.equalTo(followingBt)
      make.bottom.equalToSuperview().offset(kRealWidth(-6))
    }

    let followerBt = TouchAreaButton.init()
    followerBt.setImage(UIImage.init(named: FOLLOWER_ICON), for: .normal)
    followerBt.addTarget(self, action: #selector(followerAction), for: .touchUpInside)
    statsBackgroundView.addSubview(followerBt)
    followerBt.snp.makeConstraints { make in
      make.left.equalTo(followingBt.snp.right)
      make.top.equalToSuperview().offset(kRealWidth(6))
      make.width.equalTo(statsBackgroundView.snp.width).multipliedBy(1.0 / 3.0)
    }

    followerLabel.textColor = .fz_WiteColor_27
    followerLabel.font = .ft_Din_Bold_16
    statsBackgroundView.addSubview(followerLabel)
    followerLabel.snp.makeConstraints { make in
      make.centerX.equalTo(followerBt)
      make.bottom.equalToSuperview().offset(kRealWidth(-6))
    }

    let likeBt = TouchAreaButton.init()
    likeBt.setImage(UIImage.init(named: LIKE_ICON), for: .normal)
    likeBt.blockAction { [weak self] (button) in
      let likeListVC = FZMyLikeListVC.init()
      self?.navigationController?.pushViewController(likeListVC, animated: true)
    }
    statsBackgroundView.addSubview(likeBt)
    likeBt.snp.makeConstraints { make in
      make.left.equalTo(followerBt.snp.right)
      make.top.equalToSuperview().offset(kRealWidth(6))
      make.width.equalTo(statsBackgroundView.snp.width).multipliedBy(1.0 / 3.0)
    }

    // let imageView = UIImageView()
    // imageView.image = UIImage(named: "52")
    // imageView.contentMode = .scaleAspectFit
    // contentView.addSubview(imageView)
    // imageView.snp.makeConstraints { make in
    //   make.centerX.equalToSuperview()
    //   make.width.equalTo(contentView.snp.width).offset(-kRealWidth(48))
    //   make.top.equalTo(statsBackgroundView.snp.bottom).offset(kRealWidth(50))
    //   make.height.equalTo(imageView.snp.width).multipliedBy(
    //     (imageView.image?.size.height ?? 1) / (imageView.image?.size.width ?? 1))
    // }

    likeLabel.textColor = .fz_WiteColor_27
    likeLabel.font = .ft_Din_Bold_16
    statsBackgroundView.addSubview(likeLabel)
    likeLabel.snp.makeConstraints { make in
      make.centerX.equalTo(likeBt)
      make.bottom.equalToSuperview().offset(kRealWidth(-6))
    }

    let homepageBgView = UIView()
    homepageBgView.backgroundColor = .fz_HighBlackColor2
    homepageBgView.layer.cornerRadius = kRealWidth(12)
    homepageBgView.layer.shadowColor = UIColor.black.cgColor
    homepageBgView.layer.shadowOffset = CGSize(width: 0, height: 2)
    homepageBgView.layer.shadowOpacity = 0.3
    homepageBgView.layer.shadowRadius = kRealWidth(4)
    contentView.addSubview(homepageBgView)
    homepageBgView.snp.makeConstraints { make in
      make.left.equalToSuperview().offset(kRealWidth(12))
      make.right.equalToSuperview().offset(-kRealWidth(12))
      make.bottom.equalToSuperview().offset(-kRealWidth(260))
      make.height.equalTo(kRealWidth(44))
    }

    configureButtonImage(homepageBt, imageName: HOMEPAGE_ICON)
    homepageBt.setTitle("主页", for: .normal)
    homepageBt.setTitleColor(.fz_lightWiteColor, for: .normal)
    homepageBt.titleLabel?.font = .ft_SourceHanserifSC_Meium_14
    homepageBt.contentHorizontalAlignment = .left
    homepageBt.imageEdgeInsets = UIEdgeInsets(top: 0, left: kRealWidth(16), bottom: 0, right: 0)
    homepageBt.titleEdgeInsets = UIEdgeInsets(top: 0, left: kRealWidth(24), bottom: 0, right: 0)
    homepageBt.addTarget(self, action: #selector(homepageAction), for: .touchUpInside)
    homepageBgView.addSubview(homepageBt)
    homepageBt.snp.makeConstraints { make in
      make.edges.equalToSuperview()
    }

    let arrowImage = UIImage(named: "goto")
    let arrowImageView = UIImageView(image: arrowImage)
    homepageBgView.addSubview(arrowImageView)
    arrowImageView.snp.makeConstraints { make in
      make.centerY.equalToSuperview()
      make.right.equalToSuperview().offset(-kRealWidth(16))
      make.size.equalTo(CGSize(width: kRealWidth(4), height: kRealWidth(8)))
    }

    let draftBoxBgView = UIView()
    draftBoxBgView.backgroundColor = .fz_HighBlackColor2
    draftBoxBgView.layer.cornerRadius = kRealWidth(12)
    draftBoxBgView.layer.shadowColor = UIColor.black.cgColor
    draftBoxBgView.layer.shadowOffset = CGSize(width: 0, height: 2)
    draftBoxBgView.layer.shadowOpacity = 0.3
    draftBoxBgView.layer.shadowRadius = kRealWidth(4)
    contentView.addSubview(draftBoxBgView)
    draftBoxBgView.snp.makeConstraints { make in
      make.left.right.equalTo(homepageBgView)
      make.bottom.equalToSuperview().offset(-kRealWidth(200))
      make.height.equalTo(homepageBgView)
    }

    configureButtonImage(draftBoxBt, imageName: DRAFTBOX_ICON)
    draftBoxBt.setTitle("草稿箱", for: .normal)
    draftBoxBt.titleLabel?.font = .ft_SourceHanserifSC_Meium_14
    draftBoxBt.setTitleColor(.fz_lightWiteColor, for: .normal)
    draftBoxBt.contentHorizontalAlignment = .left
    draftBoxBt.imageEdgeInsets = UIEdgeInsets(top: 0, left: kRealWidth(16), bottom: 0, right: 0)
    draftBoxBt.titleEdgeInsets = UIEdgeInsets(top: 0, left: kRealWidth(24), bottom: 0, right: 0)
    draftBoxBt.addTarget(self, action: #selector(draftAction), for: .touchUpInside)
    draftBoxBgView.addSubview(draftBoxBt)
    draftBoxBt.snp.makeConstraints { make in
      make.edges.equalToSuperview()
    }

    let draftArrowImageView = UIImageView(image: arrowImage)
    draftBoxBgView.addSubview(draftArrowImageView)
    draftArrowImageView.snp.makeConstraints { make in
      make.centerY.equalToSuperview()
      make.right.equalToSuperview().offset(-kRealWidth(16))
      make.size.equalTo(arrowImageView)
    }

    let settingBgView = UIView()
    settingBgView.backgroundColor = .fz_HighBlackColor2
    settingBgView.layer.cornerRadius = kRealWidth(12)
    settingBgView.layer.shadowColor = UIColor.black.cgColor
    settingBgView.layer.shadowOffset = CGSize(width: 0, height: 2)
    settingBgView.layer.shadowOpacity = 0.3
    settingBgView.layer.shadowRadius = kRealWidth(4)
    contentView.addSubview(settingBgView)
    settingBgView.snp.makeConstraints { make in
      make.left.right.equalTo(homepageBgView)
      make.bottom.equalToSuperview().offset(-kRealWidth(140))
      make.height.equalTo(homepageBgView)
    }

    configureButtonImage(settingBt, imageName: SETTING_ICON)
    settingBt.setTitle("设置", for: .normal)
    settingBt.addTarget(self, action: #selector(settingAction), for: .touchUpInside)
    settingBt.titleLabel?.font = .ft_SourceHanserifSC_Meium_14
    settingBt.setTitleColor(.fz_lightWiteColor, for: .normal)
    settingBt.contentHorizontalAlignment = .left
    settingBt.imageEdgeInsets = UIEdgeInsets(top: 0, left: kRealWidth(16), bottom: 0, right: 0)
    settingBt.titleEdgeInsets = UIEdgeInsets(top: 0, left: kRealWidth(24), bottom: 0, right: 0)
    settingBgView.addSubview(settingBt)
    settingBt.snp.makeConstraints { make in
      make.edges.equalToSuperview()
    }

    let settingArrowImageView = UIImageView(image: arrowImage)
    settingBgView.addSubview(settingArrowImageView)
    settingArrowImageView.snp.makeConstraints { make in
      make.centerY.equalToSuperview()
      make.right.equalToSuperview().offset(-kRealWidth(16))
      make.size.equalTo(arrowImageView)
    }

    let cocreationBgView = UIView()
    cocreationBgView.backgroundColor = .fz_HighBlackColor2
    cocreationBgView.layer.cornerRadius = kRealWidth(12)
    cocreationBgView.layer.shadowColor = UIColor.black.cgColor
    cocreationBgView.layer.shadowOffset = CGSize(width: 0, height: 2)
    cocreationBgView.layer.shadowOpacity = 0.3
    cocreationBgView.layer.shadowRadius = kRealWidth(4)
    contentView.addSubview(cocreationBgView)
    cocreationBgView.snp.makeConstraints { make in
      make.left.right.equalTo(homepageBgView)
      make.bottom.equalToSuperview().offset(-kRealWidth(80))
      make.height.equalTo(homepageBgView)
    }

    configureButtonImage(cocreationBt, imageName: COCREATION_ICON)
    cocreationBt.setTitle("共创", for: .normal)
    cocreationBt.addTarget(self, action: #selector(cocreationAction), for: .touchUpInside)
    cocreationBt.titleLabel?.font = .ft_SourceHanserifSC_Meium_14
    cocreationBt.setTitleColor(.fz_lightWiteColor, for: .normal)
    cocreationBt.contentHorizontalAlignment = .left
    cocreationBt.imageEdgeInsets = UIEdgeInsets(top: 0, left: kRealWidth(16), bottom: 0, right: 0)
    cocreationBt.titleEdgeInsets = UIEdgeInsets(top: 0, left: kRealWidth(24), bottom: 0, right: 0)
    cocreationBgView.addSubview(cocreationBt)
    cocreationBt.snp.makeConstraints { make in
      make.edges.equalToSuperview()
    }

    let cocreationArrowImageView = UIImageView(image: arrowImage)
    cocreationBgView.addSubview(cocreationArrowImageView)
    cocreationArrowImageView.snp.makeConstraints { make in
      make.centerY.equalToSuperview()
      make.right.equalToSuperview().offset(-kRealWidth(16))
      make.size.equalTo(arrowImageView)
    }

    let aboutBt = UIButton.init()
    aboutBt.isHidden = true
    aboutBt.setTitle("关于", for: .normal)
    aboutBt.setTitleColor(.fz_lightWiteColor, for: .normal)
    aboutBt.titleLabel?.font = .ft_SourceHanserifSC_Normal_12
    aboutBt.blockAction { [weak self] (button) in
      let activeWeb = ActivityWebVC.init()
      self?.navigationController?.pushViewController(activeWeb, animated: true)
    }

    contentView.addSubview(aboutBt)
    aboutBt.snp_makeConstraints { (make) in
      make.left.equalToSuperview().offset(kRealWidth(24))
      make.bottom.equalToSuperview().offset(-kRealWidth(24))
    }

    let agreementStack = UIStackView()
    agreementStack.axis = .horizontal
    agreementStack.spacing = kRealWidth(8)
    agreementStack.alignment = .center
    contentView.addSubview(agreementStack)
    agreementStack.snp.makeConstraints { make in
      make.centerX.equalToSuperview()
      make.bottom.equalToSuperview().offset(-kRealWidth(24))
    }

    let userAgreementBtn = UIButton()
    userAgreementBtn.setTitle("《用户协议》", for: .normal)
    userAgreementBtn.setTitleColor(
      UIColor(red: 51 / 255.0, green: 191 / 255.0, blue: 204 / 255.0, alpha: 1.0), for: .normal)
    userAgreementBtn.titleLabel?.font = .ft_SourceHanserifSC_Normal_12
    userAgreementBtn.addTarget(self, action: #selector(showUserAgreement), for: .touchUpInside)

    let separatorLabel = UILabel()
    separatorLabel.text = "/"
    separatorLabel.textColor = .white
    separatorLabel.font = .ft_SourceHanserifSC_Normal_12

    let privacyPolicyBtn = UIButton()
    privacyPolicyBtn.setTitle("《隐私政策》", for: .normal)
    privacyPolicyBtn.setTitleColor(
      UIColor(red: 51 / 255.0, green: 191 / 255.0, blue: 204 / 255.0, alpha: 1.0), for: .normal)
    privacyPolicyBtn.titleLabel?.font = .ft_SourceHanserifSC_Normal_12
    privacyPolicyBtn.addTarget(self, action: #selector(showPrivacyPolicy), for: .touchUpInside)

    agreementStack.addArrangedSubview(userAgreementBtn)
    agreementStack.addArrangedSubview(separatorLabel)
    agreementStack.addArrangedSubview(privacyPolicyBtn)
  }

  private func showAgreement(type: String) {
    let agreementView = UIView(
      frame: CGRect(x: 0, y: 0, width: view.frame.width, height: view.frame.height))

    let maskView = UIView(frame: agreementView.frame)
    maskView.backgroundColor = .black
    maskView.alpha = 0.6
    agreementView.addSubview(maskView)

    let contentView = UIView(
      frame: CGRect(x: 30, y: 80, width: view.frame.width - 60, height: view.frame.height - 160))
    contentView.backgroundColor = .white
    contentView.layer.cornerRadius = 5.0
    agreementView.addSubview(contentView)

    let titleLabel = UILabel(
      frame: CGRect(x: 15, y: 18, width: contentView.frame.width - 20, height: 18))
    titleLabel.textColor = .fz_HighBlackColor
    titleLabel.textAlignment = .center
    titleLabel.font = .systemFont(ofSize: 18.0)
    titleLabel.text = type == "0" ? "52赫兹服务协议" : "52赫兹用户隐私政策"
    contentView.addSubview(titleLabel)

    let lineView = UIView(frame: CGRect(x: 0, y: 46, width: contentView.frame.width, height: 1))
    lineView.backgroundColor = .fz_HighBlackColor
    contentView.addSubview(lineView)

    let scrollView = UIScrollView(
      frame: CGRect(
        x: 10,
        y: lineView.frame.maxY,
        width: contentView.frame.width - 20,
        height: contentView.frame.height - 55 - 55))
    contentView.addSubview(scrollView)

    let activityIndicator = UIActivityIndicatorView(style: .gray)
    activityIndicator.center = scrollView.center
    activityIndicator.startAnimating()
    scrollView.addSubview(activityIndicator)

    let closeButton = UIButton(
      frame: CGRect(
        x: 0,
        y: contentView.frame.height - 65,
        width: contentView.frame.width,
        height: 65))
    closeButton.setTitle("知道了", for: .normal)
    closeButton.setTitleColor(.fz_HighBlackColor, for: .normal)
    closeButton.titleLabel?.font = .systemFont(ofSize: 18.0)
    closeButton.addTarget(self, action: #selector(hideAgreement(_:)), for: .touchUpInside)
    contentView.addSubview(closeButton)

    DispatchQueue.global(qos: .userInitiated).async {
      let fileName = type == "0" ? "52fuwu" : "52yinsi"
      if let filePath = Bundle.main.path(forResource: fileName, ofType: "txt"),
        let content = try? String(contentsOfFile: filePath, encoding: .utf8)
      {

        DispatchQueue.main.async {
          activityIndicator.stopAnimating()
          activityIndicator.removeFromSuperview()

          let contentLabel = UILabel(
            frame: CGRect(x: 5, y: 5, width: scrollView.frame.width - 10, height: 0))
          contentLabel.numberOfLines = 0
          contentLabel.font = .systemFont(ofSize: 13.0)
          contentLabel.textColor = .fz_HighBlackColor
          contentLabel.text = content
          scrollView.addSubview(contentLabel)
          contentLabel.sizeToFit()

          scrollView.contentSize = CGSize(
            width: contentLabel.frame.width,
            height: contentLabel.frame.height + contentLabel.frame.origin.y)
        }
      } else {
        DispatchQueue.main.async {
          activityIndicator.stopAnimating()
          activityIndicator.removeFromSuperview()

          let errorLabel = UILabel(
            frame: CGRect(x: 5, y: 5, width: scrollView.frame.width - 10, height: 20))
          errorLabel.text = "加载失败，请稍后重试"
          errorLabel.textAlignment = .center
          errorLabel.textColor = .fz_HighBlackColor
          scrollView.addSubview(errorLabel)
        }
      }
    }

    view.addSubview(agreementView)
  }
  @objc private func hideAgreement(_ sender: UIButton) {
    sender.superview?.superview?.removeFromSuperview()
  }

  @objc private func showUserAgreement() {
    showAgreement(type: "0")
  }

  @objc private func showPrivacyPolicy() {
    showAgreement(type: "1")
  }

  private func configureButtonImage(_ button: TouchAreaButton, imageName: String) {
    guard let originalImage = UIImage(named: imageName)?.withRenderingMode(.alwaysOriginal) else {
      return
    }

    let targetWidth = kRealWidth(16)
    let scaleFactor = targetWidth / originalImage.size.width
    let targetHeight = originalImage.size.height * scaleFactor

    let size = CGSize(width: targetWidth, height: targetHeight)
    if size.width <= 0 || size.height <= 0 { return }
    UIGraphicsBeginImageContextWithOptions(size, false, 0.0)
    originalImage.draw(in: CGRect(origin: .zero, size: size))
    let resizedImage = UIGraphicsGetImageFromCurrentImageContext()
    UIGraphicsEndImageContext()

    button.setImage(resizedImage, for: .normal)
    button.imageView?.contentMode = .scaleAspectFit
  }

  private func checkUnreadCount() {
    guard homepageBadge != nil else {
      return
    }

    LiuyanNotificationModel.getLiuyanUnreadCount(
      { [weak self] (resultObject) -> Void in
        if let dict = resultObject as? [String: Any],
          let code = dict["code"] as? Int,
          code == 0,
          let data = dict["data"] as? [[String: Any]],
          let countInfo = data.first,
          let unreadCount = countInfo["unreadCount"] as? Int
        {
          DispatchQueue.main.async {
            self?.homepageBadge?.isHidden = unreadCount == 0
          }
        }
      },
      failure: { [weak self] (error) -> Void in
        DispatchQueue.main.async {
          self?.homepageBadge?.isHidden = true
        }
      })
  }

  func updateUserInfo() {
    avatarIV.netImgByUrl(NemoUtil.getUrlWithUserDefartIcon(USERINFO?.avatar), "empty")
    userName.text = USERINFO?.nickname
    hzLabel.text = (USERINFO?.hertz ?? "") + "Hz"
    hzLabel.textColor = USERINFO.gender == "1" ? .fz_skyBlueColor : .fz_femalePinkColor
    // sexMark.backgroundColor = USERINFO.gender == "1" ? .fz_skyBlueColor : .fz_femalePinkColor
    sologenLabel.text = USERINFO?.signature
    let formatNumberText = { (numStr: String?) -> String in
      guard let str = numStr, let num = Int(str) else { return "0" }
      return num > 999 ? "999+" : str
    }

    GetUserAFLModel.getUserAFL(
      { [weak self] (resultObject) -> Void in
        if let dict = resultObject as? [String: Any],
          let code = dict["code"] as? Int,
          code == 0,
          let data = dict["data"] as? [String: Any]
        {
          DispatchQueue.main.async {
            self?.followingLabel.text = formatNumberText(data["attentionNum"] as? String)
            self?.followerLabel.text = formatNumberText(data["fansNum"] as? String)
            self?.likeLabel.text = formatNumberText(data["likeNum"] as? String)
          }
        } else {
          DispatchQueue.main.async {
            self?.followingLabel.text = formatNumberText(USERINFO.attentionNum)
            self?.followerLabel.text = formatNumberText(USERINFO.fansNum)
            self?.likeLabel.text = formatNumberText(USERINFO.likeCount)
          }
        }
      },
      failure: { [weak self] (error) -> Void in
        DispatchQueue.main.async {
          self?.followingLabel.text = formatNumberText(USERINFO.attentionNum)
          self?.followerLabel.text = formatNumberText(USERINFO.fansNum)
          self?.likeLabel.text = formatNumberText(USERINFO.likeCount)
        }
      })
  }

  @objc func followingAction() {
    let followVC = FZFollowVC()
    followVC.setInitialPage(index: 0)
    navigationController?.pushViewController(followVC, animated: true)
  }

  @objc func followerAction() {
    let followVC = FZFollowVC()
    followVC.setInitialPage(index: 1)
    navigationController?.pushViewController(followVC, animated: true)
  }

  @objc func editInfoAction() {
    let userinfoVC = UserInformationVC.init()
    navigationController?.pushViewController(userinfoVC, animated: true)
  }

  @objc func homepageAction() {
    enterPersonalHomePage()
  }
  @objc func draftAction() {
    let draftVC = FZDraftVC.init()
    navigationController?.pushViewController(draftVC, animated: true)
  }

  @objc func invitactAction() {
    let invitactVC = FZGiftVC.init()
    navigationController?.pushViewController(invitactVC, animated: true)
  }

  @objc func messageAction() {
    let liuyanDetailVC = LiuyanDetailVC()
    navigationController?.pushViewController(liuyanDetailVC, animated: true)
  }

  @objc func customerAction() {

  }

  @objc func settingAction() {
    let setting = SettingVC.init()
    navigationController?.pushViewController(setting, animated: true)
  }

  @objc func cocreationAction() {
    let cocreationVC = FZCocreationVC()
    navigationController?.pushViewController(cocreationVC, animated: true)
  }

  override func viewDidLoad() {
    super.viewDidLoad()
    view.backgroundColor = .fz_HighBlackColor
    navigationController?.isNavigationBarHidden = true
    setupTop()
    checkUnreadCount()
  }

  override func viewWillAppear(_ animated: Bool) {
    super.viewWillAppear(animated)
    updateUserInfo()
    UIApplication.shared.statusBarStyle = .lightContent
    isNeedStatusBarWhite = false
    checkUnreadCount()
  }

  override func viewWillDisappear(_ animated: Bool) {
    super.viewWillDisappear(animated)
  }

}
