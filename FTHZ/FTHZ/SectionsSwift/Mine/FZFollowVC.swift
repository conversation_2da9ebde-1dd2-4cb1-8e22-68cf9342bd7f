import MJRefresh
import SnapKit
import UIKit

class FZFollowVC: FZBaseVC {
  private var pageViewController: UIPageViewController!
  private var viewControllers: [UIViewController] = []
  private var currentIndex = 0
  private var initialPageIndex: Int = 0
  private var edgePanGesture: UIScreenEdgePanGestureRecognizer?
  private var lastDisplayedIndex: Int = 0

  private lazy var customNavBar: UIView = {
    let view = UIView()
    view.backgroundColor = .clear
    return view
  }()

  private lazy var backButton: UIButton = {
    let button = UIButton(type: .custom)
    button.setImage(UIImage(named: "back"), for: .normal)
    button.addTarget(self, action: #selector(backButtonTapped), for: .touchUpInside)
    return button
  }()

  private lazy var myAttentionLabel: UILabel = {
    let label = UILabel()
    label.text = "我关注的"
    label.font = .ft_SourceHanserifSC_Meium_16
    label.textColor = .fz_HighBlackColor
    label.textAlignment = .center
    label.isUserInteractionEnabled = true
    let tap = UITapGestureRecognizer(target: self, action: #selector(titleTapped(_:)))
    label.addGestureRecognizer(tap)
    label.tag = 0
    return label
  }()

  private lazy var attentionMeLabel: UILabel = {
    let label = UILabel()
    label.text = "关注我的"
    label.font = .ft_SourceHanserifSC_Meium_16
    label.textColor = .fz_lightBlackColor
    label.textAlignment = .center
    label.isUserInteractionEnabled = true
    let tap = UITapGestureRecognizer(target: self, action: #selector(titleTapped(_:)))
    label.addGestureRecognizer(tap)
    label.tag = 1
    return label
  }()

  private lazy var myAttentionVC: MyAttentionVC = {
    let vc = MyAttentionVC()
    return vc
  }()

  private lazy var attentionMeVC: AttentionMeVC = {
    let vc = AttentionMeVC()
    return vc
  }()

  override func viewDidLoad() {
    super.viewDidLoad()
    view.backgroundColor = .white
    navigationController?.view.backgroundColor = .white
    setupUI()
    setupPageViewController()
    lastDisplayedIndex = initialPageIndex

    navigationController?.interactivePopGestureRecognizer?.delegate = self
    navigationController?.interactivePopGestureRecognizer?.isEnabled = true
  }

  override func viewDidAppear(_ animated: Bool) {
    super.viewDidAppear(animated)
  }

  override func viewWillAppear(_ animated: Bool) {
    super.viewWillAppear(animated)
    navigationController?.interactivePopGestureRecognizer?.isEnabled = true

    UIView.performWithoutAnimation {
      pageViewController.setViewControllers(
        [viewControllers[lastDisplayedIndex]], direction: .forward, animated: false)
      currentIndex = lastDisplayedIndex
      updateTitleColors()
    }
  }

  private func setupUI() {
    navigationController?.setNavigationBarHidden(true, animated: false)
    view.backgroundColor = .white

    view.addSubview(customNavBar)
    customNavBar.snp.makeConstraints { make in
      make.top.left.right.equalToSuperview()
      make.height.equalTo(44 + UIApplication.shared.statusBarFrame.height)
    }

    customNavBar.addSubview(backButton)
    backButton.snp.makeConstraints { make in
      make.left.equalToSuperview().offset(16)
      make.centerY.equalTo(customNavBar.snp.bottom).offset(-22)
      make.size.equalTo(CGSize(width: 24, height: 24))
    }

    let titleContainer = UIView()
    customNavBar.addSubview(titleContainer)
    titleContainer.snp.makeConstraints { make in
      make.centerX.equalToSuperview()
      make.centerY.equalTo(backButton)
      make.width.equalTo(200)
      make.height.equalTo(44)
    }

    titleContainer.addSubview(myAttentionLabel)
    titleContainer.addSubview(attentionMeLabel)

    myAttentionLabel.snp.makeConstraints { make in
      make.left.top.bottom.equalToSuperview()
      make.width.equalTo(titleContainer.snp.width).multipliedBy(0.5)
    }

    attentionMeLabel.snp.makeConstraints { make in
      make.right.top.bottom.equalToSuperview()
      make.width.equalTo(titleContainer.snp.width).multipliedBy(0.5)
    }
  }

  @objc private func backButtonTapped() {
    NotificationCenter.default.post(name: NSNotification.Name("ReopenSideMenu"), object: nil)
    navigationController?.popViewController(animated: true)
  }

  @objc private func titleTapped(_ gesture: UITapGestureRecognizer) {
    guard let label = gesture.view as? UILabel else { return }
    let index = label.tag
    if index != currentIndex {
      let direction: UIPageViewController.NavigationDirection =
        index > currentIndex ? .forward : .reverse
      pageViewController.setViewControllers(
        [viewControllers[index]], direction: direction, animated: true)
      currentIndex = index
      lastDisplayedIndex = index
      updateTitleColors()
    }
  }

  private func updateTitleColors() {
    myAttentionLabel.textColor = currentIndex == 0 ? .fz_HighBlackColor : .fz_lightBlackColor
    attentionMeLabel.textColor = currentIndex == 1 ? .fz_HighBlackColor : .fz_lightBlackColor

    edgePanGesture?.isEnabled = currentIndex == 0
  }

  private func setupPageViewController() {
    let myAttentionVC = MyAttentionVC()
    let attentionMeVC = AttentionMeVC()

    if let myAttentionScrollView = myAttentionVC.view as? UIScrollView {
      let header = MJChiBaoZiHeader(
        refreshingTarget: myAttentionVC,
        refreshingAction: #selector(MyAttentionVC.refreshData))
      header?.lastUpdatedTimeLabel.isHidden = true
      header?.stateLabel.isHidden = true
      myAttentionScrollView.mj_header = header
    }

    if let attentionMeScrollView = attentionMeVC.view as? UIScrollView {
      let header = MJChiBaoZiHeader(
        refreshingTarget: attentionMeVC,
        refreshingAction: #selector(AttentionMeVC.refreshData))
      header?.lastUpdatedTimeLabel.isHidden = true
      header?.stateLabel.isHidden = true
      attentionMeScrollView.mj_header = header
    }

    viewControllers = [myAttentionVC, attentionMeVC]

    pageViewController = UIPageViewController(
      transitionStyle: .scroll, navigationOrientation: .horizontal)
    pageViewController.delegate = self
    pageViewController.dataSource = self

    addChild(pageViewController)
    view.addSubview(pageViewController.view)
    pageViewController.view.snp.makeConstraints { make in
      make.top.equalTo(customNavBar.snp.bottom)
      make.left.right.bottom.equalToSuperview()
    }
    pageViewController.didMove(toParent: self)
  }

  @objc private func loadInitialData() {
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
      guard let self = self else { return }
      self.pageViewController.setViewControllers(
        [self.viewControllers[self.initialPageIndex]], direction: .forward, animated: false)
      self.updateTitleColors()

      if let scrollView = self.pageViewController.viewControllers?.first?.view as? UIScrollView {
        scrollView.mj_header?.endRefreshing()
      }
    }
  }

  func setInitialPage(index: Int) {
    initialPageIndex = index
    currentIndex = index
    edgePanGesture?.isEnabled = currentIndex == 0
  }

  @objc func refreshData() {
    if currentIndex == 0 {
      myAttentionVC.refreshData()
    } else {
      attentionMeVC.refreshData()
    }
  }
}

extension FZFollowVC: UIPageViewControllerDelegate, UIPageViewControllerDataSource {
  func pageViewController(
    _ pageViewController: UIPageViewController,
    viewControllerBefore viewController: UIViewController
  ) -> UIViewController? {
    guard let index = viewControllers.firstIndex(of: viewController), index > 0 else { return nil }
    return viewControllers[index - 1]
  }

  func pageViewController(
    _ pageViewController: UIPageViewController, viewControllerAfter viewController: UIViewController
  ) -> UIViewController? {
    guard let index = viewControllers.firstIndex(of: viewController),
      index < viewControllers.count - 1
    else { return nil }
    return viewControllers[index + 1]
  }

  func pageViewController(
    _ pageViewController: UIPageViewController,
    willTransitionTo pendingViewControllers: [UIViewController]
  ) {
    navigationController?.interactivePopGestureRecognizer?.isEnabled = false
  }

  func pageViewController(
    _ pageViewController: UIPageViewController,
    didFinishAnimating finished: Bool,
    previousViewControllers: [UIViewController],
    transitionCompleted completed: Bool
  ) {
    if completed,
      let currentVC = pageViewController.viewControllers?.first,
      let index = viewControllers.firstIndex(of: currentVC)
    {
      currentIndex = index
      lastDisplayedIndex = index
      updateTitleColors()
    }

    navigationController?.interactivePopGestureRecognizer?.isEnabled = true
  }
}

extension FZFollowVC: UIGestureRecognizerDelegate {
  func gestureRecognizer(
    _ gestureRecognizer: UIGestureRecognizer,
    shouldRecognizeSimultaneouslyWith otherGestureRecognizer: UIGestureRecognizer
  ) -> Bool {
    if gestureRecognizer == navigationController?.interactivePopGestureRecognizer {
      let isPageViewControllerScrolling =
        pageViewController.isViewLoaded && pageViewController.view.window != nil
        && pageViewController.viewControllers?.count == 1 && currentIndex != 0

      if currentIndex == 0 {
        return false
      }

      return !isPageViewControllerScrolling
    }
    return true
  }

  func gestureRecognizer(
    _ gestureRecognizer: UIGestureRecognizer,
    shouldBeRequiredToFailBy otherGestureRecognizer: UIGestureRecognizer
  ) -> Bool {
    if gestureRecognizer == navigationController?.interactivePopGestureRecognizer
      && currentIndex == 0
    {
      return true
    }
    return false
  }
}
