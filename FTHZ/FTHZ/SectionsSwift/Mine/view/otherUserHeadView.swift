import Foundation

@objc protocol otherUserActionDelegate {
  func messageAction()
  func followAction()
}

class otherUserHeadView: UIView {
  private var unreadBadgeView: UIView?
  @objc public private(set) var avatarBgView: UIView?
  @objc public private(set) var contentView: UIView?
  @objc public private(set) var profileBottomView: UIView?

  @objc var delegate: otherUserActionDelegate?

  @objc var otherUserInfo: OtherUserPersonResult! {
    didSet {
      if otherUserInfo != nil {
        setupUI()
      }
    }
  }

  func setupSpecialUI() {
    let topView = UIView.init()
    topView.backgroundColor = .fz_HighBlackColor
    topView.layer.cornerRadius = kRealWidth(20)
    topView.layer.maskedCorners = [.layerMinXMaxYCorner, .layerMaxXMaxYCorner]
    topView.clipsToBounds = true
    self.addSubview(topView)
    let waveView = WaveView(frame: .zero)
    topView.addSubview(waveView)
    waveView.snp.makeConstraints { make in
      make.edges.equalToSuperview()
    }
    waveView.startWave()
    topView.snp.makeConstraints { (make) in
      make.top.equalToSuperview()
      make.right.left.equalToSuperview()
      if let superview = self.superview {
        make.width.equalTo(superview).priority(.high)
      } else {
        make.width.greaterThanOrEqualTo(1).priority(.high)
      }
    }

    let avatarBgView = UIView.init()
    topView.addSubview(avatarBgView)
    avatarBgView.snp.makeConstraints { (make) in
      make.top.equalToSuperview().offset(kRealWidth(1000))
      make.centerX.equalToSuperview()
      make.size.equalTo(CGSize.init(width: kRealWidth(100), height: kRealWidth(100)))
    }
    let avatarIV = UIImageView.init()
    avatarIV.tio_imageUrl(
      NemoUtil.getUrlWithUserDefartIcon(otherUserInfo.avatar)?.absoluteString ?? "",
      placeHolderImageName: EMPTY_ICON, radius: kRealWidth(50))
    avatarBgView.addSubview(avatarIV)
    avatarIV.applyshadowWithCorner(containerView: avatarBgView, cornerRadious: kRealWidth(50))

    avatarIV.addTapGestureRecognizer {
    }
    avatarIV.snp.makeConstraints { (make) in
      make.edges.equalToSuperview()
    }

    let userName = UILabel.init()
    userName.font = .ft_SourceHanserifSC_Meium_18
    userName.text = otherUserInfo?.nickname
    userName.textColor = .white
    topView.addSubview(userName)
    userName.snp.makeConstraints { (make) in
      make.centerX.equalToSuperview()
      make.top.equalTo(avatarIV.snp_bottom).offset(kRealWidth(19))

    }

    let botName = UILabel.init()
    botName.font = .ft_SourceHanserifSC_Meium_12
    botName.text = "管理员"
    botName.textColor = .fz_greenBlueColor
    topView.addSubview(botName)
    botName.snp.makeConstraints { (make) in
      make.centerX.equalToSuperview()
      make.top.equalTo(userName.snp_bottom).offset(kRealWidth(19))

    }

    let messageIV = UIImageView.init()
    messageIV.image = UIImage.init(named: "聊天互关男")
    topView.addSubview(messageIV)
    messageIV.addTapGestureRecognizer { [weak self] in
      guard let strongSelf = self else { return }
      strongSelf.delegate?.messageAction()
    }

    messageIV.snp.makeConstraints { (make) in
      make.top.equalTo(botName.snp_bottom).offset(kRealWidth(24))
      make.centerX.equalToSuperview()
      make.size.equalTo(CGSize.init(width: kRealWidth(30), height: kRealWidth(16)))
      make.bottom.equalTo(topView).offset(-kRealWidth(24))
    }

    let bottomView = UIView.init()
    bottomView.backgroundColor = .white
    addSubview(bottomView)
    bottomView.snp.makeConstraints { (make) in
      make.left.right.bottom.equalToSuperview()
      make.top.equalTo(topView.snp_bottom)
    }

    let signL = UILabelPadding.init()
    signL.textColor = .white
    signL.layer.cornerRadius = kRealWidth(12)
    signL.layer.masksToBounds = true
    signL.backgroundColor = .fz_HighBlackColor
    signL.font = .ft_SourceHanserifSC_Meium_12
    signL.numberOfLines = 3
    let paragraphStyle = NSMutableParagraphStyle()
    paragraphStyle.lineSpacing = 4
    let attributes: [NSAttributedString.Key: Any] = [
      .paragraphStyle: paragraphStyle,
      .font: UIFont.ft_SourceHanserifSC_Meium_12,
    ]

    if otherUserInfo.signature.count == 0 {
      let text = "Hi, 我是\(otherUserInfo.nickname)。"
      signL.attributedText = NSAttributedString(string: text, attributes: attributes)
    } else {
      signL.attributedText = NSAttributedString(
        string: otherUserInfo.signature, attributes: attributes)
    }
    bottomView.addSubview(signL)
    signL.snp.makeConstraints { (make) in
      make.left.equalToSuperview().offset(kRealWidth(14))
      make.right.equalToSuperview().offset(-kRealWidth(14))
      make.top.equalTo(topView.snp_bottom).offset(kRealWidth(12))
      make.bottom.equalTo(bottomView).offset(-kRealWidth(0))
    }
  }

  @objc func getContentHeight() -> CGFloat {
    self.setNeedsLayout()
    self.layoutIfNeeded()
    return self.systemLayoutSizeFitting(UIView.layoutFittingCompressedSize).height
  }

  func setupUI() {
    if otherUserInfo.special > 0 {
      setupSpecialUI()
      return
    }
    let topView = UIView.init()
    topView.backgroundColor = .fz_HighBlackColor
    topView.layer.cornerRadius = kRealWidth(20)
    topView.layer.maskedCorners = [.layerMinXMaxYCorner, .layerMaxXMaxYCorner]
    topView.clipsToBounds = true
    self.addSubview(topView)
    let waveView = WaveView(frame: .zero)
    topView.addSubview(waveView)
    waveView.snp.makeConstraints { make in
      make.edges.equalToSuperview()
    }
    waveView.startWave()
    topView.snp.makeConstraints { (make) in
      make.top.equalToSuperview()
      make.left.right.equalToSuperview()
      make.width.equalTo(self)
    }

    let avatarBgView = UIView.init()
    topView.addSubview(avatarBgView)
    avatarBgView.snp.makeConstraints { (make) in
      make.top.equalToSuperview().offset(kRealWidth(1000))
      make.centerX.equalToSuperview()
      make.size.equalTo(CGSize(width: kRealWidth(100), height: kRealWidth(100)))
    }
    self.avatarBgView = avatarBgView
    let avatarIV = UIImageView.init()
    avatarIV.tio_imageUrl(
      NemoUtil.getUrlWithUserDefartIcon(otherUserInfo.avatar)?.absoluteString ?? "",
      placeHolderImageName: EMPTY_ICON, radius: kRealWidth(50))
    avatarBgView.addSubview(avatarIV)
    avatarIV.applyshadowWithCorner(containerView: avatarBgView, cornerRadious: kRealWidth(50))

    avatarIV.addTapGestureRecognizer { [weak self] in
      guard let self = self else { return }
      let imageViewer = UIImageView(frame: UIScreen.main.bounds)
      imageViewer.contentMode = .scaleAspectFit
      imageViewer.backgroundColor = .black
      imageViewer.isUserInteractionEnabled = true

      imageViewer.tio_imageUrl(
        NemoUtil.getUrlWithUserDefartIcon(self.otherUserInfo.avatar)?.absoluteString ?? "",
        placeHolderImageName: EMPTY_ICON,
        radius: 0
      )

      imageViewer.addTapGestureRecognizer {
        UIView.animate(
          withDuration: 0.3,
          animations: {
            imageViewer.alpha = 0
          }
        ) { _ in
          imageViewer.removeFromSuperview()
        }
      }

      if let window = UIApplication.shared.windows.first {
        imageViewer.alpha = 0
        window.addSubview(imageViewer)
        UIView.animate(withDuration: 0.3) {
          imageViewer.alpha = 1
        }
      }
    }
    avatarIV.snp.makeConstraints { (make) in
      make.edges.equalToSuperview()
    }

    let nameBar = UIView.init()
    topView.addSubview(nameBar)
    nameBar.snp.makeConstraints { (make) in
      make.centerX.equalToSuperview()
      make.top.equalTo(avatarIV.snp.bottom).offset(kRealWidth(24))
    }

    let userName = UILabel.init()
    userName.font = .ft_SourceHanserifSC_Meium_18
    userName.text = otherUserInfo?.nickname
    userName.textColor = .white
    nameBar.addSubview(userName)
    userName.snp.makeConstraints { (make) in
      make.left.top.bottom.equalToSuperview()
    }

    let sexMark = UIView.init()
    sexMark.backgroundColor = otherUserInfo.gender == "1" ? .fz_skyBlueColor : .fz_femalePinkColor
    sexMark.layer.cornerRadius = kRealWidth(6)
    nameBar.addSubview(sexMark)
    sexMark.snp.makeConstraints { (make) in
      make.left.equalTo(userName.snp_right).offset(kRealWidth(8))
      make.centerY.equalTo(userName)
      make.size.equalTo(CGSize.init(width: kRealWidth(12), height: kRealWidth(12)))
    }

    let hertzL = UILabel.init()
    hertzL.font = .ft_SourceHanserifSC_Meium_14
    hertzL.text = "\(otherUserInfo.hertz)Hz"
    hertzL.textColor = .white
    nameBar.addSubview(hertzL)
    hertzL.snp.makeConstraints { (make) in
      make.left.equalTo(sexMark.snp_right).offset(kRealWidth(8))
      make.right.equalToSuperview()
      make.centerY.equalTo(userName)
    }

    let addressInfo = UILabel.init()
    addressInfo.textColor = .fz_WiteColor_87
    addressInfo.font = .ft_SourceHanserifSC_Normal_12
    addressInfo.numberOfLines = 0
    addressInfo.textAlignment = .center

    let paragraphStyle1 = NSMutableParagraphStyle()
    paragraphStyle1.lineSpacing = 4
    paragraphStyle1.alignment = .center

    let attrs1__ = [
      NSAttributedString.Key.foregroundColor: UIColor.fz_WiteColor_87,
      NSAttributedString.Key.paragraphStyle: paragraphStyle1,
    ]
    let attrs2__ = [
      NSAttributedString.Key.foregroundColor: UIColor.fz_WiteColor_87,
      NSAttributedString.Key.paragraphStyle: paragraphStyle1,
    ]

    let prefixText: String
    if otherUserInfo.userid == USERINFO.userid {
      prefixText = "我是:  "
    } else {
      prefixText = otherUserInfo.gender == "1" ? "他喜欢:  " : "她喜欢:  "
    }
    let attributedString1__ = NSMutableAttributedString(string: prefixText, attributes: attrs1__)
    let attributedString2__ = NSMutableAttributedString(
      string: NemoUtil.getYiDuiPaoXieGang(otherUserInfo.tag, fenType: " • "),
      attributes: attrs2__)
    attributedString1__.append(attributedString2__)
    addressInfo.attributedText = attributedString1__

    topView.addSubview(addressInfo)
    addressInfo.snp.makeConstraints { (make) in
      make.centerX.equalToSuperview()
      make.width.equalTo(topView).offset(-kRealWidth(48))
      make.top.equalTo(nameBar.snp.bottom).offset(kRealWidth(12))
    }

    let tagInfo = UILabel.init()
    tagInfo.textColor = .fz_WiteColor_87
    tagInfo.font = .ft_SourceHanserifSC_Normal_12
    tagInfo.numberOfLines = 0
    tagInfo.textAlignment = .center
    let paragraphStyle2 = NSMutableParagraphStyle()
    paragraphStyle2.lineSpacing = kRealWidth(2)
    paragraphStyle2.alignment = .center

    let attrs1_ = [
      NSAttributedString.Key.foregroundColor: UIColor.fz_WiteColor_87,
      NSAttributedString.Key.paragraphStyle: paragraphStyle2,
    ]
    let attrs2_ = [
      NSAttributedString.Key.foregroundColor: UIColor.fz_WiteColor_87,
      NSAttributedString.Key.paragraphStyle: paragraphStyle2,
    ]

    let prefixText_: String
    if otherUserInfo.userid == USERINFO.userid {
      prefixText_ = "我喜欢:  "
    } else {
      prefixText_ = otherUserInfo.gender == "1" ? "他喜欢:  " : "她喜欢:  "
    }
    let attributedString1_ = NSMutableAttributedString(string: prefixText_, attributes: attrs1_)
    let attributedString2_ = NSMutableAttributedString(
      string: NemoUtil.getYiDuiPaoXieGang(otherUserInfo.paoLikeTag, fenType: " • "),
      attributes: attrs2_)
    attributedString1_.append(attributedString2_)
    tagInfo.attributedText = attributedString1_
    topView.addSubview(tagInfo)
    tagInfo.snp.makeConstraints { (make) in
      make.centerX.equalToSuperview()
      make.width.equalTo(topView).offset(-kRealWidth(48))
      make.top.equalTo(addressInfo.snp.bottom).offset(kRealWidth(12))
      if otherUserInfo.userid == USERINFO.userid {
        make.bottom.equalTo(topView).offset(-kRealWidth(24))
      }
    }

    let bottomView = UIView.init()
    bottomView.backgroundColor = .white
    addSubview(bottomView)
    self.profileBottomView = bottomView

    bottomView.snp.makeConstraints { (make) in
      make.left.right.equalToSuperview()
      make.top.equalTo(topView.snp.bottom)
      make.bottom.equalToSuperview().priority(.high)
    }

    let signL = UILabelPadding.init()
    signL.textColor = .white
    signL.layer.cornerRadius = kRealWidth(12)
    signL.layer.masksToBounds = true
    signL.backgroundColor = .fz_HighBlackColor
    signL.font = .ft_SourceHanserifSC_Meium_12
    signL.numberOfLines = 0
    let paragraphStyle = NSMutableParagraphStyle()
    paragraphStyle.lineSpacing = kRealWidth(4)
    let attributes: [NSAttributedString.Key: Any] = [
      .paragraphStyle: paragraphStyle,
      .font: UIFont.ft_SourceHanserifSC_Meium_12,
    ]

    signL.attributedText = NSAttributedString(
      string: otherUserInfo.signature, attributes: attributes)

    bottomView.addSubview(signL)
    signL.snp.makeConstraints { (make) in
      make.left.equalToSuperview().offset(kRealWidth(24))
      make.right.equalToSuperview().offset(-kRealWidth(24))
      make.top.equalToSuperview().offset(kRealWidth(14))
      make.bottom.equalTo(bottomView).offset(-kRealWidth(0)).priority(.high)
    }

    if otherUserInfo.userid != USERINFO.userid {
      let messageIV = UIImageView.init()
      if otherUserInfo.attentionRs == "2" {
        messageIV.image = UIImage.init(named: otherUserInfo.gender == "1" ? "聊天互关男" : "聊天互关女")
      } else {
        messageIV.image = UIImage.init(named: otherUserInfo.gender == "1" ? "聊天未互关男" : "聊天未互关女")
      }
      topView.addSubview(messageIV)
      messageIV.addTapGestureRecognizer { [weak self] in
        guard let strongSelf = self else { return }
        if strongSelf.otherUserInfo.attentionRs != "2" {
          let dialog = FTHZAlertDialogController.init(title: "提示", message: "需要互相关注才能留言")
          dialog.add(FTHZAlertDialogAction.init(title: "确定", action: nil, style: .default))
          UIViewController.top()?.present(dialog, animated: true) {}
          return
        }
        self?.delegate?.messageAction()
      }

      messageIV.snp.makeConstraints { (make) in
        make.centerX.equalToSuperview()
        make.top.equalTo(tagInfo.snp_bottom).offset(kRealWidth(12))
        make.size.equalTo(CGSize.init(width: kRealWidth(30), height: kRealWidth(16)))
        make.bottom.equalTo(topView).offset(-kRealWidth(24))
      }

      let followBt = UIButton.init()
      followBt.backgroundColor = .white
      followBt.layer.cornerRadius = kRealWidth(10)
      followBt.layer.masksToBounds = true
      followBt.contentEdgeInsets = UIEdgeInsets(
        top: 0, left: kRealWidth(8), bottom: 0, right: kRealWidth(8))

      if otherUserInfo.attentionRs == "0" {
        followBt.setTitle("关注", for: .normal)
      } else if otherUserInfo.attentionRs == "1" {
        followBt.setTitle("已关注 ▼", for: .normal)
      } else {
        followBt.setTitle("\(otherUserInfo.attention_time)天 ▼", for: .normal)
      }
      followBt.setTitleColor(.fz_HighBlackColor, for: .normal)
      followBt.titleLabel?.font = .ft_SourceHanserifSC_Meium_12
      topView.addSubview(followBt)
      followBt.blockAction { [weak self] (bt) in
        self?.delegate?.followAction()
      }

      followBt.snp.makeConstraints { (make) in
        make.right.equalToSuperview().offset(-kRealWidth(24))
        make.centerY.equalTo(messageIV)
        make.height.equalTo(kRealWidth(20))
      }
    }

    if otherUserInfo.badge.count > 0 {
      var lastIV: UIImageView?
      let maxBadges = min(otherUserInfo.badge.count, 5)

      let badgeSize: CGFloat = kRealWidth(22)
      let badgeRadius: CGFloat = kRealWidth(11)

      if otherUserInfo.badge.count > 5 {
        let moreIV = UIImageView.init()
        moreIV.image = UIImage.init(named: "badge_type_more")
        moreIV.contentMode = .scaleAspectFill
        moreIV.layer.cornerRadius = badgeRadius
        moreIV.layer.masksToBounds = true
        moreIV.layer.shadowColor = UIColor.black.cgColor
        moreIV.layer.shadowOffset = CGSize(width: 0, height: 3)
        moreIV.layer.shadowOpacity = 0.3
        moreIV.layer.shadowRadius = 4
        moreIV.layer.borderWidth = 1.5
        moreIV.layer.borderColor = UIColor.white.cgColor
        moreIV.isUserInteractionEnabled = true
        moreIV.addTapGestureRecognizer { [weak self] in
          self?.showBadgeInfo()
        }
        addSubview(moreIV)
        moreIV.snp.makeConstraints { (make) in
          make.right.equalToSuperview().offset(-kRealWidth(24))
          make.top.equalTo(topView.snp_bottom).offset(-badgeRadius)
          make.size.equalTo(CGSize.init(width: badgeSize, height: badgeSize))
        }
        lastIV = moreIV
      }

      for i in 0..<maxBadges {
        let badgeInfo = TagModelResult.mj_object(withKeyValues: otherUserInfo.badge[i])
        let badgeIV = UIImageView.init()
        
        let localImageName = "badge_type" + (badgeInfo?.type ?? "0")
        if let localImage = UIImage(named: localImageName) {
          badgeIV.image = localImage
        } else if let iconUrl = badgeInfo?.icon, !iconUrl.isEmpty {
          badgeIV.tio_imageUrl(
            iconUrl,
            placeHolderImageName: localImageName,
            radius: kRealWidth(15)
          )
        } else {
          badgeIV.image = UIImage(named: localImageName)
        }
        
        badgeIV.contentMode = .scaleAspectFill
        badgeIV.layer.cornerRadius = badgeRadius
        badgeIV.layer.masksToBounds = true
        badgeIV.isUserInteractionEnabled = true
        badgeIV.addTapGestureRecognizer { [weak self] in
          self?.showBadgeInfo()
        }
        addSubview(badgeIV)
        badgeIV.snp.makeConstraints { (make) in
          if let lastIV = lastIV {
            make.right.equalTo(lastIV.snp.left).offset(-kRealWidth(12))
          } else {
            make.right.equalToSuperview().offset(-kRealWidth(24))
          }
          make.top.equalTo(topView.snp_bottom).offset(-badgeRadius)
          make.size.equalTo(CGSize.init(width: badgeSize, height: badgeSize))
        }
        lastIV = badgeIV
      }
    }
  }

  @objc func updateUnreadBadge(_ show: Bool) {
    return
  }

  @objc func showBadgeInfo() {
    let itemSpacing: CGFloat = 15
    
    let dialogWidth = kRealWidth(270)
    
    var totalHeight: CGFloat = 0
    var itemHeights: [CGFloat] = []
    
    for i in 0..<otherUserInfo.badge.count {
      let badgeInfo = TagModelResult.mj_object(withKeyValues: otherUserInfo.badge[i])
      
      let detailText = badgeInfo?.introduction ?? ""
      let detailFont = UIFont.ft_SourceHanserifSC_Normal_14
      let maxWidth = dialogWidth - kRealWidth(65) - kRealWidth(20)
      
      let detailHeight = detailText.boundingRect(
        with: CGSize(width: maxWidth, height: CGFloat.greatestFiniteMagnitude),
        options: [.usesLineFragmentOrigin, .usesFontLeading],
        attributes: [.font: detailFont],
        context: nil
      ).height
      
      let itemHeight = kRealWidth(30) + kRealWidth(20) + detailHeight + kRealWidth(10)
      itemHeights.append(itemHeight)
      totalHeight += itemHeight
      
      if i < otherUserInfo.badge.count - 1 {
        totalHeight += itemSpacing
      }
    }
    
    let listView = UIView.init(
      frame: CGRect.init(x: 0, y: 0, width: dialogWidth, height: kRealWidth(totalHeight))
    )
    
    var currentY: CGFloat = 0
    for i in 0..<otherUserInfo.badge.count {
      let badgeInfo = TagModelResult.mj_object(withKeyValues: otherUserInfo.badge[i])
      
      let badgeIV = UIImageView.init(
        frame: CGRect.init(
          x: kRealWidth(20), 
          y: kRealWidth(currentY), 
          width: kRealWidth(30), 
          height: kRealWidth(30))
      )
      let localImageName = "badge_type" + (badgeInfo?.type ?? "0")
      if let localImage = UIImage(named: localImageName) {
        badgeIV.image = localImage
      } else if let iconUrl = badgeInfo?.icon, !iconUrl.isEmpty {
        badgeIV.tio_imageUrl(
          iconUrl,
          placeHolderImageName: localImageName,
          radius: kRealWidth(15)
        )
      } else {
        badgeIV.image = UIImage(named: localImageName)
      }
      badgeIV.contentMode = .scaleAspectFill
      badgeIV.layer.cornerRadius = kRealWidth(15)
      badgeIV.layer.masksToBounds = true
      listView.addSubview(badgeIV)

      let titleL = UILabel.init(
        frame: CGRect.init(
          x: kRealWidth(65), 
          y: kRealWidth(currentY), 
          width: dialogWidth - kRealWidth(65) - kRealWidth(20),
          height: kRealWidth(20)))
      titleL.font = .ft_SourceHanserifSC_Blod_16
      titleL.text = badgeInfo?.type_name
      titleL.textColor = .white
      listView.addSubview(titleL)

      let detailL = UILabel.init(
        frame: CGRect.init(
          x: kRealWidth(65), 
          y: kRealWidth(currentY + 25), 
          width: dialogWidth - kRealWidth(65) - kRealWidth(20), 
          height: itemHeights[i] - kRealWidth(55)))
      detailL.font = .ft_SourceHanserifSC_Normal_14
      detailL.textColor = .fz_WiteColor_87
      detailL.text = badgeInfo?.introduction
      detailL.numberOfLines = 0 
      listView.addSubview(detailL)

      currentY += itemHeights[i]
      if i < otherUserInfo.badge.count - 1 {
        currentY += itemSpacing
      }
    }
    
    let dialog = FTHZAlertDialogController.init(customView: listView, title: "")
    UIViewController.top()?.present(dialog, animated: true) {}
  }

  override init(frame: CGRect) {
    super.init(frame: frame)
    self.translatesAutoresizingMaskIntoConstraints = false
    let screenWidth = UIScreen.main.bounds.width
    self.frame = CGRect(
      x: frame.origin.x,
      y: frame.origin.y,
      width: max(frame.width, screenWidth),
      height: max(frame.height, 100)
    )
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }
}
