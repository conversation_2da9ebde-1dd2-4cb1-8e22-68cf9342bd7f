import Foundation

class DraftCell: UITableViewCell {

  private let dateL: UILabel
  private let contentL: UILabel
  private let imgTxtL: UILabel
  private let musicTxtL: UILabel

  override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
    dateL = UILabel.init()
    dateL.textColor = .fz_titleBlackColor
    dateL.font = .ft_SourceHanserifSC_Meium_12

    contentL = UILabel.init()
    contentL.numberOfLines = 2
    contentL.textColor = .fz_titleBlackColor
    contentL.font = .ft_SourceHanserifSC_Normal_14

    imgTxtL = UILabel.init()
    imgTxtL.textColor = .fz_titleGreyColor
    imgTxtL.font = .ft_SourceHanserifSC_Normal_14

    musicTxtL = UILabel.init()
    musicTxtL.textColor = .fz_titleGreyColor
    musicTxtL.font = .ft_SourceHanserifSC_Normal_14

    super.init(style: style, reuseIdentifier: reuseIdentifier)

    addSubview(dateL)
    dateL.snp.makeConstraints { (make) in
      make.left.equalToSuperview().offset(kRealWidth(26))
      make.right.equalToSuperview().offset(-kRealWidth(26))
      make.top.equalToSuperview().offset(kRealWidth(20))
    }

    addSubview(contentL)
    contentL.snp.makeConstraints { (make) in
      make.left.equalToSuperview().offset(kRealWidth(27))
      make.right.equalToSuperview().offset(-kRealWidth(26))
      make.top.equalTo(dateL.snp_bottom).offset(kRealWidth(14))
    }

    let editIV = UIImageView.init(
      frame: CGRect.init(x: 0, y: 0, width: kRealWidth(16), height: kRealWidth(16)))
    editIV.image = UIImage.init(named: DRAFT_EDIT_ICON)
    addSubview(editIV)
    editIV.snp.makeConstraints { (make) in
      make.centerY.equalTo(dateL)
      make.right.equalToSuperview().offset(-kRealWidth(26))
    }
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func layoutSubviews() {
    super.layoutSubviews()
  }

  func update(viewData: FZDraftModel) {
    let dateFormatterPrint = DateFormatter()
    dateFormatterPrint.dateFormat = "M月d日"
    dateL.text = dateFormatterPrint.string(from: viewData.time)
    contentL.text = viewData.articleContent
    var images: String = ""
    viewData.pictureArrays.forEach({ (m) in
      images += "[图片]"
    })
    if images.count > 0 {
      imgTxtL.text = images
      addSubview(imgTxtL)
      imgTxtL.snp.makeConstraints { (make) in
        make.left.right.equalTo(dateL)
        make.right.equalToSuperview().offset(-kRealWidth(26))
        make.top.equalTo(contentL.snp_bottom)
      }
    } else {
      imgTxtL.removeFromSuperview()
    }
    if let url = viewData.musicInfo.imgUrl {
      musicTxtL.text = url
      addSubview(musicTxtL)
      musicTxtL.snp.makeConstraints { (make) in
        make.left.right.equalTo(dateL)
        make.right.equalToSuperview().offset(-kRealWidth(26))
        make.top.equalTo(contentL.snp_bottom).offset(kRealWidth(20))
      }
    } else {
      musicTxtL.removeFromSuperview()
    }
  }
}
