import Foundation

class homepageHeadView: UIView {

  func setupUI() {
    let topView = UIView.init()
    topView.backgroundColor = .fz_HighBlackColor
    self.addSubview(topView)
    topView.snp.makeConstraints { (make) in
      make.top.equalToSuperview()
      make.right.left.equalToSuperview()
      make.height.equalTo(kRealWidth(368))
    }
    let avatarBgView = UIView.init()
    avatarBgView.backgroundColor = .red
    topView.addSubview(avatarBgView)
    avatarBgView.snp.makeConstraints { (make) in
      make.top.equalToSuperview().offset(kRealWidth(32))
      make.centerX.equalToSuperview()
      make.size.equalTo(CGSize.init(width: kRealWidth(80), height: kRealWidth(80)))
    }
    let avatarIV = UIImageView.init()
    avatarIV.netImgByUrl(NemoUtil.getUrlWithUserDefartIcon(USERINFO?.avatar), "empty")
    avatarBgView.addSubview(avatarIV)
    avatarIV.applyshadowWithCorner(containerView: avatarBgView, cornerRadious: kRealWidth(40))

    avatarIV.addTapGestureRecognizer {
    }
    avatarIV.snp.makeConstraints { (make) in
      make.edges.equalToSuperview()
    }

    let nameBar = UIView.init()
    topView.addSubview(nameBar)
    nameBar.snp.makeConstraints { (make) in
      make.centerX.equalToSuperview()
      make.top.equalTo(avatarIV.snp_bottom).offset(kRealWidth(16))
    }

    let userName = UILabel.init()
    userName.font = .ft_SourceHanserifSC_Meium_18
    userName.text = USERINFO?.nickname
    userName.textColor = .white
    nameBar.addSubview(userName)
    userName.snp.makeConstraints { (make) in
      make.left.top.bottom.equalToSuperview()
    }

    let sexMark = UIView.init()
    sexMark.backgroundColor = USERINFO.gender == "1" ? .fz_skyBlueColor : .fz_femalePinkColor
    sexMark.layer.cornerRadius = kRealWidth(6)
    nameBar.addSubview(sexMark)
    sexMark.snp.makeConstraints { (make) in
      make.left.equalTo(userName.snp_right).offset(kRealWidth(8))
      make.right.equalToSuperview()
      make.centerY.equalTo(userName)
      make.size.equalTo(CGSize.init(width: kRealWidth(12), height: kRealWidth(12)))
    }

    let addressInfo = UILabel.init()
    addressInfo.textColor = .fz_WiteColor_87
    addressInfo.font = .ft_SourceHanserifSC_Normal_12
    addressInfo.text = String(
      "\(USERINFO.city)的\(String(NemoUtil.getAgeOfData(USERINFO.birth)))岁\(USERINFO.whale)，\(USERINFO.hertz)Hz。"
    )
    topView.addSubview(addressInfo)
    addressInfo.snp.makeConstraints { (make) in
      make.centerX.equalToSuperview()
      make.top.equalTo(nameBar.snp_bottom).offset(kRealWidth(24))
    }

    let tagInfo = UILabel.init()
    tagInfo.textColor = .fz_WiteColor_87
    tagInfo.font = .ft_SourceHanserifSC_Normal_12
    tagInfo.numberOfLines = 0
    var strOfTags = ""
    for i in 0...USERINFO.tag.count - 1 {
      let tag: TagModelResult = TagModelResult.mj_object(withKeyValues: USERINFO.tag[i])
      if i % 2 > 0 {
        strOfTags += tag.name
        strOfTags += "\n"
      } else {
        strOfTags += tag.name
        strOfTags += "  /  "
      }
    }
    tagInfo.text = strOfTags
    topView.addSubview(tagInfo)
    tagInfo.snp.makeConstraints { (make) in
      make.centerX.equalToSuperview()
      make.top.equalTo(addressInfo.snp_bottom).offset(kRealWidth(60))
    }

    let bottomView = UIView.init()
    bottomView.backgroundColor = .white
    addSubview(bottomView)
    bottomView.snp.makeConstraints { (make) in
      make.left.right.bottom.equalToSuperview()
      make.top.equalTo(topView.snp_bottom)
    }

    let likeTags = UILabel.init()
    likeTags.numberOfLines = 0
    likeTags.font = .ft_SourceHanserifSC_Normal_12
    bottomView.addSubview(likeTags)
    likeTags.snp.makeConstraints { (make) in
      make.left.equalToSuperview().offset(kRealWidth(24))
      make.right.equalToSuperview().offset(-kRealWidth(24))
      make.top.equalToSuperview().offset(kRealWidth(34))
    }

    let attrs1 = [NSAttributedString.Key.foregroundColor: UIColor.fz_titleBlackColor]
    let attrs2 = [NSAttributedString.Key.foregroundColor: UIColor.fz_titleGreyColor]
    let attributedString1 = NSMutableAttributedString(string: "喜欢:  ", attributes: attrs1)
    let attributedString2 = NSMutableAttributedString(
      string: NemoUtil.getYiDuiPaoXieGang(USERINFO.paoLikeTag, fenType: " / "), attributes: attrs2)
    attributedString1.append(attributedString2)
    likeTags.attributedText = attributedString1

    let signL = UILabelPadding.init()
    signL.textColor = .white
    signL.layer.cornerRadius = kRealWidth(2)
    signL.layer.masksToBounds = true
    signL.backgroundColor = .fz_HighBlackColor
    signL.font = .ft_SourceHanserifSC_Meium_12
    signL.numberOfLines = 0
    bottomView.addSubview(signL)
    signL.snp.makeConstraints { (make) in
      make.left.right.equalTo(likeTags)
      make.top.equalTo(likeTags.snp_bottom).offset(kRealWidth(16))
    }

    signL.text = USERINFO.signature

    if USERINFO.badge.count > 0 {
      var lastIV: UIImageView?
      for i in 0...USERINFO.badge.count - 1 {

        let badgeMarker = UIView.init()
        badgeMarker.backgroundColor = .fz_skyBlueColor
        addSubview(badgeMarker)
        badgeMarker.snp.makeConstraints { (make) in
          make.right.equalToSuperview().offset(-kRealWidth(CGFloat(29 + i * 24)))
          make.top.equalTo(topView.snp_bottom).offset(-kRealWidth(2))
          make.size.equalTo(CGSize.init(width: kRealWidth(6), height: kRealWidth(8)))
        }

        let badgeInfo = TagModelResult.mj_object(withKeyValues: USERINFO.badge[i])
        let badgeIV = UIImageView.init()
        badgeIV.image = UIImage.init(named: "badge_type" + (badgeInfo?.type ?? "0"))
        addSubview(badgeIV)
        badgeIV.snp.makeConstraints { (make) in
          make.top.equalTo(badgeMarker.snp_bottom).offset(-kRealWidth(3))
          make.centerX.equalTo(badgeMarker)
          make.size.equalTo(CGSize.init(width: kRealWidth(16), height: kRealWidth(16)))
        }
        if i == USERINFO.badge.count - 1 {
          lastIV = badgeIV
        }
      }

      if lastIV != nil {
        let dummybt = UIButton.init()
        dummybt.addTarget(self, action: #selector(showBadgeInfo), for: .touchUpInside)
        addSubview(dummybt)
        dummybt.snp.makeConstraints { (make) in
          make.right.equalToSuperview().offset(-kRealWidth(29))
          make.top.left.bottom.equalTo(lastIV!)
        }
      }
    }

  }

  @objc func showBadgeInfo() {
    let perHiehgt = 10 + 50 * USERINFO.badge.count
    let listView = UIView.init(
      frame: CGRect.init(x: 0, y: 0, width: kRealWidth(240), height: kRealWidth(CGFloat(perHiehgt)))
    )
    var top: CGFloat = 10
    for i in 0...USERINFO.badge.count - 1 {
      let badgeInfo = TagModelResult.mj_object(withKeyValues: USERINFO.badge[i])
      let badgeIV = UIImageView.init(
        frame: CGRect.init(
          x: kRealWidth(28), y: kRealWidth(top + 10), width: kRealWidth(20), height: kRealWidth(20))
      )
      badgeIV.image = UIImage.init(named: "badge_type" + (badgeInfo?.type ?? "0"))
      listView.addSubview(badgeIV)
      let titleL = UILabel.init(
        frame: CGRect.init(
          x: kRealWidth(72), y: kRealWidth(top), width: kRealWidth(270 - 72 - 15),
          height: kRealWidth(20)))
      titleL.font = .ft_SourceHanserifSC_Blod_16
      titleL.text = badgeInfo?.type_name
      titleL.textColor = .fz_HighBlackColor
      listView.addSubview(titleL)

      let detailL = UILabel.init(
        frame: CGRect.init(
          x: kRealWidth(72), y: kRealWidth(CGFloat(top + 23)), width: kRealWidth(270 - 73 - 5),
          height: kRealWidth(20)))
      detailL.font = .ft_SourceHanserifSC_Normal_14
      detailL.textColor = .fz_tipTxtColor
      detailL.text = badgeInfo?.introduction
      listView.addSubview(detailL)

      top = kRealWidth(CGFloat(i * 63 + 70))
    }
    let dialog = FTHZAlertDialogController.init(customView: listView, title: "")
    dialog.add(FTHZAlertDialogAction.init(title: "确定", action: nil, style: .default))
    UIViewController.top()?.present(dialog, animated: true) {}
  }

  override init(frame: CGRect) {
    super.init(frame: frame)
    setupUI()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }
}
