import Foundation

class MyLikedCell: UITableViewCell {
  private let nameL: UILabel
  private let avatarIV: UIImageView
  private let contentL: UILabel
  private let thumb: UIImageView
  private let timeL: UILabel

  override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
    avatarIV = UIImageView.init(
      frame: CGRect.init(x: 0, y: 0, width: kRealWidth(48), height: kRealWidth(48)))

    nameL = UILabel.init()
    nameL.textColor = .fz_grayBlueColor
    nameL.font = .ft_SourceHanSerif_Blod_14

    contentL = UILabel.init()
    contentL.textColor = .fz_titleGreyColor
    contentL.font = .ft_SourceHanserifSC_Normal_13

    timeL = UILabel.init()
    timeL.textColor = .fz_lightBlackColor
    timeL.font = .systemFont(ofSize: kRealWidth(12))
    timeL.textAlignment = .left

    thumb = UIImageView.init(
      frame: CGRect.init(x: 0, y: 0, width: kRealWidth(24), height: kRealWidth(24)))

    super.init(style: style, reuseIdentifier: reuseIdentifier)

    addSubview(avatarIV)
    avatarIV.snp.makeConstraints { (make) in
      make.left.equalToSuperview().offset(kRealWidth(24))
      make.top.equalToSuperview().offset(kRealWidth(8))
      make.size.equalTo(CGSize.init(width: kRealWidth(48), height: kRealWidth(48)))
    }

    addSubview(nameL)
    nameL.snp.makeConstraints { (make) in
      make.left.equalTo(avatarIV.snp_right).offset(kRealWidth(16))
      make.top.equalTo(avatarIV.snp_top).offset(kRealWidth(4))
    }

    addSubview(timeL)
    timeL.snp.makeConstraints { (make) in
      make.left.equalTo(nameL.snp_right).offset(kRealWidth(12))
      make.centerY.equalTo(nameL)
      make.width.equalTo(kRealWidth(100))
      make.height.equalTo(nameL)
    }

    addSubview(contentL)
    contentL.snp.makeConstraints { (make) in
      make.left.equalTo(nameL)
      make.bottom.equalTo(avatarIV.snp_bottom).offset(-kRealWidth(4))
      make.right.equalToSuperview().offset(-kRealWidth(110))
    }
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  func update(viewData: MyLikedModel) {
    thumb.removeFromSuperview()

    nameL.text = viewData.nickname
    if let colorM = viewData.colorFont {
      nameL.applyGradient(type: colorM.type!, expireTime: colorM.expire_time!)
    }
    avatarIV.tio_imageUrl(
      viewData.avatar ?? "", placeHolderImageName: EMPTY_ICON, radius: kRealWidth(24))
    contentL.text = viewData.content

    if let timeStamp = viewData.create_time, let timeDouble = Double(timeStamp) {
      timeL.text = NemoUtil.distanceTimeWith(beforeTime: timeDouble)
    } else {
      timeL.text = ""
    }

    if let imgs = viewData.image?.split(separator: ","),
      imgs.count > 0
    {
      addSubview(thumb)
      thumb.layer.cornerRadius = kRealWidth(8)
      thumb.layer.masksToBounds = true
      thumb.contentMode = .scaleAspectFill
      thumb.snp.makeConstraints { (make) in
        make.right.equalToSuperview().offset(-kRealWidth(24))
        make.top.equalTo(avatarIV.snp_top)
        make.bottom.equalTo(contentL.snp_bottom)
        make.width.equalTo(thumb.snp_height)
      }
      thumb.netImg(String(imgs[0]), "empty")
    }
  }
}

extension MyLikedCell: Updatable {
  typealias ViewData = MyLikedModel
}
