import Foundation

class FZMyHomePageVC: FZBaseVC {

  private lazy var myMoment: MyDynamicVC = MyDynamicVC.init()

  func setupUI() {
    view.backgroundColor = .fz_HighBlackColor
    let backBt = UIButton.init()
    backBt.setImage(UIImage.init(named: "back_white"), for: .normal)
    backBt.addTarget(self, action: #selector(backAction), for: .touchUpInside)
    contentView.addSubview(backBt)
    backBt.snp.makeConstraints { (make) in
      make.left.equalToSuperview().offset(kRealWidth(24))
      make.top.equalToSuperview().offset(kRealWidth(22))
      make.size.equalTo(CGSize.init(width: kRealWidth(10), height: kRealWidth(15)))
    }

    addChild(myMoment)
    contentView.backgroundColor = .fz_HighBlackColor
    contentView.addSubview(myMoment.view)
    myMoment.view.snp.makeConstraints { (make) in
      make.left.right.equalToSuperview()
      make.top.equalTo(backBt.snp_bottom).offset(kRealWidth(25))
      make.bottom.equalTo(view)
    }

  }

  @objc func backAction() {
    navigationController?.popViewController(animated: true)
    NotificationCenter.default.post(
      name: NSNotification.Name(rawValue: "NotificationForOpenLeftMenu"), object: nil)
  }

  override func viewDidLoad() {
    super.viewDidLoad()
    setupUI()
  }

  override func viewWillAppear(_ animated: Bool) {
    super.viewWillAppear(animated)
  }

}
