import Foundation
import UIKit

struct CocreationResponse: Codable {
  let code: Int
  let msg: String
  let data: [CocreationGroup]
}

@objcMembers class CocreationUser: NSObject, Codable {
  var userid: Int = 0
  var nickname: String?
  var avatar: String = ""
  var desc: String = ""
}

@objcMembers class CocreationGroup: NSObject, Codable {
  var type: Int = 0
  var desc: String = ""
  var users: [CocreationUser] = []
}

class UserCell: UICollectionViewCell {
  private let avatarImageView = UIImageView()
  private let nicknameLabel = UILabel()
  var tapAction: (() -> Void)?
  var longPressAction: ((CocreationUser) -> Void)?
  private var user: CocreationUser?
  override init(frame: CGRect) {
    super.init(frame: frame)
    setupUI()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  private func setupUI() {
    let avatarBgView = UIView()
    contentView.addSubview(avatarBgView)
    avatarBgView.snp.makeConstraints { make in
      make.top.equalToSuperview().offset(kRealWidth(12))
      make.centerX.equalToSuperview()
      make.size.equalTo(CGSize(width: kRealWidth(60), height: kRealWidth(60)))
    }

    avatarImageView.contentMode = .scaleAspectFill
    avatarImageView.layer.cornerRadius = kRealWidth(30)
    avatarImageView.layer.masksToBounds = true
    avatarBgView.addSubview(avatarImageView)
    avatarImageView.snp.makeConstraints { make in
      make.edges.equalToSuperview()
    }

    avatarImageView.applyshadowWithCorner(
      containerView: avatarBgView, cornerRadious: kRealWidth(30))

    nicknameLabel.font = .ft_SourceHanSerif_Blod_12
    nicknameLabel.textColor = .white
    nicknameLabel.textAlignment = .center
    contentView.addSubview(nicknameLabel)
    nicknameLabel.snp.makeConstraints { make in
      make.top.equalTo(avatarImageView.snp.bottom).offset(kRealWidth(8))
      make.left.right.equalToSuperview()
      make.bottom.equalToSuperview().offset(-kRealWidth(12))
    }

    avatarImageView.isUserInteractionEnabled = true
    let tapGesture = UITapGestureRecognizer(target: self, action: #selector(avatarTapped))
    avatarImageView.addGestureRecognizer(tapGesture)
    let longPressGesture = UILongPressGestureRecognizer(target: self, action: #selector(avatarLongPressed(_:)))
    avatarImageView.addGestureRecognizer(longPressGesture)
  }

  @objc private func avatarTapped() {
    tapAction?()
  }

  @objc private func avatarLongPressed(_ gesture: UILongPressGestureRecognizer) {
    if gesture.state == .began, let user = self.user {
      longPressAction?(user)
    }
  }

  func configure(with user: CocreationUser) {
    self.user = user
    avatarImageView.netImg(user.avatar, "52hz_avatar_new")
    if let nickname = user.nickname, !nickname.isEmpty {
      nicknameLabel.text = nickname
    } else {
      nicknameLabel.text = "whale"
    }
    nicknameLabel.setNeedsLayout()
    nicknameLabel.layoutIfNeeded()
  }
}

class CocreationModel: NSObject {
  class func getCoCreation(
    _ success: @escaping (_ resultObject: [CocreationGroup]) -> Void,
    failure: @escaping (_ error: Error) -> Void
  ) {
    NetworkManager<CocreationGroup>().requestListModel(
      FindAPI.cocreation,
      completion: { (response) in
        if let groups = response?.dataList {
          DispatchQueue.main.async {
            success(groups)
          }
        }
      }
    ) { (error) in
      DispatchQueue.main.async {
        failure(error)
      }
    }
  }
}

class FZCocreationVC: FZBaseVC {
  private var groups: [CocreationGroup] = []
  private lazy var stackView: UIStackView = {
    let stack = UIStackView()
    stack.axis = .vertical
    stack.spacing = kRealWidth(40)
    stack.alignment = .fill
    return stack
  }()

  private lazy var scrollView: UIScrollView = {
    let scroll = UIScrollView()
    scroll.backgroundColor = .white
    scroll.showsVerticalScrollIndicator = false
    return scroll
  }()

  private var isNotchScreen: Bool {
    if #available(iOS 11.0, *) {
      let window = UIApplication.shared.windows.first
      return window?.safeAreaInsets.top ?? 0 > 20
    }
    return false
  }

  override func viewDidLoad() {
    super.viewDidLoad()
    setupUI()
    loadData()
  }

  private func setupUI() {
    view.backgroundColor = .white

    let backBt = UIButton()
    backBt.setImage(UIImage(named: "back"), for: .normal)
    backBt.addTarget(self, action: #selector(backAction), for: .touchUpInside)
    contentView.addSubview(backBt)
    backBt.snp.makeConstraints { make in
      make.left.equalToSuperview().offset(kRealWidth(24))
      make.top.equalToSuperview().offset(self.isNotchScreen ? kRealWidth(58) : kRealWidth(34))
      make.size.equalTo(CGSize(width: kRealWidth(10), height: kRealWidth(15)))
    }

    let titleLabel = UILabel()
    titleLabel.text = "共创成员"
    titleLabel.textColor = .fz_HighBlackColor
    titleLabel.font = .ft_SourceHanserifSC_Blod_16
    contentView.addSubview(titleLabel)
    titleLabel.snp.makeConstraints { make in
      make.centerY.equalTo(backBt)
      make.centerX.equalToSuperview()
    }

    let descLabel = UILabel()
    descLabel.text = "-长按头像查看详情-"
    descLabel.textColor = .fz_titleGreyColor
    descLabel.font = .ft_SourceHanserifSC_Meium_12
    descLabel.textAlignment = .center
    contentView.addSubview(descLabel)
    descLabel.snp.makeConstraints { make in
        make.top.equalTo(titleLabel.snp.bottom).offset(kRealWidth(8))
        make.centerX.equalToSuperview()
    }

    contentView.addSubview(scrollView)
    scrollView.snp.makeConstraints { make in
      make.left.right.bottom.equalToSuperview()
      make.top.equalTo(descLabel.snp.bottom).offset(kRealWidth(12))
    }

    scrollView.addSubview(stackView)
    stackView.snp.makeConstraints { make in
      make.edges.equalToSuperview().inset(
        UIEdgeInsets(top: 0, left: kRealWidth(24), bottom: kRealWidth(24), right: kRealWidth(24)))
      make.width.equalTo(scrollView).offset(-kRealWidth(48))
    }
  }

  override func viewWillAppear(_ animated: Bool) {
    super.viewWillAppear(animated)
    AppConfig.statusbarStyle(true)
  }

  @objc private func backAction() {
    navigationController?.popViewController(animated: true)
  }

  private func loadData() {
    CocreationModel.getCoCreation({ [weak self] groups in
      self?.groups = groups
      self?.setupGroups()
    }) { [weak self] error in
      self?.showMsgFast("加载失败，请稍后重试")
    }
  }

  private func setupGroups() {
    stackView.arrangedSubviews.forEach { $0.removeFromSuperview() }

    for group in groups {
      let groupView = createGroupView(group)
      stackView.addArrangedSubview(groupView)
    }
  }

  private func createGroupView(_ group: CocreationGroup) -> UIView {
    let containerView = UIView()

    let titleLabel = UILabel()
    titleLabel.text = group.desc
    titleLabel.font = .ft_SourceHanserifSC_Meium_20
    titleLabel.textColor = .fz_HighBlackColor
    containerView.addSubview(titleLabel)
    titleLabel.snp.makeConstraints { make in
      make.top.left.equalToSuperview()
    }

    let backgroundView = UIView()
    backgroundView.backgroundColor = .fz_HighBlackColor
    backgroundView.layer.cornerRadius = kRealWidth(16)
    backgroundView.layer.masksToBounds = true
    containerView.addSubview(backgroundView)
    backgroundView.snp.makeConstraints { make in
      make.top.equalTo(titleLabel.snp.bottom).offset(kRealWidth(16))
      make.left.right.bottom.equalToSuperview()
    }

    let gridView = createUserGridView(group.users)
    backgroundView.addSubview(gridView)
    gridView.snp.makeConstraints { make in
      make.edges.equalToSuperview().inset(
        UIEdgeInsets(
          top: kRealWidth(12),
          left: kRealWidth(12),
          bottom: kRealWidth(12),
          right: kRealWidth(12)
        ))
    }

    return containerView
  }

  private func createUserGridView(_ users: [CocreationUser]) -> UIView {
    let containerView = UIView()
    let itemsPerRow = 4
    let spacing = kRealWidth(12)
    let itemWidth = (UIScreen.main.bounds.width - kRealWidth(72) - spacing * 3) / 4

    for (index, user) in users.enumerated() {
      let row = index / itemsPerRow
      let col = index % itemsPerRow

      let userView = UserCell(frame: .zero)
      userView.configure(with: user)
      userView.tapAction = { [weak self] in
        self?.navigateToUserProfile(userId: user.userid)
      }
      userView.longPressAction = { [weak self] user in
        self?.showDescPopup(for: user)
      }
      containerView.addSubview(userView)

      userView.snp.makeConstraints { make in
        make.width.equalTo(itemWidth)
        make.height.equalTo(itemWidth + kRealWidth(32))
        make.left.equalToSuperview().offset(CGFloat(col) * (itemWidth + spacing))
        make.top.equalToSuperview().offset(CGFloat(row) * (itemWidth + kRealWidth(32) + spacing))
      }
    }

    let rows = ceil(Double(users.count) / Double(itemsPerRow))
    containerView.snp.makeConstraints { make in
      make.height.equalTo(rows * (itemWidth + kRealWidth(32) + spacing) - spacing)
    }

    return containerView
  }

  private func navigateToUserProfile(userId: Int) {
    let infoVC = WhaleDetailVC()
    infoVC.uid = String(userId)
    navigationController?.pushViewController(infoVC, animated: true)
  }

  private var descPopup: DescPopupView?

  private func showDescPopup(for user: CocreationUser) {
    if descPopup != nil { descPopup?.removeFromSuperview() }
    let popup = DescPopupView(desc: user.desc)
    popup.dismissHandler = { [weak self] in
      self?.descPopup?.removeFromSuperview()
      self?.descPopup = nil
    }
    view.addSubview(popup)
    popup.snp.makeConstraints { make in
      make.edges.equalToSuperview()
    }
    descPopup = popup
  }
}

extension FZCocreationVC: UITableViewDelegate, UITableViewDataSource {
  func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
    return groups.count
  }

  func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
    let cell =
      tableView.dequeueReusableCell(withIdentifier: "CocreationGroupCell", for: indexPath)
      as! CocreationGroupCell
    cell.configure(with: groups[indexPath.row])
    return cell
  }

  func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
    let group = groups[indexPath.row]
    let itemsPerRow = 4
    let rows = ceil(Double(group.users.count) / Double(itemsPerRow))
    return kRealWidth(50 + rows * 100)
  }
}

class CocreationGroupCell: UITableViewCell {
  private let titleLabel = UILabel()
  private var group: CocreationGroup?
  private lazy var collectionView: UICollectionView = {
    let layout = UICollectionViewFlowLayout()
    layout.minimumInteritemSpacing = kRealWidth(12)
    layout.minimumLineSpacing = kRealWidth(16)
    let itemWidth = (UIScreen.main.bounds.width - kRealWidth(24 * 2 + 12 * 3)) / 4
    layout.itemSize = CGSize(width: itemWidth, height: itemWidth + kRealWidth(20))

    let cv = UICollectionView(frame: .zero, collectionViewLayout: layout)
    cv.backgroundColor = .white
    cv.delegate = self
    cv.dataSource = self
    cv.register(UserCell.self, forCellWithReuseIdentifier: "UserCell")
    return cv
  }()

  override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
    super.init(style: style, reuseIdentifier: reuseIdentifier)
    setupUI()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  private func setupUI() {
    selectionStyle = .none
    backgroundColor = .white

    titleLabel.font = .ft_SourceHanserifSC_Meium_16
    titleLabel.textColor = .fz_HighBlackColor
    contentView.addSubview(titleLabel)
    titleLabel.snp.makeConstraints { make in
      make.top.equalToSuperview().offset(kRealWidth(24))
      make.left.equalToSuperview().offset(kRealWidth(24))
    }

    contentView.addSubview(collectionView)
    collectionView.snp.makeConstraints { make in
      make.top.equalTo(titleLabel.snp.bottom).offset(kRealWidth(16))
      make.left.equalToSuperview().offset(kRealWidth(24))
      make.right.equalToSuperview().offset(-kRealWidth(24))
      make.bottom.equalToSuperview()
    }
  }

  func configure(with group: CocreationGroup) {
    self.group = group
    titleLabel.text = group.desc
    collectionView.reloadData()
    print("Configuring group cell with \(group.users.count) users")
  }
}

extension CocreationGroupCell: UICollectionViewDelegate, UICollectionViewDataSource {
  func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int)
    -> Int
  {
    return group?.users.count ?? 0
  }

  func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath)
    -> UICollectionViewCell
  {
    let cell =
      collectionView.dequeueReusableCell(withReuseIdentifier: "UserCell", for: indexPath)
      as! UserCell
    if let user = group?.users[indexPath.item] {
      cell.configure(with: user)
    }
    return cell
  }
}
