import UIKit

class DescPopupView: UIView {
  var dismissHandler: (() -> Void)?

  init(desc: String) {
    super.init(frame: .zero)
    backgroundColor = UIColor.black.withAlphaComponent(0.3)
    let tap = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped(_:)))
    addGestureRecognizer(tap)

    let contentView = UIView()
    contentView.backgroundColor = .white
    contentView.layer.cornerRadius = kRealWidth(20)
    contentView.layer.masksToBounds = true
    addSubview(contentView)
    contentView.snp.makeConstraints { make in
      make.center.equalToSuperview()
      make.left.greaterThanOrEqualToSuperview().offset(kRealWidth(40))
      make.right.lessThanOrEqualToSuperview().offset(kRealWidth(-40))
    }

    let label = UILabel()
    label.text = desc
    label.textColor = .fz_HighBlackColor
    label.numberOfLines = 0
    label.textAlignment = .center
    label.font = .ft_SourceHanserifSC_Meium_16
    contentView.addSubview(label)
    label.snp.makeConstraints { make in
      make.edges.equalToSuperview().inset(kRealWidth(24))
    }
  }

  @objc private func backgroundTapped(_ gesture: UITapGestureRecognizer) {
    let location = gesture.location(in: self)
    if let contentView = subviews.first, !contentView.frame.contains(location) {
      dismissHandler?()
    }
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }
} 