import Foundation
import Photos
import TZImagePickerController

class FZDraftVC: FZBaseVC {

  var items = [FZDraftModel]()
  private lazy var listTable = UITableView()
  private var isNotchScreen: Bool {
    if #available(iOS 11.0, *) {
      let window = UIApplication.shared.windows.first
      return window?.safeAreaInsets.top ?? 0 > 20
    }
    return false
  }
  lazy var emptyTipL = UILabel.init()

  func setupUI() {
    view.backgroundColor = .white
    let backBt = UIButton.init()
    backBt.setImage(UIImage.init(named: "back"), for: .normal)
    backBt.addTarget(self, action: #selector(backAction), for: .touchUpInside)
    contentView.addSubview(backBt)
    backBt.snp.makeConstraints { (make) in
      make.left.equalToSuperview().offset(kRealWidth(24))
      make.top.equalToSuperview().offset(isNotchScreen ? kRealWidth(58) : kRealWidth(34))
      make.size.equalTo(CGSize.init(width: kRealWidth(10), height: kRealWidth(15)))
    }

    let title = UILabel.init()
    title.text = "草稿箱"
    title.textColor = .fz_HighBlackColor
    title.font = .ft_SourceHanserifSC_Blod_16
    contentView.addSubview(title)
    title.snp.makeConstraints { (make) in
      make.centerY.equalTo(backBt)
      make.centerX.equalToSuperview()
    }

    let clearBtn = UIButton(type: .custom)
    let clearImage = UIImage(named: "clear")?.withRenderingMode(.alwaysOriginal)
    clearBtn.setImage(clearImage, for: .normal)
    clearBtn.imageView?.contentMode = .scaleAspectFit
    clearBtn.addTarget(self, action: #selector(clearAllDrafts), for: .touchUpInside)
    contentView.addSubview(clearBtn)
    clearBtn.snp.makeConstraints { make in
      make.centerY.equalTo(backBt)
      make.right.equalToSuperview().offset(-kRealWidth(24))
      make.width.height.equalTo(kRealWidth(20))
    }

    listTable.separatorStyle = .none

    listTable.separatorStyle = .none
    listTable.delegate = self
    listTable.dataSource = self
    listTable.register(DraftCell.self, forCellReuseIdentifier: NSStringFromClass(DraftCell.self))
    contentView.addSubview(listTable)
    listTable.snp.makeConstraints { (make) in
      make.left.right.bottom.equalToSuperview()
      make.top.equalTo(backBt.snp_bottom).offset(kRealWidth(20))
    }

  }

  func loadListData() {
    if let arr = FZDraftModel.getUnArchiveData() as? [FZDraftModel] {
      items = arr
      items.sort { (m1, m2) -> Bool in
        return m1.time.compare(m2.time) == .orderedDescending
      }
      listTable.reloadData()
    }

  }

  @objc func clearAllDrafts() {
    let alert = UIAlertController(title: "提示", message: "确定要清除所有草稿吗？", preferredStyle: .alert)
    alert.addAction(UIAlertAction(title: "取消", style: .cancel, handler: nil))
    alert.addAction(
      UIAlertAction(
        title: "确定", style: .destructive,
        handler: { [weak self] (_) in
          guard let self = self else { return }
          self.items.removeAll()
          FZKeyedArchiver.sharedClient().setArchiverDataWithData(NSMutableArray())
          self.listTable.reloadData()
        }))
    present(alert, animated: true, completion: nil)
  }

  @objc func backAction() {
    navigationController?.popViewController(animated: true)
    NotificationCenter.default.post(
      name: NSNotification.Name(rawValue: "NotificationForOpenLeftMenu"), object: nil)
  }

  override func viewDidLoad() {
    super.viewDidLoad()
    navigationController?.navigationBar.isHidden = true
    setupUI()
  }

  override func viewWillAppear(_ animated: Bool) {
    super.viewWillAppear(animated)
    AppConfig.statusbarStyle(true)
    loadListData()
  }
}

extension FZDraftVC: UITableViewDelegate, UITableViewDataSource {
  func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
    return items.count
  }

  func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
    let reuseIdentifier: String = NSStringFromClass(DraftCell.self)
    var cell =
      tableView.dequeueReusableCell(withIdentifier: reuseIdentifier, for: indexPath) as? DraftCell
    if cell == nil {
      cell = DraftCell.init(style: .default, reuseIdentifier: reuseIdentifier)
    }
    if items.count > indexPath.row {
      cell?.update(viewData: items[indexPath.row])
    }
    return cell!
  }

  func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
    return kRealWidth(126)
  }

  func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
    return UIView.init()
  }

  func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
    return 0.1
  }

  func PHAssetToUIImage(asset: PHAsset) -> UIImage {
    var image = UIImage()
    let imageManager = PHImageManager.default()
    let imageRequestOption = PHImageRequestOptions()
    imageRequestOption.isSynchronous = true
    imageRequestOption.resizeMode = .none
    imageRequestOption.deliveryMode = .highQualityFormat
    imageManager.requestImage(
      for: asset, targetSize: PHImageManagerMaximumSize, contentMode: .aspectFill,
      options: imageRequestOption,
      resultHandler: {
        (result, _) -> Void in
        if let img = result {
          image = img
        }
      })
    return image
  }

  func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
    tableView.deselectRow(at: indexPath, animated: true)
    let drafmodel: FZDraftModel = items[indexPath.row]
    let releaseVC = ReleaseDynamicVC.init()

    // 修改 HXPhotoModel 相关代码，改为使用 PHAsset
    let tempArr: NSMutableArray = NSMutableArray.init()
    for ids: String in drafmodel.pictureArrays {
      if let asset = PHAsset.fetchAssets(withLocalIdentifiers: [ids], options: nil).firstObject {
        // 直接添加 PHAsset 到数组
        tempArr.add(asset)
      }
    }

    // 修改类型转换
    releaseVC.hxModelArr = tempArr.copy() as! [PHAsset]
    releaseVC.draftData = drafmodel
    releaseVC.draftIndex = Int32(indexPath.row)
    releaseVC.modalPresentationStyle = .fullScreen
    present(releaseVC, animated: true)
  }

  func tableView(
    _ tableView: UITableView, commit editingStyle: UITableViewCell.EditingStyle,
    forRowAt indexPath: IndexPath
  ) {
    let tempArr = NSMutableArray.init(array: items)
    tempArr.removeObject(at: indexPath.row)
    FZKeyedArchiver.sharedClient().setArchiverDataWithData(tempArr)
    items = tempArr.copy() as! [FZDraftModel]
    tableView.deleteRows(at: [indexPath], with: UITableView.RowAnimation.bottom)

  }

  func tableView(
    _ tableView: UITableView, titleForDeleteConfirmationButtonForRowAt indexPath: IndexPath
  ) -> String? {
    return "删除"
  }

  internal func tableView(_ tableView: UITableView, editingStyleForRowAt indexPath: IndexPath)
    -> UITableViewCell.EditingStyle
  {
    return UITableViewCell.EditingStyle.delete
  }
}
