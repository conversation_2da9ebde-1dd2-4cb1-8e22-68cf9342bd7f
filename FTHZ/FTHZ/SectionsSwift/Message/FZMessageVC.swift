import Foundation

class FZMessageVC: FZBaseVC, UIGestureRecognizerDelegate {
  private var notificationUnreadCount = 0
  private var treeHoleUnreadCount = 0
  private var categoryView: JXCategoryNumberView!
  private var scroll: UIScrollView!
  private var topTitleView: UIView!
  private var imUnreadCount = 0
  private var sysMsgUnreadCount = 0
  private var clearButton: UIButton!
  private var loadingIndicator: UIActivityIndicatorView!
  private var deleteButton: UIButton!
  private var cancelButton: UIButton!
  private var isSelectionModeActive = false
  private var canScroll: Bool = true
  private var isNotchScreen: Bool {
    if #available(iOS 11.0, *) {
      let window = UIApplication.shared.windows.first
      return window?.safeAreaInsets.top ?? 0 > 20
    }
    return false
  }
  private func setUI() {
    categoryView = JXCategoryNumberView()
    categoryView.delegate = self

    scroll = UIScrollView(frame: CGRect())
    scroll.isPagingEnabled = true
    scroll.showsHorizontalScrollIndicator = false

    contentView.addSubview(scroll)
    scroll.snp.makeConstraints { make in
      make.top.equalToSuperview()
      make.left.right.bottom.equalToSuperview()
    }

    if #available(iOS 11, *) {
      scroll.contentInsetAdjustmentBehavior = .never
    } else {
      automaticallyAdjustsScrollViewInsets = false
    }

    loadChildVC()
    topTitleView = UIView.init()
    contentView.addSubview(topTitleView)
    topTitleView.snp.makeConstraints { make in
      make.left.right.top.equalToSuperview()
      make.height.equalTo(isNotchScreen ? kRealWidth(108) : kRealWidth(84))
    }
    let blurEffect = UIBlurEffect(style: .extraLight)
    let visualEffectView = UIVisualEffectView(effect: blurEffect)
    visualEffectView.frame = topTitleView.bounds
    visualEffectView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
    visualEffectView.layer.cornerRadius = 20.0
    visualEffectView.layer.maskedCorners = [.layerMinXMaxYCorner, .layerMaxXMaxYCorner]
    visualEffectView.clipsToBounds = true
    topTitleView.addSubview(visualEffectView)
    
    let backgroundView = UIView()
    backgroundView.backgroundColor = UIColor.white.withAlphaComponent(0.1)
    backgroundView.frame = visualEffectView.bounds
    backgroundView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
    visualEffectView.contentView.addSubview(backgroundView)

    let topBorder = CALayer()
    topBorder.frame = CGRect(x: 0, y: 0, width: topTitleView.frame.size.width, height: 0.5)
    topBorder.backgroundColor = UIColor(white: 0.0, alpha: 0.1).cgColor
    visualEffectView.layer.addSublayer(topBorder)

    topTitleView.layer.shadowColor = UIColor.black.cgColor
    topTitleView.layer.shadowOffset = CGSize(width: 0, height: 2.0)
    topTitleView.layer.shadowOpacity = 0.2
    topTitleView.layer.shadowRadius = 4.0
    topTitleView.layer.masksToBounds = false
    visualEffectView.layer.masksToBounds = true

    categoryView.titles = ["留言", "通知"]
    categoryView.isAverageCellSpacingEnabled = false
    categoryView.titleLabelZoomScale = 1.5
    categoryView.isTitleLabelZoomEnabled = true
    categoryView.titleFont = UIFont.ft_SourceHanserifSC_Blod_18
    categoryView.isTitleLabelZoomScrollGradientEnabled = true
    categoryView.titleColor = UIColor.fz_lightBlackColor
    categoryView.titleSelectedColor = UIColor.fz_HighBlackColor
    categoryView.isCellWidthZoomEnabled = false
    categoryView.contentScrollView = scroll
    categoryView.numberTitleColor = .white
    categoryView.numberBackgroundColor =
      USERINFO.gender == "1" ? .fz_skyBlueColor : .fz_femalePinkColor
    categoryView.numberLabelOffset = CGPoint(x: 5, y: kRealWidth(0))
    categoryView.shouldMakeRoundWhenSingleNumber = true
    categoryView.numberStringFormatterBlock = { count -> String in
      if count > 0 && count < 1000 {
        return "\(count)"
      }
      if count > 999 {
        return "999+"
      }
      return ""
    }
    categoryView.cellWidth = JXCategoryViewAutomaticDimension
    categoryView.contentEdgeInsetLeft = 20
    categoryView.contentEdgeInsetRight = 0

    topTitleView.addSubview(categoryView)
    categoryView.snp.makeConstraints { make in
      make.left.equalToSuperview().offset(kRealWidth(12))
      make.right.equalToSuperview().offset(-kRealWidth(24))
      make.height.equalTo(kRealWidth(52))
      make.top.equalToSuperview().offset(isNotchScreen ? kRealWidth(45) : kRealWidth(21))
    }

    let selectedIndex = 0
    var counts = [NSNumber(value: 0), NSNumber(value: 0)]
    if selectedIndex == 0 {
      counts[0] = NSNumber(value: treeHoleUnreadCount)
    } else if selectedIndex == 1 {
      counts[1] = NSNumber(value: notificationUnreadCount)
    }
    categoryView.counts = counts
    categoryView.reloadDataWithoutListContainer()

    clearButton = UIButton(type: .custom)
    clearButton.backgroundColor = .clear
    let clearImage = UIImage(named: "clear")?.withRenderingMode(.alwaysOriginal)
    clearButton.setImage(clearImage, for: .normal)
    clearButton.imageView?.contentMode = .scaleAspectFit
    clearButton.addTarget(self, action: #selector(clearButtonTapped), for: .touchUpInside)
    clearButton.isHidden = true
    contentView.addSubview(clearButton)
    clearButton.snp.makeConstraints { make in
      make.right.equalToSuperview().offset(-kRealWidth(30))
      make.centerY.equalTo(categoryView)
      make.width.height.equalTo(kRealWidth(20))
    }

    let buttonBackgroundView = UIView()
    buttonBackgroundView.backgroundColor = .fz_HighBlackColor
    buttonBackgroundView.layer.cornerRadius = kRealWidth(15)
    buttonBackgroundView.isHidden = true
    contentView.addSubview(buttonBackgroundView)
    buttonBackgroundView.snp.makeConstraints { make in
      make.right.equalToSuperview().offset(-kRealWidth(20))
      make.centerY.equalTo(categoryView)
      make.height.equalTo(kRealWidth(30))
      make.width.equalTo(kRealWidth(80))
    }

    cancelButton = UIButton(type: .custom)
    cancelButton.backgroundColor = .clear
    let cancelImage = UIImage(named: "hzclose")?.withRenderingMode(.alwaysOriginal)
    cancelButton.setImage(cancelImage, for: .normal)
    cancelButton.imageView?.contentMode = .scaleAspectFit
    cancelButton.addTarget(self, action: #selector(cancelButtonTapped), for: .touchUpInside)
    cancelButton.isHidden = true
    buttonBackgroundView.addSubview(cancelButton)
    cancelButton.snp.makeConstraints { make in
      make.right.equalTo(buttonBackgroundView).offset(-kRealWidth(10))
      make.centerY.equalToSuperview()
      make.width.height.equalTo(kRealWidth(20))
    }

    let separatorLine = UIView()
    separatorLine.backgroundColor = .lightGray
    separatorLine.tag = 1001
    separatorLine.isHidden = true
    buttonBackgroundView.addSubview(separatorLine)
    separatorLine.snp.makeConstraints { make in
      make.centerY.equalToSuperview()
      make.centerX.equalToSuperview()
      make.width.equalTo(1)
      make.height.equalTo(kRealWidth(15))
    }

    deleteButton = UIButton(type: .custom)
    deleteButton.backgroundColor = .clear
    let deleteImage = UIImage(named: "hzdel")?.withRenderingMode(.alwaysOriginal)
    deleteButton.setImage(deleteImage, for: .normal)
    deleteButton.imageView?.contentMode = .scaleAspectFit
    deleteButton.addTarget(self, action: #selector(deleteButtonTapped), for: .touchUpInside)
    deleteButton.isHidden = true
    buttonBackgroundView.addSubview(deleteButton)
    deleteButton.snp.makeConstraints { make in
      make.left.equalTo(buttonBackgroundView).offset(kRealWidth(10))
      make.centerY.equalToSuperview()
      make.width.height.equalTo(kRealWidth(20))
    }

    loadingIndicator = UIActivityIndicatorView(style: .gray)
    loadingIndicator.hidesWhenStopped = true
    contentView.addSubview(loadingIndicator)
    loadingIndicator.snp.makeConstraints { make in
      make.center.equalToSuperview()
    }
  }

  func scrollViewWillBeginDragging(_ scrollView: UIScrollView) {
  }

  func gestureRecognizer(
    _ gestureRecognizer: UIGestureRecognizer,
    shouldRecognizeSimultaneouslyWith otherGestureRecognizer: UIGestureRecognizer
  ) -> Bool {
    if gestureRecognizer == scroll.panGestureRecognizer {
      let velocity = (gestureRecognizer as? UIPanGestureRecognizer)?.velocity(in: scroll) ?? .zero
      return abs(velocity.x) > abs(velocity.y)
    }
    return false
  }

  @objc private func clearButtonTapped() {
    let selectedIndex = categoryView.selectedIndex
    let alertTitle = selectedIndex == 0 ? "清理留言" : "清理通知"
    let alertMsg = selectedIndex == 0 ? "清理所有未读留言？" : "清理所有未读通知？"
    let alertController = UIAlertController(
      title: alertTitle,
      message: alertMsg,
      preferredStyle: .alert
    )
    let cancelAction = UIAlertAction(title: "取消", style: .cancel, handler: nil)
    let confirmAction = UIAlertAction(title: "确认", style: .destructive) { [weak self] _ in
      guard let self = self else { return }
      if selectedIndex == 0 {
        self.clearAllLiuyan()
      } else {
        self.clearAllNotifications()
      }
    }
    alertController.addAction(cancelAction)
    alertController.addAction(confirmAction)
    present(alertController, animated: true, completion: nil)
  }

  @objc private func deleteButtonTapped() {
    let selectedIndex = categoryView.selectedIndex
    if selectedIndex == 0 {
      guard let liuyanVC = children.first(where: { $0 is LiuyanDetailVC }) as? LiuyanDetailVC else {
        showToast(message: "无法获取留言视图控制器")
        return
      }
      let selectedIds = liuyanVC.getSelectedLiuyanIds()
      guard !selectedIds.isEmpty else {
        showToast(message: "请选择要删除的留言")
        return
      }
      let alertController = UIAlertController(
        title: "删除留言",
        message: "确定要删除选中的留言吗？",
        preferredStyle: .alert
      )
      let cancelAction = UIAlertAction(title: "取消", style: .cancel, handler: nil)
      let confirmAction = UIAlertAction(title: "确认", style: .destructive) { [weak self] _ in
        self?.deleteSelectedLiuyan(ids: selectedIds)
      }
      alertController.addAction(cancelAction)
      alertController.addAction(confirmAction)
      present(alertController, animated: true, completion: nil)
    } else {
      guard let sysMsgVC = children.first(where: { $0 is MessageDetailVC }) as? MessageDetailVC
      else {
        showToast(message: "无法获取消息视图控制器")
        return
      }
      let selectedIds = sysMsgVC.getSelectedNotificationIds()
      guard !selectedIds.isEmpty else {
        showToast(message: "请选择要删除的通知")
        return
      }
      let alertController = UIAlertController(
        title: "删除通知",
        message: "确定要删除选中的通知吗？",
        preferredStyle: .alert
      )
      let cancelAction = UIAlertAction(title: "取消", style: .cancel, handler: nil)
      let confirmAction = UIAlertAction(title: "确认", style: .destructive) { [weak self] _ in
        self?.deleteSelectedNotifications(ids: selectedIds)
      }
      alertController.addAction(cancelAction)
      alertController.addAction(confirmAction)
      present(alertController, animated: true, completion: nil)
    }
  }
  private func deleteSelectedLiuyan(ids: String) {
    loadingIndicator.startAnimating()
    LiuyanNotificationModel.deleteLiuyanMessage(
      ids,
      success: { [weak self] (resultObject: Any?) in
        self?.loadingIndicator.stopAnimating()
        let result = resultObject as? [String: Any]
        let code = result?["code"] as? Int ?? -1
        if code == 0 {
          self?.exitSelectionMode()
          self?.showToast(message: "删除成功")
          NotificationCenter.default.post(
            name: NSNotification.Name("ReloadLiuyanList"), object: nil)
        } else {
          let msg = result?["msg"] as? String ?? "删除失败"
          self?.showToast(message: msg)
        }
      },
      failure: { [weak self] (error: Error?) in
        self?.loadingIndicator.stopAnimating()
        self?.showToast(message: "网络错误，请稍后重试")
      }
    )
  }

  private func clearAllLiuyan() {
    loadingIndicator.startAnimating()
    LiuyanNotificationModel.clearAllLiuyanMessages(
      { [weak self] resultObject in
        self?.loadingIndicator.stopAnimating()
        let result = resultObject as? [String: Any]
        let code = result?["code"] as? Int ?? -1
        if code == 0 {
          self?.treeHoleUnreadCount = 0
          self?.updateCategoryViewCounts()
          self?.updateClearButtonVisibility()
          if let liuyanVC = self?.children.first(where: { $0 is LiuyanDetailVC }) as? LiuyanDetailVC
          {
            liuyanVC.markAllMessagesAsRead()
          }
          NotificationCenter.default.post(
            name: NSNotification.Name("ReloadLiuyanList"), object: nil)
          self?.showToast(message: "清理成功")
        } else {
          let msg = result?["msg"] as? String ?? "已读失败"
          self?.showToast(message: msg)
        }
      },
      failure: { [weak self] error in
        self?.loadingIndicator.stopAnimating()
        self?.showToast(message: "网络错误，请稍后重试")
      }
    )
  }

  private func deleteSelectedNotifications(ids: String) {
    loadingIndicator.startAnimating()
    MessageChangeStatsModel.postDelNotificationModel(
      ids,
      success: { [weak self] (resultObject: Any?) in
        self?.loadingIndicator.stopAnimating()
        let result = resultObject as? [String: Any]
        let code = result?["code"] as? Int ?? -1
        if code == 0 {
          self?.exitSelectionMode()
          self?.showToast(message: "删除成功")
          NotificationCenter.default.post(
            name: NSNotification.Name("ReloadNotiList"), object: nil)
        } else {
          let msg = result?["msg"] as? String ?? "删除失败"
          self?.showToast(message: msg)
        }
      },
      failure: { [weak self] (error: Error?) in
        self?.loadingIndicator.stopAnimating()
        self?.showToast(message: "网络错误，请稍后重试")
      }
    )
  }

  private func clearAllNotifications() {
    loadingIndicator.startAnimating()
    ClearNotificationsModel.postClearNotifications(
      { [weak self] resultObject in
        self?.loadingIndicator.stopAnimating()
        let result = resultObject as? [String: Any]
        let code = result?["code"] as? Int ?? -1
        if code == 0 {
          self?.notificationUnreadCount = 0
          self?.updateCategoryViewCounts()
          self?.updateClearButtonVisibility()
          NotificationCenter.default.post(
            name: NSNotification.Name("ReloadNotiList"), object: nil)
          self?.showToast(message: "清理成功")
        } else {
          let msg = result?["msg"] as? String ?? "已读失败"
          self?.showToast(message: msg)
        }
      },
      failure: { [weak self] error in
        self?.loadingIndicator.stopAnimating()
        self?.showToast(message: "网络错误，请稍后重试")
      }
    )
  }

  private func updateCategoryViewCounts() {
    var counts = [NSNumber(value: 0), NSNumber(value: 0)]
    let selectedIndex = self.categoryView.selectedIndex
    if selectedIndex == 0 {
      counts[0] = NSNumber(value: treeHoleUnreadCount)
    } else if selectedIndex == 1 {
      counts[1] = NSNumber(value: notificationUnreadCount)
    }
    categoryView.counts = counts
    categoryView.reloadDataWithoutListContainer()
  }

  private func showToast(message: String) {
    view.makeToast(message, duration: 1.5, position: CSToastPositionCenter)
  }

  private func updateClearButtonVisibility() {
    guard categoryView != nil, clearButton != nil else { return }
    let selectedIndex = categoryView.selectedIndex
    if selectedIndex == 0 {
      clearButton.isHidden = isSelectionModeActive || !(treeHoleUnreadCount > 0)
    } else {
      clearButton.isHidden = isSelectionModeActive || !(notificationUnreadCount > 0)
    }
  }

  private func loadChildVC() {
    let liuyanVC = LiuyanDetailVC()
    liuyanVC.mainTempScale = kRealWidth(1)
    liuyanVC.delegate = self
    addChild(liuyanVC)
    scroll.addSubview(liuyanVC.view)
    liuyanVC.view.snp.makeConstraints { make in
      make.left.top.equalToSuperview()
      make.width.equalTo(view.frame.width)
      make.bottom.equalTo(contentView)
    }

    let sysMsgVC = MessageDetailVC()
    sysMsgVC.mainTempScale = kRealWidth(1)
    sysMsgVC.delegate = self
    addChild(sysMsgVC)
    scroll.addSubview(sysMsgVC.view)
    sysMsgVC.view.snp.makeConstraints { make in
      make.top.equalToSuperview()
      make.left.equalTo(liuyanVC.view.snp.right)
      make.width.equalTo(view.frame.width)
      make.bottom.equalTo(contentView)
    }
  }

  override func viewDidAppear(_ animated: Bool) {
    super.viewDidAppear(animated)
    self.view.setNeedsLayout()
    self.view.layoutIfNeeded()
  }

  override func viewWillDisappear(_ animated: Bool) {
    super.viewWillDisappear(animated)
    if isSelectionModeActive {
      exitSelectionMode()
    }
  }

  override func viewDidLoad() {
    super.viewDidLoad()
    navigationController?.interactivePopGestureRecognizer?.delegate = self
    setUI()
    self.categoryView.counts = [
      NSNumber(value: treeHoleUnreadCount),
      NSNumber(value: 0),
    ]
    updateClearButtonVisibility()

    NotificationCenter.default.addObserver(
      self,
      selector: #selector(acceptMsg),
      name: NSNotification.Name(CurrentSelectedChildViewControllerIndex),
      object: nil
    )

    NotificationCenter.default.addObserver(
      self,
      selector: #selector(acceptMsg),
      name: NSNotification.Name(SegementViewChildVCBackToTop),
      object: nil
    )

    NotificationCenter.default.addObserver(
      self,
      selector: #selector(handleLongPress),
      name: NSNotification.Name("NotificationCellLongPressed"),
      object: nil
    )

    NotificationCenter.default.addObserver(
      self,
      selector: #selector(hideClearButton),
      name: NSNotification.Name("HideClearButton"),
      object: nil
    )
  }

  @objc private func acceptMsg(_ notification: Notification) {

  }

  func scrollViewShouldScrollToTop(_ scrollView: UIScrollView) -> Bool {
    NotificationCenter.default.post(
      name: NSNotification.Name(SegementViewChildVCBackToTop),
      object: nil
    )
    return true
  }

  @objc private func hideClearButton() {
    clearButton.isHidden = true
  }

  @objc private func handleLongPress() {
    let selectedIndex = categoryView.selectedIndex
    if !isSelectionModeActive && selectedIndex == 1 {
      enterSelectionMode()
    }
    if !isSelectionModeActive && selectedIndex == 0 {
      enterSelectionMode()
    }
  }

  override func viewWillAppear(_ animated: Bool) {
    super.viewWillAppear(animated)
    navigationController?.setNavigationBarHidden(true, animated: false)
    AppConfig.statusbarStyle(true)
    navigationController?.navigationBar.isHidden = true

    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
      guard let self = self else { return }
      self.topTitleView.snp.updateConstraints { make in
        make.top.equalToSuperview()
        make.height.equalTo(self.isNotchScreen ? kRealWidth(108) : kRealWidth(84))
      }
      self.categoryView.snp.updateConstraints { make in
        make.top.equalToSuperview().offset(self.isNotchScreen ? kRealWidth(45) : kRealWidth(21))
      }
      self.view.layoutIfNeeded()
    }
  }

  override func viewDidLayoutSubviews() {
    let width = scroll.subviews.reduce(0) { $0 + $1.frame.width }
    let height = scroll.frame.height
    scroll.contentSize = CGSize(width: width, height: height)
  }

  @objc private func cancelButtonTapped() {
    exitSelectionMode()
  }

  func enterSelectionMode() {
    if isSelectionModeActive { return }
    isSelectionModeActive = true
    clearButton.isHidden = true
    deleteButton.isHidden = false
    cancelButton.isHidden = false
    if let separatorLine = contentView.viewWithTag(1001) {
      separatorLine.isHidden = false
    }
    if let buttonBackgroundView = contentView.subviews.first(where: {
      $0.subviews.contains(where: { $0.tag == 1001 })
    }) {
      buttonBackgroundView.isHidden = false
    }
    let selectedIndex = categoryView.selectedIndex
    if selectedIndex == 0 {
      NotificationCenter.default.post(
        name: NSNotification.Name("EnterLiuyanSelectionMode"),
        object: nil)
    } else {
      NotificationCenter.default.post(
        name: NSNotification.Name("EnterNotificationSelectionMode"),
        object: nil)
    }
  }

  func exitSelectionMode() {
    if !isSelectionModeActive { return }
    isSelectionModeActive = false
    deleteButton.isHidden = true
    cancelButton.isHidden = true
    if let separatorLine = contentView.viewWithTag(1001) {
      separatorLine.isHidden = true
    }
    if let buttonBackgroundView = contentView.subviews.first(where: {
      $0.subviews.contains(where: { $0.tag == 1001 })
    }) {
      buttonBackgroundView.isHidden = true
    }
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) { [weak self] in
      self?.updateClearButtonVisibility()
    }
    NotificationCenter.default.post(
      name: NSNotification.Name("ExitLiuyanSelectionMode"),
      object: nil)
    NotificationCenter.default.post(
      name: NSNotification.Name("ExitNotificationSelectionMode"),
      object: nil)
  }

  deinit {
    NotificationCenter.default.removeObserver(self)
  }
}

extension FZMessageVC: JXCategoryViewDelegate, MessageDetailDelegate, LiuyanDetailDelegate {
  func unreadMessage(_ index: Int, number count: Int) {
    DispatchQueue.main.async {
      switch index {
      case 0:
        self.treeHoleUnreadCount = count
      case 1:
        self.notificationUnreadCount = count
      default:
        break
      }
      if self.isViewLoaded {
        var counts = [NSNumber(value: 0), NSNumber(value: 0)]
        let selectedIndex = self.categoryView.selectedIndex
        if selectedIndex == 0 {
          counts[0] = NSNumber(value: self.treeHoleUnreadCount)
        } else if selectedIndex == 1 {
          counts[1] = NSNumber(value: self.notificationUnreadCount)
        }
        self.categoryView.counts = counts
        self.categoryView.reloadDataWithoutListContainer()
        self.updateClearButtonVisibility()
      }
    }
  }

  func categoryView(_ categoryView: JXCategoryBaseView!, didSelectedItemAt index: Int) {
    if isSelectionModeActive {
      exitSelectionMode()
    }

    var counts = [NSNumber(value: 0), NSNumber(value: 0)]
    if index == 0 {
      counts[0] = NSNumber(value: treeHoleUnreadCount)
    } else if index == 1 {
      counts[1] = NSNumber(value: notificationUnreadCount)
    }
    self.categoryView.counts = counts
    self.categoryView.reloadDataWithoutListContainer()

    updateClearButtonVisibility()
  }

  func preferredCategoryView() -> JXCategoryNumberView {
    return JXCategoryNumberView()
  }
}
