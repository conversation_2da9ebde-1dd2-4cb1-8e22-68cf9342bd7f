import UIKit

extension UIFont {
  open class var ft_SourceHanSerif_Blod_12: UIFont {
    return UIFont.systemFont(ofSize: kReal<PERSON>idth(12), weight: .bold)
  }

  open class var ft_SourceHanSerif_Blod_13: UIFont {
    return UIFont.systemFont(ofSize: kReal<PERSON>idth(13), weight: .bold)
  }

  open class var ft_SourceHanSerif_Blod_14: UIFont {
    return UIFont.systemFont(ofSize: kRealWidth(14), weight: .semibold)
  }

  open class var ft_SourceHanSerif_Blod_15: UIFont {
    return UIFont.systemFont(ofSize: kRealWidth(15), weight: .bold)
  }

  open class var ft_SourceHanSerif_Blod_16: UIFont {
    return UIFont.systemFont(ofSize: kRealWidth(16), weight: .bold)
  }

  open class var ft_SourceHanSerif_Blod_24: UIFont {
    return UIFont.systemFont(ofSize: kReal<PERSON>idth(24), weight: .bold)
  }
}

// 粗体字体
extension UIFont {
  open class var ft_SourceHanserifSC_Blod_120: UIFont {
    return UIFont.systemFont(ofSize: kReal<PERSON>idth(120), weight: .semibold)
  }

  open class var ft_SourceHanserifSC_Blod_10: UIFont {
    return UIFont.systemFont(ofSize: kRealWidth(10), weight: .semibold)
  }

  open class var ft_SourceHanserifSC_Blod_12: UIFont {
    return UIFont.systemFont(ofSize: kRealWidth(12), weight: .semibold)
  }

  open class var ft_SourceHanserifSC_Blod_14: UIFont {
    return UIFont.systemFont(ofSize: kRealWidth(14), weight: .semibold)
  }

  open class var ft_SourceHanserifSC_Blod_15: UIFont {
    return UIFont.systemFont(ofSize: kRealWidth(15), weight: .semibold)
  }

  open class var ft_SourceHanserifSC_Blod_16: UIFont {
    return UIFont.systemFont(ofSize: kRealWidth(16), weight: .semibold)
  }

  open class var ft_SourceHanserifSC_Blod_18: UIFont {
    return UIFont.systemFont(ofSize: kRealWidth(18), weight: .semibold)
  }

  open class var ft_SourceHanserifSC_Blod_20: UIFont {
    return UIFont.systemFont(ofSize: kRealWidth(20), weight: .semibold)
  }

  open class var ft_SourceHanserifSC_Blod_24: UIFont {
    return UIFont.systemFont(ofSize: kRealWidth(24), weight: .semibold)
  }
}

// 中型字体
extension UIFont {
  open class var ft_SourceHanserifSC_Meium_18: UIFont {
    return UIFont.systemFont(ofSize: kRealWidth(18), weight: .medium)
  }

  open class var ft_SourceHanserifSC_Meium_10: UIFont {
    return UIFont.systemFont(ofSize: kRealWidth(10), weight: .medium)
  }

  open class var ft_SourceHanserifSC_Meium_12: UIFont {
    return UIFont.systemFont(ofSize: kRealWidth(12), weight: .medium)
  }

  open class var ft_SourceHanserifSC_Meium_13: UIFont {
    return UIFont.systemFont(ofSize: kRealWidth(13), weight: .medium)
  }

  open class var ft_SourceHanserifSC_Meium_14: UIFont {
    return UIFont.systemFont(ofSize: kRealWidth(14), weight: .medium)
  }

  open class var ft_SourceHanserifSC_Meium_16: UIFont {
    return UIFont.systemFont(ofSize: kRealWidth(16), weight: .medium)
  }

  open class var ft_SourceHanserifSC_Meium_20: UIFont {
    return UIFont.systemFont(ofSize: kRealWidth(20), weight: .medium)
  }

  open class var ft_SourceHanserifSC_Meium_24: UIFont {
    return UIFont.systemFont(ofSize: kRealWidth(24), weight: .medium)
  }
}

// 普通字体
extension UIFont {
  open class var ft_SourceHanserifSC_Normal_18: UIFont {
    return UIFont.systemFont(ofSize: kRealWidth(18), weight: .regular)
  }

  open class var ft_SourceHanserifSC_Normal_10: UIFont {
    return UIFont.systemFont(ofSize: kRealWidth(10), weight: .regular)
  }

  open class var ft_SourceHanserifSC_Normal_12: UIFont {
    return UIFont.systemFont(ofSize: kRealWidth(12), weight: .regular)
  }

  open class var ft_SourceHanserifSC_Normal_13: UIFont {
    return UIFont.systemFont(ofSize: kRealWidth(13), weight: .regular)
  }

  open class var ft_SourceHanserifSC_Normal_14: UIFont {
    return UIFont.systemFont(ofSize: kRealWidth(14), weight: .regular)
  }

  open class var ft_SourceHanserifSC_Normal_16: UIFont {
    return UIFont.systemFont(ofSize: kRealWidth(16), weight: .regular)
  }
}

/// ding-数字字体
extension UIFont {
  open class var ft_Din_12: UIFont {
    return UIFont.systemFont(ofSize: kRealWidth(12))
  }

  open class var ft_Din_14: UIFont {
    return UIFont.systemFont(ofSize: kRealWidth(14))
  }

  open class var ft_Din_16: UIFont {
    return UIFont.systemFont(ofSize: kRealWidth(16))
  }

  open class var ft_Din_Bold_10: UIFont {
    return UIFont.boldSystemFont(ofSize: kRealWidth(10))
  }

  open class var ft_Din_Bold_12: UIFont {
    return UIFont.boldSystemFont(ofSize: kRealWidth(12))
  }

  open class var ft_Din_Bold_14: UIFont {
    return UIFont.boldSystemFont(ofSize: kRealWidth(14))
  }

  open class var ft_Din_Bold_15: UIFont {
    return UIFont.boldSystemFont(ofSize: kRealWidth(15))
  }

  open class var ft_Din_Bold_16: UIFont {
    return UIFont.boldSystemFont(ofSize: kRealWidth(16))
  }

  open class var ft_Din_Bold_20: UIFont {
    return UIFont.boldSystemFont(ofSize: kRealWidth(20))
  }

  open class var ft_Din_Bold_24: UIFont {
    return UIFont.boldSystemFont(ofSize: kRealWidth(24))
  }

  open class var ft_Din_Bold_32: UIFont {
    return UIFont.boldSystemFont(ofSize: kRealWidth(32))
  }
}
