import Foundation

enum FZButtonImageEdgeInsetsStyle {
  case top, left, right, bottom, center
}

extension UIButton {

  func startAnimatingPressActions() {
    addTarget(self, action: #selector(animateDown), for: [.touchDown, .touchDragEnter])
    addTarget(
      self, action: #selector(animateUp),
      for: [.touchDragExit, .touchCancel, .touchUpInside, .touchUpOutside])
  }

  @objc private func animateDown(sender: UIButton) {
    animate(sender, transform: CGAffineTransform.identity.scaledBy(x: 1.25, y: 1.25))
  }

  @objc private func animateUp(sender: UIButton) {
    animate(sender, transform: .identity)
  }

  private func animate(_ button: UIButton, transform: CGAffineTransform) {
    UIView.animate(
      withDuration: 0.4,
      delay: 0,
      usingSpringWithDamping: 0.5,
      initialSpringVelocity: 3,
      options: [.curveEaseInOut],
      animations: {
        button.transform = transform
      }, completion: nil)
  }

}
extension UIButton {

  open override func point(inside point: CGPoint, with event: UIEvent?) -> Bool {
    let newArea = CGRect(
      x: self.bounds.origin.x - 5.0,
      y: self.bounds.origin.y - 5.0,
      width: self.bounds.size.width + 10.0,
      height: self.bounds.size.height + 10.0
    )
    return newArea.contains(point)
  }

  func imagePosition(at style: FZButtonImageEdgeInsetsStyle, space: CGFloat) {
    guard let imageV = imageView else { return }
    guard let titleL = titleLabel else { return }
    let imageWidth = imageV.frame.size.width
    let imageHeight = imageV.frame.size.height
    let labelWidth = titleL.intrinsicContentSize.width
    let labelHeight = titleL.intrinsicContentSize.height

    var imageEdgeInsets = UIEdgeInsets.zero
    var labelEdgeInsets = UIEdgeInsets.zero
    switch style {
    case .center:
      let totalWidth = imageWidth + space + labelWidth
      let imageOffset = (bounds.width - totalWidth) / 2

      imageEdgeInsets = UIEdgeInsets(
        top: 0,
        left: imageOffset,
        bottom: 0,
        right: bounds.width - imageWidth - imageOffset
      )

      labelEdgeInsets = UIEdgeInsets(
        top: 0,
        left: -(imageWidth - imageOffset),
        bottom: 0,
        right: 0
      )
    case .left:
      imageEdgeInsets = UIEdgeInsets(top: 0, left: -space * 0.5, bottom: 0, right: space * 0.5)
      labelEdgeInsets = UIEdgeInsets(top: 0, left: space * 0.5, bottom: 0, right: -space * 0.5)
    case .right:
      imageEdgeInsets = UIEdgeInsets(
        top: 0, left: labelWidth + space * 0.5, bottom: 0, right: -labelWidth - space * 0.5)
      labelEdgeInsets = UIEdgeInsets(
        top: 0, left: -imageWidth - space * 0.5, bottom: 0, right: imageWidth + space * 0.5)
    case .top:
      imageEdgeInsets = UIEdgeInsets(
        top: -imageHeight * 0.5 - space * 0.5, left: labelWidth * 0.5,
        bottom: imageHeight * 0.5 + space * 0.5, right: -labelWidth * 0.5)
      labelEdgeInsets = UIEdgeInsets(
        top: labelHeight * 0.5 + space * 0.5, left: -imageWidth * 0.5,
        bottom: -labelHeight * 0.5 - space * 0.5, right: imageWidth * 0.5)
    case .bottom:
      imageEdgeInsets = UIEdgeInsets(
        top: imageHeight * 0.5 + space * 0.5, left: labelWidth * 0.5,
        bottom: -imageHeight * 0.5 - space * 0.5, right: -labelWidth * 0.5)
      labelEdgeInsets = UIEdgeInsets(
        top: -labelHeight * 0.5 - space * 0.5, left: -imageWidth * 0.5,
        bottom: labelHeight * 0.5 + space * 0.5, right: imageWidth * 0.5)
    }
    self.titleEdgeInsets = labelEdgeInsets
    self.imageEdgeInsets = imageEdgeInsets
  }
}
typealias BtnAction = (UIButton) -> Void

extension UIButton {
  private struct AssociatedKeys {
    static var actionKey = "actionKey"
  }

  @objc dynamic var action: BtnAction? {
    set {
      objc_setAssociatedObject(
        self, &AssociatedKeys.actionKey, newValue, objc_AssociationPolicy.OBJC_ASSOCIATION_COPY)
    }
    get {
      if let action = objc_getAssociatedObject(self, &AssociatedKeys.actionKey) as? BtnAction {
        return action
      }
      return nil
    }
  }

  func blockAction(action: @escaping BtnAction) {
    self.action = action
    self.addTarget(self, action: #selector(touchUpInSideBtnAction), for: .touchUpInside)
  }

  @objc func touchUpInSideBtnAction(btn: UIButton) {
    if let action = self.action {
      action(btn)
    }
  }
}
extension UIButton {
  func setBackgroundColor(color: UIColor, forState: UIControl.State) {
    self.clipsToBounds = true
    if 1 <= 0 || 1 <= 0 { return }
    UIGraphicsBeginImageContext(CGSize(width: 1, height: 1))
    if let context = UIGraphicsGetCurrentContext() {
      context.setFillColor(color.cgColor)
      context.fill(CGRect(x: 0, y: 0, width: 1, height: 1))
      let colorImage = UIGraphicsGetImageFromCurrentImageContext()
      UIGraphicsEndImageContext()
      self.setBackgroundImage(colorImage, for: forState)
    }
  }
}
