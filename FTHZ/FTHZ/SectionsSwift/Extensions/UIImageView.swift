import Foundation

extension UIImageView {
  func setCornerImage() {

    if self.bounds.size.width <= 0 || self.bounds.size.height <= 0 { return }
    UIGraphicsBeginImageContextWithOptions(self.bounds.size, true, 0)

    let ctx = UIGraphicsGetCurrentContext()

    UIColor.white.setFill()
    UIRectFill(self.bounds)

    ctx?.addEllipse(in: self.bounds)
    ctx?.clip()

    self.draw(self.bounds)

    let image = UIGraphicsGetImageFromCurrentImageContext()

    UIGraphicsEndImageContext()

    DispatchQueue.main.async(execute: {
      self.image = image
    })

  }
  func netImgByUrl(_ url: URL, _ placeName: String) {
    setImageWith(url, placeholderImage: UIImage.init(named: placeName))
  }

  func netImg(_ urlstring: String, _ placeName: String) {
    guard let url = URL.init(string: urlstring) else { return }
    setImageWith(url, placeholderImage: UIImage.init(named: placeName))
  }
}

extension UIImageView {
  func applyshadowWithCorner(containerView: UIView, cornerRadious: CGFloat) {
    containerView.clipsToBounds = false
    containerView.layer.shadowColor = UIColor.lightGray.cgColor
    containerView.layer.shadowOpacity = 0.7
    containerView.layer.shadowOffset = CGSize.zero
    containerView.layer.shadowRadius = 10
    containerView.layer.cornerRadius = cornerRadious
    self.clipsToBounds = true
    self.layer.cornerRadius = cornerRadious
  }
  func applySmallShadowWithCorner(containerView: UIView, cornerRadious: CGFloat) {
    containerView.clipsToBounds = false
    containerView.layer.shadowColor = UIColor.lightGray.cgColor
    containerView.layer.shadowOpacity = 0.7
    containerView.layer.shadowOffset = CGSize.zero
    containerView.layer.shadowRadius = 3
    containerView.layer.cornerRadius = cornerRadious
    self.clipsToBounds = true
    self.layer.cornerRadius = cornerRadious
  }
}
