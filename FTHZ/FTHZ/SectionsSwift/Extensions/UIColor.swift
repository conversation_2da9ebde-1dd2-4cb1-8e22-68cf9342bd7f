import UIKit

extension UIColor {
  open class var fz_HighBlackColor: UIColor {
    // return fz_color(withHex: 0x1A223D)
    return fz_color(withHex: 0x1A223D)
  }

  open class var fz_HighBlackColor2: UIColor {
    return fz_color(withHex: 0x242E56)
  }

  open class var fz_DrawBlackColor: UIColor {
    return fz_color(withHex: 0x191919)
  }

  open class var fz_extraLightGray: UIColor {
    return fz_color(withHex: 0xF8FAFB)
  }

  open class var fz_bigGreyColor: UIColor {
    return fz_color(withHex: 0x101010, alpha: 0.54)
  }

  open class var fz_lightgrayBlackColor: UIColor {
    return fz_color(withHex: 0x13192C, alpha: 0.12)
  }

  open class var fz_detailLightGray: UIColor {
    return fz_color(withHex: 0x000000, alpha: 0.27)
  }

  open class var fz_lightBlackColor: UIColor {
    return fz_color(withHex: 0x13192C, alpha: 0.27)
  }

  open class var fz_titleBlackColor: UIColor {
    return fz_color(withHex: 0x13192C, alpha: 0.87)
  }

  open class var fz_grayBlueColor: UIColor {
    return fz_color(withHex: 0x445A7E)
  }

  open class var fz_titleGreyColor: UIColor {
    return fz_color(withHex: 0x13192C, alpha: 0.54)
  }

  open class var fz_skyBlueColor: UIColor {
    return fz_color(withHex: 0x5DADE2)
  }

  open class var fz_skylightBlueColor: UIColor {
    return fz_color(withHex: 0x62E2FF, alpha: 0.27)
  }
  open class var fz_greenBlueColor: UIColor {
    return fz_color(withHex: 0x61e1fe)
  }

  open class var fz_femalePinkColor: UIColor {
    return fz_color(withHex: 0xF28CB2)
  }

  open class var fz_lightWiteColor: UIColor {
    return fz_color(withHex: 0xffffff, alpha: 0.54)
  }

  open class var fz_WiteColor_27: UIColor {
    return fz_color(withHex: 0xffffff, alpha: 0.27)
  }

  open class var fz_WiteColor_87: UIColor {
    return fz_color(withHex: 0xffffff, alpha: 0.87)
  }

  open class var fz_tipFillColor: UIColor {
    return fz_color(withHex: 0xF2F4F8)
  }

  open class var fz_tipTxtColor: UIColor {
    return fz_color(withHex: 0x000000, alpha: 0.54)
  }

  open class var fz_tipTitleColor: UIColor {
    return fz_color(withHex: 0x131415, alpha: 0.27)
  }

  open class var fz_bgBlackFillColor: UIColor {
    return fz_color(withHex: 0x131415)
  }

  open class var fz_tipButtonColor: UIColor {
    return fz_color(withHex: 0x000000, alpha: 0.87)
  }

  open class var fz_tinyGrayColor: UIColor {
    return fz_color(withHex: 0x000000, alpha: 0.08)
  }

  open class var fz_keyboardBgColor: UIColor {
    return fz_color(withHex: 0x2E2F30)
  }

  open class var fz_pointYellow: UIColor {
    return fz_color(withHex: 0xFF9F00)
  }

  open class var fz_pointPink: UIColor {
    return fz_color(withHex: 0xE57986)
  }

  open class var fz_pointGreen: UIColor {
    return fz_color(withHex: 0x254C5A)
  }

  open class var fz_tabbarNormalTxtColor: UIColor {
    return fz_color(withHex: 0x7F8389)
  }

  open class var fz_pointhailan: UIColor {
    return fz_color(withHex: 0x5BA3BC)
  }

  open class var fz_rankBgColor: UIColor {
    return fz_color(withHex: 0xF4F5F6)
  }

  open class var fz_gameOrange: UIColor {
    return fz_color(withHex: 0xFFA502)
  }
  open class var fz_gameYellow: UIColor {
    return fz_color(withHex: 0xFFC53E)
  }
  open class var fz_gameGreen: UIColor {
    return fz_color(withHex: 0x008000)
  }
  open class var fz_gamePurple: UIColor {
    return fz_color(withHex: 0xAD00B5)
  }
  open class var fz_gameLightBlue: UIColor {
    return fz_color(withHex: 0x629FAC)
  }
  open class var fz_gameLightPurple: UIColor {
    return fz_color(withHex: 0x410AD7)
  }
  open class var fz_gameLightPink: UIColor {
    return fz_color(withHex: 0xC717AD)
  }
  open class var fz_unreadBadgeColor: UIColor {
    return fz_color(withHex: 0x13192C)
  }

  class func fz_color(withHex: UInt32, alpha: CGFloat = 1.0) -> UIColor {

    let r = ((CGFloat)((withHex & 0xFF0000) >> 16)) / 255.0
    let g = ((CGFloat)((withHex & 0xFF00) >> 8)) / 255.0
    let b = ((CGFloat)(withHex & 0xFF)) / 255.0

    return UIColor(red: r, green: g, blue: b, alpha: alpha)
  }

  class func fz_color(withRed: UInt8, green: UInt8, blue: UInt8) -> UIColor {

    let r = CGFloat(withRed) / 255.0
    let g = CGFloat(green) / 255.0
    let b = CGFloat(blue) / 255.0

    return UIColor(red: r, green: g, blue: b, alpha: 1.0)
  }
}
