import Foundation

public struct RangedAttributes {

  let attributes: [NSAttributedString.Key: Any]
  let range: NSRange

  public init(_ attributes: [NSAttributedString.Key: Any], inRange range: NSRange) {
    self.attributes = attributes
    self.range = range
  }
}
public protocol ChangableFont: AnyObject {
  var text: String? { get set }
  var attributedText: NSAttributedString? { get set }
  var rangedAttributes: [RangedAttributes] { get }
  func getFont() -> UIFont?
  func changeFont(ofText text: String, with font: UIFont)
  func changeFont(inRange range: NSRange, with font: UIFont)
  func changeTextColor(ofText text: String, with color: UIColor)
  func changeTextColor(inRange range: NSRange, with color: UIColor)
  func resetFontChanges()
}

extension UILabel: ChangableFont {

  public func getFont() -> UIFont? {
    return font
  }

  func setLineSpacing(_ lineSpacing: CGFloat = 4.0) {
    guard let text = self.text else { return }

    let paragraphStyle = NSMutableParagraphStyle()
    paragraphStyle.lineSpacing = lineSpacing
    paragraphStyle.lineBreakMode = self.lineBreakMode
    paragraphStyle.alignment = self.textAlignment

    let attributes: [NSAttributedString.Key: Any] = [
      .paragraphStyle: paragraphStyle,
      .font: self.font as Any,
    ]

    self.attributedText = NSAttributedString(string: text, attributes: attributes)
  }
}

extension ChangableFont {

  public var rangedAttributes: [RangedAttributes] {
    guard let attributedText = attributedText else {
      return []
    }
    var rangedAttributes: [RangedAttributes] = []
    let fullRange = NSRange(
      location: 0,
      length: attributedText.string.count
    )
    attributedText.enumerateAttributes(
      in: fullRange,
      options: []
    ) { (attributes, range, stop) in
      guard range != fullRange, !attributes.isEmpty else { return }
      rangedAttributes.append(RangedAttributes(attributes, inRange: range))
    }
    return rangedAttributes
  }

  public func changeFont(ofText text: String, with font: UIFont) {
    return
  }

  public func changeFont(inRange range: NSRange, with font: UIFont) {
    add(attributes: [.font: font], inRange: range)
  }

  public func changeTextColor(ofText text: String, with color: UIColor) {
    guard let range = (self.attributedText?.string ?? self.text)?.range(of: text) else { return }
    changeTextColor(inRange: NSRange.init(range, in: text), with: color)
  }

  public func changeTextColor(inRange range: NSRange, with color: UIColor) {
    add(attributes: [.foregroundColor: color], inRange: range)
  }

  private func add(attributes: [NSAttributedString.Key: Any], inRange range: NSRange) {
    guard !attributes.isEmpty else { return }

    var rangedAttributes: [RangedAttributes] = self.rangedAttributes

    var attributedString: NSMutableAttributedString

    if let attributedText = attributedText {
      attributedString = NSMutableAttributedString(attributedString: attributedText)
    } else if let text = text {
      attributedString = NSMutableAttributedString(string: text)
    } else {
      return
    }

    rangedAttributes.append(RangedAttributes(attributes, inRange: range))

    rangedAttributes.forEach { (rangedAttributes) in
      attributedString.addAttributes(
        rangedAttributes.attributes,
        range: rangedAttributes.range
      )
    }

    attributedText = attributedString
  }

  public func resetFontChanges() {
    guard let text = text else { return }
    attributedText = NSMutableAttributedString(string: text)
  }
}

@objc extension UILabel {
  func applyGradient(type: Int, expireTime: Int64) {
    guard let gradientText = self.text else {
      return
    }

    let expired = Date().currentUnixTimeStamp() - expireTime

    if type != 1 || expired > 0 {
      return
    }

    let textSize: CGSize = gradientText.size(withAttributes: [
      .font: self.font ?? UIFont.systemFontSize
    ])
    let width: CGFloat = textSize.width
    let height: CGFloat = textSize.height

    if width <= 0 || height <= 0 { return }
    UIGraphicsBeginImageContext(CGSize(width: width, height: height))

    guard let context = UIGraphicsGetCurrentContext() else {
      UIGraphicsEndImageContext()
      return
    }

    UIGraphicsPushContext(context)

    let glossGradient: CGGradient?
    let rgbColorspace: CGColorSpace?
    let num_locations: size_t = 3
    let locations: [CGFloat] = [0.0, 0.5, 1.0]
    let components: [CGFloat] = [
      98 / 255, 159 / 255, 172 / 255, 1.0,
      65 / 255, 10 / 255, 215 / 255, 1.0,
      199 / 255, 23 / 255, 173 / 255, 1.0,
    ]
    rgbColorspace = CGColorSpaceCreateDeviceRGB()
    glossGradient = CGGradient(
      colorSpace: rgbColorspace!, colorComponents: components, locations: locations,
      count: num_locations)
    let topCenter = CGPoint.zero
    let bottomCenter = CGPoint(x: textSize.width, y: textSize.height)
    context.drawLinearGradient(
      glossGradient!, start: topCenter, end: bottomCenter,
      options: CGGradientDrawingOptions.drawsBeforeStartLocation)

    UIGraphicsPopContext()

    guard let gradientImage = UIGraphicsGetImageFromCurrentImageContext() else {
      UIGraphicsEndImageContext()
      return
    }

    UIGraphicsEndImageContext()

    self.textColor = UIColor(patternImage: gradientImage)

    return
  }

}
