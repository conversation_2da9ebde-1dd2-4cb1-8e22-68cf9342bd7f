import Foundation

extension UIImage {

  func imageToCircle(image: UIImage) -> UIImage {
    let width = image.size.width
    let height = image.size.height
    let redius = ((width <= height) ? width : height) / 2
    let rect = CGRect(
      x: width / 2 - redius, y: height / 2 - redius, width: redius * 2, height: redius * 2)
    let sourceImageRef = image.cgImage
    let newImageRef = sourceImageRef?.cropping(to: rect)
    let newImage = UIImage(cgImage: newImageRef!)
    if newImage.size.width <= 0 || newImage.size.height <= 0 { return image }
    UIGraphicsBeginImageContextWithOptions(
      CGSize(width: newImage.size.width, height: newImage.size.height), false, 0)
    let path = UIBezierPath(
      arcCenter: CGPoint(x: newImage.size.width / 2, y: newImage.size.height / 2), radius: redius,
      startAngle: 0, endAngle: CGFloat(Double.pi * 2.0), clockwise: false)
    path.addClip()
    newImage.draw(at: CGPoint.zero)
    let imageCut = UIGraphicsGetImageFromCurrentImageContext()
    return imageCut!
  }

  func reSizeImage(reSize: CGSize) -> UIImage {
    if reSize.width <= 0 || reSize.height <= 0 { return self }
    UIGraphicsBeginImageContextWithOptions(reSize, false, UIScreen.main.scale)
    self.draw(in: CGRect(x: 0, y: 0, width: reSize.width, height: reSize.height))
    let reSizeImage: UIImage = UIGraphicsGetImageFromCurrentImageContext()!
    UIGraphicsEndImageContext()
    return reSizeImage
  }

}
@objc extension UIImage {

  convenience init?(color: UIColor, size: CGSize = CGSize(width: 1, height: 1)) {
    if size.width <= 0 || size.height <= 0 { return nil }
    let rect = CGRect(origin: .zero, size: size)
    UIGraphicsBeginImageContextWithOptions(rect.size, false, 0.0)
    color.setFill()
    UIRectFill(rect)
    let image = UIGraphicsGetImageFromCurrentImageContext()
    UIGraphicsEndImageContext()

    guard let cgImage = image?.cgImage else { return nil }
    self.init(cgImage: cgImage)
  }
}
