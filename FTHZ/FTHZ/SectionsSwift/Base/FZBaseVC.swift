import SnapKit

class FZBaseVC: UIViewController {
  var contentView: UIView!

  private var isNotchScreen: Bool {
    if #available(iOS 11.0, *) {
      let window = UIApplication.shared.windows.first
      return window?.safeAreaInsets.top ?? 0 > 20
    }
    return false
  }

  func setupContentView() {
    contentView = UIView()
    contentView.backgroundColor = .clear
    view.addSubview(contentView)
    contentView.snp.makeConstraints { (make) in
      if isNotchScreen {
        make.edges.equalToSuperview()
      } else {
        if #available(iOS 11, *) {
          make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
          make.left.right.bottom.equalToSuperview()
        } else {
          make.edges.equalToSuperview()
        }
      }
    }
    view.backgroundColor = .white
  }

  func showMsgFast(_ msg: String) {
    view.makeToast(msg, duration: 1.0, position: CSToastPositionCenter)
  }

  func showMsgWithTime(_ msg: String, _ time: Double) {
    view.makeToast(msg, duration: time, position: CSToastPositionCenter)
  }

  override func viewDidLoad() {
    super.viewDidLoad()
    setupContentView()
  }
}
