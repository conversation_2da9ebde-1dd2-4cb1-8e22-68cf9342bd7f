#import "DynamicDetailVC.h"
#import "AffairCommentsModel.h"
#import "AffairDetailModel.h"
#import "CenterTouchTableView.h"
#import "DoCommentModel.h"
#import "DoLikeModel.h"
#import "DynamicCommentVC.h"
#import "DynamicHeaderView.h"
#import "DynamicLikeVC.h"
#import "DynamicSegmentView.h"
#import "PopoverView.h"
#import "ReportModel.h"
#import "ReportVC.h"
#import "SettingVC.h"
#import "UIView+MJExtension.h"
#import "UserInformationVC.h"
#import "WhaleDetailVC.h"

#import "BlackUserModel.h"
#import "DeleteDynamicModel.h"

#import "XHInputView.h"

@interface DynamicDetailVC () <UITableViewDelegate, UITableViewDataSource,
                               UIGestureRecognizerDelegate, XHInputViewDelagete,
                               DCommentDelegate> {
  CGFloat upHeight;
}
@property(nonatomic, strong) CenterTouchTableView *mainTableView;
@property(nonatomic, strong) DynamicSegmentView *segmentView;
@property(nonatomic, strong) UIView *naviView;
@property(nonatomic, strong) UIImageView *headerImageView;
@property(nonatomic, strong) UIImageView *headerContentView;
@property(nonatomic, strong) UIImageView *headerRightImageView;
@property(nonatomic, strong) DynamicHeaderView *dynamicHeaderView;
@property(nonatomic, assign) BOOL canScroll; // mainTableView是否可以滚动
@property(nonatomic, assign) BOOL isBacking; // 是否正在pop
@property(nonatomic, strong) UILabel *titleLabel;
@property(nonatomic, strong) UILabel *userDetail;
@property(nonatomic, strong) AffairResult *affairDatail;

// 评论相关

@property(nonatomic, strong) FlatButton *comBtn;
@property(nonatomic, strong) UIButton *messageButton;
@end

@implementation DynamicDetailVC
- (void)loadAffairData {
  @weakify(self);
  [AffairDetailModel
      getAffairDetail:self.ddy.affair.aid
             authorid:self.ddy.user.uid
                 from:@"1"
              success:^(NSDictionary *resultObject) {
                @strongify(self);
                AffairDetailModel *member =
                    [AffairDetailModel mj_objectWithKeyValues:resultObject];
                if ([member.success boolValue]) {
                  self.affairDatail = [AffairResult
                      mj_objectWithKeyValues:member.data.firstObject];
                  self.affairDatail.aid = self.ddy.affair.aid;
                  [self.dynamicHeaderView loadHeaderData:self.affairDatail];
                  [self.mainTableView reloadData];
                  if (self.isTouchComment) {
                    self.isTouchComment = NO;
                    [self performSelector:@selector(doCommentAction)
                               withObject:nil
                               afterDelay:0.5];
                  }
                } else {
                  [self backAction];
                }
              }
              failure:^(NSError *requestErr){

              }];
}

// 评论
- (void)loaddoCommentAction:(NSString *)sendText sendIM:(BOOL)sendIM {
  __weak typeof(self) wSelf = self;
  [HUD show];
  [DoCommentModel postDoCommentModel:self.ddy.user.uid
      contentid:self.ddy.affair.aid
      comment:sendText
      to_userid:self.ddy.user.uid
      to_commentid:@"0"
      belongs:@"0"
      success:^(NSDictionary *resultObject) {
        DoCommentModel *member =
            [DoCommentModel mj_objectWithKeyValues:resultObject];
        [HUD dissmiss];
        if ([member.success boolValue]) {
          // 清除文本框内容
          if ([[USERDEFAULT objectForKey:ContenText] isValid]) {
            NSString *contentText = @"";
            [USERDEFAULT setObject:contentText forKey:ContenText];
            [USERDEFAULT synchronize];
          }

          [wSelf showToastFast:@"评论成功"];
          [wSelf loadAffairData];
          if (![self.ddy.user.uid isEqualToString:CurrentUser.userid] &&
              sendIM == YES) {
            NSString *imgTempstr = @"";
            if ([wSelf.affairDatail.images isEqualToString:@""]) {
              imgTempstr = @"null";
            } else {
              imgTempstr = wSelf.affairDatail.images;
            }
            NSString *contentTempstr = @"";
            if ([wSelf.affairDatail.content isEqualToString:@""]) {
              contentTempstr = @"分享图片";
            } else {
              contentTempstr = wSelf.affairDatail.content;
            }
          }

          [NOTIFICENTER postNotificationName:TacthBtnofCommentForSeaReload
                                      object:nil];

        } else if ([member.code integerValue] == FTHZErrorCodeAccountBanned) {
          ShowBanTip(member.msg);
        } else {
          [wSelf showToastFast:member.msg];
        }
      }
      failure:^(NSError *requestErr) {
        [HUD dissmiss];
        [wSelf showToastFast:@"数据有误,请检查网络后重试"];
      }];
}

// 举报
- (void)loadReportData:(NSString *)report_type detail:(NSString *)detail {
  __weak typeof(self) wSelf = self;
  [ReportModel postReportModel:self.ddy.user.uid
      report_type:report_type
      type:@"1"
      rid:self.ddy.affair.aid
      detail:detail
      success:^(NSDictionary *resultObject) {
        ReportModel *member = [ReportModel mj_objectWithKeyValues:resultObject];
        if ([member.success boolValue]) {
          [wSelf showToastFast:@"举报成功"];
        } else {
          [wSelf showToastFast:member.msg];
        }
      }
      failure:^(NSError *requestErr) {
        [wSelf showToastFast:@"举报失败,请重试"];
      }];
}

// 删除动态
- (void)loadDelDy {
  __weak typeof(self) wSelf = self;
  [DeleteDynamicModel postDeleteDynamicModel:self.ddy.affair.aid
      success:^(NSDictionary *resultObject) {
        DeleteDynamicModel *member =
            [DeleteDynamicModel mj_objectWithKeyValues:resultObject];
        if ([member.success boolValue]) {
          [wSelf showToastFast:@"删除成功"];
          [NOTIFICENTER postNotificationName:UserHomeReload object:nil];
          [wSelf.delegate dyrightMoreAction];
          [wSelf performSelector:@selector(backAction)
                      withObject:nil
                      afterDelay:1.0];
        } else {
          [wSelf showToastFast:member.msg];
        }
      }
      failure:^(NSError *requestErr) {
        [wSelf showToastFast:@"删除失败,请重试"];
      }];
}

// 拉黑用户
- (void)loadLaheiUser {
  __weak typeof(self) wSelf = self;
  [BlackUserModel postBlackUserModel:self.ddy.user.uid
      success:^(NSDictionary *resultObject) {
        BlackUserModel *member =
            [BlackUserModel mj_objectWithKeyValues:resultObject];
        if ([member.success boolValue]) {
          [wSelf showToastFast:@"拉黑成功"];
          [wSelf.delegate dyrightMoreAction];
          [wSelf performSelector:@selector(backAction)
                      withObject:nil
                      afterDelay:1.0];
        } else {
          [wSelf showToastFast:member.msg];
        }
      }
      failure:^(NSError *requestErr) {
        [wSelf showToastFast:@"拉黑失败,请重试"];
      }];
}
#pragma mark Life Cycle
- (void)viewDidLoad {
  [super viewDidLoad];
  [UIApplication sharedApplication].applicationSupportsShakeToEdit = NO;
  CGSize tempSize = [NemoUtil
      calculateLabelHeightByText:[UIFont systemFontOfSize:14 * kMainTemp]
                           width:252 * kMainTemp
                       heightMax:kMainHeight * 10
                         content:self.ddy.affair.content];
  CGFloat tempheight =
      94 * kMainTemp + tempSize.height + 2 +
      [NemoUtil
          getHZPhotoHeight:[NemoUtil
                               getHZPhotoStringCount:self.ddy.affair.images]
                     index:[self.ddy.affair.imageType integerValue]];
  if ([self.ddy.affair.musicContent isKindOfClass:[MusicInfoData class]]) {
    tempheight += 40 + 12 * kMainTemp;
  }
  upHeight = tempheight;

  self.isEnlarge = YES;
  if (@available(iOS 11.0, *)) {
    [[UIScrollView appearance] setContentInsetAdjustmentBehavior:
                                   UIScrollViewContentInsetAdjustmentNever];
  } else {
    self.automaticallyAdjustsScrollViewInsets = NO;
  }
  // 如果使用自定义的按钮去替换系统默认返回按钮，会出现滑动返回手势失效的情况
  self.navigationController.interactivePopGestureRecognizer.delegate = self;
  [self setupSubViews];
  // 注册允许外层tableView滚动通知-解决和分页视图的上下滑动冲突问题
  [[NSNotificationCenter defaultCenter] addObserver:self
                                           selector:@selector(acceptMsg:)
                                               name:@"leaveTop"
                                             object:nil];
  // 分页的scrollView左右滑动的时候禁止mainTableView滑动，停止滑动的时候允许mainTableView滑动
  [[NSNotificationCenter defaultCenter]
      addObserver:self
         selector:@selector(acceptMsg:)
             name:IsEnablePersonalCenterVCMainTableViewScroll
           object:nil];

  [self loadAffairData];
}

- (void)reloadDyAffair {
  [self loadAffairData];
}

- (void)showXHInputViewWithStyle:(InputViewStyle)style {
  __weak typeof(self) wSelf = self;
  [XHInputView showWithStyle:style
      configurationBlock:^(XHInputView *inputView) {
        /** 请在此block中设置inputView属性 */
        /** 代理 */
        inputView.delegate = self;
        /** 占位符文字 */
        inputView.placeholder = @"呼应TA的声波";
        /** 设置最大输入字数 */
        inputView.maxCount = 2000;
        /** 输入框颜色 */
        inputView.textViewBackgroundColor =
            [UIColor groupTableViewBackgroundColor];
        /** 更多属性设置,详见XHInputView.h文件 */
      }
      sendBlock:^BOOL(NSString *text, BOOL selTemp) {
        if (text.length) {
          [wSelf loaddoCommentAction:text sendIM:selTemp];
          //            _textLab.text = text;
          return YES; // return YES,收起键盘
        } else {
          return NO; // return NO,不收键盘
        }
      }];
}
#pragma mark - XHInputViewDelagete
/** XHInputView 将要显示 */
- (void)xhInputViewWillShow:(XHInputView *)inputView {
  /** 如果你工程中有配置IQKeyboardManager,并对XHInputView造成影响,请在XHInputView将要显示时将其关闭 */
  //    [IQKeyboardManager sharedManager].enableAutoToolbar = NO;
  //    [IQKeyboardManager sharedManager].enable = NO;
}

/** XHInputView 将要影藏 */
- (void)xhInputViewWillHide:(XHInputView *)inputView {

  /** 如果你工程中有配置IQKeyboardManager,并对XHInputView造成影响,请在XHInputView将要影藏时将其打开 */

  //[IQKeyboardManager sharedManager].enableAutoToolbar = YES;
  //[IQKeyboardManager sharedManager].enable = YES;
}

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];
  [self.navigationController setNavigationBarHidden:YES animated:YES];
  //    self.naviView.hidden = NO;
  //    [UIApplication sharedApplication].statusBarStyle =
  //    UIStatusBarStyleDefault;
  [AppConfig statusbarStyle:YES];
}

- (void)viewDidAppear:(BOOL)animated {
  [super viewDidAppear:animated];
  self.isBacking = NO;
  [[NSNotificationCenter defaultCenter]
      postNotificationName:PersonalCenterVCBackingStatus
                    object:nil
                  userInfo:@{@"isBacking" : @(self.isBacking)}];
}

- (void)viewWillDisappear:(BOOL)animated {
  [super viewWillDisappear:animated];
  self.isBacking = YES;
  [self.navigationController setNavigationBarHidden:YES animated:YES];
  [[NSNotificationCenter defaultCenter]
      postNotificationName:PersonalCenterVCBackingStatus
                    object:nil
                  userInfo:@{@"isBacking" : @(self.isBacking)}];
}

- (void)viewDidDisappear:(BOOL)animated {
  [super viewDidDisappear:animated];
  // 清除文本框内容
  if ([[USERDEFAULT objectForKey:ContenText] isValid]) {
    NSString *contentText = @"";
    [USERDEFAULT setObject:contentText forKey:ContenText];
    [USERDEFAULT synchronize];
  }
}

- (void)dealloc {
  [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark - Private Methods
- (void)setupSubViews {
  [self.view addSubview:self.mainTableView];
  [self.view addSubview:self.naviView];

  // 背景
  _headerImageView = [[UIImageView alloc] init];
  _headerImageView.userInteractionEnabled = YES;
  _headerImageView.backgroundColor = KColor_White;
  _headerImageView.frame = CGRectMake(0, -upHeight, kMainWidth, upHeight);
  [self.mainTableView addSubview:self.headerImageView];

  self.dynamicHeaderView = [[DynamicHeaderView alloc] init];
  @weakify(self);
  self.dynamicHeaderView.tapIconAction = ^{
    @strongify(self);
    WhaleDetailVC *infoVC = [[WhaleDetailVC alloc] init];
    infoVC.uid = self.ddy.user.uid;
    [self.navigationController pushViewController:infoVC animated:YES];
  };
  [self.dynamicHeaderView.awesomeBtn addTarget:self
                                        action:@selector(doLikeAction)
                              forControlEvents:UIControlEventTouchUpInside];
  [self.dynamicHeaderView.commentBtn addTarget:self
                                        action:@selector(doCommentAction)
                              forControlEvents:UIControlEventTouchUpInside];
  [self.mainTableView addSubview:self.dynamicHeaderView];
  [self.dynamicHeaderView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.bottom.centerX.mas_equalTo(self.headerImageView);
    make.width.mas_equalTo(kMainWidth);
    make.height.mas_equalTo(upHeight);
  }];
}

- (void)doLikeAction {

  [self doLike:self.ddy.affair.aid uid:self.ddy.user.uid];
}

- (void)doCommentAction {
  [self showXHInputViewWithStyle:InputViewStyleDefault]; // 显示样式一
}

- (void)doLike:(NSString *)aid uid:(NSString *)uid {
  __weak typeof(self) wSelf = self;
  void (^action)(NSDictionary *) = ^(NSDictionary *resultObject) {
    DoLikeModel *member = [DoLikeModel mj_objectWithKeyValues:resultObject];
    if ([member.success boolValue]) {
      if ([wSelf.ddy.affair.likeRs isEqualToString:@"0"]) {
        NSInteger tepLike = [wSelf.ddy.affair.likeNum integerValue];
        tepLike++;
        wSelf.ddy.affair.likeNum = [NSString stringWithFormat:@"%ld", tepLike];
        self.affairDatail.likeNum = [NSString stringWithFormat:@"%ld", tepLike];
        wSelf.ddy.affair.likeRs = @"1";
      } else {
        NSInteger tepLike = [wSelf.ddy.affair.likeNum integerValue];
        tepLike--;
        wSelf.ddy.affair.likeNum = [NSString stringWithFormat:@"%ld", tepLike];
        self.affairDatail.likeNum = [NSString stringWithFormat:@"%ld", tepLike];
        wSelf.ddy.affair.likeRs = @"0";
      }
      if ([wSelf.ddy.affair.likeRs isEqualToString:@"0"]) {
        [self.dynamicHeaderView.awesomeBtn setImage:KImage_name(@"Unlike")
                                           forState:UIControlStateNormal];
      } else {
        [self.dynamicHeaderView.awesomeBtn setImage:KImage_name(@"like")
                                           forState:UIControlStateNormal];
      }
      //            [cell.contentView addSubview:self.setPageViewControllers];
      self.selectedIndex = 1;
      [self.mainTableView reloadData];
      self.dynamicHeaderView.awesomeLabel.text = wSelf.ddy.affair.likeNum;
      [NOTIFICENTER postNotificationName:ToutchLikeReloadOfAffair object:nil];
    } else if ([member.code integerValue] == FTHZErrorCodeAccountBanned) {
      ShowBanTip(member.msg);
    } else {
      [wSelf.view makeToast:member.msg
                   duration:1.0
                   position:CSToastPositionCenter];
    }
  };

  if (self.ddy.affair.likeRs == nil && self.affairDatail.likeRs) {
    // 信息不全的情况
    self.ddy.affair = self.affairDatail;
  }
  if ([self.ddy.affair upvoted]) {
    [DoLikeModel postUnlikeModel:uid
                       contentid:aid
                         success:action
                         failure:^(NSError *requestErr) {
                           [self.view makeToast:@"数据有误,请检查网络后重试"
                                       duration:2.0
                                       position:CSToastPositionCenter];
                         }];
  } else {
    [DoLikeModel postLikeModel:uid
                     contentid:aid
                       success:action
                       failure:^(NSError *requestErr) {
                         [self.view makeToast:@"数据有误,请检查网络后重试"
                                     duration:2.0
                                     position:CSToastPositionCenter];
                       }];
  }
}

#pragma mark - TargetAction
- (void)goChangeInformation {
  UserInformationVC *vc = [[UserInformationVC alloc] init];
  [self.navigationController pushViewController:vc animated:YES];
}

- (void)goSetting {
  SettingVC *vc = [[SettingVC alloc] init];
  [self.navigationController pushViewController:vc animated:YES];
}

- (void)backAction {
  if (self.isPoptoPre) {
    [self.navigationController popViewControllerAnimated:YES];
  } else {
    [self.navigationController popToRootViewControllerAnimated:YES];
  }
}

- (void)gotoMessagePage:(UIButton *)sender {
  PopoverView *popoverView = [PopoverView popoverView];
  popoverView.showShade = YES; // 显示阴影背景
  [popoverView showToView:sender withActions:[self QQActions]];
}
- (NSArray<PopoverAction *> *)QQActions {
  if (![CurrentUser.userid isEqualToString:self.ddy.user.uid]) {
    PopoverAction *multichatAction = [PopoverAction
        actionWithImage:[UIImage imageNamed:@"Report"]
                  title:@"举报"
                handler:^(PopoverAction *action) {
                  ReportVC *ddVC = [[ReportVC alloc] init];
                  ddVC.delegate = self;
                  [self.navigationController pushViewController:ddVC
                                                       animated:YES];
                }];
    PopoverAction *addFriAction = [PopoverAction
        actionWithImage:[UIImage imageNamed:@"more-lahei"]
                  title:@"拉黑"
                handler:^(PopoverAction *action) {
                  UIAlertController *alertController = [UIAlertController
                      alertControllerWithTitle:@"确定拉黑该用户?"
                                       message:@"拉黑后你们将互相看不到对方的动"
                                               @"态。如需解除，请在设置-"
                                               @"黑名单管理中移除黑名单。"
                                preferredStyle:UIAlertControllerStyleAlert];
                  UIAlertAction *cancelAction =
                      [UIAlertAction actionWithTitle:@"取消"
                                               style:UIAlertActionStyleCancel
                                             handler:nil];
                  __weak typeof(self) wSelf = self;
                  UIAlertAction *okAction = [UIAlertAction
                      actionWithTitle:@"确定"
                                style:UIAlertActionStyleDefault
                              handler:^(UIAlertAction *_Nonnull action) {
                                [wSelf loadLaheiUser];
                              }];

                  [alertController addAction:cancelAction];
                  [alertController addAction:okAction];
                  [self presentViewController:alertController
                                     animated:YES
                                   completion:nil];
                }];

    return @[ multichatAction, addFriAction ];
  } else {
    PopoverAction *deleteAction = [PopoverAction
        actionWithImage:[UIImage imageNamed:@"more-delete"]
                  title:@"删除"
                handler:^(PopoverAction *action) {
                  UIAlertController *alertController = [UIAlertController
                      alertControllerWithTitle:@"提示"
                                       message:@"确定删除该动态?"
                                preferredStyle:UIAlertControllerStyleAlert];
                  UIAlertAction *cancelAction =
                      [UIAlertAction actionWithTitle:@"取消"
                                               style:UIAlertActionStyleCancel
                                             handler:nil];
                  __weak typeof(self) wSelf = self;
                  UIAlertAction *okAction = [UIAlertAction
                      actionWithTitle:@"确定"
                                style:UIAlertActionStyleDefault
                              handler:^(UIAlertAction *_Nonnull action) {
                                [wSelf loadDelDy];
                              }];

                  [alertController addAction:cancelAction];
                  [alertController addAction:okAction];
                  [self presentViewController:alertController
                                     animated:YES
                                   completion:nil];
                }];
    return @[ deleteAction ];
  }
}

- (void)acceptMsg:(NSNotification *)notification {
  NSDictionary *userInfo = notification.userInfo;

  if ([notification.name isEqualToString:@"leaveTop"]) {
    NSString *canScroll = userInfo[@"canScroll"];
    if ([canScroll isEqualToString:@"1"]) {
      self.canScroll = YES;
    }
  } else if ([notification.name
                 isEqualToString:IsEnablePersonalCenterVCMainTableViewScroll]) {
    NSString *canScroll = userInfo[@"canScroll"];
    if ([canScroll isEqualToString:@"1"]) {
      self.mainTableView.scrollEnabled = YES;
    } else if ([canScroll isEqualToString:@"0"]) {
      self.mainTableView.scrollEnabled = NO;
    }
  }
}

#pragma mark - UiScrollViewDelegate
- (BOOL)scrollViewShouldScrollToTop:(UIScrollView *)scrollView {
  // 通知分页子控制器列表返回顶部
  [[NSNotificationCenter defaultCenter]
      postNotificationName:SegementViewChildVCBackToTop
                    object:nil];
  return YES;
}

/**
 * 处理联动
 * 因为要实现下拉头部放大的问题，tableView设置了contentInset，所以试图刚加载的时候会调用一遍这个方法，所以要做一些特殊处理，
 */
- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
  // 当前y轴偏移量
  CGFloat currentOffsetY = scrollView.contentOffset.y;
  // 临界点偏移量
  CGFloat criticalPointOffsetY =
      [self.mainTableView rectForSection:0].origin.y - STATUS_BAR_HEIGHT -
      kTabarHeight;

  // 第一部分: 更改导航栏的背景图的透明度
  //     CGFloat alpha = 0;
  //     if (-currentOffsetY <= STATUS_BAR_HEIGHT + kTabarHeight) {
  //         alpha = 1;
  //     } else if ((-currentOffsetY > STATUS_BAR_HEIGHT + kTabarHeight) &&
  //     -currentOffsetY < 348*kMainTemp) {
  //         alpha = (348*kMainTemp + currentOffsetY) / (348*kMainTemp -
  //         STATUS_BAR_HEIGHT - kTabarHeight);
  //     } else {
  //         alpha = 0;
  //     }
  //     self.naviView.backgroundColor = RGBA(255, 255, 255, alpha);

  // 第二部分：
  // 利用contentOffset处理内外层scrollView的滑动冲突问题
  if (currentOffsetY >= criticalPointOffsetY) {
    scrollView.contentOffset = CGPointMake(0, criticalPointOffsetY);
    [[NSNotificationCenter defaultCenter]
        postNotificationName:@"goTop"
                      object:nil
                    userInfo:@{@"canScroll" : @"1"}];
    self.canScroll = NO;
  } else {
    if (!self.canScroll) {
      scrollView.contentOffset = CGPointMake(0, criticalPointOffsetY);
    }
  }

  // 第三部分：
  /**
   * 处理头部自定义背景视图 (如: 下拉放大)
   * 图片会被拉伸多出状态栏的高度
   */
  if (currentOffsetY <= -upHeight) {
    if (self.isEnlarge) {
      CGRect f = self.headerImageView.frame;
      // 改变HeadImageView的frame
      // 上下放大
      f.origin.y = currentOffsetY;
      f.size.height = -currentOffsetY;
      // 左右放大
      f.origin.x = (currentOffsetY * kMainWidth / upHeight + kMainWidth) / 2;
      f.size.width = -currentOffsetY * kMainWidth / upHeight;
      // 改变头部视图的frame
      self.headerImageView.frame = f;
    } else {
      scrollView.bounces = NO;
    }
  } else {
    scrollView.bounces = YES;
  }
}

#pragma mark - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView
    numberOfRowsInSection:(NSInteger)section {
  return 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView
         cellForRowAtIndexPath:(NSIndexPath *)indexPath {
  UITableViewCell *cell =
      [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault
                             reuseIdentifier:nil];
  cell.selectionStyle = UITableViewCellSelectionStyleNone;
  [cell.contentView addSubview:self.setPageViewControllers];
  return cell;
}

#pragma mark - UITableViewDelegate
- (CGFloat)tableView:(UITableView *)tableView
    heightForRowAtIndexPath:(NSIndexPath *)indexPath {
  return kMainHeight - STATUS_BAR_HEIGHT - kTabarHeight;
}

#pragma mark - Lazy
- (UIView *)naviView {
  if (!_naviView) {
    _naviView = [[UIView alloc]
        initWithFrame:CGRectMake(0, 0, kMainWidth,
                                 STATUS_BAR_HEIGHT + kTabarHeight)];
    _naviView.backgroundColor = KColor_White;

    UILabel *wblineLabel = [[UILabel alloc] init];
    wblineLabel.backgroundColor = KColor_LineGray;
    [_naviView addSubview:wblineLabel];
    [wblineLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.bottom.mas_equalTo(self.naviView.mas_bottom)
          .offset(-0.8 * kMainTemp);
      make.left.mas_equalTo(self.naviView);
      make.size.mas_equalTo(CGSizeMake(kMainWidth, 0.8 * kMainTemp));
    }];

    // 添加返回按钮
    UIButton *backButton = [UIButton buttonWithType:(UIButtonTypeCustom)];
    [backButton setImage:[UIImage imageNamed:@"back"]
                forState:(UIControlStateNormal)];
    backButton.frame = CGRectMake(10, 8 + STATUS_BAR_HEIGHT, 20, 30);
    backButton.adjustsImageWhenHighlighted = YES;
    [backButton addTarget:self
                   action:@selector(backAction)
         forControlEvents:(UIControlEventTouchUpInside)];
    [_naviView addSubview:backButton];

    _titleLabel = [[UILabel alloc]
        initWithFrame:CGRectMake(kMainWidth * 0.25, STATUS_BAR_HEIGHT + 8,
                                 kMainWidth * 0.5, 34)];
    _titleLabel.text = @"动态详情";
    _titleLabel.textAlignment = NSTextAlignmentCenter;
    [_naviView addSubview:_titleLabel];

    // 添加消息按钮
    _messageButton = [UIButton buttonWithType:(UIButtonTypeCustom)];
    [_messageButton setImage:[UIImage imageNamed:@"more-black"]
                    forState:(UIControlStateNormal)];
    _messageButton.frame =
        CGRectMake(kMainWidth - 35, 8 + STATUS_BAR_HEIGHT, 25, 25);
    _messageButton.adjustsImageWhenHighlighted = YES;
    [_messageButton addTarget:self
                       action:@selector(gotoMessagePage:)
             forControlEvents:(UIControlEventTouchUpInside)];
    [_naviView addSubview:_messageButton];
  }
  return _naviView;
}

- (UITableView *)mainTableView {
  if (FT_IS_IPhoneX_All) {
    if (!_mainTableView) {
      // ⚠️这里的属性初始化一定要放在mainTableView.contentInset的设置滚动之前,
      // 不然首次进来视图就会偏移到临界位置，contentInset会调用scrollViewDidScroll这个方法。
      // 初始化变量
      self.canScroll = YES;

      self.mainTableView = [[CenterTouchTableView alloc]
          initWithFrame:CGRectMake(0, 44, kMainWidth,
                                   kMainHeight - self.safeAreaInset.bottom - 44)
                  style:UITableViewStylePlain];
      _mainTableView.delegate = self;
      _mainTableView.dataSource = self;
      _mainTableView.showsVerticalScrollIndicator = NO;
      [_mainTableView setSeparatorStyle:UITableViewCellSeparatorStyleNone];
      // 注意：这里不能使用动态高度_headimageHeight,
      // 不然tableView会往下移，在iphone
      // X下，头部不放大的时候，上方依然会有白色空白
      _mainTableView.contentInset = UIEdgeInsetsMake(
          upHeight + 64, 0, 0,
          0); // 内容视图开始正常显示的坐标为(0, HeaderImageViewHeight)
    }
    return _mainTableView;

  } else {
    if (!_mainTableView) {
      // ⚠️这里的属性初始化一定要放在mainTableView.contentInset的设置滚动之前,
      // 不然首次进来视图就会偏移到临界位置，contentInset会调用scrollViewDidScroll这个方法。
      // 初始化变量
      self.canScroll = YES;

      self.mainTableView = [[CenterTouchTableView alloc]
          initWithFrame:CGRectMake(0, 0, kMainWidth, kMainHeight)
                  style:UITableViewStylePlain];
      _mainTableView.delegate = self;
      _mainTableView.dataSource = self;
      _mainTableView.showsVerticalScrollIndicator = NO;
      [_mainTableView setSeparatorStyle:UITableViewCellSeparatorStyleNone];
      // 注意：这里不能使用动态高度_headimageHeight,
      // 不然tableView会往下移，在iphone
      // X下，头部不放大的时候，上方依然会有白色空白
      _mainTableView.contentInset = UIEdgeInsetsMake(
          upHeight + 64, 0, 0,
          0); // 内容视图开始正常显示的坐标为(0, HeaderImageViewHeight)
    }
    return _mainTableView;
  }
}

/*
 * 这里可以设置替换你喜欢的segmentView
 */
- (UIView *)setPageViewControllers {
  if (_segmentView) {
    [_segmentView removeFromSuperview];
    _segmentView = nil;
  }
  if (!_segmentView) {
    // 设置子控制器
    //         MyDynamicVC *firstVC  = [[MyDynamicVC alloc] init];
    DynamicLikeVC *secondVC = [[DynamicLikeVC alloc] init];
    secondVC.aid = self.ddy.affair.aid;
    DynamicCommentVC *thirdVC = [[DynamicCommentVC alloc] init];
    thirdVC.aid = self.ddy.affair.aid;
    thirdVC.uid = self.ddy.user.uid;
    thirdVC.delegate = self;
    NSArray *controllers = @[ thirdVC, secondVC ];
    if (self.affairDatail) {
      NSArray *titleArray =
          @[ self.affairDatail.commentNum, self.affairDatail.likeNum ];
      DynamicSegmentView *segmentView = [[DynamicSegmentView alloc]
             initWithFrame:CGRectMake(0, 0, kMainWidth,
                                      kMainHeight - STATUS_BAR_HEIGHT -
                                          kTabarHeight)
               controllers:controllers
                titleArray:(NSArray *)titleArray
          parentController:self];
      // 注意：不能通过初始化方法传递selectedIndex的初始值，因为内部使用的是Masonry布局的方式,
      // 否则设置selectedIndex不起作用
      segmentView.selectedIndex = self.selectedIndex;
      _segmentView = segmentView;
    } else {
      NSArray *titleArray = @[ @"0", @"0" ];
      DynamicSegmentView *segmentView = [[DynamicSegmentView alloc]
             initWithFrame:CGRectMake(0, 0, kMainWidth,
                                      kMainHeight - STATUS_BAR_HEIGHT -
                                          kTabarHeight)
               controllers:controllers
                titleArray:(NSArray *)titleArray
          parentController:self];
      // 注意：不能通过初始化方法传递selectedIndex的初始值，因为内部使用的是Masonry布局的方式,
      // 否则设置selectedIndex不起作用
      segmentView.selectedIndex = self.selectedIndex;
      _segmentView = segmentView;
    }
  }
  return _segmentView;
}

- (void)delegateGetReport:(NSInteger)RelationshipType
                   detail:(NSString *)detail {
  [self loadReportData:[NSString stringWithFormat:@"%ld", RelationshipType]
                detail:detail];
}

@end
