#import "DynamicVC.h"
#import "AffairListModel.h"
#import "AttentionTableViewCell.h"
#import "DJStatusBarHUD.h"
#import "DoLikeModel.h"
#import "DynamicDetailVC.h"
#import "DynamicTableViewCell.h"
#import "FTHZLocationManager.h"
#import "FTHZMusicPlayerView.h"
#import "LoginBusiness.h"
#import "POSTIPStatusModel.h"
#import "TagAffairVC.h"
#import "UIView+MJExtension.h"
#import "UserUserinfoModel.h"
#import "WhaleDetailVC.h"
#import "_2hz-Swift.h"
#import <YYCache.h>

#define DynamicTableViewCellID @"DynamicTableViewCellID"
#define DynamicListCache @"DynamicListCache_ID"
#define DynamicRecommendListCache @"DynamicRecommendListCache_ID"
#define POST_ICON @"post_icon"

#define kRealWidth(width) (width * kMainTemp)

static NSString *const kDynamicListCache = @"DynamicListCache_ID";
static NSString *const kDynamicRecommendListCache =
    @"DynamicRecommendListCache_ID";
static NSString *const kDynamicTableViewCellID = @"DynamicTableViewCellID";

@interface FTHZDynamicCacheManager : NSObject

@property(nonatomic, strong, readonly) YYCache *cache;

+ (instancetype)shared;
- (void)saveRecommendList:(NSString *)jsonString;
- (NSString *)getRecommendList;
- (void)clearCache;

@end

@implementation FTHZDynamicCacheManager

+ (instancetype)shared {
  static FTHZDynamicCacheManager *instance = nil;
  static dispatch_once_t onceToken;
  dispatch_once(&onceToken, ^{
    instance = [[FTHZDynamicCacheManager alloc] init];
  });
  return instance;
}

- (instancetype)init {
  self = [super init];
  if (self) {
    _cache = [YYCache cacheWithName:kDynamicListCache];
  }
  return self;
}

- (void)saveRecommendList:(NSString *)jsonString {
  if (!jsonString)
    return;
  dispatch_async(
      dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        [self.cache setObject:jsonString forKey:kDynamicRecommendListCache];
      });
}

- (NSString *)getRecommendList {
  return [self.cache objectForKey:kDynamicRecommendListCache];
}

- (void)clearCache {
  [self.cache removeAllObjects];
}

@end

@interface DynamicVC () <UITableViewDelegate, UITableViewDataSource,
                         DynamicDelegate>

@property(nonatomic, strong) NSMutableArray *cellData;
@property(nonatomic, strong) NSMutableSet *oldIdSet;
@property(nonatomic, strong) UITableView *tableView;

@property(nonatomic, assign) BOOL showHud;
@property(nonatomic, strong) FTHZMusicPlayerBanner *banner;
@property(nonatomic, assign) int pageNum;
@property(nonatomic, assign) int hertzNum;
@property(nonatomic, copy) NSString *lastId;

@property(nonatomic, assign) BOOL isRefreshing;
@property(nonatomic, assign) BOOL isDataLoaded;
@property(nonatomic, assign) BOOL isRandoming;
@property(nonatomic, assign) int randomTryCount;
@property(nonatomic, assign) BOOL isFirstLoad;

@property(nonatomic, strong) dispatch_queue_t dataQueue;

@property(nonatomic, strong) CLLocation *latestLocation;
@property(nonatomic, copy) NSString *latestLocationName;
@property(nonatomic, assign) BOOL locationHasBeenArrived;
@property(nonatomic, copy) void (^watingLocationAction)(void);

- (void)handleUnauthorizedAccess;
- (void)handleRequestError:(NSError *)error;
- (void)handleSuccessResponse:(AffairListModel *)member
                      hudType:(NSInteger)hudType
                   isLoadMore:(BOOL)isLoadMore;
- (void)handleFailureResponse:(AffairListModel *)member;

@end

@implementation DynamicVC

- (void)handleUnauthorizedAccess {
  [AccountManager logoutWithCompletion:^(UserPersonResult *_Nullable user,
                                         NSError *_Nullable error) {
    [FTHZBusiness loginThenAction:^(FTHZLoginResult result,
                                    UserPersonResult *_Nullable user,
                                    NSError *_Nullable error){

    }];
  }];
}

- (void)handleRequestError:(NSError *)error {
  [self.tableView.mj_header endRefreshing];
  [self.tableView.mj_footer endRefreshing];
  self.isRefreshing = NO;
  [self.view makeToast:@"数据有误,请检查网络后重试"
              duration:2.0
              position:CSToastPositionCenter];
}

- (void)handleSuccessResponse:(AffairListModel *)member
                      hudType:(NSInteger)hudType
                   isLoadMore:(BOOL)isLoadMore {
  NewDynamicModelResult *tempMember =
      [NewDynamicModelResult mj_objectWithKeyValues:member.data.firstObject];

  if (tempMember.data.count == 0) {
    [self checkAutoJumpHerzDynmic];
  } else {
    [self.tableView.mj_header endRefreshing];
    [self.tableView.mj_footer endRefreshing];

    if (self.hertzNum < 10 && tempMember.data.count > 0) {
      [[FTHZDynamicCacheManager shared]
          saveRecommendList:[tempMember mj_JSONString]];
    }

    if (self.isRandoming) {
      [self handleRandomingSuccess];
    }

    if (member.lastId.length > 0) {
      self.lastId = member.lastId;
    } else {
    }

    [self setupDataForTable:[tempMember mj_JSONString]
                        hud:hudType
                       from:YES
                 isLoadMore:isLoadMore];
  }
}

- (void)handleRandomingSuccess {
  self.isRandoming = NO;
  self.isDataLoaded = YES;
  self.showHud = NO;
}

- (void)handleFailureResponse:(AffairListModel *)member {
  if (self.hertzNum < 11 || !self.isDataLoaded) {
    [self checkAutoJumpHerzDynmic];
  } else {
    [self.tableView.mj_header endRefreshing];
    [self.tableView.mj_footer endRefreshing];
    [self.view makeToast:member.msg
                duration:1.0
                position:CSToastPositionCenter];
  }
}

- (void)refreshByHerz:(int)herz after:(CGFloat)time {
  self.hertzNum = herz;
  dispatch_after(dispatch_time(DISPATCH_TIME_NOW, time * NSEC_PER_SEC),
                 dispatch_get_main_queue(), ^{
                   [self.tableView.mj_header beginRefreshing];
                 });
}

- (void)loadOfData:(NSInteger)hudType isLoadMore:(BOOL)isLoadMore {
  if (!self.cellData) {
    self.cellData = [NSMutableArray new];
    self.oldIdSet = [NSMutableSet new];
  }

  if (self.isRefreshing && !self.isRandoming) {
    return;
  }

  @weakify(self);
  self.isRefreshing = YES;
  NSString *lastIdToUse = isLoadMore ? self.lastId : nil;

  void (^performFetchBlock)(void) = ^{
    @strongify(self);
    NSNumber *currentLat =
        self.latestLocation ? @(self.latestLocation.coordinate.latitude) : nil;
    NSNumber *currentLon =
        self.latestLocation ? @(self.latestLocation.coordinate.longitude) : nil;
    NSString *currentLocationName = self.latestLocationName;

    [AffairListModel getAffairListNewModel:lastIdToUse
        herzNum:self.hertzNum
        lat:currentLat
        lon:currentLon
        location:currentLocationName
        success:^(NSDictionary *resultObject) {
          @strongify(self);
          dispatch_async(self.dataQueue, ^{
            AffairListModel *member =
                [AffairListModel mj_objectWithKeyValues:resultObject];
            dispatch_async(dispatch_get_main_queue(), ^{
              [self handleAffairListResponse:member
                                     hudType:hudType
                                  isLoadMore:isLoadMore];
            });
          });
        }
        failure:^(NSError *requestErr) {
          @strongify(self);
          dispatch_async(dispatch_get_main_queue(), ^{
            [self handleRequestError:requestErr];
          });
        }];
  };

  if (self.locationHasBeenArrived) {
    performFetchBlock();
  } else {
    self.watingLocationAction = [performFetchBlock copy];
  }
}

- (void)handleAffairListResponse:(AffairListModel *)member
                         hudType:(NSInteger)hudType
                      isLoadMore:(BOOL)isLoadMore {
  if ([member.code intValue] == FTHZErrorCodeUnlogin ||
      [member.code intValue] == FTHZErrorCodePermissionDenied) {
    [self handleUnauthorizedAccess];
    return;
  }

  if ([member.success boolValue]) {
    [self handleSuccessResponse:member hudType:hudType isLoadMore:isLoadMore];
  } else {
    [self handleFailureResponse:member];
  }

  self.isRefreshing = NO;
}

- (int)randomValueBetween:(int)min and:(int)max {
  return (int)(min + arc4random_uniform(max - min + 1));
}

- (void)checkAutoJumpHerzDynmic {
  if (!self.isDataLoaded && (_randomTryCount == 0) && _hertzNum < 11) {
    _isRandoming = YES;
    _randomTryCount++;
    self.hertzNum = [self randomValueBetween:11 and:30];
    [self loadOfData:1 isLoadMore:NO];

  } else if (_isRandoming && _randomTryCount < 4) {
    self.hertzNum = [self randomValueBetween:30 * _randomTryCount
                                         and:30 * (1 + _randomTryCount)];
    [self loadOfData:1 isLoadMore:NO];
    _randomTryCount++;

  } else {
    [self.tableView.mj_header endRefreshing];
    [self.tableView.mj_footer endRefreshing];

    if (_randomTryCount > 3) {
      _isRandoming = NO;
    }
  }
}

- (void)setupDataForTable:(NSString *)jsonData
                      hud:(NSInteger)hudType
                     from:(BOOL)remote
               isLoadMore:(BOOL)isLoadMore {
  NewDynamicModelResult *tempMember =
      [NewDynamicModelResult mj_objectWithKeyValues:jsonData];
  if (tempMember.data.count > 0) {
    NSMutableArray *items = [[NSMutableArray alloc] init];
    if (!isLoadMore) {
      [self.cellData removeAllObjects];
      [self.oldIdSet removeAllObjects];
      [self.tableView scrollRectToVisible:CGRectMake(0, 0, 1, 1) animated:NO];
    }
    for (int i = 0; i < tempMember.data.count; i++) {
      DynamicModelResult *dy = [DynamicModelResult
          mj_objectWithKeyValues:[tempMember.data objectAtIndex:i]];
      if (dy.affair.aid.length == 0) {
        continue;
      }
      if (![self.oldIdSet containsObject:dy.affair.aid]) {
        [self.oldIdSet addObject:dy.affair.aid];
        [items addObject:dy];
      }
    }
    [self.cellData addObjectsFromArray:items];
    if (hudType == 1 && self.showHud == YES) {
      if (items.count > 0 && remote) {
        self.isFirstLoad = NO;
      }
    } else {
      self.showHud = YES;
    }

    if (self.cellData.count > 3) {
      if (!self.tableView.mj_footer) {
        MJChiBaoZiFooter *footer = [MJChiBaoZiFooter
            footerWithRefreshingTarget:self
                      refreshingAction:@selector(LoadMoreData)];
        footer.triggerAutomaticallyRefreshPercent = 6;
        footer.onlyRefreshPerDrag = YES;
        footer.stateLabel.hidden = YES;
        footer.refreshingTitleHidden = YES;
        self.tableView.mj_footer = footer;
      }
    } else {
      [self.tableView.mj_footer removeFromSuperview];
      self.tableView.mj_footer = nil;
    }
  }

  [self.tableView reloadData];
}

- (void)doLike:(NSString *)aid
           uid:(NSString *)uid
      indexRow:(NSInteger)indexRow {
  DynamicModelResult *dy = [_cellData objectAtIndex:indexRow];

  __weak typeof(self) wSelf = self;
  void (^action)(NSDictionary *) = ^(NSDictionary *resultObject) {
    DoLikeModel *member = [DoLikeModel mj_objectWithKeyValues:resultObject];
    if ([member.success boolValue]) {
      if ([dy.affair.likeRs isEqualToString:@"0"]) {
        NSInteger tepLike = [dy.affair.likeNum integerValue];
        tepLike++;
        dy.affair.likeNum = [NSString stringWithFormat:@"%ld", (long)tepLike];
        dy.affair.likeRs = @"1";
      } else {
        NSInteger tepLike = [dy.affair.likeNum integerValue];
        tepLike--;
        dy.affair.likeNum = [NSString stringWithFormat:@"%ld", (long)tepLike];
        dy.affair.likeRs = @"0";
      }

      NSMutableArray *newArray = [wSelf.cellData mutableCopy];
      [newArray replaceObjectAtIndex:indexRow withObject:dy];
      wSelf.cellData = newArray;

      NSIndexPath *indexPath = [NSIndexPath indexPathForRow:indexRow
                                                  inSection:0];
      AttentionTableViewCell *cell =
          [wSelf.tableView cellForRowAtIndexPath:indexPath];
      if (cell) {
        [cell updateLikeStatus:dy.affair.likeRs likeNum:dy.affair.likeNum];
      } else {
        [wSelf.tableView
            reloadRowsAtIndexPaths:[NSArray arrayWithObjects:indexPath, nil]
                  withRowAnimation:UITableViewRowAnimationNone];
      }

    } else if ([member.code integerValue] == FTHZErrorCodeAccountBanned) {
      ShowBanTip(member.msg);
    } else {
      [wSelf.view makeToast:member.msg
                   duration:1.0
                   position:CSToastPositionCenter];
    }
  };

  if ([dy.affair upvoted]) {
    [DoLikeModel postUnlikeModel:uid
                       contentid:aid
                         success:action
                         failure:^(NSError *requestErr) {
                           [self.view makeToast:@"数据有误,请检查网络后重试"
                                       duration:2.0
                                       position:CSToastPositionCenter];
                         }];
  } else {
    [DoLikeModel postLikeModel:uid
                     contentid:aid
                       success:action
                       failure:^(NSError *requestErr) {
                         [self.view makeToast:@"数据有误,请检查网络后重试"
                                     duration:2.0
                                     position:CSToastPositionCenter];
                       }];
  }
}

- (void)getUserHeziInfo {
  [AccountManager syncUserInfoWithCompletion:^(UserPersonResult *_Nullable user,
                                               NSError *_Nullable error){

  }];
}

- (void)viewDidLoad {
  self.isFirstLoad = YES;
  [super viewDidLoad];
  self.tableView.estimatedRowHeight = 200;
  self.tableView.backgroundColor = [UIColor whiteColor];
  self.tableView.rowHeight = UITableViewAutomaticDimension;
  self.dataQueue = dispatch_queue_create("com.52hz.dynamic.dataQueue",
                                         DISPATCH_QUEUE_SERIAL);

  self.latestLocation = nil;
  self.latestLocationName = nil;
  self.locationHasBeenArrived = NO;

  [self setupUI];
  [self setupNotifications];
  [self loadInitialData];

  @weakify(self);
  if (CurrentUserConfig.isLocationEnabled) {
    [LocationManager
        beginUpdateLocationWithIdentifier:NSStringFromClass([self class])
                              updateBlock:^(CLLocation *_Nullable location,
                                            NSError *_Nullable anError) {
                                dispatch_async(dispatch_get_main_queue(), ^{
                                  @strongify(self);
                                  self.locationHasBeenArrived = YES;

                                  if (location && !anError) {
                                    self.latestLocation = location;
                                    [LocationManager
                                        reverseGeocodeLocation:location
                                             completionHandler:^(
                                                 NSArray<CLPlacemark *>
                                                     *_Nullable placemarks,
                                                 NSError *_Nullable error) {
                                               CLPlacemark *place =
                                                   placemarks.firstObject;
                                               if (place) {
                                                 if ([place.ISOcountryCode
                                                         isEqualToString:
                                                             @"CN"]) {
                                                   self.latestLocationName =
                                                       place.locality;
                                                 } else {
                                                   self.latestLocationName =
                                                       place.country;
                                                 }
                                               } else {
                                                 self.latestLocationName = nil;
                                               }
                                               void (^action)(void) =
                                                   self.watingLocationAction;
                                               self.watingLocationAction = nil;
                                               if (action) {
                                                 action();
                                               }
                                             }];
                                  } else {
                                    self.latestLocation = nil;
                                    self.latestLocationName = nil;
                                    void (^action)(void) =
                                        self.watingLocationAction;
                                    self.watingLocationAction = nil;
                                    if (action) {
                                      action();
                                    }
                                  }
                                });
                              }];
  } else {
    self.locationHasBeenArrived = YES;
    self.latestLocation = nil;
    self.latestLocationName = nil;
  }
}

- (void)setupUI {
  if (self.mainTempScale == 0) {
    self.mainTempScale = kMainTemp;
  }
  CGFloat headerHeight = [self isNotchScreen] ? (108 * self.mainTempScale)
                                              : (84 * self.mainTempScale);

  [self.view addSubview:self.tableView];
  [self.view addSubview:self.banner];
  self.banner.canShow = NO;
  self.showHud = YES;
  self.hertzNum = 0;

  MJChiBaoZiHeader *header =
      [MJChiBaoZiHeader headerWithRefreshingTarget:self
                                  refreshingAction:@selector(LoadData)];
  header.lastUpdatedTimeLabel.hidden = YES;
  header.stateLabel.hidden = YES;
  header.ignoredScrollViewContentInsetTop = -headerHeight;
  self.tableView.mj_header = header;
  UIView *headerView = [[UIView alloc]
      initWithFrame:CGRectMake(0, 0, self.view.frame.size.width, headerHeight)];
  headerView.backgroundColor = [UIColor clearColor];
  self.tableView.tableHeaderView = headerView;
}

- (void)setupNotifications {
  [NOTIFICENTER addObserver:self
                   selector:@selector(nofLoadData)
                       name:DyreloadData
                     object:nil];
  [NOTIFICENTER addObserver:self
                   selector:@selector(sendSussessTost)
                       name:DySendSussreloadData
                     object:nil];
  [NOTIFICENTER addObserver:self
                   selector:@selector(LoadindexData)
                       name:ReloadIndexOne
                     object:nil];
}

- (void)loadInitialData {
  if (![AccountManager isLogined] ||
      ![CurrentUser.NOOType isEqualToString:@"1"]) {
    [AccountManager logoutWithCompletion:^(UserPersonResult *_Nullable user,
                                           NSError *_Nullable error) {
      [FTHZBusiness loginThenAction:^(FTHZLoginResult result,
                                      UserPersonResult *_Nullable user,
                                      NSError *_Nullable error){
      }];
    }];
    return;
  }

  NSString *cachedData = [[FTHZDynamicCacheManager shared] getRecommendList];
  if (cachedData) {
    [self setupDataForTable:cachedData hud:1 from:NO isLoadMore:NO];
  }
  [self loadOfData:1 isLoadMore:NO];
}

- (void)sharedMusicPlayerStateChanged {
  [self updateBanner];
}

- (void)viewDidLayoutSubviews {
  [super viewDidLayoutSubviews];
  self.banner.frame = CGRectMake(0, 0, CGRectGetWidth(self.view.bounds), 40);
}

- (void)sendSussessTost {
  [self.view makeToast:@"发布成功" duration:2.0 position:CSToastPositionCenter];
}
- (void)nofLoadData {
  [self loadOfData:0 isLoadMore:NO];
}

- (void)LoadindexData {
  self.showHud = NO;
  [self.tableView.mj_header beginRefreshing];
}

- (void)LoadData {
  self.lastId = nil;
  [self loadOfData:1 isLoadMore:NO];
}

- (void)LoadMoreData {
  if (self.lastId.length > 0) {
    [self loadOfData:1 isLoadMore:YES];
  } else {
    [self.tableView.mj_footer endRefreshing];
    [self.view makeToast:@"没有更多内容了"
                duration:1.0
                position:CSToastPositionCenter];
  }
}
- (UITableView *)tableView {
  if (!_tableView) {
    _tableView = [[UITableView alloc] initWithFrame:CGRectZero
                                              style:UITableViewStyleGrouped];
    _tableView.delegate = self;
    _tableView.dataSource = self;
    [_tableView registerClass:[AttentionTableViewCell class]
        forCellReuseIdentifier:DynamicTableViewCellID];
    _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;

    _tableView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:_tableView];

    [_tableView.topAnchor constraintEqualToAnchor:self.view.topAnchor].active =
        YES;
    [_tableView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor]
        .active = YES;
    [_tableView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor]
        .active = YES;
    [_tableView.bottomAnchor constraintEqualToAnchor:self.view.bottomAnchor]
        .active = YES;

    if (@available(iOS 11.0, *)) {
      _tableView.contentInsetAdjustmentBehavior =
          UIScrollViewContentInsetAdjustmentNever;
    } else {
      self.automaticallyAdjustsScrollViewInsets = NO;
    }
  }
  return _tableView;
}
#pragma mark - Table view data source
- (CGFloat)tableView:(UITableView *)tableView
    heightForFooterInSection:(NSInteger)section {
  return 40;
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForHeaderInSection:(NSInteger)section {
  if (_cellData.count == 0) {
    return kMainHeight * 0.8;
  }
  return 0;
  return 12 * kMainTemp;
}

- (UIView *)tableView:(UITableView *)tableView
    viewForFooterInSection:(NSInteger)section {
  UIView *fView =
      [[UIView alloc] initWithFrame:CGRectMake(0, 0, kMainWidth, 40)];
  return fView;
}

- (UIView *)tableView:(UITableView *)tableView
    viewForHeaderInSection:(NSInteger)section {
  if (_cellData.count == 0) {
    UIView *bgView = [[UIView alloc]
        initWithFrame:CGRectMake(0, 0, kMainWidth, kMainHeight)];
    UIImageView *empty = [[UIImageView alloc] init];
    empty.image = KImage_name(@"Bigf");
    [bgView addSubview:empty];
    [empty mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.centerX.equalTo(bgView);
      make.centerY.equalTo(bgView).offset(-10 * kMainTemp);

      make.size.mas_equalTo(CGSizeMake(kMainWidth * 0.6, kMainWidth * 0.6));
    }];
    return bgView;
  }
  return nil;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
  return 1;
}

- (NSInteger)tableView:(UITableView *)tableView
    numberOfRowsInSection:(NSInteger)section {
  return _cellData.count + 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView
         cellForRowAtIndexPath:(NSIndexPath *)indexPath {
  if (indexPath.row == _cellData.count) {
    UITableViewCell *cell =
        [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault
                               reuseIdentifier:@"EmptyCell"];
    cell.backgroundColor = [UIColor clearColor];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    return cell;
  }

  DynamicModelResult *dy = [_cellData objectAtIndex:indexPath.row];

  AttentionTableViewCell *cell = (AttentionTableViewCell *)[tableView
      dequeueReusableCellWithIdentifier:DynamicTableViewCellID
                           forIndexPath:indexPath];
  if (!cell) {
    cell = [[AttentionTableViewCell alloc]
          initWithStyle:(UITableViewCellStyleDefault)
        reuseIdentifier:DynamicTableViewCellID];
  }

  [cell setDynamic:dy showTime:NO];

  [cell.awesomeBtn addTarget:self
                      action:@selector(onTouchBtnInCell:)
            forControlEvents:(UIControlEventTouchUpInside)];
  cell.commentBtn.tag = indexPath.row;
  [cell.commentBtn addTarget:self
                      action:@selector(onTouchContentCell:)
            forControlEvents:(UIControlEventTouchUpInside)];

  @weakify(self);
  cell.tapIconAction = ^(NSString *_Nonnull uid) {
    @strongify(self);
    WhaleDetailVC *infoVC = [[WhaleDetailVC alloc] init];
    infoVC.uid = uid;
    [self.navigationController pushViewController:infoVC animated:YES];
  };
  return cell;
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForRowAtIndexPath:(NSIndexPath *)indexPath {
  if (indexPath.row == _cellData.count) {
    return 49 * kMainTemp;
  }

  return UITableViewAutomaticDimension;
}

- (void)tableView:(UITableView *)tableView
    didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
  DynamicModelResult *dy = [_cellData objectAtIndex:indexPath.row];
  FZMomentVC *ddVC = [[FZMomentVC alloc] init];
  ddVC.userId = dy.user.uid;
  ddVC.momentId = dy.affair.aid;
  [self.navigationController pushViewController:ddVC animated:YES];
  [tableView deselectRowAtIndexPath:indexPath animated:YES];
}
- (void)dyrightMoreAction {
  [self nofLoadData];
}

- (void)onTouchBtnTagInCell:(UIButton *)sender {
  CGPoint point = sender.center;
  point = [self.tableView convertPoint:point fromView:sender.superview];
  NSIndexPath *indexpath = [self.tableView indexPathForRowAtPoint:point];
  DynamicModelResult *dy = [_cellData objectAtIndex:indexpath.row];
  TagAffairVC *ddVC = [[TagAffairVC alloc] init];
  ddVC.tagId = dy.affair.tagType;
  ddVC.tagName = dy.affair.tagName;
  [self.navigationController pushViewController:ddVC animated:YES];
}

- (void)onTouchBtnInCell:(UIButton *)sender {
  CGPoint point = sender.center;
  point = [self.tableView convertPoint:point fromView:sender.superview];
  NSIndexPath *indexpath = [self.tableView indexPathForRowAtPoint:point];
  DynamicModelResult *dy = [_cellData objectAtIndex:indexpath.row];
  [self doLike:dy.affair.aid uid:dy.user.uid indexRow:indexpath.row];
}

- (void)onTouchContentCell:(UIButton *)sender {
  DynamicModelResult *dy = [_cellData objectAtIndex:sender.tag];
  FZMomentVC *momentVC = [[FZMomentVC alloc] init];
  momentVC.userId = dy.user.uid;
  momentVC.momentId = dy.affair.aid;
  momentVC.withComment = YES;
  [self.navigationController pushViewController:momentVC animated:YES];
}

- (void)updateBanner {
  self.banner.canShow = ![self isMusicPlayingInScreen];
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
  CGPoint point = [scrollView.panGestureRecognizer translationInView:self.view];

  if (point.y > 0) {
  } else if (point.y < 0) {
  }
}

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];
  [AppConfig statusbarStyle:YES];
}

- (void)viewDidAppear:(BOOL)animated {
  [super viewDidAppear:animated];
}

- (void)viewWillDisappear:(BOOL)animated {
  [super viewWillDisappear:animated];
  [DJStatusBarHUD hide];
}
- (void)didReceiveMemoryWarning {
  [super didReceiveMemoryWarning];
}

- (void)dealloc {
  [NOTIFICENTER removeObserver:self];
  [[FTHZDynamicCacheManager shared] clearCache];
  [LocationManager
      endUpdateLocationWithIdentifier:NSStringFromClass([self class])];
}

- (BOOL)isUserNotificationEnable {
  BOOL isEnable = NO;
  UIUserNotificationSettings *setting =
      [[UIApplication sharedApplication] currentUserNotificationSettings];
  isEnable = (UIUserNotificationTypeNone == setting.types) ? NO : YES;
  return isEnable;
}

- (FTHZMusicPlayerBanner *)banner {
  if (!_banner) {
    _banner = [[FTHZMusicPlayerBanner alloc] init];
  }
  return _banner;
}

- (BOOL)isMusicPlayingInScreen {
  for (UITableViewCell *cell in [self.tableView visibleCells]) {
    if (![cell isKindOfClass:[DynamicTableViewCell class]]) {
      continue;
    }
    if ([(DynamicTableViewCell *)cell isMusicPlaying]) {
      return YES;
    }
  }
  return NO;
}

- (BOOL)isNotchScreen {
  if (@available(iOS 11.0, *)) {
    UIWindow *window = [UIApplication sharedApplication].windows.firstObject;
    return window.safeAreaInsets.top > 20;
  }
  return NO;
}
@end