#import "LiuyanVC.h"
#import "FTHZChannelReplyModel.h"

@interface LiuyanVC () <UITextFieldDelegate, UITextViewDelegate>

@property(nonatomic, strong) UITextView *contentText;
@property(nonatomic, strong) UILabel *contentPlaceHolderL;
@property(nonatomic, strong) UILabel *characterCountLabel;
@property(nonatomic, strong) UIView *optionBGView;
@property(nonatomic, strong) FlatButton *imgCheck;
@property(nonatomic, strong) FlatButton *voiceCheck;

@end

@implementation LiuyanVC

#pragma mark -private
- (void)setupUI {
  UIView *topbar = [UIView new];

  [self.view addSubview:topbar];
  @weakify(self);
  [topbar mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.right.equalTo(self.view);
    make.top.equalTo(self.view).offset(self.safeAreaInset.top);
    make.height.mas_equalTo(70 * kWidthFactor);
  }];

  FlatButton *cancelBt = [FlatButton new];
  [cancelBt setImage:KImage_name(@"Close") forState:UIControlStateNormal];
  [cancelBt addTarget:self
                action:@selector(cancelAction)
      forControlEvents:UIControlEventTouchUpInside];
  [topbar addSubview:cancelBt];
  [cancelBt mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(topbar).offset(24 * kWidthFactor);
    make.centerY.equalTo(topbar);
  }];

  UILabel *navTitle = [UILabel new];
  navTitle.text = [self.to isEqualToString:@"108474"] ? @"反馈" : @"留言";
  navTitle.font = SourceHanSerifSemiBoldFont(16 * kWidthFactor);
  navTitle.textColor = KColor_HighBlack;
  [topbar addSubview:navTitle];
  [navTitle mas_makeConstraints:^(MASConstraintMaker *make) {
    make.centerY.equalTo(topbar);
    make.centerX.equalTo(topbar);
  }];
  FlatButton *sendBt = [FlatButton new];
  [sendBt setImage:KImage_name(@"Send") forState:UIControlStateNormal];
  [sendBt addTarget:self
                action:@selector(sendAction)
      forControlEvents:UIControlEventTouchUpInside];
  [topbar addSubview:sendBt];
  [sendBt mas_makeConstraints:^(MASConstraintMaker *make) {
    make.right.equalTo(topbar).offset(-24 * kWidthFactor);
    make.centerY.equalTo(topbar);
  }];

  UIScrollView *scrollView = [[UIScrollView alloc] init];
  [self.view addSubview:scrollView];
  [scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.right.equalTo(self.view);
    make.top.equalTo(topbar.mas_bottom);
    make.bottom.equalTo(self.view).offset(-self.safeAreaInset.bottom);
  }];

  UIView *contentView = [UIView new];
  [scrollView addSubview:contentView];
  [contentView mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.width.mas_equalTo(kMainWidth);
    make.height.mas_equalTo(kMainHeight - 64 * kWidthFactor -
                            self.safeAreaInset.top - self.safeAreaInset.bottom);
    make.edges.equalTo(scrollView);
  }];

  _contentText = [UITextView new];
  _contentText.font = SourceHanSerifMediumFont(14 * kWidthFactor);
  _contentText.keyboardAppearance = UIKeyboardAppearanceDark;
  _contentText.backgroundColor = [UIColor colorWithWhite:0.95 alpha:1.0];
  _contentText.layer.cornerRadius = 8 * kWidthFactor;
  _contentText.layer.masksToBounds = YES;
  _contentText.textContainer.lineFragmentPadding = 0;
  _contentText.textContainerInset =
      UIEdgeInsetsMake(12 * kWidthFactor, 12 * kWidthFactor, 12 * kWidthFactor,
                       12 * kWidthFactor);
  _contentText.delegate = self;
  [contentView addSubview:_contentText];
  [_contentText mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(contentView).offset(14 * kWidthFactor);
    make.left.equalTo(contentView).offset(24 * kWidthFactor);
    make.right.equalTo(contentView).offset(-24 * kWidthFactor);
    make.height.mas_equalTo(200 * kWidthFactor);
  }];

  UIView *countContainer = [[UIView alloc] init];
  countContainer.backgroundColor = [UIColor clearColor];
  countContainer.userInteractionEnabled = NO;
  [contentView addSubview:countContainer];

  [countContainer mas_makeConstraints:^(MASConstraintMaker *make) {
    make.right.equalTo(_contentText).offset(-12 * kWidthFactor);
    make.bottom.equalTo(_contentText).offset(-12 * kWidthFactor);
    make.width.mas_equalTo(70 * kWidthFactor);
    make.height.mas_equalTo(20 * kWidthFactor);
  }];

  _characterCountLabel = [UILabel new];
  _characterCountLabel.text = @"0/200";
  _characterCountLabel.font = SourceHanSerifMediumFont(12 * kWidthFactor);
  _characterCountLabel.textColor =
      [KColor_HighBlack colorWithAlphaComponent:0.5];
  _characterCountLabel.textAlignment = NSTextAlignmentRight;
  [countContainer addSubview:_characterCountLabel];

  [_characterCountLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.edges.equalTo(countContainer);
  }];

  _contentPlaceHolderL = [UILabel new];
  _contentPlaceHolderL.text =
      [self.to isEqualToString:@"108474"] ? @"点击输入反馈" : @"点击输入留言";
  _contentPlaceHolderL.textColor =
      [KColor_HighBlack colorWithAlphaComponent:0.25];
  _contentPlaceHolderL.font = SourceHanSerifMediumFont(14 * kWidthFactor);
  [_contentText addSubview:_contentPlaceHolderL];
  [_contentPlaceHolderL mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.edges.equalTo(self.contentText)
        .insets(UIEdgeInsetsMake(12 * kWidthFactor, 12 * kWidthFactor,
                                 12 * kWidthFactor, 12 * kWidthFactor));
  }];

  _optionBGView = [[UIView alloc]
      initWithFrame:CGRectMake(0, kMainHeight, kMainWidth, 48 * kWidthFactor)];
  _optionBGView.backgroundColor = KColor_DarkKeyboard;
  [self.view addSubview:_optionBGView];
}

- (void)cancelAction {
  [self.navigationController popViewControllerAnimated:YES];
  [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)sendAction {
  if (self.contentText.text.length > 200) {
    [self showToastFast:@"不能超过200字 🐳"];
  } else {
    [HUD show];
    @weakify(self);
    [LiuyanCreateModel message:self.contentText.text
        to:self.to
        success:^(NSDictionary *resultObject) {
          @strongify(self);
          [HUD dissmiss];
          LiuyanCreateModel *member =
              [LiuyanCreateModel mj_objectWithKeyValues:resultObject];

          if ([member.success boolValue]) {
            [self.navigationController popViewControllerAnimated:YES];
            [self dismissViewControllerAnimated:YES completion:nil];
          } else {
            [self.view makeToast:member.msg
                        duration:1.0
                        position:CSToastPositionCenter];
          }
        }
        failure:^(NSError *requestErr) {
          @strongify(self);
          [HUD dissmiss];
          [self showToastFast:@"数据有误,请检查网络后重试"];
        }];
  }
}

- (void)viewDidLoad {
  [super viewDidLoad];
  [self setupUI];
}

- (void)textViewDidEndEditing:(UITextView *)textView {
  if (![textView hasText]) {
    _contentPlaceHolderL.hidden = NO;
  }
}

- (void)textViewDidChange:(UITextView *)textView {
  if (![textView hasText]) {
    _contentPlaceHolderL.hidden = NO;
    _characterCountLabel.text = @"0/200";
  } else {
    _contentPlaceHolderL.hidden = YES;

    NSInteger characterCount = textView.text.length;
    _characterCountLabel.text =
        [NSString stringWithFormat:@"%ld/200", (long)characterCount];

    if (characterCount > 200) {
      _characterCountLabel.textColor = [UIColor redColor];
    } else {
      _characterCountLabel.textColor =
          [KColor_HighBlack colorWithAlphaComponent:0.5];
    }
  }
}

@end
