#import "FTHZChannelVoiceReplyVC.h"
#import "CertificateModel.h"
#import "FTHZChannelReplyModel.h"
#import "LGAudioKit.h"
#import <AVFoundation/AVFoundation.h>
#import <QiniuSDK.h>

@interface FTHZChannelVoiceReplyVC () <UIGestureRecognizerDelegate,
                                       LGAudioPlayerDelegate>

@property(nonatomic, strong) UIView *contentBG;
@property(nonatomic, strong) UIView *recordView;
@property(nonatomic, strong) UIView *recordReplyView;
@property(nonatomic, strong) UITextField *replyTF;
@property(nonatomic, strong) UILabel *timerL;
@property(nonatomic, strong) UIImageView *voiceAnimationIV;
@property(nonatomic, strong) FlatButton *sendVoiceBt;
@property(nonatomic, strong) dispatch_source_t recordTimer;
@property(nonatomic, strong) UIImageView *playAnimationIV;
@property(nonatomic, assign) int recordTime;
@property(nonatomic, assign) BOOL keyboradShowing;
@property(nonatomic, strong) dispatch_source_t countdownTimer;
@property(nonatomic, assign) int remainingSeconds;
@property(nonatomic, strong) UILabel *secLabel;

@end

@implementation FTHZChannelVoiceReplyVC

#pragma mark - network

- (void)sendAction {
  [self loadQNtoken];
}

- (void)loadQNtoken {
  [HUD show];
  @weakify(self);
  [CertificateModel getCertificateModel:@"5"
      image_name:@""
      success:^(NSDictionary *resultObject) {
        @strongify(self);
        CertificateModel *member =
            [CertificateModel mj_objectWithKeyValues:resultObject];
        if ([member.success boolValue]) {
          CertificateModelResult *da = [CertificateModelResult
              mj_objectWithKeyValues:[member.data objectAtIndex:0]];
          [self uploadVoiceFile:da.token];
        } else {
          [self showToastFast:member.msg];
        }
      }
      failure:^(NSError *requestErr) {
        [self showToastFast:@"数据有误,请检查网络后重试"];
      }];
}

- (void)uploadVoiceFile:(NSString *)qnToken {
  QNConfiguration *config =
      [QNConfiguration build:^(QNConfigurationBuilder *builder) {
        builder.zone = [QNFixedZone zone0];
      }];
  QNUploadManager *upManager =
      [[QNUploadManager alloc] initWithConfiguration:config];
  QNUploadOption *option = [[QNUploadOption alloc]
      initWithProgressHandler:^(NSString *key, float percent){
      }];

  NSString *nameKey =
      [NSString stringWithFormat:@"%@%@.amr", [NemoUtil randomString],
                                 [NemoUtil getNowTimeIntervalStr]];
  NSString *duringTime = [NSString stringWithFormat:@"%d", self.recordTime];
  NSString *filePath = [LGSoundRecorder shareInstance].soundFilePath;
  NSData *amrRecordData =
      [[LGSoundRecorder shareInstance] convertCAFtoAMR:filePath];
  NSString *amrFilePath =
      [VoiceFileManager.recordPath stringByAppendingPathComponent:nameKey];
  [amrRecordData writeToFile:amrFilePath atomically:YES];

  [upManager
       putFile:amrFilePath
           key:nameKey
         token:qnToken
      complete:^(QNResponseInfo *info, NSString *key, NSDictionary *resp) {
        if (info.ok) {

          [self loadPostComent:nameKey time:duringTime];

        } else {
          [HUD dissmiss];
          [self showToastFast:@"发送失败,请检查网络连接后重试"];
        }
      }
        option:option];
}

- (void)loadPostComent:(NSString *)fileName time:(NSString *)time {
  NSString *commentTxt = self.replyTF.text;
  if (commentTxt.length == 0) {
    commentTxt = @"回应声波";
  }
  [FTHZChannelReplyModel postChannelCommentModel:_detailModel.userid
      contentid:_detailModel.discussId
      comment:commentTxt
      to_userid:_detailModel.userid
      to_commentid:@"0"
      belongs:@"0"
      type:@"1"
      voiceFile:fileName
      voiceTime:time
      images:@""
      success:^(NSDictionary *resultObject) {
        FTHZChannelReplyModel *member =
            [FTHZChannelReplyModel mj_objectWithKeyValues:resultObject];
        [HUD dissmiss];
        if ([member.success boolValue]) {
          if ([[USERDEFAULT objectForKey:ContenText] isValid]) {
            NSString *contentText = @"";
            [USERDEFAULT setObject:contentText forKey:ContenText];
            [USERDEFAULT synchronize];
          }
          [self showToastFast:@"回应成功"];

          [self replyComplet];
        } else {
          [self showToastFast:member.msg];
        }
      }
      failure:^(NSError *requestErr) {
        [HUD dissmiss];
        [self showToastFast:@"数据有误,请检查网络后重试"];
      }];
}

#pragma mark - private
- (void)setupUI {

  [self.view addGestureRecognizer:[[UITapGestureRecognizer alloc]
                                      initWithTarget:self
                                              action:@selector(tapAction:)]];

  @weakify(self);

  _contentBG = [UIView new];
  _contentBG.frame =
      CGRectMake(0, kMainHeight - 118 - self.safeAreaInset.bottom, kMainWidth,
                 118 + self.safeAreaInset.bottom);
  _contentBG.backgroundColor = KColor_replyBgBlack;
  UIImageView *iv = [UIImageView new];
  iv.image = [UIImage imageNamed:@"voice_reply_bg"];
  [_contentBG addSubview:iv];
  [iv mas_remakeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.edges.equalTo(self.contentBG);
  }];

  [self.view addSubview:_contentBG];

  _recordView = [self createRecordView];
  [_contentBG addSubview:_recordView];
  [_recordView mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.edges.equalTo(self.contentBG);
  }];

  [[NSNotificationCenter defaultCenter]
      addObserver:self
         selector:@selector(keyboardWillAppear:)
             name:UIKeyboardWillShowNotification
           object:nil];
  [[NSNotificationCenter defaultCenter]
      addObserver:self
         selector:@selector(keyboardWillDisappear:)
             name:UIKeyboardWillHideNotification
           object:nil];
}

#pragma mark - lifecyle
- (void)viewDidLoad {
  [super viewDidLoad];
  [self setupUI];
}

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];
  self.view.backgroundColor =
      [[UIColor blackColor] colorWithAlphaComponent:0.4];
}

- (void)viewWillDisappear:(BOOL)animated {
  [super viewWillDisappear:animated];
  [self.replyTF resignFirstResponder];
}

#pragma mark - eventhandle

- (void)deleteVoiceFile {
  UIAlertController *alertController = [UIAlertController
      alertControllerWithTitle:@"删除声波"
                       message:self.replyTF.text.length == 0
                                   ? @"确定要删除声波吗？"
                                   : @"确定要删除声波吗？（文字会被保留）"
                preferredStyle:UIAlertControllerStyleAlert];

  UIAlertAction *cancelAction =
      [UIAlertAction actionWithTitle:@"取消"
                               style:UIAlertActionStyleCancel
                             handler:nil];

  @weakify(self);
  UIAlertAction *confirmAction =
      [UIAlertAction actionWithTitle:@"确认"
                               style:UIAlertActionStyleDestructive
                             handler:^(UIAlertAction *action) {
                               @strongify(self);
                               [self cancelVoiceFileAction];
                               [self showToastFast:@"删除成功"];
                             }];

  [alertController addAction:cancelAction];
  [alertController addAction:confirmAction];

  [[UIViewController topViewController] presentViewController:alertController
                                                     animated:YES
                                                   completion:nil];
}

- (void)cancelVoiceFileAction {
  [self.recordReplyView removeFromSuperview];
  self.recordView = [self createRecordView];
  [self.contentBG addSubview:self.recordView];
  @weakify(self);
  [self.recordView mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.edges.equalTo(self.contentBG);
  }];
}

- (void)replyComplet {
  [self dismissViewControllerAnimated:YES completion:nil];
  if (self.delegate) {
    [self.delegate completeReply];
  }
}

#pragma mark -event

- (void)replayAction {
  if (_recordTime > 0) {
    [self stopCountdown];
    [[LGAudioPlayer sharePlayer]
        playAudioWithURLString:[LGSoundRecorder shareInstance].soundFilePath
                       atIndex:0];
    [LGAudioPlayer sharePlayer].delegate = self;
  }
}

- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer
       shouldReceiveTouch:(UITouch *)touch {

  BOOL isTouchResponse = YES;
  CGRect rect = [touch.view convertRect:touch.view.frame toView:self.view];
  CGRect notouchViewRect = self.contentBG.frame;
  if (notouchViewRect.origin.y - 20 < rect.origin.y) {
    isTouchResponse = NO;
  }

  return isTouchResponse;
}
- (void)tapAction:(UITapGestureRecognizer *)sender {

  CGPoint touchPoint = [sender locationInView:self.view];
  if (CGRectContainsPoint(self.contentBG.frame, touchPoint)) {
    return;
  }

  if (self.keyboradShowing) {
    [self.replyTF resignFirstResponder];
  } else if (!self.keyboradShowing && self.delegate) {
    [self.delegate cancelReplyView];
    [self dismissViewControllerAnimated:YES completion:nil];
  }
}

- (void)keyboardWillAppear:(NSNotification *)noti {
  self.keyboradShowing = YES;
  NSDictionary *info = [noti userInfo];
  NSValue *value = [info objectForKey:UIKeyboardFrameEndUserInfoKey];
  CGFloat keyboardAnimationDuration =
      [[info objectForKey:UIKeyboardAnimationDurationUserInfoKey] floatValue];
  CGSize keyboardSize = [value CGRectValue].size;
  [UIView animateWithDuration:keyboardAnimationDuration
                   animations:^{
                     CGRect frame = self.contentBG.frame;
                     frame.origin.y = self.view.frame.size.height -
                                      keyboardSize.height - 118;
                     self.contentBG.frame = frame;
                   }];
}

- (void)keyboardWillDisappear:(NSNotification *)noti {
  self.keyboradShowing = NO;
  NSDictionary *info = [noti userInfo];
  CGFloat keyboardAnimationDuration =
      [[info objectForKey:UIKeyboardAnimationDurationUserInfoKey] floatValue];
  [UIView animateWithDuration:keyboardAnimationDuration
                   animations:^{
                     CGRect frame = self.contentBG.frame;
                     frame.origin.y = self.view.frame.size.height - 118 -
                                      self.safeAreaInset.bottom;
                     self.contentBG.frame = frame;
                   }];
}

#pragma mark - lgaudioplayer delegate
- (void)startAnimating {
  [UIView animateWithDuration:1.5
                        delay:0
                      options:UIViewAnimationOptionRepeat |
                              UIViewAnimationOptionAutoreverse |
                              UIViewAnimationOptionAllowUserInteraction
                   animations:^{
                     self.playAnimationIV.alpha = 0.3;
                   }
                   completion:nil];
}

- (void)stopAnimating {
  [self.playAnimationIV.layer removeAllAnimations];
  self.playAnimationIV.alpha = 1.0;
}

- (void)startCountdown:(int)seconds {
  self.remainingSeconds = seconds;

  self.secLabel.text =
      [NSString stringWithFormat:@"%ds", self.remainingSeconds];

  if (self.countdownTimer) {
    dispatch_source_cancel(self.countdownTimer);
  }

  self.countdownTimer = dispatch_source_create(DISPATCH_SOURCE_TYPE_TIMER, 0, 0,
                                               dispatch_get_main_queue());
  dispatch_source_set_timer(self.countdownTimer, DISPATCH_TIME_NOW,
                            1.0 * NSEC_PER_SEC, 0);

  @weakify(self);
  dispatch_source_set_event_handler(self.countdownTimer, ^{
    @strongify(self);
    self.remainingSeconds--;
    if (self.remainingSeconds >= 0) {
      self.secLabel.text =
          [NSString stringWithFormat:@"%ds", self.remainingSeconds];
    }
  });

  dispatch_resume(self.countdownTimer);
}

- (void)stopCountdown {
  if (self.countdownTimer) {
    dispatch_source_cancel(self.countdownTimer);
    self.countdownTimer = nil;
  }
  self.secLabel.text = [NSString stringWithFormat:@"%ds", self.recordTime];
}

- (void)audioPlayerStateDidChanged:(LGAudioPlayerState)audioPlayerState
                          forIndex:(NSUInteger)index {
  if (audioPlayerState != LGAudioPlayerStatePlaying) {
    [self stopAnimating];
    [self stopCountdown];
  } else {
    [self startAnimating];
    [self startCountdown:self.recordTime];
  }
}

#pragma mark - 录音
- (void)recordStart:(UIButton *)button {
  __block BOOL isAllow = 0;
  AVAudioSession *audioSession = [AVAudioSession sharedInstance];
  if ([audioSession respondsToSelector:@selector(requestRecordPermission:)]) {
    [audioSession performSelector:@selector(requestRecordPermission:)
                       withObject:^(BOOL granted) {
                         if (granted) {
                           isAllow = 1;
                         } else {
                           isAllow = 0;
                         }
                       }];
  }
  if (isAllow) {
    [[LGAudioPlayer sharePlayer] stopAudioPlayer];
    [[LGSoundRecorder shareInstance]
        startSoundRecord:self.view
              recordPath:VoiceFileManager.recordPath];
    [self startTimerForRecord];

  } else {
  }

  [self.sendVoiceBt setTitle:@"松开结束" forState:UIControlStateNormal];

  [self.voiceAnimationIV startAnimating];
}

- (void)recordCancel {
  [[LGSoundRecorder shareInstance] soundRecordFailed:self.view];
  [self.sendVoiceBt setTitle:@"长按录制" forState:UIControlStateNormal];
  [self stopTimer];
  self.timerL.text = @"00:00";
  [self.voiceAnimationIV stopAnimating];
}

- (void)updateCancelRecordVoice {
  [[LGSoundRecorder shareInstance] readyCancelSound];

  [self.voiceAnimationIV stopAnimating];
}

- (void)updateContinueRecordVoice {
  [[LGSoundRecorder shareInstance] resetNormalRecord];
  [self.voiceAnimationIV startAnimating];
}

- (void)recordFinish {
  [self.sendVoiceBt setTitle:@"长按录制" forState:UIControlStateNormal];

  if ([[LGSoundRecorder shareInstance] soundRecordTime] == 0) {
    [self recordCancel];
    return;
  }
  [self stopTimer];
  if ([[LGSoundRecorder shareInstance] soundRecordTime] < 1.0f) {
    [self showShotTimeSign];
    return;
  }

  [self sendSound];
  [[LGSoundRecorder shareInstance] stopSoundRecord:self.view];

  [self.voiceAnimationIV stopAnimating];
}

- (void)showShotTimeSign {
  [self.voiceAnimationIV stopAnimating];
  [self.view makeToast:@"说话时间太短"
              duration:0.25
              position:CSToastPositionCenter];

  [[LGSoundRecorder shareInstance] showShotTimeSign:self.view];
}

- (void)sendSound {
  [self.recordView removeFromSuperview];
  self.recordReplyView = [self createRecordReplyView];
  [self.contentBG addSubview:self.recordReplyView];
  @weakify(self);
  [self.recordReplyView mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.edges.equalTo(self.contentBG);
  }];
}

- (void)startTimerForRecord {
  @weakify(self);
  [self stopTimer];
  self.recordTime = 0;
  self.recordTimer = dispatch_source_create(
      DISPATCH_SOURCE_TYPE_TIMER, 0, 0,
      dispatch_queue_create("Channel.reply.Timer", NULL));
  dispatch_source_set_timer(
      self.recordTimer,
      dispatch_walltime(DISPATCH_TIME_NOW, NSEC_PER_SEC * 1ull),
      NSEC_PER_SEC * 1ull, 1ull * NSEC_PER_SEC);
  dispatch_source_set_event_handler(self.recordTimer, ^() {
    @strongify(self);
    dispatch_async(dispatch_get_main_queue(), ^{
      @strongify(self);
      int time = [LGSoundRecorder shareInstance].soundRecordTime;
      self.recordTime = time;
      if (time < 60) {
        if (time < 10) {
          self.timerL.text = [NSString stringWithFormat:@"00:0%d", time];

        } else {
          self.timerL.text = [NSString stringWithFormat:@"00:%d", time];
        }

      } else {
        self.timerL.text = @"01:00";
        [self recordFinish];
      }
    });
  });

  dispatch_resume(self.recordTimer);
}

- (void)stopTimer {

  if (self.recordTimer) {
    dispatch_source_cancel(self.recordTimer);
    self.recordTimer = nil;
  }
}

#pragma mark - setter/getter

- (UIView *)createRecordView {
  UIView *view = [UIView new];
  view.backgroundColor = KColor_replyBgBlack;
  @weakify(self);
  _timerL = [UILabel new];
  _timerL.text = @"00:00";
  _timerL.font = DinCondensedBoldFont(16);
  _timerL.textColor = KColor_White;
  [view addSubview:_timerL];
  [_timerL mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(view).offset(20);
    make.top.equalTo(view).offset(62);
  }];

  _sendVoiceBt = [FlatButton buttonWithType:UIButtonTypeCustom];
  [_sendVoiceBt setTitle:@"长按录制" forState:UIControlStateNormal];
  [_sendVoiceBt setTitleColor:KColor_White forState:UIControlStateNormal];
  [_sendVoiceBt addTarget:self
                   action:@selector(recordStart:)
         forControlEvents:UIControlEventTouchDown];
  [_sendVoiceBt addTarget:self
                   action:@selector(recordCancel)
         forControlEvents:UIControlEventTouchUpOutside];
  [_sendVoiceBt addTarget:self
                   action:@selector(recordFinish)
         forControlEvents:UIControlEventTouchUpInside];
  [_sendVoiceBt addTarget:self
                   action:@selector(updateCancelRecordVoice)
         forControlEvents:UIControlEventTouchDragExit];
  [_sendVoiceBt addTarget:self
                   action:@selector(updateContinueRecordVoice)
         forControlEvents:UIControlEventTouchDragEnter];
  _sendVoiceBt.titleLabel.font = SourceHanSerifRegularFont(10);
  _sendVoiceBt.layer.borderWidth = 1.0;
  _sendVoiceBt.layer.cornerRadius = 2;
  _sendVoiceBt.layer.borderColor = KColor_White.CGColor;
  [view addSubview:_sendVoiceBt];
  [_sendVoiceBt mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.centerY.equalTo(self.timerL);
    make.width.mas_equalTo(60);
    make.height.mas_equalTo(21);
    make.right.equalTo(view).offset(-24);
  }];

  _voiceAnimationIV = [UIImageView new];
  NSMutableArray *images = [NSMutableArray array];
  for (int i = 50; i < 75; i++) {
    [images addObject:[UIImage
                          imageNamed:[NSString
                                         stringWithFormat:@"合成 1_000%d", i]]];
  }
  _voiceAnimationIV.animationImages = images;
  _voiceAnimationIV.image = images.firstObject;
  _voiceAnimationIV.animationDuration = 2;
  _voiceAnimationIV.contentMode = UIViewContentModeScaleAspectFit;
  [view addSubview:_voiceAnimationIV];

  [_voiceAnimationIV mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.centerY.equalTo(self.timerL);
    make.left.equalTo(self.timerL.mas_right).offset(5);
    make.right.equalTo(self.sendVoiceBt.mas_left).offset(-9);
  }];
  return view;
}

- (UIView *)createRecordReplyView {
  UIView *view = [UIView new];
  view.backgroundColor = KColor_replyBgBlack;

  FlatButton *sendBt = [FlatButton buttonWithType:UIButtonTypeCustom];
  [sendBt setTitle:@"发送" forState:UIControlStateNormal];
  [sendBt addTarget:self
                action:@selector(sendAction)
      forControlEvents:UIControlEventTouchUpInside];
  [sendBt setTitleColor:KColor_White forState:UIControlStateNormal];
  sendBt.titleLabel.font = SourceHanSerifRegularFont(14);
  [view addSubview:sendBt];
  @weakify(self);
  [sendBt mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.bottom.equalTo(view).offset(-18 - self.safeAreaInset.bottom);
    make.right.equalTo(view).offset(-12);
  }];

  UIView *inputView = [UIView new];
  inputView.layer.borderWidth = 0.5;
  inputView.layer.borderColor = [UIColor colorWithRed:235 / 255.0
                                                green:237 / 255.0
                                                 blue:245 / 255.0
                                                alpha:1.0]
                                    .CGColor;
  inputView.layer.backgroundColor = [UIColor colorWithRed:255 / 255.0
                                                    green:255 / 255.0
                                                     blue:255 / 255.0
                                                    alpha:1.0]
                                        .CGColor;
  inputView.layer.cornerRadius = 2;

  [view addSubview:inputView];
  [inputView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(view).offset(16);
    make.right.equalTo(view).offset(-52);
    make.centerY.equalTo(sendBt);
    make.height.mas_equalTo(26);
  }];

  if (_replyTF == nil) {
    _replyTF = [UITextField new];
    _replyTF.keyboardAppearance = UIKeyboardAppearanceDark;
    _replyTF.placeholder = @"补充文字描述";
    _replyTF.font = SourceHanSerifRegularFont(14);
  }
  [inputView addSubview:_replyTF];
  [_replyTF mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(inputView).offset(8);
    make.right.equalTo(inputView).offset(-8);
    make.bottom.top.equalTo(inputView);
    make.centerY.equalTo(inputView);
  }];

  FlatButton *voiceFileV = [FlatButton new];
  voiceFileV.layer.backgroundColor = [UIColor colorWithRed:242 / 255.0
                                                     green:244 / 255.0
                                                      blue:248 / 255.0
                                                     alpha:1.0]
                                         .CGColor;
  voiceFileV.layer.cornerRadius = 4;
  [voiceFileV addTarget:self
                 action:@selector(replayAction)
       forControlEvents:UIControlEventTouchUpInside];
  [view addSubview:voiceFileV];
  [voiceFileV mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.right.equalTo(inputView);
    make.bottom.equalTo(inputView.mas_top).offset(-12);
    make.height.mas_equalTo(32);
  }];

  UIImageView *typeIcon = [[UIImageView alloc] init];
  typeIcon.image = [UIImage imageNamed:@"voiceb"];
  typeIcon.contentMode = UIViewContentModeScaleAspectFit;
  [voiceFileV addSubview:typeIcon];
  [typeIcon mas_makeConstraints:^(MASConstraintMaker *make) {
    make.width.height.mas_equalTo(16);
    make.left.equalTo(voiceFileV).offset(12);
    make.centerY.equalTo(voiceFileV);
  }];

  _playAnimationIV = typeIcon;

  FlatButton *closeBt = [FlatButton buttonWithType:UIButtonTypeCustom];
  [closeBt setImage:[UIImage imageNamed:@"music_close_black"]
           forState:UIControlStateNormal];
  [closeBt addTarget:self
                action:@selector(deleteVoiceFile)
      forControlEvents:UIControlEventTouchUpInside];
  [voiceFileV addSubview:closeBt];
  [closeBt mas_makeConstraints:^(MASConstraintMaker *make) {
    make.width.height.mas_equalTo(16);
    make.centerY.equalTo(voiceFileV);
    make.right.equalTo(voiceFileV).offset(-8);
  }];

  UILabel *secL = [UILabel new];
  int time = [LGSoundRecorder shareInstance].soundRecordTime;
  secL.text = [NSString stringWithFormat:@"%ds", time];
  secL.font = SourceHanSerifRegularFont(12);
  secL.textColor = [KColor_HighBlack colorWithAlphaComponent:0.65];
  [voiceFileV addSubview:secL];
  [secL mas_makeConstraints:^(MASConstraintMaker *make) {
    make.centerY.equalTo(voiceFileV);
    make.right.equalTo(closeBt.mas_left).offset(-12);
  }];
  self.secLabel = secL;

  return view;
}

- (void)dealloc {
  [self stopCountdown];
}

@end
