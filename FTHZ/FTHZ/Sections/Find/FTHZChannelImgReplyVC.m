#import "FTHZChannelImgReplyVC.h"
#import "CertificateModel.h"
#import "FTHZChannelReplyModel.h"
#import "TZImagePickerManager.h"
#import "UIImage+Wechat.h"
#import <Photos/Photos.h>
#import <QiniuSDK.h>

@interface FTHZChannelImgReplyVC () <UIImagePickerControllerDelegate>
@property(nonatomic, strong) UIView *contentBG;
@property(nonatomic, strong) UITextField *replyTF;
@property(nonatomic, assign) BOOL keyboradShowing;
@property(nonatomic, strong) NSString *imageToken;
@property(nonatomic, strong) FlatButton *sendBt;
@property(nonatomic, strong) UIView *photoContainerView;
@property(nonatomic, assign) BOOL needDeleteItem;
@property(nonatomic, strong) UIImage *uploadImage;

@end

@implementation FTHZChannelImgReplyVC

#pragma mark - network

- (void)sendAction {
  if (self.gifDataArray.count > 0 && self.gifDataArray[0] != [NSNull null]) {
    [self loadQNtoken];
  } else if (self.uploadImage != nil) {
    [self loadQNtoken];
  } else if (!self.uploadImage && (self.replyTF.text.length > 0)) {
    [self loadPostComent:@""];
  }
}

- (void)loadQNtoken {
  [HUD show];
  @weakify(self);
  [CertificateModel getCertificateModel:@"2"
      image_name:@""
      success:^(NSDictionary *resultObject) {
        @strongify(self);
        CertificateModel *member =
            [CertificateModel mj_objectWithKeyValues:resultObject];
        if ([member.success boolValue]) {
          CertificateModelResult *da = [CertificateModelResult
              mj_objectWithKeyValues:[member.data objectAtIndex:0]];
          [self uploadVoiceFile:da.token];
        } else {
          [self showToastFast:member.msg];
        }
      }
      failure:^(NSError *requestErr) {
        [self showToastFast:@"数据有误,请检查网络后重试"];
      }];
}

- (void)uploadVoiceFile:(NSString *)qnToken {
  if (self.gifDataArray.count > 0 && self.gifDataArray[0] != [NSNull null]) {
    NSData *gifData = self.gifDataArray[0];
    [self uploadGifData:gifData token:qnToken];
    return;
  }
  QNConfiguration *config =
      [QNConfiguration build:^(QNConfigurationBuilder *builder) {
        builder.zone = [QNFixedZone zone0];
      }];
  QNUploadManager *upManager =
      [[QNUploadManager alloc] initWithConfiguration:config];
  QNUploadOption *option = [[QNUploadOption alloc]
      initWithProgressHandler:^(NSString *key, float percent){
      }];
  NSString *nameKey =
      [NSString stringWithFormat:@"%@%@", [NemoUtil randomString],
                                 [NemoUtil getNowTimeIntervalStr]];
  [upManager
       putFile:[self getImagePath:self.uploadImage]
           key:nameKey
         token:qnToken
      complete:^(QNResponseInfo *info, NSString *key, NSDictionary *resp) {
        if (info.ok) {
          [self loadPostComent:nameKey];
        } else {
          [HUD dissmiss];
          [self showToastFast:@"发送失败,请检查网络连接后重试"];
        }
      }
        option:option];
}

- (void)loadPostComent:(NSString *)fileName {
  NSString *commentTxt = self.replyTF.text;
  if (commentTxt.length == 0) {
    commentTxt = @"回应声波";
  }
  [FTHZChannelReplyModel postChannelCommentModel:self.detailModel.userid
      contentid:self.detailModel.discussId
      comment:commentTxt
      to_userid:self.detailModel.userid
      to_commentid:@"0"
      belongs:@"0"
      type:@"1"
      voiceFile:@""
      voiceTime:@"0"
      images:fileName
      success:^(NSDictionary *resultObject) {
        FTHZChannelReplyModel *member =
            [FTHZChannelReplyModel mj_objectWithKeyValues:resultObject];
        [HUD dissmiss];
        if ([member.success boolValue]) {
          if ([[USERDEFAULT objectForKey:ContenText] isValid]) {
            NSString *contentText = @"";
            [USERDEFAULT setObject:contentText forKey:ContenText];
            [USERDEFAULT synchronize];
          }
          [self showToastFast:@"回应成功"];

          [self replyComplet];

        } else {
          [self showToastFast:member.msg];
        }
      }
      failure:^(NSError *requestErr) {
        [HUD dissmiss];
        [self showToastFast:@"数据有误,请检查网络后重试"];
      }];
}

#pragma mark - private
- (void)setupUI {

  @weakify(self);
  _contentBG = [UIView new];
  _contentBG.frame =
      CGRectMake(0, kMainHeight - 110 - self.safeAreaInset.bottom, kMainWidth,
                 110 + self.safeAreaInset.bottom);
  _contentBG.backgroundColor = KColor_replyBgBlack;
  [self.view addSubview:_contentBG];

  UIView *photoContainerView = [[UIView alloc] init];
  photoContainerView.frame = CGRectMake(16, 10, 44, 44);
  [_contentBG addSubview:photoContainerView];
  self.photoContainerView = photoContainerView;

  UIButton *photoButton = [UIButton buttonWithType:UIButtonTypeCustom];
  photoButton.frame = CGRectMake(0, 0, 44, 44);
  [photoButton setImage:[UIImage imageNamed:@"图片声波"]
               forState:UIControlStateNormal];
  [photoButton addTarget:self
                  action:@selector(takePhoto)
        forControlEvents:UIControlEventTouchUpInside];
  [photoContainerView addSubview:photoButton];

  UIImageView *previewImageView = [[UIImageView alloc] init];
  previewImageView.frame = CGRectMake(0, 0, 44, 44);
  previewImageView.contentMode = UIViewContentModeScaleAspectFill;
  previewImageView.clipsToBounds = YES;
  previewImageView.layer.cornerRadius = 4;
  previewImageView.hidden = YES;
  [photoContainerView addSubview:previewImageView];
  self.previewImageView = previewImageView;

  UIButton *deleteButton = [UIButton buttonWithType:UIButtonTypeCustom];
  deleteButton.frame = CGRectMake(34, -6, 16, 16);
  [deleteButton setImage:[UIImage imageNamed:@"删除"]
                forState:UIControlStateNormal];
  [deleteButton addTarget:self
                   action:@selector(deleteImage)
         forControlEvents:UIControlEventTouchUpInside];
  deleteButton.hidden = YES;
  [photoContainerView addSubview:deleteButton];
  self.deleteButton = deleteButton;

  FlatButton *sendBt = [FlatButton buttonWithType:UIButtonTypeCustom];
  [sendBt setTitle:@"发送" forState:UIControlStateNormal];
  [sendBt addTarget:self
                action:@selector(sendAction)
      forControlEvents:UIControlEventTouchUpInside];
  [sendBt setTitleColor:KColor_White forState:UIControlStateNormal];
  sendBt.enabled = NO;

  sendBt.titleLabel.font = SourceHanSerifRegularFont(14 * kWidthFactor);
  [_contentBG addSubview:sendBt];
  [sendBt mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.bottom.equalTo(self.contentBG).offset(-18 - self.safeAreaInset.bottom);
    make.right.equalTo(self.contentBG).offset(-12);
  }];
  self.sendBt = sendBt;

  UIView *inputView = [UIView new];
  inputView.layer.borderWidth = 0.5;
  inputView.layer.borderColor = [UIColor colorWithRed:235 / 255.0
                                                green:237 / 255.0
                                                 blue:245 / 255.0
                                                alpha:1.0]
                                    .CGColor;
  inputView.layer.backgroundColor = [UIColor colorWithRed:255 / 255.0
                                                    green:255 / 255.0
                                                     blue:255 / 255.0
                                                    alpha:1.0]
                                        .CGColor;
  inputView.layer.cornerRadius = 2;

  [_contentBG addSubview:inputView];
  [inputView mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.equalTo(self.contentBG).offset(16);
    make.right.equalTo(self.contentBG).offset(-52);
    make.centerY.equalTo(sendBt);
    make.height.mas_equalTo(26);
  }];

  if (_replyTF == nil) {
    _replyTF = [UITextField new];
    _replyTF.placeholder = @"回应声波";
    _replyTF.font = SourceHanSerifRegularFont(14);
    _replyTF.keyboardAppearance = UIKeyboardAppearanceDark;
    [_replyTF addTarget:self
                  action:@selector(textFieldDidChange:)
        forControlEvents:UIControlEventEditingChanged];
  }
  [inputView addSubview:_replyTF];
  [_replyTF mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(inputView).offset(8);
    make.right.equalTo(inputView).offset(-8);
    make.bottom.top.equalTo(inputView);
    make.centerY.equalTo(inputView);
  }];

  [[NSNotificationCenter defaultCenter]
      addObserver:self
         selector:@selector(keyboardWillAppear:)
             name:UIKeyboardWillShowNotification
           object:nil];
  [[NSNotificationCenter defaultCenter]
      addObserver:self
         selector:@selector(keyboardWillDisappear:)
             name:UIKeyboardWillHideNotification
           object:nil];
}

- (void)takePhoto {
  [[TZImagePickerManager sharedManager]
      presentSinglePhotoPickerWithViewController:self
                                       allowCrop:YES
                                        delegate:nil
                               completionHandler:^(
                                   NSArray<UIImage *> *_Nullable images,
                                   NSArray<PHAsset *> *_Nullable assets,
                                   BOOL isOriginal) {
                                 [self.gifDataArray removeAllObjects];
                                 if (assets.count > 0) {
                                   PHAsset *asset = assets.firstObject;
                                   [[PHImageManager defaultManager]
                                       requestImageDataForAsset:asset
                                                        options:nil
                                                  resultHandler:^(
                                                      NSData *imageData,
                                                      NSString *dataUTI,
                                                      UIImageOrientation
                                                          orientation,
                                                      NSDictionary *info) {
                                                    BOOL isGif =
                                                        [dataUTI.lowercaseString
                                                            containsString:
                                                                @"gif"] ||
                                                        (imageData.length > 0 &&
                                                         ((const uint8_t *)
                                                              imageData
                                                                  .bytes)[0] ==
                                                             0x47);
                                                    if (isGif) {
                                                      [self.gifDataArray
                                                          addObject:imageData];
                                                      UIImage *firstFrame =
                                                          [UIImage
                                                              imageWithData:
                                                                  imageData];
                                                      self.uploadImage = nil;
                                                      self.previewImageView
                                                          .image =
                                                          firstFrame
                                                              ?: [UIImage
                                                                     imageNamed:
                                                                         @"gif_"
                                                                         @"plac"
                                                                         @"ehol"
                                                                         @"de"
                                                                         @"r"];
                                                      self.previewImageView
                                                          .hidden = NO;
                                                      self.deleteButton.hidden =
                                                          NO;
                                                      [self checkCanSend];
                                                    } else if (images.count >
                                                               0) {
                                                      self.uploadImage =
                                                          images.firstObject;
                                                      [self.gifDataArray
                                                          addObject:[NSNull
                                                                        null]];
                                                      self.previewImageView
                                                          .image =
                                                          self.uploadImage;
                                                      self.previewImageView
                                                          .hidden = NO;
                                                      self.deleteButton.hidden =
                                                          NO;
                                                      [self checkCanSend];
                                                    }
                                                  }];
                                 } else if (images.count > 0) {
                                   self.uploadImage = images.firstObject;
                                   [self.gifDataArray addObject:[NSNull null]];
                                   self.previewImageView.image =
                                       self.uploadImage;
                                   self.previewImageView.hidden = NO;
                                   self.deleteButton.hidden = NO;
                                   [self checkCanSend];
                                 }
                               }
                                   cancelHandler:^{
                                   }];
}

- (void)deleteImage {
  self.uploadImage = nil;
  self.previewImageView.image = nil;
  self.previewImageView.hidden = YES;
  self.deleteButton.hidden = YES;
  [self checkCanSend];
}

- (void)checkCanSend {
  if (_replyTF.text.length == 0 && _uploadImage == nil) {
    _sendBt.enabled = NO;
  } else {
    _sendBt.enabled = YES;
  }
}
#pragma mark - lifecyle

- (void)viewDidLoad {
  [super viewDidLoad];
  UITapGestureRecognizer *tapGesture =
      [[UITapGestureRecognizer alloc] initWithTarget:self
                                              action:@selector(tapAction:)];
  tapGesture.delegate = self;
  [self.view addGestureRecognizer:tapGesture];
}
- (void)viewWillDisappear:(BOOL)animated {
  [super viewWillDisappear:animated];
  [self.replyTF resignFirstResponder];
}

#pragma mark - eventhandle
- (void)replyComplet {
  if (self.delegate) {
    [self.delegate completeReply];
  }
  [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)tapAction:(UITapGestureRecognizer *)sender {
  CGPoint touchPoint = [sender locationInView:self.view];
  if (CGRectContainsPoint(self.contentBG.frame, touchPoint)) {
    return;
  }
  if (self.keyboradShowing) {
    [self.replyTF resignFirstResponder];
  } else if (!self.keyboradShowing && self.delegate) {
    [self.delegate cancelReplyView];
    [self dismissViewControllerAnimated:YES
                             completion:^{

                             }];
  }
}

- (void)textFieldDidChange:(UITextField *)textfiled {
  [self checkCanSend];
}

- (void)keyboardWillAppear:(NSNotification *)noti {
  self.keyboradShowing = YES;
  NSDictionary *info = [noti userInfo];
  NSValue *value = [info objectForKey:UIKeyboardFrameEndUserInfoKey];
  CGFloat keyboardAnimationDuration =
      [[info objectForKey:UIKeyboardAnimationDurationUserInfoKey] floatValue];
  CGSize keyboardSize = [value CGRectValue].size;
  [UIView animateWithDuration:keyboardAnimationDuration
                   animations:^{
                     CGRect frame = self.contentBG.frame;
                     frame.origin.y = self.view.frame.size.height -
                                      keyboardSize.height - 110;
                     self.contentBG.frame = frame;
                   }];
}

- (void)keyboardWillDisappear:(NSNotification *)noti {
  self.keyboradShowing = NO;
  NSDictionary *info = [noti userInfo];
  CGFloat keyboardAnimationDuration =
      [[info objectForKey:UIKeyboardAnimationDurationUserInfoKey] floatValue];
  [UIView animateWithDuration:keyboardAnimationDuration
                   animations:^{
                     CGRect frame = self.contentBG.frame;
                     frame.origin.y = self.view.frame.size.height - 110 -
                                      self.safeAreaInset.bottom;
                     self.contentBG.frame = frame;
                   }];
}

#pragma mark - delegate
- (void)imagePickerController:(UIImagePickerController *)picker
    didFinishPickingMediaWithInfo:(NSDictionary<NSString *, id> *)info {
  [picker dismissViewControllerAnimated:YES completion:nil];
  NSURL *imageURL = [info objectForKey:@"UIImagePickerControllerImageURL"];
  if (imageURL &&
      [[imageURL.pathExtension lowercaseString] isEqualToString:@"gif"]) {
    NSData *gifData = [NSData dataWithContentsOfURL:imageURL];
    if (gifData) {
      [self.gifDataArray removeAllObjects];
      [self.gifDataArray addObject:gifData];
      UIImage *firstFrame = [UIImage imageWithData:gifData];
      self.uploadImage = nil;
      self.previewImageView.image =
          firstFrame ?: [UIImage imageNamed:@"gif_placeholder"];
      self.previewImageView.hidden = NO;
      self.deleteButton.hidden = NO;
      [self checkCanSend];
      return;
    }
  }
  UIImage *image = info[UIImagePickerControllerOriginalImage];
  self.uploadImage = image;
  [self.gifDataArray removeAllObjects];
  [self.gifDataArray addObject:[NSNull null]];
  self.previewImageView.image = self.uploadImage;
  self.previewImageView.hidden = NO;
  self.deleteButton.hidden = NO;
  [self checkCanSend];
}
- (void)imagePickerControllerDidCancel:(UIImagePickerController *)picker {
  [picker dismissViewControllerAnimated:YES completion:nil];
}

- (NSString *)typeForImageData:(NSData *)data {
  uint8_t c;
  [data getBytes:&c length:1];
  switch (c) {
  case 0xFF:
    return @"jpeg";
  case 0x89:
    return @"png";
  case 0x47:
    return @"gif";
  case 0x49:
  case 0x4D:
    return @"tiff";
  }
  return nil;
}

#pragma mark - setter/getter

- (NSMutableArray *)gifDataArray {
  if (!_gifDataArray) {
    _gifDataArray = [NSMutableArray array];
  }
  return _gifDataArray;
}

- (NSString *)getImagePath:(UIImage *)Image {
  if ((id)Image == [NSNull null])
    return nil;
  NSString *filePath = nil;
  NSData *data = nil;
  data = [Image wcTimelineCompress];

  NSString *tmpDir = NSTemporaryDirectory();

  NSFileManager *fileManager = [NSFileManager defaultManager];

  [fileManager createDirectoryAtPath:tmpDir
         withIntermediateDirectories:YES
                          attributes:nil
                               error:nil];
  NSString *ImagePath =
      [[NSString alloc] initWithFormat:@"/tempForChannel.png"];
  [fileManager createFileAtPath:[tmpDir stringByAppendingString:ImagePath]
                       contents:data
                     attributes:nil];

  filePath = [[NSString alloc] initWithFormat:@"%@%@", tmpDir, ImagePath];
  return filePath;
}

- (void)uploadGifData:(NSData *)gifData token:(NSString *)qnToken {
  QNConfiguration *config =
      [QNConfiguration build:^(QNConfigurationBuilder *builder) {
        builder.zone = [QNFixedZone zone0];
      }];
  QNUploadManager *upManager =
      [[QNUploadManager alloc] initWithConfiguration:config];
  QNUploadOption *option = [[QNUploadOption alloc]
      initWithProgressHandler:^(NSString *key, float percent){
      }];
  NSString *imgKey = [NSString
      stringWithFormat:@"%@%@%@.gif", [NemoUtil randomString],
                       CurrentUser.userid, [NemoUtil getNowTimeIntervalStr]];
  NSString *DocumentsPath =
      [NSHomeDirectory() stringByAppendingPathComponent:@"Documents"];
  NSString *ImagePath = [NSString stringWithFormat:@"/theGifImage.gif"];
  NSString *filePath = [DocumentsPath stringByAppendingString:ImagePath];
  [[NSFileManager defaultManager] createFileAtPath:filePath
                                          contents:gifData
                                        attributes:nil];
  [upManager
       putFile:filePath
           key:imgKey
         token:qnToken
      complete:^(QNResponseInfo *info, NSString *key, NSDictionary *resp) {
        if (info.ok) {
          [self loadPostComent:imgKey];
        } else {
          [HUD dissmiss];
          [self showToastFast:@"发送失败,请检查网络连接后重试"];
        }
      }
        option:option];
}

@end
