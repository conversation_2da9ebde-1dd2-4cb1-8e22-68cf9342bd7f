#import "FTHZChannelDetailVC.h"

#import "CenterTouchTableView.h"
#import "ChannelCommentCell.h"
#import "FTHZChannelImgReplyVC.h"
#import "FTHZChannelReplyModel.h"
#import "FTHZChannelVoiceReplyVC.h"
#import "XHInputView.h"

#import "LGAudioKit.h"
#import "WhaleDetailVC.h"

#import "PopoverView.h"
#import "ReportModel.h"
#import "ReportVC.h"

#import "BlackUserModel.h"

@interface UIImageView (Shadow)
- (void)applyshadowWithCorner:(UIView *)containerView
                cornerRadious:(CGFloat)cornerRadious;
@end

@implementation UIImageView (Shadow)
- (void)applyshadowWithCorner:(UIView *)containerView
                cornerRadious:(CGFloat)cornerRadious {
  containerView.layer.shadowColor = [UIColor whiteColor].CGColor;
  containerView.layer.shadowOffset = CGSizeMake(0, 0);
  containerView.layer.shadowOpacity = 0.5;
  containerView.layer.shadowRadius = 4.0;
  containerView.layer.cornerRadius = cornerRadious;
  containerView.clipsToBounds = NO;
}
@end

@interface FTHZChannelDetailVC () <
    UITableViewDelegate, XHInputViewDelagete, ChannelCommentCellDelgate,
    UITableViewDataSource, UIScrollViewDelegate, FTHZChannelReplyVCDelegate,
    NicknameDelegate> {
  NSInteger currentPage;
}
@property(nonatomic, strong) CenterTouchTableView *mainTableView;
@property(nonatomic, strong) ChannelCommentDetailModel *commentDetailModel;
@property(nonatomic, strong) NSArray *cellData;
@property(nonatomic, strong) UIImageView *placeHolderIV;
@property(nonatomic, strong) UIView *bottomInput;
@property(nonatomic, strong) FTHZChannelImgReplyVC *replyBgVC;
@end

@implementation FTHZChannelDetailVC
#pragma mark - netrequest

- (void)loadChannleContent {
  @weakify(self);
  [FTHZChannelDetailModel getChannelDetailBy:self.discussId
      succes:^(NSDictionary *resultObject) {
        @strongify(self);
        FTHZChannelDetailModel *detailModel =
            [FTHZChannelDetailModel mj_objectWithKeyValues:resultObject];
        if ([detailModel.success boolValue]) {
          self.commentDetailModel = [ChannelCommentDetailModel
              mj_objectWithKeyValues:detailModel.data.firstObject];
          [self.mainTableView reloadData];
          [self loadMoreData];
        } else {
          [self showToastFast:detailModel.msg];
        }
      }
      failure:^(NSError *requestErr) {
        [self showToastFast:@"数据有误,请检查网络后重试"];
      }];
}
- (void)loadPostCommentAction:(NSString *)sendText
                       sendIM:(BOOL)sendIM
                       rowdex:(NSInteger)rowdex
                       secdex:(NSInteger)setion {
  [HUD show];
  NSString *userid = @"";
  NSString *toUserid = @"";
  NSString *discussID = @"";
  NSString *toCommentid = @"";
  NSString *belongs = @"";
  @weakify(self);
  ChannelCommentsReplyResult *dy = [_cellData objectAtIndex:setion - 1];
  if (dy.data.count > 0 && rowdex != 0) {
    ChannelCommentsToReplyResult *redy = [ChannelCommentsToReplyResult
        mj_objectWithKeyValues:[dy.data objectAtIndex:rowdex - 1]];

    userid = dy.userid;
    toUserid = redy.userid;
    toCommentid = redy.commentid;
  } else {
    toUserid = dy.userid;
    toCommentid = dy.commentid;
  }
  userid = self.commentDetailModel.userid;

  belongs = dy.commentid;
  discussID = self.commentDetailModel.discussId;
  [FTHZChannelReplyModel postChannelCommentModel:userid
      contentid:discussID
      comment:sendText
      to_userid:toUserid
      to_commentid:toCommentid
      belongs:belongs
      type:@"1"
      voiceFile:@""
      voiceTime:@"0"
      images:@""
      success:^(NSDictionary *resultObject) {
        @strongify(self);
        FTHZChannelReplyModel *member =
            [FTHZChannelReplyModel mj_objectWithKeyValues:resultObject];
        [HUD dissmiss];
        if ([member.success boolValue]) {
          if ([[USERDEFAULT objectForKey:ContenText] isValid]) {
            NSString *contentText = @"";
            [USERDEFAULT setObject:contentText forKey:ContenText];
            [USERDEFAULT synchronize];
          }
          [self showToastFast:@"评论成功"];

          [self completeReply];

        } else {
          [self showToastFast:member.msg];
        }
      }
      failure:^(NSError *requestErr) {
        [HUD dissmiss];
        [self showToastFast:@"数据有误,请检查网络后重试"];
      }];
}

#pragma mark - private

- (void)loadCommentData {
  @weakify(self);
  if (currentPage == 1) {
    self.cellData = [NSArray new];
  }

  [ChannelCommentsModel getChannelCommentsModel:self.discussId
      authorid:self.commentDetailModel.userid
      page:[NSString stringWithFormat:@"%ld", currentPage]
      size:@"50"
      success:^(NSDictionary *resultObject) {
        @strongify(self);
        ChannelCommentsModel *member =
            [ChannelCommentsModel mj_objectWithKeyValues:resultObject];
        if ([member.success boolValue]) {
          NSMutableArray *tempDataArr =
              [NSMutableArray arrayWithArray:self.cellData];
          ChannelCommentsModelResult *tempCommentRes =
              [ChannelCommentsModelResult
                  mj_objectWithKeyValues:member.data.firstObject];

          NSArray *newData = tempCommentRes.data;

          if (newData.count == 0) {
            [self.mainTableView.mj_footer removeFromSuperview];
            self.mainTableView.mj_footer = nil;
            [self.mainTableView reloadData];
            return;
          }

          BOOL hasAddedNewData = NO;

          for (int i = 0; i < tempCommentRes.data.count; i++) {
            ChannelCommentsReplyResult *replyRes = [ChannelCommentsReplyResult
                mj_objectWithKeyValues:tempCommentRes.data[i]];
            BOOL isDuplicate = NO;
            for (ChannelCommentsReplyResult *existingReply in tempDataArr) {
              if ([existingReply.commentid
                      isEqualToString:replyRes.commentid]) {
                isDuplicate = YES;
                break;
              }
            }

            if (!isDuplicate) {
              [tempDataArr addObject:replyRes];
              hasAddedNewData = YES;
            }
          }

          if (!hasAddedNewData) {
            [self.mainTableView.mj_footer removeFromSuperview];
            self.mainTableView.mj_footer = nil;

            [self.mainTableView reloadData];
            return;
          }

          self.cellData = [tempDataArr copy];

          int serverMainCount = [tempCommentRes.mainCount integerValue];
          int serverTotalCount = [tempCommentRes.count integerValue];

          BOOL hasMoreData =
              (serverMainCount > tempDataArr.count) && hasAddedNewData;

          if (hasMoreData) {
            if (!self.mainTableView.mj_footer) {
              MJChiBaoZiFooter *footer = [MJChiBaoZiFooter
                  footerWithRefreshingTarget:self
                            refreshingAction:@selector(loadMoreData)];
              footer.refreshingTitleHidden = YES;
              self.mainTableView.mj_footer = footer;
            } else {
              [self.mainTableView.mj_footer resetNoMoreData];
              [self.mainTableView.mj_footer endRefreshing];
            }
          } else {
            [self.mainTableView.mj_footer removeFromSuperview];
            self.mainTableView.mj_footer = nil;
          }

          if (self.cellData.count == 0) {
            [self.mainTableView addSubview:self.placeHolderIV];
            self.placeHolderIV.center = self.mainTableView.center;
          } else {
            [self.placeHolderIV removeFromSuperview];
          }
          [self.mainTableView reloadData];
        }
      }
      failure:^(NSError *requestErr) {
        @strongify(self);
        if (currentPage > 1) {
          currentPage--;
        }
        if (self.mainTableView.mj_footer) {
          [self.mainTableView.mj_footer endRefreshing];
        }
      }];
}

- (void)gotoMessagePage:(UIButton *)sender {
  PopoverView *popoverView = [PopoverView popoverView];
  popoverView.showShade = YES;
  [popoverView showToView:sender withActions:[self QQActions]];
}
- (NSArray<PopoverAction *> *)QQActions {
  @weakify(self);
  if (![CurrentUser.userid isEqualToString:self.commentDetailModel.userid]) {
    PopoverAction *multichatAction = [PopoverAction
        actionWithImage:[UIImage imageNamed:@"Report"]
                  title:@"举报"
                handler:^(PopoverAction *action) {
                  @strongify(self);
                  ReportVC *ddVC = [[ReportVC alloc] init];
                  ddVC.delegate = self;
                  [self.navigationController pushViewController:ddVC
                                                       animated:YES];
                }];
    PopoverAction *addFriAction = [PopoverAction
        actionWithImage:[UIImage imageNamed:@"more-lahei"]
                  title:@"拉黑"
                handler:^(PopoverAction *action) {
                  @strongify(self);
                  UIAlertController *alertController = [UIAlertController
                      alertControllerWithTitle:@"确定拉黑该用户?"
                                       message:@"拉黑后你们将互相看不到对方的动"
                                               @"态。如需解除，请在设置-"
                                               @"黑名单管理中移除黑名单。"
                                preferredStyle:UIAlertControllerStyleAlert];
                  UIAlertAction *cancelAction =
                      [UIAlertAction actionWithTitle:@"取消"
                                               style:UIAlertActionStyleCancel
                                             handler:nil];
                  UIAlertAction *okAction = [UIAlertAction
                      actionWithTitle:@"确定"
                                style:UIAlertActionStyleDefault
                              handler:^(UIAlertAction *_Nonnull action) {
                                @strongify(self);
                                [self loadLaheiUser];
                              }];

                  [alertController addAction:cancelAction];
                  [alertController addAction:okAction];
                  [self presentViewController:alertController
                                     animated:YES
                                   completion:nil];
                }];

    return @[ multichatAction, addFriAction ];
  } else {
    PopoverAction *deleteAction = [PopoverAction
        actionWithImage:[UIImage imageNamed:@"more-delete"]
                  title:@"删除"
                handler:^(PopoverAction *action) {
                  UIAlertController *alertController = [UIAlertController
                      alertControllerWithTitle:@"提示"
                                       message:@"确定删除该动态?"
                                preferredStyle:UIAlertControllerStyleAlert];
                  UIAlertAction *cancelAction =
                      [UIAlertAction actionWithTitle:@"取消"
                                               style:UIAlertActionStyleCancel
                                             handler:nil];
                  UIAlertAction *okAction = [UIAlertAction
                      actionWithTitle:@"确定"
                                style:UIAlertActionStyleDefault
                              handler:^(UIAlertAction *_Nonnull action) {
                                @strongify(self);
                                [self loadDelChannel];
                              }];

                  [alertController addAction:cancelAction];
                  [alertController addAction:okAction];
                  [self presentViewController:alertController
                                     animated:YES
                                   completion:nil];
                }];
    return @[ deleteAction ];
  }
}

- (void)setupUI {
  [self.view addSubview:self.mainTableView];
  @weakify(self);
  UIButton *button = [[UIButton alloc] initWithFrame:CGRectMake(0, 0, 10, 15)];
  [button setImage:KImage_name(@"back") forState:UIControlStateNormal];
  [button setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
  [button addTarget:self
                action:@selector(back)
      forControlEvents:UIControlEventTouchUpInside];
  [self.safeContentView addSubview:button];
  [button mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.equalTo(self.safeContentView).offset(24 * kWidthFactor);
    make.top.equalTo(self.safeContentView).offset(12 * kWidthFactor);
  }];
  UILabel *titleL = UILabel.new;
  titleL.text = @"声波详情";
  titleL.textColor = KColor_HighBlack;
  titleL.font = SourceHanSerifSemiBoldFont(16 * kWidthFactor);
  [self.safeContentView addSubview:titleL];
  [titleL mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.centerX.equalTo(self.safeContentView);
    make.centerY.equalTo(button);
  }];

  UIButton *_messageButton = [UIButton buttonWithType:(UIButtonTypeCustom)];
  [_messageButton setImage:[UIImage imageNamed:@"more-black"]
                  forState:(UIControlStateNormal)];
  _messageButton.frame = CGRectMake(0, 0, 25, 25);
  _messageButton.adjustsImageWhenHighlighted = YES;
  [_messageButton addTarget:self
                     action:@selector(gotoMessagePage:)
           forControlEvents:(UIControlEventTouchUpInside)];
  [self.safeContentView addSubview:_messageButton];
  [_messageButton mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.right.equalTo(self.safeContentView.mas_right)
        .offset(-24 * kWidthFactor);
    make.centerY.equalTo(button);
  }];

  self.bottomInput = [[UIView alloc] init];
  self.bottomInput.backgroundColor = KColor_HighBlack;
  self.bottomInput.layer.cornerRadius = 20 * kWidthFactor;
  if (@available(iOS 11.0, *)) {
    self.bottomInput.layer.maskedCorners =
        kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
  }
  self.bottomInput.layer.masksToBounds = YES;
  [self.safeContentView addSubview:self.mainTableView];
  [self.safeContentView addSubview:self.bottomInput];

  [self.mainTableView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.right.equalTo(self.safeContentView);
    make.top.equalTo(button.mas_bottom).offset(5 * kWidthFactor);
    make.bottom.equalTo(self.view);
  }];

  [self.bottomInput mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.right.equalTo(self.safeContentView);
    make.bottom.equalTo(self.view);
    make.height.mas_equalTo(80 * kWidthFactor);
  }];

  UIButton *touchArea = [UIButton buttonWithType:UIButtonTypeCustom];
  [touchArea addTarget:self
                action:@selector(replyAction)
      forControlEvents:UIControlEventTouchUpInside];
  [self.bottomInput addSubview:touchArea];
  [touchArea mas_makeConstraints:^(MASConstraintMaker *make) {
    make.edges.equalTo(self.bottomInput);
  }];

  UILabel *tipLabel = [[UILabel alloc] init];
  tipLabel.text = @"呼应";
  tipLabel.textColor = [UIColor whiteColor];
  tipLabel.font = SourceHanSerifBoldFont(14 * kWidthFactor);
  [self.bottomInput addSubview:tipLabel];
  [tipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.right.equalTo(self.bottomInput).offset(-24 * kWidthFactor);
    make.centerY.equalTo(self.bottomInput);
  }];

  UIImageView *replyImageView =
      [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"回复w2"]];
  [self.bottomInput addSubview:replyImageView];
  [replyImageView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.right.equalTo(tipLabel.mas_left).offset(-12 * kWidthFactor);
    make.centerY.equalTo(self.bottomInput);
  }];

  self.mainTableView.estimatedRowHeight = 72 * kWidthFactor;
  self.mainTableView.rowHeight = UITableViewAutomaticDimension;
}
- (void)back {
  [self.navigationController popViewControllerAnimated:YES];
}

- (void)loadMoreData {
  if (self.mainTableView.mj_footer &&
      self.mainTableView.mj_footer.state == MJRefreshStateNoMoreData) {
    return;
  }

  if (currentPage > 100) {
    [self.mainTableView.mj_footer endRefreshingWithNoMoreData];
    return;
  }
  currentPage += 1;
  [self loadCommentData];
}

#pragma mark - lifecycle
- (void)viewDidLoad {
  [super viewDidLoad];
  [UIApplication sharedApplication].applicationSupportsShakeToEdit = NO;
  self.navigationController.interactivePopGestureRecognizer.delegate = self;
  [self setupUI];
  [self loadChannleContent];
}
- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];
  self.edgesForExtendedLayout = UIRectEdgeNone;
}

- (void)viewWillDisappear:(BOOL)animated {
  [super viewWillDisappear:animated];
  [[LGAudioPlayer sharePlayer] stopAudioPlayer];
}

- (void)viewDidDisappear:(BOOL)animated {
  [super viewDidDisappear:animated];
  if ([[USERDEFAULT objectForKey:ContenText] isValid]) {
    NSString *contentText = @"";
    [USERDEFAULT setObject:contentText forKey:ContenText];
    [USERDEFAULT synchronize];
  }
}

#pragma mark - 录音

#pragma mark - event handle

- (void)tapUserInfo {
  [self tapUserAvtarAction:self.commentDetailModel.userid];
}
- (void)tauchNameBtn:(UIButton *)sender {
  NSString *idStr = [NSString stringWithFormat:@"%ld", sender.tag];
  [self tapUserAvtarAction:idStr];
}

- (void)loadDelChannel {
  @weakify(self);
  [FTHZDeleteChannelModel delChannelDiscuss:self.discussId
      success:^(NSDictionary *resultObject) {
        @strongify(self);
        FTHZDeleteChannelModel *member =
            [FTHZDeleteChannelModel mj_objectWithKeyValues:resultObject];
        if ([member.success boolValue]) {
          [self showToastFast:@"删除成功"];
          [self performSelector:@selector(backAction)
                     withObject:nil
                     afterDelay:1.0];
        } else {
          [self showToastFast:member.msg];
        }
      }
      failure:^(NSError *requestErr) {
        @strongify(self);
        [self showToastFast:@"删除失败,请重试"];
      }];
}

- (void)loadReportData:(NSString *)report_type detail:(NSString *)detail {
  __weak typeof(self) wSelf = self;
  [ReportModel postReportModel:self.commentDetailModel.userid
      report_type:report_type
      type:@"3"
      rid:self.discussId
      detail:detail
      success:^(NSDictionary *resultObject) {
        ReportModel *member = [ReportModel mj_objectWithKeyValues:resultObject];
        if ([member.success boolValue]) {
          [wSelf showToastFast:@"举报成功"];
        } else {
          [wSelf showToastFast:member.msg];
        }
      }
      failure:^(NSError *requestErr) {
        [wSelf showToastFast:@"举报失败,请重试"];
      }];
}

- (void)loadLaheiUser {
  __weak typeof(self) wSelf = self;
  [BlackUserModel postBlackUserModel:self.commentDetailModel.userid
      success:^(NSDictionary *resultObject) {
        BlackUserModel *member =
            [BlackUserModel mj_objectWithKeyValues:resultObject];
        if ([member.success boolValue]) {
          [wSelf showToastFast:@"拉黑成功"];
          [wSelf performSelector:@selector(backAction)
                      withObject:nil
                      afterDelay:1.0];
        } else {
          [wSelf showToastFast:member.msg];
        }
      }
      failure:^(NSError *requestErr) {
        [wSelf showToastFast:@"拉黑失败,请重试"];
      }];
}

- (void)backAction {
  [self.navigationController popViewControllerAnimated:YES];
}

- (void)tapUserAvtarAction:(NSString *)userid {
  WhaleDetailVC *infoVC = [[WhaleDetailVC alloc] init];
  infoVC.uid = userid;
  [self.navigationController pushViewController:infoVC animated:YES];
}

#pragma mark - tableView Delegate
- (CGFloat)tableView:(UITableView *)tableView
    heightForFooterInSection:(NSInteger)section {
  BOOL noMoreData = (self.mainTableView.mj_footer == nil);
  if (noMoreData && section == self.cellData.count + 1) {
    return 0;
  }
  if (section == 0)
    return 0;
  return 12.0;
}

- (UIView *)tableView:(UITableView *)tableView
    viewForFooterInSection:(NSInteger)section {
  if (section == 0) {
    return nil;
  }
  UIView *footerView = [[UIView alloc] init];
  footerView.backgroundColor = [UIColor clearColor];
  footerView.frame = CGRectMake(0, 0, tableView.frame.size.width, 12.0);
  return footerView;
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForHeaderInSection:(NSInteger)section {
  return 0;
}

- (UIView *)tableView:(UITableView *)tableView
    viewForHeaderInSection:(NSInteger)section {
  return nil;
}

- (CGFloat)tableView:(UITableView *)tableView
    estimatedHeightForHeaderInSection:(NSInteger)section {
  return 0;
}

- (NSInteger)tableView:(UITableView *)tableView
    numberOfRowsInSection:(NSInteger)section {
  BOOL noMoreData = (self.mainTableView.mj_footer == nil);
  if (noMoreData && section == self.cellData.count + 1) {
    return 1;
  }
  if (section == 0)
    return 1;
  ChannelCommentsReplyResult *model =
      [self.cellData objectAtIndex:(section - 1)];
  return 1 + model.data.count;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
  BOOL noMoreData = (self.mainTableView.mj_footer == nil);
  return self.cellData.count + 1 + (noMoreData ? 1 : 0);
}

- (UITableViewCell *)tableView:(UITableView *)tableView
         cellForRowAtIndexPath:(NSIndexPath *)indexPath {
  BOOL noMoreData = (self.mainTableView.mj_footer == nil);
  if (noMoreData && indexPath.section == self.cellData.count + 1) {
    UITableViewCell *cell =
        [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault
                               reuseIdentifier:@"blankCell"];
    cell.backgroundColor = [UIColor clearColor];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    return cell;
  }
  if (indexPath.section == 0) {
    return [self headerCellView];
  }

  static NSString *cellIdentifier = @"channelCommentCell";
  ChannelCommentCell *cell =
      [tableView dequeueReusableCellWithIdentifier:cellIdentifier];
  if (!cell) {
    cell = [[ChannelCommentCell alloc] initWithStyle:UITableViewCellStyleDefault
                                     reuseIdentifier:cellIdentifier];
  }
  cell.delegate = self;
  cell.discussID = self.discussId;

  @weakify(self);
  ChannelCommentsReplyResult *model =
      [self.cellData objectAtIndex:(indexPath.section - 1)];

  cell.tapIconAction = ^(NSString *uid) {
    @strongify(self);
    [self tapUserAvtarAction:uid];
  };

  if (model.data.count > 0 && indexPath.row > 0) {
    ChannelCommentsToReplyResult *toReply = [ChannelCommentsToReplyResult
        mj_objectWithKeyValues:model.data[indexPath.row - 1]];
    cell.toReplyModel = toReply;

    [cell.AnameBtn addTarget:self
                      action:@selector(tauchNameBtn:)
            forControlEvents:UIControlEventTouchUpInside];
    [cell.BnameBtn addTarget:self
                      action:@selector(tauchNameBtn:)
            forControlEvents:UIControlEventTouchUpInside];

    cell.AnameBtn.tag = [toReply.userid integerValue];
    cell.BnameBtn.tag = [toReply.to_userid integerValue];
  } else {
    [cell setReplyModel:model withIndex:(int)indexPath.section];
  }

  return cell;
}

- (void)tableView:(UITableView *)tableView
    didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
  if (indexPath.section != 0) {
    [self showXHInputViewWithStyle:InputViewStyleNoSendDefault
                            rowdex:indexPath.row
                            secdex:indexPath.section];
  }
  [tableView deselectRowAtIndexPath:indexPath animated:YES];
}

- (void)showXHInputViewWithStyle:(InputViewStyle)style
                          rowdex:(NSInteger)rowdex
                          secdex:(NSInteger)secdex {
  @weakify(self);

  [XHInputView showWithStyle:style
      configurationBlock:^(XHInputView *inputView) {
        @strongify(self);
        inputView.delegate = self;
        inputView.placeholder = @"呼应TA的声波";
        inputView.maxCount = 2000;
        inputView.textViewBackgroundColor =
            [UIColor groupTableViewBackgroundColor];
      }
      sendBlock:^BOOL(NSString *text, BOOL selTemp) {
        @strongify(self);

        if (text.length) {
          [self loadPostCommentAction:text
                               sendIM:selTemp
                               rowdex:rowdex
                               secdex:secdex];
          return YES;
        } else {
          return NO;
        }
      }];
}

#pragma mark - relplyview delegate

- (void)completeReply {
  [self loadCommentData];
  self.bottomInput.hidden = NO;
}

- (void)cancelReplyView {
  self.bottomInput.hidden = NO;
}

#pragma mark - cell delegate
- (void)deleteComplete:(BOOL)success {
  if (success) {
    [self loadCommentData];
  } else {
    [self showToastFast:@"数据有误,请检查网络后重试"];
  }
}

- (void)likeAction:(NSString *)errMsg {
  [self showToastFast:errMsg];
}
#pragma mark - XHInputViewDelagete
- (void)xhInputViewWillShow:(XHInputView *)inputView {
}

- (void)xhInputViewWillHide:(XHInputView *)inputView {
}

- (void)dealloc {
  [NOTIFICENTER removeObserver:self];
}

- (void)delegateGetReport:(NSInteger)RelationshipType
                   detail:(NSString *)detail {
  [self loadReportData:[NSString stringWithFormat:@"%ld", RelationshipType]
                detail:detail];
}

#pragma mark - getter/setter

- (UITableViewCell *)headerCellView {
  UITableViewCell *headCell =
      [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault
                             reuseIdentifier:nil];
  headCell.selectionStyle = UITableViewCellSelectionStyleNone;
  UIView *headerContent = [UIView new];
  headerContent.backgroundColor = KColor_HighBlack;
  headerContent.layer.cornerRadius = 12.0 * kWidthFactor;
  headerContent.layer.masksToBounds = YES;
  [headCell.contentView addSubview:headerContent];
  [headerContent mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(headCell.contentView).offset(16 * kWidthFactor);
    make.right.equalTo(headCell.contentView).offset(-16 * kWidthFactor);
    make.top.equalTo(headCell.contentView).offset(16 * kWidthFactor);
    make.bottom.equalTo(headCell.contentView).offset(-24 * kWidthFactor);
  }];

  UIImageView *typeIcon = [[UIImageView alloc] init];
  typeIcon.image =
      [UIImage imageNamed:[self.commentDetailModel.voice_type boolValue]
                              ? @"channel_voice"
                              : @"channel_capture"];
  [headerContent addSubview:typeIcon];
  [typeIcon mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(headerContent).offset(24 * kWidthFactor);
    make.top.equalTo(headerContent).offset(24 * kWidthFactor);
  }];

  UILabel *titleL = [UILabel new];
  titleL.font = SourceHanSerifSemiBoldFont(14);
  titleL.textColor = [UIColor whiteColor];
  titleL.text = self.commentDetailModel.title;
  [headerContent addSubview:titleL];
  [titleL mas_makeConstraints:^(MASConstraintMaker *make) {
    make.centerY.equalTo(typeIcon);
    make.left.equalTo(headerContent).offset(50 * kWidthFactor);
    make.right.equalTo(headerContent).offset(-24 * kWidthFactor);
  }];

  UILabel *contentL = [UILabel new];
  NSString *contentStr =
      [self.commentDetailModel.content replaceCharcter:@"\\n"
                                          withCharcter:@"\n"];

  if (contentStr.length > 0) {
    contentL.text = [contentStr
        stringByTrimmingCharactersInSet:[NSCharacterSet
                                            whitespaceAndNewlineCharacterSet]];
    contentL.font = SourceHanSerifRegularFont(12);
    contentL.textColor = [UIColor whiteColor];
    contentL.numberOfLines = 0;
    contentL.lineBreakMode = NSLineBreakByWordWrapping;
    [headerContent addSubview:contentL];

    [contentL mas_makeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(typeIcon);
      make.right.equalTo(titleL);
      make.top.equalTo(titleL.mas_bottom).offset(16 * kWidthFactor);
    }];
  }

  UIView *avatarBgView = [[UIView alloc] init];
  [headerContent addSubview:avatarBgView];
  [avatarBgView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.width.height.mas_equalTo(20 * kWidthFactor);
    make.left.equalTo(headerContent).offset(22 * kWidthFactor);
    if (contentStr.length > 0) {
      make.top.equalTo(contentL.mas_bottom).offset(16 * kWidthFactor);
    } else {
      make.top.equalTo(titleL.mas_bottom).offset(16 * kWidthFactor);
    }
    make.bottom.equalTo(headerContent).offset(-12 * kWidthFactor);
  }];

  UIImageView *avatarIcon = [[UIImageView alloc] init];
  avatarIcon.userInteractionEnabled = YES;
  [avatarIcon addGestureRecognizer:[[UITapGestureRecognizer alloc]
                                       initWithTarget:self
                                               action:@selector(tapUserInfo)]];
  avatarIcon.layer.cornerRadius = 10;
  avatarIcon.layer.masksToBounds = YES;
  [avatarBgView addSubview:avatarIcon];
  [avatarIcon mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.right.top.bottom.equalTo(avatarBgView);
  }];

  [avatarIcon applyshadowWithCorner:avatarBgView cornerRadious:10.0];

  [avatarIcon
      sd_setImageWithURL:[NemoUtil
                             getUrlWithUserSmallIcon:self.commentDetailModel
                                                         .avatar]
        placeholderImage:KImage_name(@"empty")];

  UILabel *nickNameL = [UILabel new];
  nickNameL.text = self.commentDetailModel.nickname;
  nickNameL.textColor = [UIColor whiteColor];
  nickNameL.font = SourceHanSerifBoldFont(12 * kWidthFactor);
  [headerContent addSubview:nickNameL];
  [nickNameL mas_makeConstraints:^(MASConstraintMaker *make) {
    make.centerY.equalTo(avatarIcon);
    make.left.equalTo(avatarIcon.mas_right).offset(8 * kWidthFactor);
  }];

  UILabel *timeLabel = [UILabel new];
  timeLabel.text =
      [NemoUtil distanceTimeWithBeforeTime:[self.commentDetailModel
                                                   .create_time doubleValue]];
  timeLabel.textColor = [UIColor whiteColor];
  timeLabel.textAlignment = NSTextAlignmentRight;
  timeLabel.font = [UIFont systemFontOfSize:12 * kWidthFactor];
  [headerContent addSubview:timeLabel];
  [timeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.centerY.equalTo(avatarIcon);
    make.right.equalTo(headerContent).offset(-24 * kWidthFactor);
    make.width.mas_equalTo(140 * kWidthFactor);
  }];

  UILabel *commentCountLabel = [UILabel new];
  commentCountLabel.text = [NSString
      stringWithFormat:@"呼应 %@", self.commentDetailModel.commentNum];
  commentCountLabel.textColor = KColor_textTinyGray;
  commentCountLabel.font = SourceHanSerifBoldFont(12 * kWidthFactor);
  [headCell.contentView addSubview:commentCountLabel];
  [commentCountLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(headCell.contentView).offset(24 * kWidthFactor);
    make.top.equalTo(headerContent.mas_bottom).offset(12 * kWidthFactor);
    make.height.mas_equalTo(12 * kWidthFactor);
  }];
  return headCell;
}

- (UITableView *)mainTableView {
  if (!_mainTableView) {
    self.mainTableView =
        [[CenterTouchTableView alloc] initWithFrame:CGRectZero
                                              style:UITableViewStylePlain];
    _mainTableView.delegate = self;
    _mainTableView.dataSource = self;
    _mainTableView.showsVerticalScrollIndicator = NO;
    [_mainTableView setSeparatorStyle:UITableViewCellSeparatorStyleNone];
    [_mainTableView registerClass:[ChannelCommentCell class]
           forCellReuseIdentifier:@"channelCommentCell"];

    _mainTableView.estimatedRowHeight = 0;
    _mainTableView.estimatedSectionHeaderHeight = 0;
    _mainTableView.estimatedSectionFooterHeight = 0;
    _mainTableView.rowHeight = UITableViewAutomaticDimension;

    if (@available(iOS 11.0, *)) {
      _mainTableView.contentInsetAdjustmentBehavior =
          UIScrollViewContentInsetAdjustmentNever;
    }
  }
  return _mainTableView;
}

- (UIImageView *)placeHolderIV {
  if (_placeHolderIV == nil) {
    _placeHolderIV = [[UIImageView alloc]
        initWithImage:[UIImage imageNamed:@"placeHolderComment"]];
  }

  return _placeHolderIV;
}

- (void)replyAction {
  self.bottomInput.hidden = YES;

  if ([self.commentDetailModel.voice_type boolValue]) {
    self.replyBgVC = [FTHZChannelVoiceReplyVC new];
  } else {
    self.replyBgVC = [FTHZChannelImgReplyVC new];
  }

  self.replyBgVC.delegate = self;
  self.replyBgVC.detailModel = self.commentDetailModel;
  self.replyBgVC.modalPresentationStyle = UIModalPresentationOverCurrentContext;
  self.replyBgVC.modalTransitionStyle = UIModalTransitionStyleCrossDissolve;
  [self presentViewController:self.replyBgVC animated:YES completion:nil];
}

- (void)scrollViewWillEndDragging:(UIScrollView *)scrollView
                     withVelocity:(CGPoint)velocity
              targetContentOffset:(inout CGPoint *)targetContentOffset {
  if (velocity.y > 0) {
    [UIView animateWithDuration:0.25
                     animations:^{
                       self.bottomInput.transform =
                           CGAffineTransformMakeTranslation(
                               0, self.bottomInput.frame.size.height);
                     }];
  } else if (velocity.y < 0) {
    [UIView animateWithDuration:0.25
                     animations:^{
                       self.bottomInput.transform = CGAffineTransformIdentity;
                     }];
  }
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForRowAtIndexPath:(NSIndexPath *)indexPath {
  BOOL noMoreData = (self.mainTableView.mj_footer == nil);
  if (noMoreData && indexPath.section == self.cellData.count + 1) {
    CGFloat blankHeight = 90 * kWidthFactor;
    return blankHeight;
  }
  return UITableViewAutomaticDimension;
}

@end
