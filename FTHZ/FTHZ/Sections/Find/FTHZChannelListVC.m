#import "FTHZChannelListVC.h"
#import "FTHZChannelDetailVC.h"
#import "FTHZCreateChannelVC.h"
#import "StraitListCell.h"

@interface FTHZChannelListVC () <UITableViewDataSource, UITableViewDelegate,
                                 UIScrollViewDelegate>
@property(nonatomic, strong) UITableView *tableView;
@property(nonatomic, strong) NSArray *listData;
@property(nonatomic, strong) FlatButton *createBt;
@property(nonatomic, assign) NSInteger currentPage;

@end

#define ChannelCellID @"channelCellID"

@implementation FTHZChannelListVC

#pragma makr - private
- (void)setupUI {

  self.navigationItem.title = self.straitModel.name;
  self.edgesForExtendedLayout = UIRectEdgeNone;
  _tableView = [[UITableView alloc]
      initWithFrame:CGRectMake(0, self.safeAreaInset.top, kMainWidth,
                               kMainHeight - KHomeNavHeight -
                                   self.safeAreaInset.bottom -
                                   self.safeAreaInset.top)
              style:UITableViewStylePlain];
  [_tableView registerClass:[StraitListCell class]
      forCellReuseIdentifier:ChannelCellID];

  _tableView.delegate = self;
  _tableView.dataSource = self;
  _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
  [self.view addSubview:_tableView];

  MJChiBaoZiHeader *header =
      [MJChiBaoZiHeader headerWithRefreshingTarget:self
                                  refreshingAction:@selector(loadData)];
  header.lastUpdatedTimeLabel.hidden = YES;
  header.stateLabel.hidden = YES;
  [header beginRefreshing];
  _tableView.mj_header = header;

  _createBt = [[FlatButton alloc] init];
  [_createBt setBackgroundImage:[UIImage imageNamed:@"channel_oval"]
                       forState:UIControlStateNormal];
  [_createBt setImage:[UIImage imageNamed:@"channel_add"]
             forState:UIControlStateNormal];
  [_createBt addTarget:self
                action:@selector(createAction)
      forControlEvents:UIControlEventTouchUpInside];
  [self.view addSubview:_createBt];
  @weakify(self);
  [_createBt mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.width.height.mas_equalTo(54);
    make.right.equalTo(self.view).offset(-15);
    make.bottom.equalTo(self.view.mas_bottom)
        .offset(-self.safeAreaInset.bottom - 14);
  }];

  MJChiBaoZiFooter *footer =
      [MJChiBaoZiFooter footerWithRefreshingTarget:self
                                  refreshingAction:@selector(loadMoreData)];
  footer.refreshingTitleHidden = YES;
  self.tableView.mj_footer = footer;

  self.currentPage = 1;
}

- (void)loadData {
  @weakify(self);
  [FTHZChannelListModel getChannelListBy:self.straitModel.islandId
      sort:@"1"
      page:[NSString stringWithFormat:@"%ld", self.currentPage]
      size:@"20"
      succes:^(NSDictionary *resultObject) {
        @strongify(self);
        [self.tableView.mj_header endRefreshing];
        [self.tableView.mj_footer endRefreshing];

        FTHZChannelListModel *cellres =
            [FTHZChannelListModel mj_objectWithKeyValues:resultObject];
        if ([cellres.success boolValue]) {
          FTHZChannelListCellModel *listRes = cellres.data;
          NSMutableArray *tempArr = [NSMutableArray new];
          for (int i = 0; i < listRes.data.count; i++) {
            FTHZChannelModel *model =
                [FTHZChannelModel mj_objectWithKeyValues:listRes.data[i]];
            [tempArr addObject:model];
          }
          if (self.currentPage == 1) {
            self.listData = [tempArr copy];
          } else {
            NSMutableArray *newData =
                [NSMutableArray arrayWithArray:self.listData];
            [newData addObjectsFromArray:tempArr];
            self.listData = [newData copy];
          }
          if ([listRes.count integerValue] > self.listData.count) {
            if (!self.tableView.mj_footer) {
              MJChiBaoZiFooter *footer = [MJChiBaoZiFooter
                  footerWithRefreshingTarget:self
                            refreshingAction:@selector(loadMoreData)];
              footer.refreshingTitleHidden = YES;
              self.tableView.mj_footer = footer;
            }
          } else {
            [self.tableView.mj_footer removeFromSuperview];
            self.tableView.mj_footer = nil;
          }
          [self.tableView reloadData];
        }
      }
      failure:^(NSError *requestErr) {
        @strongify(self);
        [self.tableView.mj_header endRefreshing];
        [self.tableView.mj_footer endRefreshing];

        [self.view makeToast:@"数据有误,请检查网络后重试"
                    duration:2.0
                    position:CSToastPositionCenter];
      }];
}

- (void)loadMoreData {
  self.currentPage += 1;
  [self loadData];
}

- (void)loadAData {
  if (_tableView) {
    self.currentPage = 1;
    [self loadData];
  }
}

- (void)createAction {
  FTHZCreateChannelVC *vc = [[FTHZCreateChannelVC alloc] init];
  vc.channelId = self.straitModel.islandId;
  vc.isVoiceType = ([self.straitModel.islandId intValue] == 1);
  vc.modalPresentationStyle = UIModalPresentationFullScreen;
  [self presentViewController:vc animated:YES completion:nil];
}
#pragma mark - lifecycle
- (void)viewDidLoad {
  [super viewDidLoad];
  [self setupUI];
}

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];
  [self.navigationController setNavigationBarHidden:NO animated:YES];
  self.showBackBtn = YES;
  [self loadData];
}

- (void)viewWillDisappear:(BOOL)animated {
  [super viewWillDisappear:animated];
  [self.navigationController setNavigationBarHidden:YES animated:NO];
  self.showBackBtn = NO;
}

#pragma mark -delegate
- (CGFloat)tableView:(UITableView *)tableView
    heightForFooterInSection:(NSInteger)section {
  return 40;
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForHeaderInSection:(NSInteger)section {
  return 12 * kMainTemp;
}

- (UIView *)tableView:(UITableView *)tableView
    viewForFooterInSection:(NSInteger)section {
  UIView *fView =
      [[UIView alloc] initWithFrame:CGRectMake(0, 0, kMainWidth, 40)];
  UILabel *ttlabel =
      [[UILabel alloc] initWithFrame:CGRectMake(0, 0, kMainWidth, 40)];

  ttlabel.textAlignment = NSTextAlignmentCenter;
  ttlabel.textColor = KColor_textGray;
  ttlabel.font = SourceHanSerifRegularFont(10);
  [fView addSubview:ttlabel];
  return fView;
}

- (UIView *)tableView:(UITableView *)tableView
    viewForHeaderInSection:(NSInteger)section {
  return [[UIView alloc] initWithFrame:CGRectZero];
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForRowAtIndexPath:(NSIndexPath *)indexPath {

  return 111;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
  return 1;
}

- (NSInteger)tableView:(UITableView *)tableView
    numberOfRowsInSection:(NSInteger)section {
  return _listData.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView
         cellForRowAtIndexPath:(NSIndexPath *)indexPath {
  FTHZChannelModel *channel = [_listData objectAtIndex:indexPath.row];
  StraitListCell *cell = (StraitListCell *)[tableView
      dequeueReusableCellWithIdentifier:ChannelCellID
                           forIndexPath:indexPath];
  if (!cell) {
    cell = [[StraitListCell alloc] initWithStyle:(UITableViewCellStyleDefault)
                                 reuseIdentifier:ChannelCellID];
  }
  cell.model = channel;

  return cell;
}

- (void)tableView:(UITableView *)tableView
    didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
  FTHZChannelModel *model = [_listData objectAtIndex:indexPath.row];
  FTHZChannelDetailVC *detailVC = [[FTHZChannelDetailVC alloc] init];
  detailVC.discussId = model.discussId;
  [tableView deselectRowAtIndexPath:indexPath animated:YES];
  [self.navigationController pushViewController:detailVC animated:YES];
}

- (void)scrollViewWillEndDragging:(UIScrollView *)scrollView
                     withVelocity:(CGPoint)velocity
              targetContentOffset:(inout CGPoint *)targetContentOffset {
  UIPanGestureRecognizer *pan = scrollView.panGestureRecognizer;
  CGFloat veloc = [pan velocityInView:scrollView].y;
  if (veloc < -15) {
    self.createBt.alpha = 0;

  } else if (veloc > 15) {
    self.createBt.alpha = 1.0;
  }
}

@end
