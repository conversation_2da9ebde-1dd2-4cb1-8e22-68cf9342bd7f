#import "AffairListModel.h"
#import "MusicInfoModel.h"
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface AffairDetailModel : BaseJsonModel
@property(nonatomic, strong) NSArray<AffairResult *> *data;

+ (void)getAffairDetail:(NSString *)contentid
               authorid:(NSString *)authorid
                   from:(NSString *)from
                success:(success)_success
                failure:(failure)_failure;

+ (void)toggleAffairHide:(NSString *)contentid
             immediately:(BOOL)immediately
                 success:(success)_success
                 failure:(failure)_failure;

+ (void)topAffair:(NSString *)contentid
          success:(success)_success
          failure:(failure)_failure;
@end

NS_ASSUME_NONNULL_END
