#import "AffairListModel.h"
@implementation UserResult
+ (NSDictionary *)objectClassInArray {
  return @{
    @"colorFont" : [ColorFont class],
  };
}

@end

@implementation AffairResult
+ (NSDictionary *)objectClassInArray {
  return @{
    @"musicContent" : [MusicInfoData class],
    @"colorFont" : [ColorFont class],
  };
}

- (NSArray<NSString *> *)photoURLs {
  return [self.images componentsSeparatedByString:@","];
}

- (NSInteger)commentCount {
  return [self.commentNum integerValue];
}

- (NSInteger)upvoteCount {
  return [self.likeNum integerValue];
}

- (BOOL)upvoted {
  return ![self.likeRs isEqualToString:@"0"];
}

@end

@implementation DynamicModelResult
+ (NSDictionary *)objectClassInArray {
  return @{
    @"user" : [UserResult class],
    @"affair" : [AffairResult class],
  };
}
@end

@implementation AffairListModelResult

+ (NSDictionary *)objectClassInArray {
  return @{
    @"data" : [DynamicModelResult class],
  };
}

@end

@implementation NewDynamicModelResult
+ (NSDictionary *)objectClassInArray {
  return @{
    @"data" : [DynamicModelResult class],
  };
}
@end

@implementation AffairListModel
+ (NSDictionary *)objectClassInArray {
  return @{
    @"data" : [NewDynamicModelResult class],
  };
}

+ (void)getAffairListModel:(int)page
                   herzNum:(int)herzNum
                   success:(success)_success
                   failure:(failure)_failure {
  NSMutableDictionary *param = [NSMutableDictionary new];
  param[@"userId"] = CurrentUser.userid;
  if (page > 0) {
    param[@"page"] = @(page);
  }
  if (herzNum > 11) {
    param[@"hertz"] = @(herzNum);
  }
  [Http getAsynRequestWithUrl:KURLPNEWAffairList
                       params:param
                      success:_success
                      failure:_failure];
}

+ (void)getAffairListNewModel:(NSString *)lastId
                      herzNum:(int)herzNum
                          lat:(NSNumber *_Nullable)lat
                          lon:(NSNumber *_Nullable)lon
                     location:(NSString *_Nullable)location
                      success:(success)_success
                      failure:(failure)_failure {
  NSMutableDictionary *param = [NSMutableDictionary new];
  if (lastId.length > 0) {
    [param setObject:lastId forKey:@"lastId"];
  }
  param[@"userId"] = CurrentUser.userid;
  if (herzNum >= 9) {
    param[@"hertz"] = @(herzNum);
  }

  if (lat) {
    param[@"lat"] = lat;
  }
  if (lon) {
    param[@"lon"] = lon;
  }
  if (location.length > 0) {
    param[@"location"] = location;
  }

  [Http getAsynRequestWithUrl:KURLPNEWAffairListNew
                       params:param
                      success:_success
                      failure:_failure];
}

@end
