#import "BaseJsonModel.h"
#import <CoreLocation/CoreLocation.h>

NS_ASSUME_NONNULL_BEGIN

@protocol ContentModelResult <NSObject>
@end
@interface ContentModelResult : BaseJsonModel
@property(nonatomic, strong) NSString<Optional> *contentid;
@end

@interface ContentModel : BaseJsonModel
@property(nonatomic, strong) NSArray<ContentModelResult *> *data;

+ (void)postContentModel:(NSString *)text
                  images:(NSString *)images
               imageType:(NSString *)imageType
                     tag:(NSString *)tag
                   music:(NSString *_Nullable)musicText
                   video:(NSString *_Nullable)video
                     lat:(NSNumber *_Nullable)lat
                     lon:(NSNumber *_Nullable)lon
                location:(NSString *_Nullable)location
               atUserIds:(NSString *_Nullable)atUserIds
                  timing:(NSNumber *_Nullable)timing
                 success:(success)_success
                 failure:(failure)_failure;

@end

NS_ASSUME_NONNULL_END
