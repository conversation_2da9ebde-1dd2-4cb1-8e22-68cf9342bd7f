#import "ContentModel.h"
@implementation ContentModelResult

@end

@implementation ContentModel
+ (void)postContentModel:(NSString *)text
                  images:(NSString *)images
               imageType:(NSString *)imageType
                     tag:(NSString *)tag
                   music:(NSString *_Nullable)music
                   video:(NSString *_Nullable)video
                     lat:(NSNumber *_Nullable)lat
                     lon:(NSNumber *_Nullable)lon
                location:(NSString *_Nullable)location
               atUserIds:(NSString *_Nullable)atUserIds
                  timing:(NSNumber *_Nullable)timing
                 success:(success)_success
                 failure:(failure)_failure {
  NSMutableDictionary *param = [NSMutableDictionary new];
  param[@"text"] = text;
  param[@"images"] = images;
  param[@"imageType"] = imageType;
  param[@"video"] = video;
  param[@"tag"] = tag;
  param[@"location"] = location;
  param[@"lat"] = lat;
  param[@"lon"] = lon;
  param[@"atUserIds"] = atUserIds;
  param[@"timing"] = timing;
  NSString *url = KURLContent;
  if (music.length > 0) {
    param[@"musicText"] = music;
    url = KURLMusicPost;
  }
  [Http postAsynRequestWithUrl:url
                        params:param
                       success:_success
                       failure:_failure];
}
@end
