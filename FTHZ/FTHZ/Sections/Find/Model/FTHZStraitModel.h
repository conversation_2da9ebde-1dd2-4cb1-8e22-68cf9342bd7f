#import "BaseJsonModel.h"

NS_ASSUME_NONNULL_BEGIN
// 海峡类型数据模型
@interface FTHZStraitModel : BaseJsonModel
@property(nonatomic, strong) NSNumber *islandId;      // id
@property(nonatomic, strong) NSString *name;          // 海峡名称
@property(nonatomic, strong) NSString *describe;      // 描述文字
@property(nonatomic, strong) NSString *picture;       // 封面
@property(nonatomic, strong) NSString *slogan;        // 宣言文字
@property(nonatomic, strong) NSString *warp_latitude; // 纬度
@property(nonatomic, strong) NSNumber *type;          // 类型

@end

// 获取海峡数据的列表
@interface FTHZStraitListModel : BaseJsonModel

@property(nonatomic, strong) NSArray<FTHZStraitModel *> *data;

+ (void)getStraitList:(success _Nullable)success
              failure:(failure _Nullable)failure;

@end

@interface FTHZChannelModel : BaseJsonModel
@property(nonatomic, strong) NSString *anthorId;
@property(nonatomic, strong) NSNumber *commentNum;
@property(nonatomic, strong) NSString *title;
@property(nonatomic, strong) NSNumber *voice_type;
@property(nonatomic, strong) NSString *content;
@property(nonatomic, strong) NSString *create_time;
@property(nonatomic, strong) NSNumber *status;
@property(nonatomic, strong) NSNumber *heat_value;
@property(nonatomic, strong) NSNumber *island_type;
@property(nonatomic, strong) NSString *discussId;

@end

@interface FTHZChannelListCellModel : BaseJsonModel
@property(nonatomic, strong) NSArray<FTHZChannelModel *> *data;
@property(nonatomic, strong) NSNumber *count;
@property(nonatomic, strong) NSNumber *page;
@property(nonatomic, strong) NSNumber *size;

@end

// 获取声波数据列表
@interface FTHZChannelListModel : BaseJsonModel
@property(nonatomic, strong) FTHZChannelListCellModel *data;

+ (void)getChannelListBy:(NSNumber *)channelId
                    sort:(NSString *)sort
                    page:(NSString *)page
                    size:(NSString *)size
                  succes:(success _Nullable)success
                 failure:(failure _Nullable)failure;

@end

// 海峡声波详情数据模型
@interface ChannelCommentDetailModel : BaseJsonModel
@property(nonatomic, strong) NSString *avatar;
@property(nonatomic, strong) NSString *title;
@property(nonatomic, strong) NSString *content;
@property(nonatomic, strong) NSString *create_time;
@property(nonatomic, strong) NSString *nickname;
@property(nonatomic, strong) NSNumber *gender;
@property(nonatomic, strong) NSString *discussId;
@property(nonatomic, strong) NSString *userid;
@property(nonatomic, strong) NSNumber *island_type;
@property(nonatomic, strong) NSNumber *voice_type;
@property(nonatomic, strong) NSNumber *status;
@property(nonatomic, strong) NSNumber *commentNum;
@end

@interface FTHZChannelDetailModel : BaseJsonModel
@property(nonatomic, strong) NSArray<ChannelCommentDetailModel *> *data;

+ (void)getChannelDetailBy:(NSString *)disscusID
                    succes:(success _Nullable)success
                   failure:(failure _Nullable)failure;

@end

@interface ChannelCommentsToReplyResult : BaseJsonModel
@property(nonatomic, strong) NSNumber *belongs;
@property(nonatomic, strong) NSString *commentid;
@property(nonatomic, strong) NSString *userid;
@property(nonatomic, strong) NSString *comment;
@property(nonatomic, strong) NSString *likeNum;
@property(nonatomic, strong) NSString *type;
@property(nonatomic, strong) NSString *avatar;
@property(nonatomic, strong) NSString *nickName;
@property(nonatomic, strong) NSString *create_time;
@property(nonatomic, strong) NSString *to_nickName;
@property(nonatomic, strong) NSString *to_avatar;
@property(nonatomic, strong) NSString *to_userid;
@property(nonatomic, strong) NSString *images;
@property(nonatomic, strong) NSString *voice;
@property(nonatomic, strong) NSString *voice_time;

@end

@interface ChannelCommentsReplyResult : BaseJsonModel
@property(nonatomic, strong) NSNumber *belongs;
@property(nonatomic, strong) NSString *commentid;
@property(nonatomic, strong) NSString *userid;
@property(nonatomic, strong) NSString *comment;
@property(nonatomic, strong) NSString *likeNum;
@property(nonatomic, strong) NSString *type;
@property(nonatomic, strong) NSString *avatar;
@property(nonatomic, strong) NSString *nickName;
@property(nonatomic, strong) NSString *create_time;
@property(nonatomic, strong) NSString *to_userid;
@property(nonatomic, strong) NSString *images;
@property(nonatomic, strong) NSString *voice;
@property(nonatomic, strong) NSString *voice_time;
@property(nonatomic, strong) NSString *like_status; // 是否当前用户点击过
@property(nonatomic, strong) NSArray<ChannelCommentsToReplyResult *> *data;
@end

@interface ChannelCommentsModelResult : BaseJsonModel
@property(nonatomic, strong) NSString *page;
@property(nonatomic, strong) NSString *size;
@property(nonatomic, strong) NSString *count;
@property(nonatomic, strong) NSString *mainCount;
@property(nonatomic, strong) NSArray<ChannelCommentsReplyResult *> *data;
@end

@interface ChannelCommentsModel : BaseJsonModel
@property(nonatomic, strong) NSArray<ChannelCommentsModelResult *> *data;

+ (void)getChannelCommentsModel:(NSString *)discussid
                       authorid:(NSString *)authorid
                           page:(NSString *)page
                           size:(NSString *)size
                        success:(success)_success
                        failure:(failure)_failure;

@end
NS_ASSUME_NONNULL_END
