#import "BaseJsonModel.h"

NS_ASSUME_NONNULL_BEGIN

@protocol LikesUserModelResult <NSObject>
@end
@interface LikesUserModelResult : BaseJsonModel
@property(nonatomic, strong) NSString *gender;
@property(nonatomic, strong) NSString *signature;
@property(nonatomic, strong) NSString *hertz;
@property(nonatomic, strong) NSString *uid;
@property(nonatomic, strong) NSString *nickname;
@property(nonatomic, strong) NSString *city;
@property(nonatomic, strong) NSString *avatar;
@property(nonatomic, strong) NSString *create_time;
@property(nonatomic, strong, nullable) ColorFont *colorFont;

@end

@protocol AffairLikesModelResult <NSObject>
@end
@interface AffairLikesModelResult : BaseJsonModel
@property(nonatomic, strong) NSString *page;
@property(nonatomic, strong) NSString *size;
@property(nonatomic, strong) NSString *count;
@property(nonatomic, strong) NSArray<LikesUserModelResult *> *data;
@end

@interface AffairLikesModel : BaseJsonModel
@property(nonatomic, strong) NSArray<AffairLikesModelResult *> *data;

+ (void)getAffairLikesModel:(NSString *)aid
                       page:(NSNumber *)page
                       size:(NSString *)size
                    success:(success)_success
                    failure:(failure)_failure;

@end

NS_ASSUME_NONNULL_END
