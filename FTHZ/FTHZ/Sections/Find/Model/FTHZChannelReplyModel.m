#import "FTHZChannelReplyModel.h"

@implementation FTHZChannelCreateModel

+ (void)postChannel:(NSString *)title
            comment:(NSString *)comment
        island_type:(NSNumber *)type
         voice_type:(NSNumber *)voiceType
            success:(success)_success
            failure:(failure)_failure {
  NSMutableDictionary *param = [NSMutableDictionary new];

  [param setObject:title forKey:@"title"];
  [param setObject:comment forKey:@"content"];
  [param setObject:type forKey:@"island_type"];
  [param setObject:voiceType forKey:@"voice_type"];

  [Http postAsynRequestWithUrl:KURLChannelDiscuss
                        params:param
                       success:_success
                       failure:_failure];
}

@end

@implementation LiuyanCreateModel

+ (void)message:(NSString *)message
             to:(NSString *)to
        success:(success)_success
        failure:(failure)_failure {
  NSMutableDictionary *param = [NSMutableDictionary new];

  [param setObject:message forKey:@"message"];
  [param setObject:to forKey:@"to"];

  [Http postAsynRequestWithUrl:KURLLiuyanCreate
                        params:param
                       success:_success
                       failure:_failure];
}

@end

@implementation ShudongCreateModel

+ (void)message:(NSString *)message
        success:(success)_success
        failure:(failure)_failure {
  NSMutableDictionary *param = [NSMutableDictionary new];

  [param setObject:message forKey:@"message"];

  [Http postAsynRequestWithUrl:KURLShudongCreate
                        params:param
                       success:_success
                       failure:_failure];
}

@end

@implementation FTHZDeleteChannelModel
+ (void)delChannelDiscuss:(NSString *)discussId
                  success:(success)_success
                  failure:(failure)_failure {
  NSMutableDictionary *param = [NSMutableDictionary new];

  [param setObject:discussId forKey:@"discussId"];

  [Http deleteAsynRequestWithUrl:KURLDeletDiscuss
                          params:param
                         success:_success
                         failure:_failure];
}

+ (void)delChannelComment:(NSString *)discussId
                commentid:(NSString *)commentid
                 authorid:(NSString *)authorid
                  success:(success)_success
                  failure:(failure)_failure {
  NSMutableDictionary *param = [NSMutableDictionary new];

  [param setObject:discussId forKey:@"discussId"];
  [param setObject:commentid forKey:@"commentid"];
  [param setObject:authorid forKey:@"authorid"];

  [Http deleteAsynRequestWithUrl:KURLChannelComment
                          params:param
                         success:_success
                         failure:_failure];
}

@end

@implementation FTHZChannelReplyModel
+ (void)postChannelCommentModel:(NSString *)authorid
                      contentid:(NSString *)discussid
                        comment:(NSString *)comment
                      to_userid:(NSString *)to_userid
                   to_commentid:(NSString *)to_commentid
                        belongs:(NSString *)belongs
                           type:(NSString *)type
                      voiceFile:(NSString *)voice
                      voiceTime:(NSString *)voiceDuring
                         images:(NSString *)images
                        success:(success)_success
                        failure:(failure)_failure {
  NSMutableDictionary *param = [NSMutableDictionary new];

  [param setObject:authorid forKey:@"authorid"];
  [param setObject:discussid forKey:@"discussId"];
  [param setObject:comment forKey:@"comment"];
  [param setObject:to_userid forKey:@"to_userid"];
  [param setObject:to_commentid forKey:@"to_commentid"];
  [param setObject:belongs forKey:@"belongs"];
  [param setObject:type forKey:@"type"];
  [param setObject:voice forKey:@"voice"];
  [param setObject:voiceDuring forKey:@"voice_time"];
  [param setObject:images forKey:@"images"];

  [Http postAsynRequestWithUrl:KURLChannelComment
                        params:param
                       success:_success
                       failure:_failure];
}

@end

@implementation FTHZChannelLikeModel

+ (void)postLike:(NSString *)discussId
         comment:(NSString *)commentid
         success:(success)_success
         failure:(failure)_failure {
  NSMutableDictionary *param = [NSMutableDictionary new];

  [param setObject:discussId forKey:@"discussId"];
  [param setObject:commentid forKey:@"commentid"];

  [Http postAsynRequestWithUrl:KURLChannelLikeComment
                        params:param
                       success:_success
                       failure:_failure];
}

@end

@implementation DownloadFileModel

+ (void)downloadVoiceWithUrl:(NSString *)urlStr
                    complete:(void (^)(NSURL *filePath))voiceFilePath
                        fail:(void (^)(NSString *error))fail {
  [Http downloadVoiceWithUrl:urlStr complete:voiceFilePath fail:fail];
}

@end

@implementation VoiceFileManager
+ (NSString *)recordPath {
  NSString *filePath = [CachePath stringByAppendingPathComponent:@"SoundFile"];

  if (![[NSFileManager defaultManager] fileExistsAtPath:filePath]) {
    NSError *error = nil;
    [[NSFileManager defaultManager] createDirectoryAtPath:filePath
                              withIntermediateDirectories:NO
                                               attributes:nil
                                                    error:&error];
    if (error) {
    }
  }
  return filePath;
}

+ (void)checkVoiceFile:(NSString *)urlPath
              complete:(void (^)(NSString *filePath))block {
  NSString *path = [[self recordPath]
      stringByAppendingPathComponent:urlPath.lastPathComponent];
  BOOL isExists = [[NSFileManager defaultManager] fileExistsAtPath:path];
  if (isExists) {
    block(path);
  } else {
    [DownloadFileModel downloadVoiceWithUrl:urlPath
        complete:^(NSURL *_Nonnull filePath) {
          block(filePath.relativePath);
        }
        fail:^(NSString *_Nonnull error) {
          block(nil);
        }];
  }
}

@end
