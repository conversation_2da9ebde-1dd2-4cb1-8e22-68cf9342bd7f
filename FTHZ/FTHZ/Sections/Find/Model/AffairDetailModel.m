#import "AffairDetailModel.h"

@implementation AffairDetailModel

+ (NSDictionary *)objectClassInArray {
  return @{
    @"data" : [AffairResult class],
  };
}

+ (void)getAffairDetail:(NSString *)contentid
               authorid:(NSString *)authorid
                   from:(NSString *)from
                success:(success)_success
                failure:(failure)_failure {
  NSMutableDictionary *param = [NSMutableDictionary new];
  [param setObject:contentid forKey:@"contentid"];
  [param setObject:authorid forKey:@"authorid"];
  [param setObject:from forKey:@"from"];
  [Http getAsynRequestWithUrl:KURLNEWGetAffairDetail
                       params:param
                      success:_success
                      failure:_failure];
}

+ (void)toggleAffairHide:(NSString *)contentid
             immediately:(BOOL)immediately
                 success:(success)_success
                 failure:(failure)_failure {
  NSMutableDictionary *param = [NSMutableDictionary new];
  [param setObject:contentid forKey:@"contentid"];
  if (immediately) {
    [param setObject:@(YES) forKey:@"immediately"];
  }
  [Http postAsynRequestWithUrl:KURLPostHideAffair
                        params:param
                       success:_success
                       failure:_failure];
}

+ (void)toggleAffairHide:(NSString *)contentid
                 success:(success)_success
                 failure:(failure)_failure {
  [self toggleAffairHide:contentid
             immediately:NO
                 success:_success
                 failure:_failure];
}

+ (void)topAffair:(NSString *)contentid
          success:(success)_success
          failure:(failure)_failure {
  NSMutableDictionary *param = [NSMutableDictionary new];
  [param setObject:contentid forKey:@"contentid"];
  [Http postAsynRequestWithUrl:KURLPostTopAffair
                        params:param
                       success:_success
                       failure:_failure];
}

@end
