#import "CreateShudongVC.h"
#import "FTHZChannelReplyModel.h"
@interface CreateShudongVC () <UITextFieldDelegate, UITextViewDelegate>

@property(nonatomic, strong) UITextView *contentText;
@property(nonatomic, strong) UILabel *contentPlaceHolderL;
@property(nonatomic, strong) UILabel *characterCountLabel; // 添加字数统计标签

@property(nonatomic, strong) UIView *optionBGView;   // keyboard上面的文字选项.
@property(nonatomic, strong) FlatButton *imgCheck;   // 图文回应
@property(nonatomic, strong) FlatButton *voiceCheck; // 声音回应

@end

@implementation CreateShudongVC

#pragma mark -private
- (void)setupUI {
  // crate topbar
  UIView *topbar = [UIView new];

  [self.view addSubview:topbar];
  @weakify(self);
  [topbar mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.right.equalTo(self.view);
    make.top.equalTo(self.view).offset(self.safeAreaInset.top);
    make.height.mas_equalTo(70 * kWidthFactor);
  }];

  FlatButton *cancelBt = [FlatButton new];
  [cancelBt setImage:KImage_name(@"Close") forState:UIControlStateNormal];
  [cancelBt addTarget:self
                action:@selector(cancelAction)
      forControlEvents:UIControlEventTouchUpInside];
  [topbar addSubview:cancelBt];
  [cancelBt mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(topbar).offset(24 * kWidthFactor);
    make.centerY.equalTo(topbar);
  }];

  UILabel *navTitle = [UILabel new];
  navTitle.text = @"碎碎念";
  navTitle.font = SourceHanSerifSemiBoldFont(16 * kWidthFactor);
  navTitle.textColor = KColor_HighBlack;
  [topbar addSubview:navTitle];
  [navTitle mas_makeConstraints:^(MASConstraintMaker *make) {
    make.centerY.equalTo(topbar);
    make.centerX.equalTo(topbar);
  }];
  FlatButton *sendBt = [FlatButton new];
  [sendBt setImage:KImage_name(@"Send") forState:UIControlStateNormal];
  [sendBt addTarget:self
                action:@selector(sendAction)
      forControlEvents:UIControlEventTouchUpInside];
  [topbar addSubview:sendBt];
  [sendBt mas_makeConstraints:^(MASConstraintMaker *make) {
    make.right.equalTo(topbar).offset(-24 * kWidthFactor);
    make.centerY.equalTo(topbar);
  }];

  UIScrollView *scrollView = [[UIScrollView alloc] init];
  [self.view addSubview:scrollView];
  [scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.right.equalTo(self.view);
    make.top.equalTo(topbar.mas_bottom);
    make.bottom.equalTo(self.view).offset(-self.safeAreaInset.bottom);
  }];

  UIView *contentView = [UIView new];
  [scrollView addSubview:contentView];
  [contentView mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.width.mas_equalTo(kMainWidth);
    make.height.mas_equalTo(kMainHeight - 64 * kWidthFactor -
                            self.safeAreaInset.top - self.safeAreaInset.bottom);
    make.edges.equalTo(scrollView);
  }];

  _contentText = [UITextView new];
  _contentText.font = SourceHanSerifMediumFont(14 * kWidthFactor);
  _contentText.keyboardAppearance = UIKeyboardAppearanceDark;
  _contentText.delegate = self;

  _contentText.layer.cornerRadius = 10 * kWidthFactor;
  _contentText.layer.masksToBounds = YES;
  // _contentText.layer.borderWidth = 1.0;
  // _contentText.layer.borderColor =
  //     [KColor_HighBlack colorWithAlphaComponent:0.1].CGColor;
  _contentText.backgroundColor = [UIColor colorWithWhite:0.95 alpha:1.0];
  _contentText.textContainerInset =
      UIEdgeInsetsMake(12 * kWidthFactor, 12 * kWidthFactor, 12 * kWidthFactor,
                       12 * kWidthFactor);

  [contentView addSubview:_contentText];
  [_contentText mas_makeConstraints:^(MASConstraintMaker *make) {
    //        @strongify(self);
    make.top.equalTo(contentView).offset(14 * kWidthFactor);
    make.left.equalTo(contentView).offset(24 * kWidthFactor);
    make.right.equalTo(contentView).offset(-24 * kWidthFactor);
    make.height.mas_equalTo(250 * kWidthFactor);
  }];

  UIView *countContainer = [[UIView alloc] init];
  countContainer.backgroundColor = [UIColor clearColor];
  countContainer.userInteractionEnabled = NO; // 不要拦截触摸事件
  [contentView addSubview:countContainer];

  [countContainer mas_makeConstraints:^(MASConstraintMaker *make) {
    make.right.equalTo(_contentText).offset(-12 * kWidthFactor);
    make.bottom.equalTo(_contentText).offset(-12 * kWidthFactor);
    make.width.mas_equalTo(70 * kWidthFactor);
    make.height.mas_equalTo(20 * kWidthFactor);
  }];

  // 修改字数统计标签，将其添加到正确的父视图
  _characterCountLabel = [UILabel new];
  _characterCountLabel.text = @"0/200";
  _characterCountLabel.font = SourceHanSerifMediumFont(12 * kWidthFactor);
  _characterCountLabel.textColor =
      [KColor_HighBlack colorWithAlphaComponent:0.5];
  _characterCountLabel.textAlignment = NSTextAlignmentRight;
  [countContainer addSubview:_characterCountLabel]; // 添加到容器视图

  // 使标签填满容器
  [_characterCountLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.edges.equalTo(countContainer);
  }];

  UILabel *tipsLabel = [UILabel new];
  tipsLabel.text = @"•碎碎念30天后会自动删除";
  tipsLabel.font = SourceHanSerifMediumFont(12 * kWidthFactor);
  tipsLabel.textColor = [KColor_HighBlack colorWithAlphaComponent:0.5];
  tipsLabel.textAlignment = NSTextAlignmentLeft;
  [contentView addSubview:tipsLabel];

  [tipsLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(self.contentText.mas_bottom).offset(8 * kWidthFactor);
    make.left.equalTo(self.contentText);
    make.right.equalTo(self.contentText);
    make.height.mas_equalTo(20 * kWidthFactor);
  }];

  _contentPlaceHolderL = [UILabel new];
  _contentPlaceHolderL.text = @"点击输入碎碎念";
  _contentPlaceHolderL.textColor =
      [KColor_HighBlack colorWithAlphaComponent:0.25];
  _contentPlaceHolderL.font = SourceHanSerifMediumFont(14 * kWidthFactor);
  [_contentText addSubview:_contentPlaceHolderL];
  [_contentPlaceHolderL mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.edges.equalTo(self.contentText)
        .insets(UIEdgeInsetsMake(12 * kWidthFactor, 12 * kWidthFactor,
                                 12 * kWidthFactor, 12 * kWidthFactor));
  }];

  _optionBGView = [[UIView alloc]
      initWithFrame:CGRectMake(0, kMainHeight, kMainWidth, 48 * kWidthFactor)];
  _optionBGView.backgroundColor = KColor_DarkKeyboard;
  [self.view addSubview:_optionBGView];

  /** 键盘监听 */
  //    [[NSNotificationCenter defaultCenter] addObserver:self
  //    selector:@selector(keyboardWillAppear:)
  //    name:UIKeyboardWillShowNotification object:nil];
  //    [[NSNotificationCenter defaultCenter] addObserver:self
  //    selector:@selector(keyboardWillDisappear:)
  //    name:UIKeyboardWillHideNotification object:nil];
}

- (void)cancelAction {
  [self.navigationController popViewControllerAnimated:YES];
  [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)sendAction {
  if (self.contentText.text.length > 200) {
    [self showToastFast:@"字数太多了 🐳"];
  } else {
    [HUD show];
    @weakify(self);
    [ShudongCreateModel message:self.contentText.text
        success:^(NSDictionary *resultObject) {
          @strongify(self);
          [HUD dissmiss];
          ShudongCreateModel *member =
              [ShudongCreateModel mj_objectWithKeyValues:resultObject];
          if ([member.success boolValue]) {
            [[NSNotificationCenter defaultCenter]
                postNotificationName:@"NeedRefreshShudongList"
                              object:nil];
            [self.navigationController popViewControllerAnimated:YES];
            [self dismissViewControllerAnimated:YES completion:nil];
          } else {
            [self.view makeToast:member.msg
                        duration:1.0
                        position:CSToastPositionCenter];
          }
        }
        failure:^(NSError *requestErr) {
          @strongify(self);
          [HUD dissmiss];
          [self showToastFast:@"数据有误,请检查网络后重试"];
        }];
  }
}
//
//- (void)checkAction:(FlatButton *)bt {
//    if (bt.tag == 1001) {
//        self.imgCheck.selected = NO;
//        self.voiceCheck.selected = YES;
//
//    }else{
//        self.imgCheck.selected = YES;
//        self.voiceCheck.selected = NO;
//    }
//}
//
//- (void)keyboardWillAppear:(NSNotification *)noti{
//    NSDictionary *info = [noti userInfo];
//    NSValue *value = [info objectForKey:UIKeyboardFrameEndUserInfoKey];
//    CGFloat keyboardAnimationDuration = [[info
//    objectForKey:UIKeyboardAnimationDurationUserInfoKey] floatValue]; CGSize
//    keyboardSize = [value CGRectValue].size; [UIView
//    animateWithDuration:keyboardAnimationDuration animations:^{
//        CGRect frame = self.optionBGView.frame;
//        frame.origin.y = self.view.frame.size.height - keyboardSize.height -
//        48*kWidthFactor; self.optionBGView.frame = frame;
//    }];
//
//
//}
//
//- (void)keyboardWillDisappear:(NSNotification *)noti{
//    NSDictionary *info = [noti userInfo];
//    CGFloat keyboardAnimationDuration = [[info
//    objectForKey:UIKeyboardAnimationDurationUserInfoKey] floatValue]; [UIView
//    animateWithDuration:keyboardAnimationDuration animations:^{
//        CGRect frame = self.optionBGView.frame;
//        frame.origin.y = kMainHeight;
//        self.optionBGView.frame = frame;
//    }];
//}
// #pragma mark -lifecycle
//
- (void)viewDidLoad {
  [super viewDidLoad];

  [self setupUI];
}

//
//- (void)viewWillAppear:(BOOL)animated {
//    [super viewWillAppear:animated];
//
//    [IQKeyboardManager sharedManager].shouldResignOnTouchOutside = NO;
//
//}
//
//- (void)viewWillDisappear:(BOOL)animated {
//    [super viewWillDisappear:animated];
//    [self.titleText resignFirstResponder];
//    [self.contentText resignFirstResponder];
//
//    [IQKeyboardManager sharedManager].shouldResignOnTouchOutside = YES;
//}
//
//-(void)dealloc {
//    [[NSNotificationCenter defaultCenter] removeObserver:self];
//}
// #pragma mark - delegate
//- (BOOL)textField:(UITextField *)textField
// shouldChangeCharactersInRange:(NSRange)range replacementString:(NSString
//*)string
//{
//    if (textField == self.titleText && range.location >= 20) {
//        return NO;
//    }
//
//    return YES;
//}
//
//- (BOOL)textView:(UITextView *)textView shouldChangeTextInRange:(NSRange)range
// replacementText:(NSString *)text {
//
//
//
//    if (textView == self.contentText ) {
//        NSString *str = [NSString stringWithFormat:@"%@%@", textView.text,
//        text]; if (str.length > 50)
//        {
//            textView.text = [textView.text substringToIndex:50];
//            [self showToastFast:@"字数太多了 🐳"];
//            return NO;
//        }
//    }
//
//    return YES;
//}
//
- (void)textViewDidEndEditing:(UITextView *)textView {
  if (![textView hasText]) {
    _contentPlaceHolderL.hidden = NO;
  }
}

- (void)textViewDidChange:(UITextView *)textView {
  if (![textView hasText]) {
    _contentPlaceHolderL.hidden = NO;
    _characterCountLabel.text = @"0/200"; // 重置字数统计
  } else {
    _contentPlaceHolderL.hidden = YES;

    // 更新字数统计
    NSInteger characterCount = textView.text.length;
    _characterCountLabel.text =
        [NSString stringWithFormat:@"%ld/200", (long)characterCount];

    // 如果超过字数限制，改变颜色提醒用户
    if (characterCount > 200) {
      _characterCountLabel.textColor = [UIColor redColor];
    } else {
      _characterCountLabel.textColor =
          [KColor_HighBlack colorWithAlphaComponent:0.5];
    }
  }
}

// 添加这个方法来监听字符输入，限制最大字数
- (BOOL)textView:(UITextView *)textView
    shouldChangeTextInRange:(NSRange)range
            replacementText:(NSString *)text {
  // 如果是删除操作，直接允许
  if ([text isEqualToString:@""]) {
    return YES;
  }

  // 计算变更后的总字数
  NSString *newText = [textView.text stringByReplacingCharactersInRange:range
                                                             withString:text];

  // 虽然在sendAction中已有判断，但这里可以防止用户继续输入
  // 仍然允许输入，只是在UI上给出提示，让用户自行删减
  if (newText.length > 200) {
    // 这里可以选择给出提示，但不强制限制输入
    [self showToastFast:@"已达字数上限"];
    return NO;
  }

  return YES;
}

@end
