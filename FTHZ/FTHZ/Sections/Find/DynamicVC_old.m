#import "AffairListModel.h"
#import "AttentionTableViewCell.h"
#import "DJStatusBarHUD.h" //HUD
#import "DoLikeModel.h"
#import "DynamicDetailVC.h"
#import "DynamicTableViewCell.h"
#import "DynamicVC.h"
#import "FTHZMusicPlayerView.h"
#import "LoginBusiness.h"
#import "POSTIPStatusModel.h"
#import "TagAffairVC.h"
#import "UIView+MJExtension.h"
#import "UserUserinfoModel.h"
#import "WhaleDetailVC.h"
#import "_2hz-Swift.h"
#import <YYCache.h>

#define DynamicTableViewCellID @"DynamicTableViewCellID"
#define DynamicListCache @"DynamicListCache_ID"
#define DynamicRecommendListCache @"DynamicRecommendListCache_ID"

static NSString *const kDynamicListCache = @"DynamicListCache_ID";
static NSString *const kDynamicRecommendListCache =
    @"DynamicRecommendListCache_ID";
static NSString *const kDynamicTableViewCellID = @"DynamicTableViewCellID";

@interface FTHZDynamicCacheManager : NSObject

@property(nonatomic, strong, readonly) YYCache *cache;

+ (instancetype)shared;
- (void)saveRecommendList:(NSString *)jsonString;
- (NSString *)getRecommendList;
- (void)clearCache;

@end

@implementation FTHZDynamicCacheManager

+ (instancetype)shared {
  static FTHZDynamicCacheManager *instance = nil;
  static dispatch_once_t onceToken;
  dispatch_once(&onceToken, ^{
    instance = [[FTHZDynamicCacheManager alloc] init];
  });
  return instance;
}

- (instancetype)init {
  self = [super init];
  if (self) {
    _cache = [YYCache cacheWithName:kDynamicListCache];
  }
  return self;
}

- (void)saveRecommendList:(NSString *)jsonString {
  if (!jsonString)
    return;
  dispatch_async(
      dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        [self.cache setObject:jsonString forKey:kDynamicRecommendListCache];
      });
}

- (NSString *)getRecommendList {
  return [self.cache objectForKey:kDynamicRecommendListCache];
}

- (void)clearCache {
  [self.cache removeAllObjects];
}

@end

@interface DynamicVC () <UITableViewDelegate, UITableViewDataSource,
                         DynamicDelegate, CLLocationManagerDelegate> {
  //    CLLocationManager *locationManager;//定位服务管理类
}

@property(nonatomic, strong) NSMutableArray *cellData;
@property(nonatomic, strong) NSMutableSet *oldIdSet;
@property(nonatomic, strong) UITableView *tableView;

@property(nonatomic, assign) BOOL showHud;
@property(nonatomic, strong)
    CLLocationManager *locationManager; // 定位服务管理类
@property(nonatomic, strong) FTHZMusicPlayerBanner *banner;
@property(nonatomic, assign) int pageNum;
@property(nonatomic, assign) int hertzNum;
@property(nonatomic, copy) NSString *lastId;

@property(nonatomic, assign) BOOL isRefreshing;
@property(nonatomic, assign) BOOL isDataLoaded;
@property(nonatomic, assign) BOOL isRandoming;   // 没有推荐，随机跳转中
@property(nonatomic, assign) int randomTryCount; // 尝试次数
@property(nonatomic, assign) BOOL isFirstLoad;   // 添加标记变量

@property(nonatomic, strong) dispatch_queue_t dataQueue; // 数据处理队列

// 添加私有方法声明
- (void)handleUnauthorizedAccess;
- (void)handleRequestError:(NSError *)error;
- (void)handleSuccessResponse:(AffairListModel *)member
                      hudType:(NSInteger)hudType
                   isLoadMore:(BOOL)isLoadMore;
- (void)handleFailureResponse:(AffairListModel *)member;
@property(nonatomic, assign) BOOL isProcessingTelephonyAccess;

@end

@implementation DynamicVC

- (void)handleUnauthorizedAccess {
  // 处理未登录状态
  [AccountManager logoutWithCompletion:^(UserPersonResult *_Nullable user,
                                         NSError *_Nullable error) {
    [FTHZBusiness loginThenAction:^(FTHZLoginResult result,
                                    UserPersonResult *_Nullable user,
                                    NSError *_Nullable error){

    }];
  }];
}

- (void)handleRequestError:(NSError *)error {
  [self.tableView.mj_header endRefreshing];
  [self.tableView.mj_footer endRefreshing];
  self.isRefreshing = NO;
  [self.view makeToast:@"数据有误,请检查网络后重试"
              duration:2.0
              position:CSToastPositionCenter];
}

- (void)handleSuccessResponse:(AffairListModel *)member
                      hudType:(NSInteger)hudType
                   isLoadMore:(BOOL)isLoadMore {
  NewDynamicModelResult *tempMember =
      [NewDynamicModelResult mj_objectWithKeyValues:member.data.firstObject];

  if (tempMember.data.count == 0) {
    [self checkAutoJumpHerzDynmic];
  } else {
    [self.tableView.mj_header endRefreshing];
    [self.tableView.mj_footer endRefreshing];

    if (self.hertzNum < 10 && tempMember.data.count > 0) {
      [[FTHZDynamicCacheManager shared]
          saveRecommendList:[tempMember mj_JSONString]];
    }

    if (self.isRandoming) {
      [self handleRandomingSuccess];
    }

    // 更新 lastId
    if (member.lastId.length > 0) {
      self.lastId = member.lastId;
    } else {
    }

    [self setupDataForTable:[tempMember mj_JSONString]
                        hud:hudType
                       from:YES
                 isLoadMore:isLoadMore];
  }
}

- (void)handleRandomingSuccess {
  self.isRandoming = NO;
  self.isDataLoaded = YES;
  if (self.delegate &&
      [self.delegate respondsToSelector:@selector(sliderValueToHerz:)]) {
    [self.delegate sliderValueToHerz:self.hertzNum];
  }
  [DJStatusBarHUD
      showSuccess:
          [NSString
              stringWithFormat:@"推荐内容计算中... （已自动跳转至%d hz频率线）",
                               self.hertzNum]];
  self.showHud = NO;
}

- (void)handleFailureResponse:(AffairListModel *)member {
  if (self.hertzNum < 11 || !self.isDataLoaded) {
    [self checkAutoJumpHerzDynmic];
  } else {
    [self.tableView.mj_header endRefreshing];
    [self.tableView.mj_footer endRefreshing];
    [self.view makeToast:member.msg
                duration:1.0
                position:CSToastPositionCenter];
  }
}

/// 外部调用刷新接口
/// @param herz 需要刷新的herz数
- (void)refreshByHerz:(int)herz after:(CGFloat)time {
  self.hertzNum = herz;
  // Delay execution of my block for 10 seconds.
  dispatch_after(dispatch_time(DISPATCH_TIME_NOW, time * NSEC_PER_SEC),
                 dispatch_get_main_queue(), ^{
                   [self.tableView.mj_header beginRefreshing];
                 });
}

- (void)loadOfData:(NSInteger)hudType isLoadMore:(BOOL)isLoadMore {
  if (!self.cellData) {
    self.cellData = [NSMutableArray new];
    self.oldIdSet = [NSMutableSet new];
  }

  if (self.isRefreshing && !self.isRandoming) {
    return;
  }

  @weakify(self);
  self.isRefreshing = YES;
  NSString *lastIdToUse = isLoadMore ? self.lastId : nil;

  [AffairListModel getAffairListNewModel:lastIdToUse
      herzNum:self.hertzNum
      success:^(NSDictionary *resultObject) {
        @strongify(self);

        dispatch_async(self.dataQueue, ^{
          AffairListModel *member =
              [AffairListModel mj_objectWithKeyValues:resultObject];

          dispatch_async(dispatch_get_main_queue(), ^{
            [self handleAffairListResponse:member
                                   hudType:hudType
                                isLoadMore:isLoadMore];
          });
        });
      }
      failure:^(NSError *requestErr) {
        @strongify(self);
        dispatch_async(dispatch_get_main_queue(), ^{
          [self handleRequestError:requestErr];
        });
      }];
}

- (void)handleAffairListResponse:(AffairListModel *)member
                         hudType:(NSInteger)hudType
                      isLoadMore:(BOOL)isLoadMore {
  // 处理未登录状态
  if ([member.code intValue] == FTHZErrorCodeUnlogin ||
      [member.code intValue] == FTHZErrorCodePermissionDenied) {
    [self handleUnauthorizedAccess];
    return;
  }

  if ([member.success boolValue]) {
    [self handleSuccessResponse:member hudType:hudType isLoadMore:isLoadMore];
  } else {
    [self handleFailureResponse:member];
  }

  self.isRefreshing = NO;
}

- (int)randomValueBetween:(int)min and:(int)max {
  return (int)(min + arc4random_uniform(max - min + 1));
}

- (void)checkAutoJumpHerzDynmic {
  if (!self.isDataLoaded && (_randomTryCount == 0) &&
      _hertzNum < 11) { // 初次刷新推荐没有数据
    _isRandoming = YES; // 开启随机推荐赫兹
    _randomTryCount++;
    self.hertzNum = [self randomValueBetween:11 and:30];
    [self loadOfData:1 isLoadMore:NO];

  } else if (_isRandoming && _randomTryCount < 4) {
    self.hertzNum = [self randomValueBetween:30 * _randomTryCount
                                         and:30 * (1 + _randomTryCount)];
    [self loadOfData:1 isLoadMore:NO];
    _randomTryCount++;

  } else {
    [self.tableView.mj_header endRefreshing];
    [self.tableView.mj_footer endRefreshing];

    if (_randomTryCount > 3) {
      _isRandoming = NO;
    }
    [DJStatusBarHUD showSuccess:@"推荐内容计算中..."];
  }
}

- (void)setupDataForTable:(NSString *)jsonData
                      hud:(NSInteger)hudType
                     from:(BOOL)remote
               isLoadMore:(BOOL)isLoadMore {
  NewDynamicModelResult *tempMember =
      [NewDynamicModelResult mj_objectWithKeyValues:jsonData];
  if (tempMember.data.count > 0) {
    NSMutableArray *items = [[NSMutableArray alloc] init];
    if (!isLoadMore) {
      // 下拉刷新,清空数据
      [self.cellData removeAllObjects];
      [self.oldIdSet removeAllObjects];
      [self.tableView scrollRectToVisible:CGRectMake(0, 0, 1, 1) animated:NO];
    }
    for (int i = 0; i < tempMember.data.count; i++) {
      DynamicModelResult *dy = [DynamicModelResult
          mj_objectWithKeyValues:[tempMember.data objectAtIndex:i]];
      if (dy.affair.aid.length == 0) {
        continue;
      }
      if (![self.oldIdSet containsObject:dy.affair.aid]) {
        [self.oldIdSet addObject:dy.affair.aid];
        [items addObject:dy];
      }
    }
    [self.cellData addObjectsFromArray:items];
    //            NSInteger temploadnum = [resultObject[@"number"] intValue]-
    //            items.count ;
    if (hudType == 1 && self.showHud == YES) {
      if (items.count > 0 && remote) {
        // if (!self.isFirstLoad) {
        //   [DJStatusBarHUD
        //       showSuccess:[NSString
        //                       stringWithFormat:@"已捕获%lu条海洋内容",
        //                                        (unsigned long)items.count]];
        // }
        self.isFirstLoad = NO;
      }
    } else {
      self.showHud = YES;
    }

    if (self.cellData.count > 3) {
      if (!self.tableView.mj_footer) { // 只在没有 footer 时创建
        MJChiBaoZiFooter *footer =
            [MJChiBaoZiFooter footerWithRefreshingTarget:self
                                        refreshingAction:@selector
                                        (LoadMoreData)]; // 注意方法名大小写
        footer.triggerAutomaticallyRefreshPercent = 6;
        footer.onlyRefreshPerDrag = YES;
        footer.stateLabel.hidden = YES;
        footer.refreshingTitleHidden = YES;
        self.tableView.mj_footer = footer;
      }
    } else {
      [self.tableView.mj_footer removeFromSuperview];
      self.tableView.mj_footer = nil;
    }
  }

  [self.tableView reloadData];
}

- (void)doLike:(NSString *)aid
           uid:(NSString *)uid
      indexRow:(NSInteger)indexRow {
  DynamicModelResult *dy = [_cellData objectAtIndex:indexRow];
  __weak typeof(self) wSelf = self;
  void (^action)(NSDictionary *) = ^(NSDictionary *resultObject) {
    DoLikeModel *member = [DoLikeModel mj_objectWithKeyValues:resultObject];
    if ([member.success boolValue]) {
      if ([dy.affair.likeRs isEqualToString:@"0"]) {
        NSInteger tepLike = [dy.affair.likeNum integerValue];
        tepLike++;
        dy.affair.likeNum = [NSString stringWithFormat:@"%ld", (long)tepLike];
        dy.affair.likeRs = @"1";
        NSMutableArray *newArray = [wSelf.cellData mutableCopy];
        [newArray replaceObjectAtIndex:indexRow withObject:dy];
        wSelf.cellData = newArray;
      } else {
        NSInteger tepLike = [dy.affair.likeNum integerValue];
        tepLike--;
        dy.affair.likeNum = [NSString stringWithFormat:@"%ld", (long)tepLike];
        dy.affair.likeRs = @"0";
        NSMutableArray *newArray = [wSelf.cellData mutableCopy];
        [newArray replaceObjectAtIndex:indexRow withObject:dy];
        wSelf.cellData = newArray;
      }
      NSIndexPath *indexPath = [NSIndexPath indexPathForRow:indexRow
                                                  inSection:0];
      [wSelf.tableView
          reloadRowsAtIndexPaths:[NSArray arrayWithObjects:indexPath, nil]
                withRowAnimation:UITableViewRowAnimationNone];
    } else if ([member.code integerValue] == FTHZErrorCodeAccountBanned) {
      ShowBanTip(member.msg);
    } else {
      [wSelf.view makeToast:member.msg
                   duration:1.0
                   position:CSToastPositionCenter];
    }
  };
  if ([dy.affair upvoted]) {
    [DoLikeModel postUnlikeModel:uid
                       contentid:aid
                         success:action
                         failure:^(NSError *requestErr) {
                           [self.view makeToast:@"数据有误,请检查网络后重试"
                                       duration:2.0
                                       position:CSToastPositionCenter];
                         }];
  } else {
    [DoLikeModel postLikeModel:uid
                     contentid:aid
                       success:action
                       failure:^(NSError *requestErr) {
                         [self.view makeToast:@"数据有误,请检查网络后重试"
                                     duration:2.0
                                     position:CSToastPositionCenter];
                       }];
  }
}

- (void)getUserHeziInfo {
  [AccountManager syncUserInfoWithCompletion:^(UserPersonResult *_Nullable user,
                                               NSError *_Nullable error){

  }];
}
- (void)viewDidLoad {
  self.isFirstLoad = YES; // 初始化为 YES
  [super viewDidLoad];
  self.dataQueue = dispatch_queue_create("com.52hz.dynamic.dataQueue",
                                         DISPATCH_QUEUE_SERIAL);

  [self setupUI];
  [self setupNotifications];
  [self loadInitialData];
  dispatch_after(
      dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)),
      dispatch_get_main_queue(), ^{
        [self setupLocationManager];
      });
}

- (void)setupUI {
  [self.view addSubview:self.tableView];
  [self.view addSubview:self.banner];
  self.banner.canShow = NO;
  self.showHud = YES;
  self.hertzNum = 0;

  // 设置下拉刷新
  MJChiBaoZiHeader *header =
      [MJChiBaoZiHeader headerWithRefreshingTarget:self
                                  refreshingAction:@selector(LoadData)];
  header.lastUpdatedTimeLabel.hidden = YES;
  header.stateLabel.hidden = YES;
  self.tableView.mj_header = header;
}

- (void)setupLocationManager {
  if (self.isProcessingTelephonyAccess) {
    return;
  }

  self.isProcessingTelephonyAccess = YES;

  @weakify(self);
  dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0),
                 ^{
                   @strongify(self);
                   if (!self)
                     return;

                   if (!self.locationManager) {
                     self.locationManager = [[CLLocationManager alloc] init];
                     self.locationManager.delegate = self;
                   }

                   dispatch_async(dispatch_get_main_queue(), ^{
                     @strongify(self);
                     if (!self)
                       return;

                     // iOS 14+ 使用新的授权状态检查方法
                     if (@available(iOS 14.0, *)) {
                       CLAuthorizationStatus status =
                           self.locationManager.authorizationStatus;
                       [self handleLocationAuthorizationStatus:status];
                     } else {
                       CLAuthorizationStatus status =
                           [CLLocationManager authorizationStatus];
                       [self handleLocationAuthorizationStatus:status];
                     }

                     self.isProcessingTelephonyAccess = NO;
                   });
                 });
}

- (void)handleLocationAuthorizationStatus:(CLAuthorizationStatus)status {
  if (self.isProcessingTelephonyAccess) {
    return;
  }

  switch (status) {
  case kCLAuthorizationStatusNotDetermined: {
    if ([self.locationManager
            respondsToSelector:@selector(requestAlwaysAuthorization)]) {
      dispatch_async(dispatch_get_main_queue(), ^{
        [self.locationManager requestAlwaysAuthorization];
      });
    }
    break;
  }

  case kCLAuthorizationStatusRestricted: {
    break;
  }

  case kCLAuthorizationStatusDenied: {
    [self safeLoadAddressWithType:@"0"];
    break;
  }

  case kCLAuthorizationStatusAuthorizedAlways:
  case kCLAuthorizationStatusAuthorizedWhenInUse: {
    [self safeLoadAddressWithType:@"1"];
    break;
  }

  default: {
    break;
  }
  }
}

- (void)safeLoadAddressWithType:(NSString *)type {
  if (self.isProcessingTelephonyAccess) {
    return;
  }

  dispatch_async(dispatch_get_main_queue(), ^{
    if ([CLLocationManager locationServicesEnabled]) {
      [self loadAdressWithType:type];
    } else {
      [self loadAdressWithType:type];
    }
  });
}

- (void)loadAdressWithType:(NSString *)adType {
  if (self.isProcessingTelephonyAccess) {
    return;
  }

  @weakify(self);
  NSString *pushStatus = [self isUserNotificationEnable] ? @"1" : @"0";

  [POSTIPStatusModel
      postPOSTIPStatusModel:adType
                 pushStatus:pushStatus
                    success:^(NSDictionary *resultObject) {
                      @strongify(self);
                      if (!self)
                        return;

                      dispatch_async(dispatch_get_main_queue(), ^{
                        POSTIPStatusModel *member = [POSTIPStatusModel
                            mj_objectWithKeyValues:resultObject];
                        if ([member.success boolValue]) {
                          [self getUserHeziInfo];
                        }
                      });
                    }
                    failure:^(NSError *requestErr){
                        // 错误处理
                    }];
}

- (void)locationManagerDidChangeAuthorization:(CLLocationManager *)manager
    API_AVAILABLE(ios(14.0)) {
  [self handleLocationAuthorizationStatus:manager.authorizationStatus];
}

- (void)setupNotifications {
  [NOTIFICENTER addObserver:self
                   selector:@selector(nofLoadData)
                       name:DyreloadData
                     object:nil];
  [NOTIFICENTER addObserver:self
                   selector:@selector(sendSussessTost)
                       name:DySendSussreloadData
                     object:nil];
  [NOTIFICENTER addObserver:self
                   selector:@selector(LoadindexData)
                       name:ReloadIndexOne
                     object:nil];
}

- (void)loadInitialData {
  // 检查登录状态
  if (![AccountManager isLogined] ||
      ![CurrentUser.NOOType isEqualToString:@"1"]) {
    [AccountManager logoutWithCompletion:^(UserPersonResult *_Nullable user,
                                           NSError *_Nullable error) {
      [FTHZBusiness loginThenAction:^(FTHZLoginResult result,
                                      UserPersonResult *_Nullable user,
                                      NSError *_Nullable error){
      }];
    }];
    return;
  }

  // 读取缓存
  NSString *cachedData = [[FTHZDynamicCacheManager shared] getRecommendList];
  if (cachedData) {
    [self setupDataForTable:cachedData hud:1 from:NO isLoadMore:NO];
  }
  [self loadOfData:1 isLoadMore:NO];
}

- (void)sharedMusicPlayerStateChanged {
  [self updateBanner];
}

- (void)viewDidLayoutSubviews {
  [super viewDidLayoutSubviews];
  self.banner.frame = CGRectMake(0, 0, CGRectGetWidth(self.view.bounds), 40);
}

- (void)sendSussessTost {
  [self.view makeToast:@"发布成功" duration:2.0 position:CSToastPositionCenter];
}
- (void)nofLoadData {
  [self loadOfData:0 isLoadMore:NO];
}

- (void)LoadindexData {
  self.showHud = NO;
  [self.tableView.mj_header beginRefreshing];
}

// 修改LoadData方法用于下拉刷新
- (void)LoadData {
  self.lastId = nil; // 重置lastId
  [self loadOfData:1 isLoadMore:NO];
}

// 新增LoadMoreData方法用于上拉加载
- (void)LoadMoreData {
  if (self.lastId.length > 0) {
    [self loadOfData:1 isLoadMore:YES];
  } else {
    [self.tableView.mj_footer endRefreshing];
    [self.view makeToast:@"没有更多内容了"
                duration:1.0
                position:CSToastPositionCenter];
  }
}
- (UITableView *)tableView {
  if (FT_IS_IPhoneX_All) {
    if (!_tableView) {
      _tableView = [[UITableView alloc]
          initWithFrame:(CGRectMake(0, 0, kMainWidth,
                                    kMainHeight - 107 - kTabarHeight - 35 + 40))
                  style:(UITableViewStylePlain)];
      _tableView.delegate = self;
      _tableView.dataSource = self;
      _tableView.estimatedRowHeight = 0;
      _tableView.estimatedSectionHeaderHeight = 0;
      _tableView.estimatedSectionFooterHeight = 0;
      [_tableView registerClass:[AttentionTableViewCell class]
          forCellReuseIdentifier:DynamicTableViewCellID];
      _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    }
  } else {
    if (!_tableView) {
      _tableView = [[UITableView alloc]
          initWithFrame:(CGRectMake(0, 0, kMainWidth,
                                    kMainHeight - KHomeNavHeight -
                                        kTabarHeight + 40))
                  style:(UITableViewStylePlain)];
      _tableView.delegate = self;
      _tableView.dataSource = self;
      _tableView.estimatedRowHeight = 0;
      _tableView.estimatedSectionHeaderHeight = 0;
      _tableView.estimatedSectionFooterHeight = 0;
      [_tableView registerClass:[AttentionTableViewCell class]
          forCellReuseIdentifier:DynamicTableViewCellID];
      _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    }
  }
  return _tableView;
}
#pragma mark - Table view data source
- (CGFloat)tableView:(UITableView *)tableView
    heightForFooterInSection:(NSInteger)section {
  return 40;
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForHeaderInSection:(NSInteger)section {
  return 12 * kMainTemp;
}

- (UIView *)tableView:(UITableView *)tableView
    viewForFooterInSection:(NSInteger)section {
  UIView *fView =
      [[UIView alloc] initWithFrame:CGRectMake(0, 0, kMainWidth, 40)];
  return fView;
}

- (UIView *)tableView:(UITableView *)tableView
    viewForHeaderInSection:(NSInteger)section {
  return [[UIView alloc] initWithFrame:CGRectZero];
}
- (CGFloat)tableView:(UITableView *)tableView
    heightForRowAtIndexPath:(NSIndexPath *)indexPath {
  DynamicModelResult *dy = [_cellData objectAtIndex:indexPath.row];

  CGSize tempSize = [NemoUtil
      calculateLabelHeightByText:SourceHanSerifMediumFont(14 * kWidthFactor)
                           width:365 * kWidthFactor
                       heightMax:100 * kWidthFactor
                         content:dy.affair.content];
  if ([dy.affair.musicContent isKindOfClass:[MusicInfoData class]]) {
    tempSize.height += 40 * kWidthFactor;
  }
  return 120 * kWidthFactor + tempSize.height +
         [NemoUtil
             getHZPhotoHeight:[NemoUtil getHZPhotoStringCount:dy.affair.images]
                        index:[dy.affair.imageType integerValue]];
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
  return 1;
}

- (NSInteger)tableView:(UITableView *)tableView
    numberOfRowsInSection:(NSInteger)section {
  return _cellData.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView
         cellForRowAtIndexPath:(NSIndexPath *)indexPath {
  DynamicModelResult *dy = [_cellData objectAtIndex:indexPath.row];
  AttentionTableViewCell *cell = (AttentionTableViewCell *)[tableView
      dequeueReusableCellWithIdentifier:DynamicTableViewCellID
                           forIndexPath:indexPath];
  if (!cell) {
    cell = [[AttentionTableViewCell alloc]
          initWithStyle:(UITableViewCellStyleDefault)
        reuseIdentifier:DynamicTableViewCellID];
  }
  [cell setDynamic:dy showTime:NO];
  // [cell.tagBtn addTarget:self
  //                 action:@selector(onTouchBtnTagInCell:)
  //       forControlEvents:(UIControlEventTouchUpInside)];
  [cell.awesomeBtn addTarget:self
                      action:@selector(onTouchBtnInCell:)
            forControlEvents:(UIControlEventTouchUpInside)];
  cell.commentBtn.tag = indexPath.row;
  [cell.commentBtn addTarget:self
                      action:@selector(onTouchContentCell:)
            forControlEvents:(UIControlEventTouchUpInside)];

  @weakify(self);
  cell.tapIconAction = ^(NSString *_Nonnull uid) {
    @strongify(self);
    WhaleDetailVC *infoVC = [[WhaleDetailVC alloc] init];
    infoVC.uid = uid;
    [self.navigationController pushViewController:infoVC animated:YES];
  };
  return cell;
}

- (void)tableView:(UITableView *)tableView
    didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
  DynamicModelResult *dy = [_cellData objectAtIndex:indexPath.row];
  //    DynamicDetailVC *ddVC = [[DynamicDetailVC alloc] init];
  //    ddVC.ddy = dy;
  //    ddVC.delegate = self;
  //    ddVC.isTouchComment = NO;
  FZMomentVC *ddVC = [[FZMomentVC alloc] init];
  ddVC.userId = dy.user.uid;
  ddVC.momentId = dy.affair.aid;
  [self.navigationController pushViewController:ddVC animated:YES];
  [tableView deselectRowAtIndexPath:indexPath animated:YES];
}
- (void)dyrightMoreAction {
  [self nofLoadData];
}

- (void)onTouchBtnTagInCell:(UIButton *)sender {
  CGPoint point = sender.center;
  point = [self.tableView convertPoint:point fromView:sender.superview];
  NSIndexPath *indexpath = [self.tableView indexPathForRowAtPoint:point];
  DynamicModelResult *dy = [_cellData objectAtIndex:indexpath.row];
  TagAffairVC *ddVC = [[TagAffairVC alloc] init];
  ddVC.tagId = dy.affair.tagType;
  ddVC.tagName = dy.affair.tagName;
  [self.navigationController pushViewController:ddVC animated:YES];
}

- (void)onTouchBtnInCell:(UIButton *)sender {
  CGPoint point = sender.center;
  point = [self.tableView convertPoint:point fromView:sender.superview];
  NSIndexPath *indexpath = [self.tableView indexPathForRowAtPoint:point];
  DynamicModelResult *dy = [_cellData objectAtIndex:indexpath.row];
  [self doLike:dy.affair.aid uid:dy.user.uid indexRow:indexpath.row];
}

- (void)onTouchContentCell:(UIButton *)sender {
  DynamicModelResult *dy = [_cellData objectAtIndex:sender.tag];
  //    DynamicDetailVC *ddVC = [[DynamicDetailVC alloc] init];
  //    ddVC.ddy = dy;
  //    ddVC.delegate = self;
  //    ddVC.isTouchComment = YES;
  //    [self.navigationController pushViewController:ddVC animated:YES];
  FZMomentVC *momentVC = [[FZMomentVC alloc] init];
  momentVC.userId = dy.user.uid;
  momentVC.momentId = dy.affair.aid;
  momentVC.withComment = YES;
  [self.navigationController pushViewController:momentVC animated:YES];
}

- (void)updateBanner {
  self.banner.canShow = ![self isMusicPlayingInScreen];
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
  //    [self updateBanner];
}

- (void)scrollViewDidEndDragging:(UIScrollView *)scrollView
                  willDecelerate:(BOOL)decelerate {
  CGPoint point = [scrollView.panGestureRecognizer translationInView:self.view];

  if (self.delegate &&
      [self.delegate respondsToSelector:@selector(contentDidScrollTo:show:)]) {
    NSArray<UITableViewCell *> *cellArray = [self.tableView visibleCells];
    NSString *hertz = nil;
    if (cellArray && _cellData.count > 0) {
      UITableViewCell *cell = [cellArray firstObject];
      NSIndexPath *indexPath = [self.tableView indexPathForCell:cell];
      DynamicModelResult *dy = [_cellData objectAtIndex:indexPath.row];
      hertz = dy.user.hertz;
    }
    if (point.y > 15) {
      // 下滑
      [self.delegate contentDidScrollTo:NO show:hertz];
    } else if (point.y < -15) {
      // 上滑
      [self.delegate contentDidScrollTo:YES show:hertz];
    }
  }
}

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];
  // 替换直接调用 authorizationStatus 的代码
  if (self.locationManager) {
    // iOS 14+ 使用新的授权状态检查方法
    if (@available(iOS 14.0, *)) {
      [self handleLocationAuthorizationStatus:self.locationManager
                                                  .authorizationStatus];
    } else {
      // 在已有实例上检查，避免创建新实例
      [self setupLocationManager];
    }
  } else {
    // 没有实例就创建一个
    [self setupLocationManager];
  }

  //    [UIApplication sharedApplication].statusBarStyle =
  //    UIStatusBarStyleDefault;
  [AppConfig statusbarStyle:YES];
}

- (void)viewDidAppear:(BOOL)animated {
  [super viewDidAppear:animated];
}

- (void)viewWillDisappear:(BOOL)animated {
  [super viewWillDisappear:animated];
  [DJStatusBarHUD hide];
}
- (void)didReceiveMemoryWarning {
  [super didReceiveMemoryWarning];
  // Dispose of any resources that can be recreated.
}

- (void)dealloc {
  [NOTIFICENTER removeObserver:self];
  [[FTHZDynamicCacheManager shared] clearCache];
  if (_locationManager) {
    _locationManager.delegate = nil;
    _locationManager = nil;
  }
}

- (void)locationManager:(CLLocationManager *)manager
    didChangeAuthorizationStatus:(CLAuthorizationStatus)status {
  [self handleLocationAuthorizationStatus:status];
}

- (BOOL)isUserNotificationEnable {
  BOOL isEnable = NO;
  UIUserNotificationSettings *setting =
      [[UIApplication sharedApplication] currentUserNotificationSettings];
  isEnable = (UIUserNotificationTypeNone == setting.types) ? NO : YES;
  return isEnable;
}

- (FTHZMusicPlayerBanner *)banner {
  if (!_banner) {
    _banner = [[FTHZMusicPlayerBanner alloc] init];
  }
  return _banner;
}

- (BOOL)isMusicPlayingInScreen {
  for (UITableViewCell *cell in [self.tableView visibleCells]) {
    if (![cell isKindOfClass:[DynamicTableViewCell class]]) {
      continue;
    }
    if ([(DynamicTableViewCell *)cell isMusicPlaying]) {
      return YES;
    }
  }
  return NO;
}

@end
