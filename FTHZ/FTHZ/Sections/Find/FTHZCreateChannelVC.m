#import "FTHZCreateChannelVC.h"
#import "FTHZChannelReplyModel.h"
#import "FTHZStraitModel.h"

@interface FTHZCreateChannelVC () <UITextFieldDelegate, UITextViewDelegate>
@property(nonatomic, strong) UITextField *titleText;
@property(nonatomic, strong) UITextView *contentText;
@property(nonatomic, strong) UILabel *contentPlaceHolderL;
@property(nonatomic, strong) UILabel *titleCountLabel;
@property(nonatomic, strong) UILabel *contentCountLabel;

@property(nonatomic, strong) UIView *optionBGView;
@property(nonatomic, strong) FlatButton *imgCheck;
@property(nonatomic, strong) FlatButton *voiceCheck;

@property(nonatomic, strong) UIView *straitButtonsView;
@property(nonatomic, strong) NSArray<FTHZStraitModel *> *straitList;
@property(nonatomic, assign) NSInteger selectedStraitIndex;
@property(nonatomic, strong) UILabel *tipL;

@end

@implementation FTHZCreateChannelVC

#pragma mark -private
- (void)setupUI {
  UIView *topbar = [UIView new];
  [self.view addSubview:topbar];
  @weakify(self);
  [topbar mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.right.equalTo(self.view);
    make.top.equalTo(self.view).offset(self.safeAreaInset.top);
    make.height.mas_equalTo(70 * kWidthFactor);
  }];

  FlatButton *cancelBt = [FlatButton new];
  [cancelBt setImage:KImage_name(@"Close") forState:UIControlStateNormal];

  [cancelBt addTarget:self
                action:@selector(cancelAction)
      forControlEvents:UIControlEventTouchUpInside];
  [topbar addSubview:cancelBt];
  [cancelBt mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(topbar).offset(24 * kWidthFactor);
    make.centerY.equalTo(topbar);
  }];

  UILabel *navTitle = [UILabel new];
  navTitle.text = @"创建声波";
  navTitle.font = SourceHanSerifSemiBoldFont(16 * kWidthFactor);
  navTitle.textColor = KColor_HighBlack;
  [topbar addSubview:navTitle];
  [navTitle mas_makeConstraints:^(MASConstraintMaker *make) {
    make.centerY.equalTo(topbar);
    make.centerX.equalTo(topbar);
  }];

  FlatButton *sendBt = [FlatButton new];
  [sendBt setImage:KImage_name(@"Send") forState:UIControlStateNormal];

  [sendBt addTarget:self
                action:@selector(sendAction)
      forControlEvents:UIControlEventTouchUpInside];
  [topbar addSubview:sendBt];
  [sendBt mas_makeConstraints:^(MASConstraintMaker *make) {
    make.right.equalTo(topbar).offset(-24 * kWidthFactor);
    make.centerY.equalTo(topbar);
  }];

  UIScrollView *scrollView = [[UIScrollView alloc] init];
  [self.view addSubview:scrollView];
  [scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.right.equalTo(self.view);
    make.top.equalTo(topbar.mas_bottom);
    make.bottom.equalTo(self.view).offset(-self.safeAreaInset.bottom);
  }];

  UIView *contentView = [UIView new];
  [scrollView addSubview:contentView];
  [contentView mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.width.mas_equalTo(kMainWidth);
    make.height.mas_equalTo(kMainHeight - 64 * kWidthFactor -
                            self.safeAreaInset.top - self.safeAreaInset.bottom);
    make.edges.equalTo(scrollView);
  }];

  _titleText = [UITextField new];
  _titleText.font = SourceHanSerifSemiBoldFont(14 * kWidthFactor);
  _titleText.keyboardAppearance = UIKeyboardAppearanceDark;
  _titleText.toolbarPlaceholder = @"标题";
  _titleText.placeholder = @"标题";
  _titleText.backgroundColor = [UIColor colorWithWhite:0.95 alpha:1.0];
  _titleText.layer.cornerRadius = 8 * kWidthFactor;
  _titleText.layer.masksToBounds = YES;
  _titleText.delegate = self;

  UIView *paddingView =
      [[UIView alloc] initWithFrame:CGRectMake(0, 0, 12 * kWidthFactor, 0)];
  _titleText.leftView = paddingView;
  _titleText.leftViewMode = UITextFieldViewModeAlways;

  [contentView addSubview:_titleText];
  [_titleText mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(contentView).offset(14);
    make.left.equalTo(contentView).offset(24 * kWidthFactor);
    make.right.equalTo(contentView).offset(-24 * kWidthFactor);
    make.height.mas_equalTo(40 * kWidthFactor);
  }];

  _titleCountLabel = [[UILabel alloc] init];
  _titleCountLabel.text = @"0/20";
  _titleCountLabel.font = SourceHanSerifMediumFont(12 * kWidthFactor);
  _titleCountLabel.textColor = [KColor_HighBlack colorWithAlphaComponent:0.5];
  _titleCountLabel.textAlignment = NSTextAlignmentRight;
  [contentView addSubview:_titleCountLabel];

  [_titleCountLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.right.equalTo(_titleText).offset(-12 * kWidthFactor);
    make.centerY.equalTo(_titleText);
    make.width.mas_equalTo(48 * kWidthFactor);
    make.height.mas_equalTo(20 * kWidthFactor);
  }];

  _contentText = [UITextView new];
  _contentText.font = SourceHanSerifMediumFont(12 * kWidthFactor);
  _contentText.keyboardAppearance = UIKeyboardAppearanceDark;
  _contentText.backgroundColor = [UIColor colorWithWhite:0.95 alpha:1.0];
  _contentText.layer.cornerRadius = 8 * kWidthFactor;
  _contentText.layer.masksToBounds = YES;
  _contentText.textContainer.lineFragmentPadding = 0;
  _contentText.textContainerInset =
      UIEdgeInsetsMake(12 * kWidthFactor, 12 * kWidthFactor, 30 * kWidthFactor,
                       12 * kWidthFactor);
  _contentText.delegate = self;
  [contentView addSubview:_contentText];
  [_contentText mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.top.equalTo(self.titleText.mas_bottom).offset(20 * kWidthFactor);
    make.left.equalTo(contentView).offset(24 * kWidthFactor);
    make.right.equalTo(contentView).offset(-24 * kWidthFactor);
    make.height.mas_equalTo(200 * kWidthFactor);
  }];

  _straitButtonsView = [UIView new];
  [contentView addSubview:_straitButtonsView];
  [_straitButtonsView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(self.contentText.mas_bottom).offset(16 * kWidthFactor);
    make.left.equalTo(contentView).offset(24 * kWidthFactor);
    make.right.equalTo(contentView).offset(-24 * kWidthFactor);
  }];

  UIView *contentCountContainer = [[UIView alloc] init];
  contentCountContainer.backgroundColor = [UIColor clearColor];
  contentCountContainer.userInteractionEnabled = NO;
  [contentView addSubview:contentCountContainer];

  [contentCountContainer mas_makeConstraints:^(MASConstraintMaker *make) {
    make.right.equalTo(_contentText).offset(-12 * kWidthFactor);
    make.bottom.equalTo(_contentText).offset(-12 * kWidthFactor);
    make.width.mas_equalTo(60 * kWidthFactor);
    make.height.mas_equalTo(20 * kWidthFactor);
  }];

  _contentCountLabel = [[UILabel alloc] init];
  _contentCountLabel.text = @"0/50";
  _contentCountLabel.font = SourceHanSerifMediumFont(12 * kWidthFactor);
  _contentCountLabel.textColor = [KColor_HighBlack colorWithAlphaComponent:0.5];
  _contentCountLabel.textAlignment = NSTextAlignmentRight;
  [contentCountContainer addSubview:_contentCountLabel];

  [_contentCountLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.edges.equalTo(contentCountContainer);
  }];

  _contentPlaceHolderL = [UILabel new];
  _contentPlaceHolderL.text = @"点击编辑内容";
  _contentPlaceHolderL.textColor =
      [KColor_HighBlack colorWithAlphaComponent:0.25];
  _contentPlaceHolderL.font = SourceHanSerifMediumFont(12 * kWidthFactor);
  [_contentText addSubview:_contentPlaceHolderL];
  [_contentPlaceHolderL mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.edges.equalTo(self.contentText)
        .insets(UIEdgeInsetsMake(12 * kWidthFactor, 12 * kWidthFactor,
                                 12 * kWidthFactor, 12 * kWidthFactor));
  }];

  _optionBGView = [[UIView alloc]
      initWithFrame:CGRectMake(0, kMainHeight, kMainWidth, 48 * kWidthFactor)];
  _optionBGView.backgroundColor = KColor_DarkKeyboard;
  [self.view addSubview:_optionBGView];

  self.tipL = [UILabel new];
  self.tipL.text =
      self.isVoiceType ? @"这条声波将被以声音回应" : @"希望这条声波得到";
  self.tipL.textColor = KColor_keyboardTxtGray;
  self.tipL.font = SourceHanSerifMediumFont(12 * kWidthFactor);
  [_optionBGView addSubview:self.tipL];
  [self.tipL mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.equalTo(self.optionBGView).offset(24 * kWidthFactor);
    make.centerY.equalTo(self.optionBGView);
  }];

  _imgCheck = [[FlatButton alloc] init];
  [_imgCheck setImage:[UIImage imageNamed:@"channel_unselected"]
             forState:UIControlStateNormal];
  [_imgCheck setImage:[UIImage imageNamed:@"channel_selected"]
             forState:UIControlStateSelected];
  _imgCheck.selected = YES;
  [_optionBGView addSubview:_imgCheck];
  [_imgCheck mas_makeConstraints:^(MASConstraintMaker *make) {
    make.width.height.mas_equalTo(10 * kWidthFactor);
    make.left.equalTo(self.tipL.mas_right).offset(37 * kWidthFactor);
    make.centerY.equalTo(self.tipL);
  }];

  UILabel *imgTipL = [UILabel new];
  imgTipL.textColor = KColor_keyboardTxtGray;
  imgTipL.font = SourceHanSerifRegularFont(12);
  imgTipL.text = @"图文回应";
  [_optionBGView addSubview:imgTipL];
  [imgTipL mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.centerY.equalTo(self.imgCheck);
    make.left.equalTo(self.imgCheck.mas_right).offset(4 * kWidthFactor);
  }];

  FlatButton *imgCheckDummy = [FlatButton buttonWithType:UIButtonTypeCustom];
  [_optionBGView addSubview:imgCheckDummy];
  imgCheckDummy.tag = 1000;
  [imgCheckDummy addTarget:self
                    action:@selector(checkAction:)
          forControlEvents:UIControlEventTouchUpInside];
  [imgCheckDummy mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.equalTo(self.imgCheck);
    make.bottom.top.right.equalTo(imgTipL);
  }];

  _voiceCheck = [[FlatButton alloc] init];
  [_voiceCheck setImage:[UIImage imageNamed:@"channel_unselected"]
               forState:UIControlStateNormal];
  [_voiceCheck setImage:[UIImage imageNamed:@"channel_selected"]
               forState:UIControlStateSelected];
  [_voiceCheck addTarget:self
                  action:@selector(checkAction:)
        forControlEvents:UIControlEventTouchUpInside];
  [_optionBGView addSubview:_voiceCheck];
  [_voiceCheck mas_makeConstraints:^(MASConstraintMaker *make) {
    make.width.height.mas_equalTo(10 * kWidthFactor);
    make.left.equalTo(imgTipL.mas_right).offset(61 * kWidthFactor);
    make.centerY.equalTo(imgTipL);
  }];

  UILabel *voiceTipL = [UILabel new];
  voiceTipL.textColor = KColor_keyboardTxtGray;
  voiceTipL.font = SourceHanSerifRegularFont(12);
  voiceTipL.text = @"声音回应";
  [_optionBGView addSubview:voiceTipL];
  [voiceTipL mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.centerY.equalTo(self.voiceCheck);
    make.left.equalTo(self.voiceCheck.mas_right).offset(4 * kWidthFactor);
  }];

  FlatButton *voiceCheckDummy = [FlatButton buttonWithType:UIButtonTypeCustom];
  [_optionBGView addSubview:voiceCheckDummy];
  voiceCheckDummy.tag = 1001;
  [voiceCheckDummy addTarget:self
                      action:@selector(checkAction:)
            forControlEvents:UIControlEventTouchUpInside];
  [voiceCheckDummy mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.equalTo(self.voiceCheck);
    make.bottom.top.right.equalTo(voiceTipL);
  }];

  [[NSNotificationCenter defaultCenter]
      addObserver:self
         selector:@selector(keyboardWillAppear:)
             name:UIKeyboardWillShowNotification
           object:nil];
  [[NSNotificationCenter defaultCenter]
      addObserver:self
         selector:@selector(keyboardWillDisappear:)
             name:UIKeyboardWillHideNotification
           object:nil];
}

- (void)cancelAction {
  [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)sendAction {
  if (self.selectedStraitIndex < 0) {
    [self showToastFast:@"请选择声波类型"];
    return;
  }
  if (self.titleText.text.length == 0) {
    [self showToastFast:@"这条声波是关于什么"];
  } else if (self.contentText.text.length == 0) {
    [self showToastFast:@"这条声波的内容是什么"];
  } else if (self.contentText.text.length > 50) {
    [self showToastFast:@"字数太多了 🐳"];
  } else {
    [HUD show];
    @weakify(self);
    BOOL isReplyVoice = self.isVoiceType ? YES : self.voiceCheck.selected;
    FTHZStraitModel *selectedStrait = self.straitList[self.selectedStraitIndex];
    [FTHZChannelCreateModel postChannel:self.titleText.text
        comment:self.contentText.text
        island_type:selectedStrait.islandId
        voice_type:[NSNumber numberWithBool:isReplyVoice]
        success:^(NSDictionary *resultObject) {
          @strongify(self);
          [HUD dissmiss];
          FTHZChannelCreateModel *member =
              [FTHZChannelCreateModel mj_objectWithKeyValues:resultObject];
          if ([member.success boolValue]) {
            [self dismissViewControllerAnimated:YES completion:nil];
          } else {
            [self.view makeToast:member.msg
                        duration:1.0
                        position:CSToastPositionCenter];
          }
        }
        failure:^(NSError *requestErr) {
          @strongify(self);
          [HUD dissmiss];
          [self showToastFast:@"数据有误,请检查网络后重试"];
        }];
  }
}

- (void)checkAction:(FlatButton *)bt {
  if (bt.tag == 1001) {
    self.imgCheck.selected = NO;
    self.voiceCheck.selected = YES;

  } else {
    self.imgCheck.selected = YES;
    self.voiceCheck.selected = NO;
  }
}

- (void)keyboardWillAppear:(NSNotification *)noti {
  NSDictionary *info = [noti userInfo];
  NSValue *value = [info objectForKey:UIKeyboardFrameEndUserInfoKey];
  CGFloat keyboardAnimationDuration =
      [[info objectForKey:UIKeyboardAnimationDurationUserInfoKey] floatValue];
  CGSize keyboardSize = [value CGRectValue].size;
  [UIView animateWithDuration:keyboardAnimationDuration
                   animations:^{
                     CGRect frame = self.optionBGView.frame;
                     frame.origin.y = self.view.frame.size.height -
                                      keyboardSize.height - 48 * kWidthFactor;
                     self.optionBGView.frame = frame;
                   }];
}

- (void)keyboardWillDisappear:(NSNotification *)noti {
  NSDictionary *info = [noti userInfo];
  CGFloat keyboardAnimationDuration =
      [[info objectForKey:UIKeyboardAnimationDurationUserInfoKey] floatValue];
  [UIView animateWithDuration:keyboardAnimationDuration
                   animations:^{
                     CGRect frame = self.optionBGView.frame;
                     frame.origin.y = kMainHeight;
                     self.optionBGView.frame = frame;
                   }];
}
#pragma mark -lifecycle

- (void)viewDidLoad {
  [super viewDidLoad];
  [self setupUI];

  _titleCountLabel.text = @"0/20";
  self.selectedStraitIndex = -1;

  [NSNotificationCenter.defaultCenter
      addObserver:self
         selector:@selector(titleTextDidChange:)
             name:UITextFieldTextDidChangeNotification
           object:self.titleText];

  [FTHZStraitListModel
      getStraitList:^(id resultObject) {
        FTHZStraitListModel *model =
            [FTHZStraitListModel mj_objectWithKeyValues:resultObject];
        self.straitList = model.data;
        dispatch_async(dispatch_get_main_queue(), ^{
          [self reloadStraitButtons];
        });
      }
            failure:^(NSError *error){
            }];
}

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];

  [IQKeyboardManager sharedManager].shouldResignOnTouchOutside = NO;
}

- (void)viewWillDisappear:(BOOL)animated {
  [super viewWillDisappear:animated];
  [self.titleText resignFirstResponder];
  [self.contentText resignFirstResponder];

  [IQKeyboardManager sharedManager].shouldResignOnTouchOutside = YES;
}

- (void)dealloc {
  [[NSNotificationCenter defaultCenter] removeObserver:self];
}
#pragma mark - delegate
- (BOOL)textField:(UITextField *)textField
    shouldChangeCharactersInRange:(NSRange)range
                replacementString:(NSString *)string {
  if (textField == self.titleText) {
    NSString *newText =
        [textField.text stringByReplacingCharactersInRange:range
                                                withString:string];

    NSInteger characterCount = newText.length;
    _titleCountLabel.text =
        [NSString stringWithFormat:@"%ld/20", (long)characterCount];

    if (characterCount > 20) {
      _titleCountLabel.textColor = [UIColor redColor];
    } else {
      _titleCountLabel.textColor =
          [KColor_HighBlack colorWithAlphaComponent:0.5];
    }

    if (range.location >= 20) {
      return NO;
    }
  }

  return YES;
}

- (BOOL)textView:(UITextView *)textView
    shouldChangeTextInRange:(NSRange)range
            replacementText:(NSString *)text {
  if (textView == self.contentText) {
    NSString *newText = [textView.text stringByReplacingCharactersInRange:range
                                                               withString:text];

    NSInteger characterCount = newText.length;
    _contentCountLabel.text =
        [NSString stringWithFormat:@"%ld/50", (long)characterCount];

    if (characterCount > 50) {
      _contentCountLabel.textColor = [UIColor redColor];
      textView.text = [textView.text substringToIndex:50];
      [self showToastFast:@"字数太多了 🐳"];
      return NO;
    } else {
      _contentCountLabel.textColor =
          [KColor_HighBlack colorWithAlphaComponent:0.5];
    }
  }

  return YES;
}

- (void)textViewDidEndEditing:(UITextView *)textView {
  if (![textView hasText]) {
    _contentPlaceHolderL.hidden = NO;
  }
}

- (void)textViewDidChange:(UITextView *)textView {
  if (![textView hasText]) {
    _contentPlaceHolderL.hidden = NO;
    _contentCountLabel.text = @"0/50";
  } else {
    _contentPlaceHolderL.hidden = YES;

    NSInteger characterCount = textView.text.length;
    _contentCountLabel.text =
        [NSString stringWithFormat:@"%ld/50", (long)characterCount];

    if (characterCount > 50) {
      _contentCountLabel.textColor = [UIColor redColor];
    } else {
      _contentCountLabel.textColor =
          [KColor_HighBlack colorWithAlphaComponent:0.5];
    }
  }
}

- (void)titleTextDidChange:(NSNotification *)notification {
  NSInteger characterCount = self.titleText.text.length;
  _titleCountLabel.text =
      [NSString stringWithFormat:@"%ld/20", (long)characterCount];

  if (characterCount > 20) {
    _titleCountLabel.textColor = [UIColor redColor];
  } else {
    _titleCountLabel.textColor = [KColor_HighBlack colorWithAlphaComponent:0.5];
  }
}

#pragma mark - Strait Buttons
- (void)reloadStraitButtons {
  [self.straitButtonsView.subviews
      makeObjectsPerformSelector:@selector(removeFromSuperview)];
  if (!self.straitList || self.straitList.count == 0)
    return;

  CGFloat margin = 8 * kWidthFactor;
  CGFloat btnHeight = 32 * kWidthFactor;
  CGFloat x = 0;
  CGFloat y = 0;
  CGFloat maxWidth = self.straitButtonsView.frame.size.width > 0
                         ? self.straitButtonsView.frame.size.width
                         : (kMainWidth - 48 * kWidthFactor);

  for (NSInteger i = 0; i < self.straitList.count; i++) {
    FTHZStraitModel *model = self.straitList[i];
    NSString *title = model.name ?: @"";
    UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
    btn.tag = 2000 + i;
    [btn setTitle:title forState:UIControlStateNormal];
    btn.titleLabel.font = SourceHanSerifMediumFont(14 * kWidthFactor);
    btn.layer.cornerRadius = 16 * kWidthFactor;
    btn.layer.masksToBounds = YES;
    btn.layer.borderWidth = 1;
    btn.layer.borderColor = KColor_HighBlack.CGColor;
    [btn addTarget:self
                  action:@selector(straitButtonTapped:)
        forControlEvents:UIControlEventTouchUpInside];

    CGFloat iconBgSize = 24 * kWidthFactor;
    CGFloat iconBgX = 4 * kWidthFactor;
    UIView *iconBg = [[UIView alloc]
        initWithFrame:CGRectMake(iconBgX, (btnHeight - iconBgSize) / 2,
                                 iconBgSize, iconBgSize)];
    iconBg.backgroundColor = KColor_White;
    iconBg.layer.cornerRadius = iconBgSize / 2.0;
    iconBg.layer.masksToBounds = YES;

    CGFloat iconIVSize = iconBgSize * 0.65;
    UIImageView *iconIV = [[UIImageView alloc]
        initWithFrame:CGRectMake((iconBgSize - iconIVSize) / 2,
                                 (iconBgSize - iconIVSize) / 2, iconIVSize,
                                 iconIVSize)];
    iconIV.contentMode = UIViewContentModeScaleAspectFit;
    iconIV.image = [UIImage imageNamed:title];
    [iconBg addSubview:iconIV];

    [btn addSubview:iconBg];

    btn.contentHorizontalAlignment = UIControlContentHorizontalAlignmentLeft;
    btn.titleEdgeInsets =
        UIEdgeInsetsMake(0, iconBgX + iconBgSize + 8 * kWidthFactor, 0, 0);

    if (i == self.selectedStraitIndex) {
      btn.backgroundColor = KColor_HighBlack;
      [btn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    } else {
      btn.backgroundColor = KColor_White;
      [btn setTitleColor:KColor_HighBlack forState:UIControlStateNormal];
    }

    CGSize titleSize =
        [title boundingRectWithSize:CGSizeMake(CGFLOAT_MAX, btnHeight)
                            options:NSStringDrawingUsesLineFragmentOrigin
                         attributes:@{NSFontAttributeName : btn.titleLabel.font}
                            context:nil]
            .size;
    CGFloat spacing = 8 * kWidthFactor;
    CGFloat btnWidth =
        iconBgSize + spacing + titleSize.width + 16 * kWidthFactor;
    if (btnWidth < 60 * kWidthFactor)
      btnWidth = 60 * kWidthFactor;
    if (x + btnWidth > maxWidth) {
      x = 0;
      y += btnHeight + margin;
    }
    btn.frame = CGRectMake(x, y, btnWidth, btnHeight);
    iconBg.frame = CGRectMake(iconBgX, (btnHeight - iconBgSize) / 2, iconBgSize,
                              iconBgSize);
    iconIV.frame =
        CGRectMake((iconBgSize - iconIVSize) / 2, (iconBgSize - iconIVSize) / 2,
                   iconIVSize, iconIVSize);

    [self.straitButtonsView addSubview:btn];
    x += btnWidth + margin;
  }
  CGFloat totalHeight = y + btnHeight;
  [self.straitButtonsView mas_updateConstraints:^(MASConstraintMaker *make) {
    make.height.mas_equalTo(totalHeight);
  }];
}

- (void)straitButtonTapped:(UIButton *)sender {
  NSInteger idx = sender.tag - 2000;
  if (self.selectedStraitIndex == idx) {
    return;
  }
  self.selectedStraitIndex = idx;
  [self reloadStraitButtons];

  FTHZStraitModel *selectedStrait = self.straitList[self.selectedStraitIndex];
  self.isVoiceType = [selectedStrait.islandId integerValue] == 1;
  [self updateOptionBGViewForVoiceType:self.isVoiceType];
}

- (void)updateOptionBGViewForVoiceType:(BOOL)isVoiceType {
  self.tipL.text =
      isVoiceType ? @"这条声波将被以声音回应" : @"希望这条声波得到";
  self.imgCheck.hidden = isVoiceType;
  self.voiceCheck.hidden = isVoiceType;
  for (UIView *subview in self.optionBGView.subviews) {
    if ([subview isKindOfClass:[UILabel class]]) {
      UILabel *label = (UILabel *)subview;
      if ([label.text isEqualToString:@"图文回应"] ||
          [label.text isEqualToString:@"声音回应"]) {
        label.hidden = isVoiceType;
      }
    }
    if ([subview isKindOfClass:[FlatButton class]]) {
      FlatButton *btn = (FlatButton *)subview;
      if (btn.tag == 1000 || btn.tag == 1001) {
        btn.hidden = isVoiceType;
      }
    }
  }
}

@end
