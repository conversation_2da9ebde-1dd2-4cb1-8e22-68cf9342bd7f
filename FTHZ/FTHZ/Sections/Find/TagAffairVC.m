#import "TagAffairVC.h"

#import "AffairTagListModel.h"
#import "AttentionDetailVC.h"
#import "AttentionTableViewCell.h"

#import "DoLikeModel.h"

#define TagAffairVCID @"TagAffairVCID"

NSString *const userWachedTopicNotification = @"hz.userWachedTopicNotification";
NSString *const userUnwachedTopicNotification =
    @"hz.userUnwachedTopicNotification";
NSString *const topicKey = @"topic";
NSString *const hasTipedUser = @"hz.hasTippedUser";

@interface TagAffairVC () <UITableViewDelegate, UITableViewDataSource> {
  NSInteger currentPage;
}
@property(nonatomic, assign) NSInteger page;
@property(nonatomic, strong) NSMutableArray *cellData;
@property(nonatomic, strong) FlatButton *watchButton;
@end

@implementation TagAffairVC

- (RACCommand *)toggleWatchCommand {
  if (!_toggleWatchCommand) {
    @weakify(self);
    _toggleWatchCommand = [[RACCommand
        alloc] initWithSignalBlock:^RACSignal *_Nonnull(id _Nullable input) {
      @strongify(self);

      RACSignal *sig;
      if (self.watched) {
        sig = [[RACSignal createSignal:^RACDisposable *_Nullable(
                              id<RACSubscriber> _Nonnull subscriber) {
          FTHZAlertDialogController *alert = [[FTHZAlertDialogController alloc]
              initWithTitle:@"提示"
                    message:@"取消关注主题，只能随机遇到该主题，无法及时查看动"
                            @"态噢~"];
          [alert
              addAction:[FTHZAlertDialogAction
                            actionWithTitle:@"点错了"
                                     action:^{
                                       [subscriber sendNext:@(NO)];
                                       [subscriber sendCompleted];
                                     }
                                      style:FTHZAlertDialogActionStyleDefault]];
          [alert
              addAction:
                  [FTHZAlertDialogAction
                      actionWithTitle:@"确认取消"
                               action:^{
                                 [subscriber sendNext:@(YES)];
                                 [subscriber sendCompleted];
                               }
                                style:FTHZAlertDialogActionStyleHighlighted]];
          [[UIViewController topViewController] presentViewController:alert
                                                             animated:YES
                                                           completion:nil];
          return nil;
        }] flattenMap:^__kindof RACSignal *_Nullable(id _Nullable value) {
          if ([value boolValue]) {
            return
                [[FTHZNetworkTask unwatchTopicWithID:self.tagId] rac_request];
          } else {
            return [RACSignal createSignal:^RACDisposable *_Nullable(
                                  id<RACSubscriber> _Nonnull subscriber) {
              [subscriber sendNext:nil];
              [subscriber sendCompleted];
              return nil;
            }];
          }
        }];
      } else {
        sig = [[RACSignal createSignal:^RACDisposable *_Nullable(
                              id<RACSubscriber> _Nonnull subscriber) {
          if (![[NSUserDefaults standardUserDefaults]
                  boolForKey:hasTipedUser]) {
            FTHZAlertDialogController *alert =
                [[FTHZAlertDialogController alloc]
                    initWithTitle:@"提示"
                          message:@"关注主题后，可在关注页面，随时查看主题最新"
                                  @"动态噢！"];
            [alert
                addAction:
                    [FTHZAlertDialogAction
                        actionWithTitle:@"好的，我知道了"
                                 action:^{
                                   [subscriber sendNext:@(YES)];
                                   [subscriber sendCompleted];
                                 }
                                  style:FTHZAlertDialogActionStyleHighlighted]];
            [[UIViewController topViewController]
                presentViewController:alert
                             animated:YES
                           completion:^{
                             [[NSUserDefaults standardUserDefaults]
                                 setBool:YES
                                  forKey:hasTipedUser];
                           }];
          } else {
            [subscriber sendNext:@(YES)];
            [subscriber sendCompleted];
          }
          return nil;
        }] flattenMap:^__kindof RACSignal *_Nullable(id _Nullable value) {
          if ([value boolValue]) {
            return [[FTHZNetworkTask watchTopicWithID:self.tagId] rac_request];
          } else {
            return [RACSignal createSignal:^RACDisposable *_Nullable(
                                  id<RACSubscriber> _Nonnull subscriber) {
              [subscriber sendNext:nil];
              [subscriber sendCompleted];
              return nil;
            }];
          }
        }];
      }
      return [[sig doNext:^(id _Nullable x) {
        if (!x) {
          return;
        }
        @strongify(self);
        self.watched = !self.watched;
        TopicModel *topic = [[TopicModel alloc] init];
        topic.name = self.tagName;
        topic.tagID = self.tagId;
        [[NSNotificationCenter defaultCenter]
            postNotificationName:self.watched ? userWachedTopicNotification
                                              : userUnwachedTopicNotification
                          object:nil
                        userInfo:@{topicKey : topic}];
      }] doError:^(NSError *_Nonnull error) {
        @strongify(self);
        [self showToastFast:error.localizedDescription];
      }];
    }];
  }
  return _toggleWatchCommand;
}

- (void)loadData {
  __weak typeof(self) wSelf = self;
  if (!wSelf.cellData) {
    wSelf.cellData = [NSMutableArray new];
  }
  [AffairTagListModel getAffairTagListModel:self.tagId
      lastId:@"0"
      success:^(NSDictionary *resultObject) {
        AffairListModel *member =
            [AffairListModel mj_objectWithKeyValues:resultObject];
        if ([member.success boolValue]) {
          if (self->currentPage == 1) {
            [wSelf.cellData removeAllObjects];
          }

          AffairListModelResult *tempMember = [AffairListModelResult
              mj_objectWithKeyValues:member.data.firstObject];
          for (int i = 0; i < tempMember.data.count; i++) {
            DynamicModelResult *dy = [DynamicModelResult
                mj_objectWithKeyValues:[tempMember.data objectAtIndex:i]];
            [wSelf.cellData addObject:dy];
          }
          if ([tempMember.count integerValue] > wSelf.cellData.count) {
            if (!self.tableView.mj_footer) {
              MJChiBaoZiFooter *footer = [MJChiBaoZiFooter
                  footerWithRefreshingTarget:self
                            refreshingAction:@selector(loadMoreData)];
              footer.refreshingTitleHidden = YES;
              self.tableView.mj_footer = footer;
            }
          } else {
            [self.tableView.mj_footer removeFromSuperview];
            self.tableView.mj_footer = nil;
          }
        }
        [wSelf.tableView.mj_header endRefreshing];
        [wSelf.tableView.mj_footer endRefreshing];
        [wSelf.tableView reloadData];
      }
      failure:^(NSError *requestErr) {
        [wSelf.tableView.mj_header endRefreshing];
        [wSelf.tableView.mj_footer endRefreshing];
      }];
}

- (void)fetchWatchState {
  @weakify(self);
  [[FTHZNetworkTask watchList]
      requestWithCompletion:^(BOOL isSuccess, NSURLResponse *_Nullable response,
                              FTHZCommonResponse *_Nullable resp,
                              NSError *_Nullable anError) {
        @strongify(self);
        if (!isSuccess) {
          return;
        }
        if (![self.tagId isKindOfClass:[NSString class]]) {
          self.watched = NO;
          return;
        }
        NSArray<TopicModel *> *models = resp.data;
        if (![models isKindOfClass:[NSArray<TopicModel *> class]]) {
          return;
        }
        BOOL contains = NO;
        for (TopicModel *topic in models) {
          if ([topic.tagID isEqualToString:self.tagId]) {
            contains = YES;
            break;
          }
        }
        self.watched = contains;
      }];
}

- (void)setWatched:(BOOL)watched {
  _watched = watched;
  [self.watchButton setTitle:watched ? @"已关注" : @"关注"
                    forState:UIControlStateNormal];
  [self.watchButton
      setTitleColor:watched ? [KColor_HighBlack colorWithAlphaComponent:0.45]
                            : KColor_HighBlack
           forState:UIControlStateNormal];
  self.watchButton.layer.borderColor =
      watched ? [[KColor_HighBlack colorWithAlphaComponent:0.45] CGColor]
              : [KColor_HighBlack CGColor];
}

- (FlatButton *)watchButton {
  if (!_watchButton) {
    _watchButton = [[FlatButton alloc] initWithFrame:CGRectMake(0, 0, 70, 24)];
    _watchButton.titleLabel.font = SourceHanSerifRegularFont(12);
    _watchButton.layer.borderWidth = 1;
    _watchButton.rac_command = self.toggleWatchCommand;
    UIBarButtonItem *item =
        [[UIBarButtonItem alloc] initWithCustomView:_watchButton];
    item.width = _watchButton.size.width;
    self.navigationItem.rightBarButtonItem = item;
    [_watchButton mas_makeConstraints:^(MASConstraintMaker *make) {
      make.size.mas_equalTo(self->_watchButton.size);
    }];
  }
  return _watchButton;
}

- (void)viewDidLoad {
  [super viewDidLoad];
  self.tableView.estimatedRowHeight = 200;
  self.tableView.rowHeight = UITableViewAutomaticDimension;
  self.title = self.tagName;
  [self.view addSubview:self.tableView];

  MJChiBaoZiFooter *footer =
      [MJChiBaoZiFooter footerWithRefreshingTarget:self
                                  refreshingAction:@selector(loadMoreData)];
  footer.refreshingTitleHidden = YES;
  self.tableView.mj_footer = footer;

  MJChiBaoZiHeader *header =
      [MJChiBaoZiHeader headerWithRefreshingTarget:self
                                  refreshingAction:@selector(loadAData)];
  header.lastUpdatedTimeLabel.hidden = YES;
  header.stateLabel.hidden = YES;
  [header beginRefreshing];
  self.tableView.mj_header = header;
  [self fetchWatchState];
}

- (void)loadMoreData {
  currentPage += 1;
  [self loadData];
}

- (void)loadAData {
  if (_tableView) {
    currentPage = 1;
    [self loadData];
  }
}

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];
  self.showBackBtn = YES;
  [AppConfig statusbarStyle:YES];
  if (!self.notControlNav) {
    [self.navigationController setNavigationBarHidden:NO animated:animated];
  }
}

- (UITableView *)tableView {

  if (FT_IS_IPhoneX_All) {
    if (!_tableView) {
      _tableView = [[UITableView alloc]
          initWithFrame:CGRectMake(0, 0, kMainWidth, kMainHeight - 83)];
      _tableView.delegate = self;
      _tableView.dataSource = self;
      _tableView.estimatedRowHeight = 0;
      _tableView.estimatedSectionHeaderHeight = 0;
      _tableView.estimatedSectionFooterHeight = 0;
      _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
      [_tableView registerClass:[AttentionTableViewCell class]
          forCellReuseIdentifier:TagAffairVCID];
      [self.view addSubview:_tableView];
    }
  } else {
    if (!_tableView) {
      _tableView = [[UITableView alloc]
          initWithFrame:CGRectMake(0, 0, kMainWidth, kMainHeight - 64)];
      _tableView.delegate = self;
      _tableView.dataSource = self;
      _tableView.estimatedRowHeight = 0;
      _tableView.estimatedSectionHeaderHeight = 0;
      _tableView.estimatedSectionFooterHeight = 0;
      _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
      [_tableView registerClass:[AttentionTableViewCell class]
          forCellReuseIdentifier:TagAffairVCID];
      [self.view addSubview:_tableView];
    }
  }
  return _tableView;
}
#pragma mark - Table view data source
- (CGFloat)tableView:(UITableView *)tableView
    heightForFooterInSection:(NSInteger)section {
  return 50.0;
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForHeaderInSection:(NSInteger)section {
  return 12 * kMainTemp;
}

- (UIView *)tableView:(UITableView *)tableView
    viewForFooterInSection:(NSInteger)section {
  return [[UIView alloc] initWithFrame:CGRectZero];
}

- (UIView *)tableView:(UITableView *)tableView
    viewForHeaderInSection:(NSInteger)section {
  return [[UIView alloc] initWithFrame:CGRectZero];
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
  return 1;
}

- (NSInteger)tableView:(UITableView *)tableView
    numberOfRowsInSection:(NSInteger)section {
  return _cellData.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView
         cellForRowAtIndexPath:(NSIndexPath *)indexPath {
  DynamicModelResult *dy = [_cellData objectAtIndex:indexPath.row];
  AttentionTableViewCell *cell = (AttentionTableViewCell *)[tableView
      dequeueReusableCellWithIdentifier:TagAffairVCID
                           forIndexPath:indexPath];
  if (!cell) {
    cell = [[AttentionTableViewCell alloc]
          initWithStyle:(UITableViewCellStyleDefault)
        reuseIdentifier:TagAffairVCID];
  }
  [cell setDynamic:dy showTime:NO];
  [cell.awesomeBtn addTarget:self
                      action:@selector(onTouchBtnInCell:)
            forControlEvents:(UIControlEventTouchUpInside)];
  cell.commentBtn.tag = indexPath.row;
  [cell.commentBtn addTarget:self
                      action:@selector(onTouchContentCell:)
            forControlEvents:(UIControlEventTouchUpInside)];
  cell.tagBtn.hidden = YES;
  return cell;
}

- (void)onTouchBtnInCell:(UIButton *)sender {
  CGPoint point = sender.center;
  point = [self.tableView convertPoint:point fromView:sender.superview];
  NSIndexPath *indexpath = [self.tableView indexPathForRowAtPoint:point];
  DynamicModelResult *dy = [_cellData objectAtIndex:indexpath.row];
  [self doLike:dy.affair.aid uid:dy.user.uid indexRow:indexpath.row];
}

- (void)onTouchContentCell:(UIButton *)sender {
  DynamicModelResult *dy = [_cellData objectAtIndex:sender.tag];
  AttentionDetailVC *ddVC = [[AttentionDetailVC alloc] init];
  ddVC.ddy = dy;
  ddVC.isTouchComment = YES;
  [self.navigationController pushViewController:ddVC animated:YES];
}

- (void)doLike:(NSString *)aid
           uid:(NSString *)uid
      indexRow:(NSInteger)indexRow {
  DynamicModelResult *dy = [_cellData objectAtIndex:indexRow];
  __weak typeof(self) wSelf = self;
  void (^action)(NSDictionary *) = ^(NSDictionary *resultObject) {
    DoLikeModel *member = [DoLikeModel mj_objectWithKeyValues:resultObject];
    if ([member.success boolValue]) {
      if ([dy.affair.likeRs isEqualToString:@"0"]) {
        NSInteger tepLike = [dy.affair.likeNum integerValue];
        tepLike++;
        dy.affair.likeNum = [NSString stringWithFormat:@"%ld", tepLike];
        dy.affair.likeRs = @"1";
        NSMutableArray *newArray = [wSelf.cellData mutableCopy];
        [newArray replaceObjectAtIndex:indexRow withObject:dy];
        wSelf.cellData = newArray;
      } else {
        NSInteger tepLike = [dy.affair.likeNum integerValue];
        tepLike--;
        dy.affair.likeNum = [NSString stringWithFormat:@"%ld", tepLike];
        dy.affair.likeRs = @"0";
        NSMutableArray *newArray = [wSelf.cellData mutableCopy];
        [newArray replaceObjectAtIndex:indexRow withObject:dy];
        wSelf.cellData = newArray;
      }
      NSIndexPath *indexPath = [NSIndexPath indexPathForRow:indexRow
                                                  inSection:0];
      [wSelf.tableView
          reloadRowsAtIndexPaths:[NSArray arrayWithObjects:indexPath, nil]
                withRowAnimation:UITableViewRowAnimationNone];
    } else if ([member.code integerValue] == FTHZErrorCodeAccountBanned) {
      ShowBanTip(member.msg);
    } else {
      [wSelf.view makeToast:member.msg
                   duration:1.0
                   position:CSToastPositionCenter];
    }
  };
  if ([dy.affair upvoted]) {
    [DoLikeModel postUnlikeModel:uid
                       contentid:aid
                         success:action
                         failure:^(NSError *requestErr) {
                           [self.view makeToast:@"数据有误,请检查网络后重试"
                                       duration:2.0
                                       position:CSToastPositionCenter];
                         }];
  } else {
    [DoLikeModel postLikeModel:uid
                     contentid:aid
                       success:action
                       failure:^(NSError *requestErr) {
                         [self.view makeToast:@"数据有误,请检查网络后重试"
                                     duration:2.0
                                     position:CSToastPositionCenter];
                       }];
  }
}

#pragma mark - UITableViewDelegate
- (void)tableView:(UITableView *)tableView
    didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
  [tableView deselectRowAtIndexPath:indexPath animated:YES];
  DynamicModelResult *dy = [_cellData objectAtIndex:indexPath.row];
  AttentionDetailVC *ddVC = [[AttentionDetailVC alloc] init];
  ddVC.ddy = dy;
  ddVC.isTouchComment = NO;
  [self.navigationController pushViewController:ddVC animated:YES];
}

@end
