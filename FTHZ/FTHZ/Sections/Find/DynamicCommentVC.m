#import "DynamicCommentVC.h"
#import "AffairCommentsModel.h"
#import "DyCommentTableViewCell.h"

#import "WhaleDetailVC.h"

#import "DoCommentModel.h"
#import "XHInputView.h"

#define AttentionCommentTableViewCellID @"DyCommentTableViewCellID"

@interface DynamicCommentVC () <UITableViewDelegate, UITableViewDataSource,
                                XHInputViewDelagete>
@property(nonatomic, strong) UITableView *tableView;
@property(nonatomic, assign) NSInteger page;
@property(nonatomic, assign) BOOL isHeader;
@property(nonatomic, strong) NSMutableArray *cellData;

@end

@implementation DynamicCommentVC

- (void)loadMoreData {
  self.page++;
  [self loadData];
}

- (void)loadData {
  @weakify(self);
  if (!self.cellData) {
    self.cellData = [NSMutableArray new];
  }
  [AffairCommentsModel getAffairCommentsModel:self.aid
      authorid:self.uid
      page:@(self.page)
      size:@"50"
      success:^(NSDictionary *resultObject) {
        @strongify(self);
        [self.tableView.mj_footer endRefreshing];
        AffairCommentsModel *member =
            [AffairCommentsModel mj_objectWithKeyValues:resultObject];

        if ([member.success boolValue]) {
          AffairCommentsModelResult *tempMember = [AffairCommentsModelResult
              mj_objectWithKeyValues:member.data.firstObject];
          if (self.page == 1) {
            [self.cellData removeAllObjects];
          }
          for (int i = 0; i < tempMember.data.count; i++) {
            AffairCommentsReplyResult *dy = [AffairCommentsReplyResult
                mj_objectWithKeyValues:[tempMember.data objectAtIndex:i]];
            [self.cellData addObject:dy];
          }
          NSInteger allCount = self.cellData.count;
          for (AffairCommentsReplyResult *afr in [self.cellData copy]) {
            allCount += afr.data.count;
          }

          if ([tempMember.count integerValue] > allCount) {
            if (!self.tableView.mj_footer) {
              MJChiBaoZiFooter *footer = [MJChiBaoZiFooter
                  footerWithRefreshingTarget:self
                            refreshingAction:@selector(loadMoreData)];
              footer.refreshingTitleHidden = YES;
              self.tableView.mj_footer = footer;
            }
          } else {
            [self.tableView.mj_footer removeFromSuperview];
            self.tableView.mj_footer = nil;
          }
          [self.tableView reloadData];
        }
      }
      failure:^(NSError *requestErr) {
        [self.tableView.mj_footer endRefreshing];
      }];
}

// 评论
- (void)loaddoCommentAction:(NSString *)sendText
                     sendIM:(BOOL)sendIM
                     rowdex:(NSInteger)rowdex
                     secdex:(NSInteger)secdex {
  __weak typeof(self) wSelf = self;
  [HUD show];
  NSString *temptouserid = @"";
  NSString *temptocomid = @"";
  NSString *tempbelongs = @"";
  AffairCommentsReplyResult *dy = [_cellData objectAtIndex:secdex];
  if (dy.data.count > 0 && rowdex != 0) {
    AffairCommentsToReplyResult *redy = [AffairCommentsToReplyResult
        mj_objectWithKeyValues:[dy.data objectAtIndex:rowdex - 1]];

    temptouserid = redy.userid;
    temptocomid = redy.commentid;

  } else {
    temptouserid = dy.userid;
    temptocomid = dy.commentid;
  }
  tempbelongs = dy.commentid;

  [DoCommentModel postDoCommentModel:self.uid
      contentid:self.aid
      comment:sendText
      to_userid:temptouserid
      to_commentid:temptocomid
      belongs:tempbelongs
      success:^(NSDictionary *resultObject) {
        DoCommentModel *member =
            [DoCommentModel mj_objectWithKeyValues:resultObject];
        [HUD dissmiss];
        if ([member.success boolValue]) {
          // 清除文本框内容
          if ([[USERDEFAULT objectForKey:ContenText] isValid]) {
            NSString *contentText = @"";
            [USERDEFAULT setObject:contentText forKey:ContenText];
            [USERDEFAULT synchronize];
          }

          [self.view makeToast:@"评论成功"
                      duration:1.0
                      position:CSToastPositionCenter];
          [wSelf loadData];
          [wSelf.delegate reloadDyAffair];
        } else {
          [self.view makeToast:member.msg
                      duration:1.0
                      position:CSToastPositionCenter];
        }
      }
      failure:^(NSError *requestErr) {
        [HUD dissmiss];
        [self.view makeToast:@"数据有误,请检查网络后重试"
                    duration:1.0
                    position:CSToastPositionCenter];
      }];
}

- (void)viewDidLoad {
  [super viewDidLoad];
  [self.view addSubview:self.tableView];
  self.page = 1;
  [self loadData];
  [NOTIFICENTER addObserver:self
                   selector:@selector(loadData)
                       name:TacthBtnofCommentForSeaReload
                     object:nil];
}

- (UITableView *)tableView {
  if (FT_IS_IPhoneX_All) {
    if (!_tableView) {
      _tableView = [[UITableView alloc]
          initWithFrame:CGRectMake(0, 0, kMainWidth, kMainHeight - 147 - 44)];
      _tableView.delegate = self;
      _tableView.dataSource = self;
      _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
      [_tableView registerClass:[DyCommentTableViewCell class]
          forCellReuseIdentifier:AttentionCommentTableViewCellID];
      [self.view addSubview:_tableView];
    }
    return _tableView;

  } else {
    if (!_tableView) {
      _tableView = [[UITableView alloc]
          initWithFrame:CGRectMake(0, 0, kMainWidth, kMainHeight - 104)];
      _tableView.delegate = self;
      _tableView.dataSource = self;
      _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
      [_tableView registerClass:[DyCommentTableViewCell class]
          forCellReuseIdentifier:AttentionCommentTableViewCellID];
      [self.view addSubview:_tableView];
    }
    return _tableView;
  }
}

#pragma mark - Table view data source
- (CGFloat)tableView:(UITableView *)tableView
    heightForFooterInSection:(NSInteger)section {
  if (section == 0) {
    return 10.0f;
  }
  if (section == _cellData.count - 1) {
    return 40.0f;
  }
  return 0.01f;
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForHeaderInSection:(NSInteger)section {
  if (_cellData.count == 0) {
    return kMainHeight * 0.3;
  }
  return 0.01f;
}

- (UIView *)tableView:(UITableView *)tableView
    viewForFooterInSection:(NSInteger)section {
  return [[UIView alloc] initWithFrame:CGRectZero];
}

- (UIView *)tableView:(UITableView *)tableView
    viewForHeaderInSection:(NSInteger)section {
  if (_cellData.count == 0) {
    UIView *bgView = [[UIView alloc]
        initWithFrame:CGRectMake(0, 0, kMainHeight * 0.3,
                                 kMainWidth * 0.34 + 96 * kMainTemp)];
    UIImageView *empty = [[UIImageView alloc] init];
    empty.image = KImage_name(@"Commentempty");
    [bgView addSubview:empty];
    [empty mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.center.equalTo(bgView);
      make.size.mas_equalTo(CGSizeMake(kMainWidth * 0.6, kMainWidth * 0.34));
    }];
    return bgView;
  }

  return [[UIView alloc] initWithFrame:CGRectZero];
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForRowAtIndexPath:(NSIndexPath *)indexPath {
  AffairCommentsReplyResult *dy = [_cellData objectAtIndex:indexPath.section];
  if (dy.data.count > 0 && indexPath.row > 0) {
    AffairCommentsToReplyResult *redy = [AffairCommentsToReplyResult
        mj_objectWithKeyValues:[dy.data objectAtIndex:indexPath.row - 1]];
    CGSize retp = [NemoUtil
        calculateLabelHeightByText:[UIFont systemFontOfSize:10 * kMainTemp]
                             width:246 * kMainTemp
                         heightMax:kMainHeight * 2
                           content:redy.comment];
    return 30 * kMainTemp + retp.height;
  } else if (dy.data.count > 0 && indexPath.row == 0) {
    CGSize tp = [NemoUtil
        calculateLabelHeightByText:[UIFont systemFontOfSize:13 * kMainTemp]
                             width:260 * kMainTemp
                         heightMax:kMainHeight * 2
                           content:dy.comment];
    return 40 * kMainTemp + tp.height;
  } else {
    CGSize tp = [NemoUtil
        calculateLabelHeightByText:[UIFont systemFontOfSize:13 * kMainTemp]
                             width:260 * kMainTemp
                         heightMax:kMainHeight * 2
                           content:dy.comment];
    return 40 * kMainTemp + tp.height;
  }
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
  return _cellData.count;
}

- (NSInteger)tableView:(UITableView *)tableView
    numberOfRowsInSection:(NSInteger)section {
  AffairCommentsReplyResult *dy = [_cellData objectAtIndex:section];
  if (dy.data.count > 0) {
    return dy.data.count + 1;
  }
  return 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView
         cellForRowAtIndexPath:(NSIndexPath *)indexPath {

  AffairCommentsReplyResult *dy = [_cellData objectAtIndex:indexPath.section];
  if (dy.data.count > 0 && indexPath.row > 0) {
    AffairCommentsToReplyResult *redy = [AffairCommentsToReplyResult
        mj_objectWithKeyValues:[dy.data objectAtIndex:indexPath.row - 1]];

    DyCommentTableViewCell *cell = (DyCommentTableViewCell *)[tableView
        dequeueReusableCellWithIdentifier:AttentionCommentTableViewCellID
                             forIndexPath:indexPath];
    if (!cell) {
      cell = [[DyCommentTableViewCell alloc]
            initWithStyle:(UITableViewCellStyleDefault)
          reuseIdentifier:AttentionCommentTableViewCellID];
    }
    [cell loadToCommentUser:redy];
    cell.contentID = self.aid;
    cell.contentAuthorID = self.uid;
    cell.AnameBtn.tag = [redy.userid integerValue];
    cell.BnameBtn.tag = [redy.to_userid integerValue];
    [cell.AnameBtn addTarget:self
                      action:@selector(tauchAnameBtn:)
            forControlEvents:UIControlEventTouchUpInside];
    [cell.BnameBtn addTarget:self
                      action:@selector(tauchBnameBtn:)
            forControlEvents:UIControlEventTouchUpInside];
    //        cell.selectionStyle = UITableViewCellSelectionStyleGray;
    return cell;

  } else {
    DyCommentTableViewCell *cell = (DyCommentTableViewCell *)[tableView
        dequeueReusableCellWithIdentifier:AttentionCommentTableViewCellID
                             forIndexPath:indexPath];
    if (!cell) {
      cell = [[DyCommentTableViewCell alloc]
            initWithStyle:(UITableViewCellStyleDefault)
          reuseIdentifier:AttentionCommentTableViewCellID];
    }
    cell.CommentUser = dy;
    cell.contentID = self.aid;
    cell.contentAuthorID = self.uid;
    @weakify(self);
    cell.tapIconAction = ^(NSString *_Nonnull uid) {
      @strongify(self);
      WhaleDetailVC *infoVC = [[WhaleDetailVC alloc] init];
      infoVC.uid = uid;
      [self.navigationController pushViewController:infoVC animated:YES];
    };
    //        cell.selectionStyle = UITableViewCellSelectionStyleGray;
    return cell;
  }
}

- (void)tauchAnameBtn:(UIButton *)sender {
  WhaleDetailVC *infoVC = [[WhaleDetailVC alloc] init];
  infoVC.uid = [NSString stringWithFormat:@"%ld", sender.tag];
  [self.navigationController pushViewController:infoVC animated:YES];
}

- (void)tauchBnameBtn:(UIButton *)sender {
  WhaleDetailVC *infoVC = [[WhaleDetailVC alloc] init];
  infoVC.uid = [NSString stringWithFormat:@"%ld", sender.tag];
  [self.navigationController pushViewController:infoVC animated:YES];
}

- (void)tableView:(UITableView *)tableView
    didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
  [self showXHInputViewWithStyle:InputViewStyleNoSendDefault
                          rowdex:indexPath.row
                          secdex:indexPath.section];
  [tableView deselectRowAtIndexPath:indexPath animated:YES];
}

- (void)showXHInputViewWithStyle:(InputViewStyle)style
                          rowdex:(NSInteger)rowdex
                          secdex:(NSInteger)secdex {
  __weak typeof(self) wSelf = self;
  [XHInputView showWithStyle:style
      configurationBlock:^(XHInputView *inputView) {
        /** 请在此block中设置inputView属性 */
        /** 代理 */
        inputView.delegate = self;
        /** 占位符文字 */
        inputView.placeholder = @"呼应TA的声波";
        /** 设置最大输入字数 */
        inputView.maxCount = 2000;
        /** 输入框颜色 */
        inputView.textViewBackgroundColor =
            [UIColor groupTableViewBackgroundColor];
        /** 更多属性设置,详见XHInputView.h文件 */
      }
      sendBlock:^BOOL(NSString *text, BOOL selTemp) {
        if (text.length) {
          [wSelf loaddoCommentAction:text
                              sendIM:selTemp
                              rowdex:rowdex
                              secdex:secdex];
          //            _textLab.text = text;
          return YES; // return YES,收起键盘
        } else {
          return NO; // return NO,不收键盘
        }
      }];
}

#pragma mark - XHInputViewDelagete
/** XHInputView 将要显示 */
- (void)xhInputViewWillShow:(XHInputView *)inputView {
  /** 如果你工程中有配置IQKeyboardManager,并对XHInputView造成影响,请在XHInputView将要显示时将其关闭 */
  //    [IQKeyboardManager sharedManager].enableAutoToolbar = NO;
  //    [IQKeyboardManager sharedManager].enable = NO;
}

/** XHInputView 将要影藏 */
- (void)xhInputViewWillHide:(XHInputView *)inputView {

  /** 如果你工程中有配置IQKeyboardManager,并对XHInputView造成影响,请在XHInputView将要影藏时将其打开 */

  //[IQKeyboardManager sharedManager].enableAutoToolbar = YES;
  //[IQKeyboardManager sharedManager].enable = YES;
}

- (void)dealloc {
  [NOTIFICENTER removeObserver:self];
}

@end
