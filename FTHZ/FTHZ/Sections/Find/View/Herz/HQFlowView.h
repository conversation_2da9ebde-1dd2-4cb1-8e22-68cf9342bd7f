#import "HQIndexBannerSubview.h"
#import <UIKit/UIKit.h>

@protocol HQFlowViewDataSource;
@protocol HQFlowViewDelegate;

typedef enum {
  HQFlowViewOrientationHorizontal = 0,
  HQFlowViewOrientationVertical
} HQFlowViewOrientation;

@interface HQFlowView : UIView <UIScrollViewDelegate>

@property(nonatomic, assign) HQFlowViewOrientation orientation;
@property(nonatomic, strong) UIScrollView *scrollView;
@property(nonatomic, assign) BOOL needsReload;
@property(nonatomic, assign) NSInteger pageCount;
@property(nonatomic, strong) NSMutableArray *cells;
@property(nonatomic, assign) NSRange visibleRange;
@property(nonatomic, strong) NSMutableArray *reusableCells;
@property(nonatomic, assign) id<HQFlowViewDataSource> dataSource;
@property(nonatomic, assign) id<HQFlowViewDelegate> delegate;
@property(nonatomic, retain) UIPageControl *pageControl;
@property(nonatomic, assign) CGFloat minimumPageAlpha;
@property(nonatomic, assign) CGFloat leftRightMargin;
@property(nonatomic, assign) CGFloat topBottomMargin;
@property(nonatomic, assign) BOOL isOpenAutoScroll;
@property(nonatomic, assign) BOOL isCarousel;
@property(nonatomic, assign, readonly) NSInteger currentPageIndex;
@property(nonatomic, weak) NSTimer *timer;
@property(nonatomic, assign) CGFloat autoTime;
@property(nonatomic, assign) NSInteger orginPageCount;
- (void)reloadData;

- (HQIndexBannerSubview *)dequeueReusableCell;

- (void)scrollToPage:(NSUInteger)pageNumber;

- (void)stopTimer;

- (void)adjustCenterSubview;

@end

@protocol HQFlowViewDelegate <NSObject>

@optional

- (CGSize)sizeForPageInFlowView:(HQFlowView *)flowView;

- (void)willScrollToPage:(NSInteger)pageNumber
              inFlowView:(HQFlowView *)flowView;

- (void)didScrollToPage:(NSInteger)pageNumber inFlowView:(HQFlowView *)flowView;

- (void)didSelectCell:(HQIndexBannerSubview *)subView
     withSubViewIndex:(NSInteger)subIndex;

@end

@protocol HQFlowViewDataSource <NSObject>

- (NSInteger)numberOfPagesInFlowView:(HQFlowView *)flowView;

- (HQIndexBannerSubview *)flowView:(HQFlowView *)flowView
                cellForPageAtIndex:(NSInteger)index;

@end
