#import "HQFlowView.h"

@interface HQFlowView ()

@property(nonatomic, assign, readwrite) NSInteger currentPageIndex;

@property(nonatomic, assign) NSInteger page;

@property(nonatomic, assign) CGSize pageSize;

@end

static NSString *subviewClassName;

@implementation HQFlowView

#pragma mark -
#pragma mark Private Methods

- (void)initialize {
  self.clipsToBounds = YES;

  self.needsReload = YES;
  self.pageCount = 0;
  self.isOpenAutoScroll = YES;
  self.isCarousel = YES;
  self.leftRightMargin = 20;
  self.topBottomMargin = 30;
  _currentPageIndex = 0;

  _minimumPageAlpha = 1.0;
  _autoTime = 5.0;

  self.visibleRange = NSMakeRange(0, 0);

  self.reusableCells = [[NSMutableArray alloc] initWithCapacity:0];
  self.cells = [[NSMutableArray alloc] initWithCapacity:0];

  self.scrollView = [[UIScrollView alloc] initWithFrame:self.bounds];
  self.scrollView.scrollsToTop = NO;
  self.scrollView.delegate = self;
  self.scrollView.pagingEnabled = YES;
  self.scrollView.clipsToBounds = NO;
  self.scrollView.showsHorizontalScrollIndicator = NO;
  self.scrollView.showsVerticalScrollIndicator = NO;
  if (@available(iOS 11.0, *)) {
    [self.scrollView setContentInsetAdjustmentBehavior:
                         UIScrollViewContentInsetAdjustmentNever];
  } else {
  }
  subviewClassName = @"HQIndexBannerSubview";

  [self addSubview:self.scrollView];
}

- (void)setLeftRightMargin:(CGFloat)leftRightMargin {
  _leftRightMargin = leftRightMargin * 0.5;
}

- (void)setTopBottomMargin:(CGFloat)topBottomMargin {
  _topBottomMargin = topBottomMargin * 0.5;
}

- (void)startTimer {

  if (self.orginPageCount > 1 && self.isOpenAutoScroll && self.isCarousel) {
    NSTimer *timer =
        [NSTimer scheduledTimerWithTimeInterval:self.autoTime
                                         target:self
                                       selector:@selector(autoNextPage)
                                       userInfo:nil
                                        repeats:YES];
    self.timer = timer;
    [[NSRunLoop mainRunLoop] addTimer:timer forMode:NSRunLoopCommonModes];
  }
}

- (void)stopTimer {

  if (self.timer) {
    [self.timer invalidate];
    self.timer = nil;
  }
}

- (void)adjustCenterSubview {
  if (self.isOpenAutoScroll && self.orginPageCount > 0) {
    [_scrollView setContentOffset:CGPointMake(_pageSize.width * self.page, 0)
                         animated:NO];
  }
}

#pragma mark--自动轮播
- (void)autoNextPage {

  self.page++;

  switch (self.orientation) {
  case HQFlowViewOrientationHorizontal: {

    [_scrollView setContentOffset:CGPointMake(self.page * _pageSize.width, 0)
                         animated:YES];
    break;
  }
  case HQFlowViewOrientationVertical: {

    [_scrollView setContentOffset:CGPointMake(0, self.page * _pageSize.height)
                         animated:YES];

    break;
  }
  default:
    break;
  }
}

- (void)queueReusableCell:(HQIndexBannerSubview *)cell {
  [_reusableCells addObject:cell];
}

- (void)removeCellAtIndex:(NSInteger)index {
  HQIndexBannerSubview *cell = [_cells objectAtIndex:index];
  if ((NSObject *)cell == [NSNull null]) {
    return;
  }

  [self queueReusableCell:cell];

  if (cell.superview) {
    [cell removeFromSuperview];
  }

  [_cells replaceObjectAtIndex:index withObject:[NSNull null]];
}

- (void)refreshVisibleCellAppearance {

  if (_minimumPageAlpha == 1.0 && self.leftRightMargin == 0 &&
      self.topBottomMargin == 0) {
    return;
  }
  switch (self.orientation) {
  case HQFlowViewOrientationHorizontal: {
    CGFloat centerX =
        _scrollView.contentOffset.x + _scrollView.bounds.size.width / 2.0;

    for (NSInteger i = self.visibleRange.location;
         i < self.visibleRange.location + _visibleRange.length; i++) {
      HQIndexBannerSubview *cell = [_cells objectAtIndex:i];
      subviewClassName = NSStringFromClass([cell class]);
      CGFloat cellCenterX = cell.center.x;
      CGFloat distance = fabs(centerX - cellCenterX);
      CGFloat maxDistance = _pageSize.width;
      CGFloat scale = 0.85 + (1 - MIN(distance / maxDistance, 1)) * 0.15;
      cell.layer.transform = CATransform3DMakeScale(scale, scale, 1.0);

      CGFloat leftRightInset =
          self.leftRightMargin * distance / _pageSize.width;
      CGFloat topBottomInset =
          self.topBottomMargin * distance / _pageSize.width;

      cell.frame = UIEdgeInsetsInsetRect(
          CGRectMake(_pageSize.width * i, 0, _pageSize.width, _pageSize.height),
          UIEdgeInsetsMake(topBottomInset, leftRightInset, topBottomInset,
                           leftRightInset));
    }
    break;
  }
  case HQFlowViewOrientationVertical: {
    CGFloat offset = _scrollView.contentOffset.y;

    for (NSInteger i = self.visibleRange.location;
         i < self.visibleRange.location + _visibleRange.length; i++) {
      HQIndexBannerSubview *cell = [_cells objectAtIndex:i];
      subviewClassName = NSStringFromClass([cell class]);
      CGFloat origin = cell.frame.origin.y;
      CGFloat delta = fabs(origin - offset);

      CGRect originCellFrame = CGRectMake(0, _pageSize.height * i,
                                          _pageSize.width, _pageSize.height);

      if (delta < _pageSize.height) {
        cell.coverView.alpha = (delta / _pageSize.height) * _minimumPageAlpha;

        CGFloat leftRightInset =
            self.leftRightMargin * delta / _pageSize.height;
        CGFloat topBottomInset =
            self.topBottomMargin * delta / _pageSize.height;

        cell.layer.transform = CATransform3DMakeScale(
            (_pageSize.width - leftRightInset * 2) / _pageSize.width,
            (_pageSize.height - topBottomInset * 2) / _pageSize.height, 1.0);
        cell.frame = UIEdgeInsetsInsetRect(
            originCellFrame, UIEdgeInsetsMake(topBottomInset, leftRightInset,
                                              topBottomInset, leftRightInset));
        cell.mainView.frame = cell.bounds;
      } else {
        cell.coverView.alpha = _minimumPageAlpha;
        cell.frame = UIEdgeInsetsInsetRect(
            originCellFrame,
            UIEdgeInsetsMake(self.topBottomMargin, self.leftRightMargin,
                             self.topBottomMargin, self.leftRightMargin));
        cell.mainView.frame = cell.bounds;
      }
    }
  }
  default:
    break;
  }
}

- (void)setPageAtIndex:(NSInteger)pageIndex {
  NSParameterAssert(pageIndex >= 0 && pageIndex < [_cells count]);

  HQIndexBannerSubview *cell = [_cells objectAtIndex:pageIndex];

  if ((NSObject *)cell == [NSNull null]) {
    cell = [_dataSource flowView:self
              cellForPageAtIndex:pageIndex % self.orginPageCount];
    NSAssert(cell != nil, @"datasource must not return nil");
    [_cells replaceObjectAtIndex:pageIndex withObject:cell];

    cell.tag = pageIndex % self.orginPageCount;
    [cell setSubviewsWithSuperViewBounds:CGRectMake(0, 0, _pageSize.width,
                                                    _pageSize.height)];

    __weak __typeof(self) weakSelf = self;
    cell.didSelectCellBlock = ^(NSInteger tag, HQIndexBannerSubview *cell) {
      [weakSelf singleCellTapAction:tag withCell:cell];
    };

    switch (self.orientation) {
    case HQFlowViewOrientationHorizontal:
      cell.frame = CGRectMake(_pageSize.width * pageIndex, 0, _pageSize.width,
                              _pageSize.height);
      break;
    case HQFlowViewOrientationVertical:
      cell.frame = CGRectMake(0, _pageSize.height * pageIndex, _pageSize.width,
                              _pageSize.height);
      break;
    default:
      break;
    }

    if (!cell.superview) {
      [_scrollView addSubview:cell];
    }
  }
}

- (void)setPagesAtContentOffset:(CGPoint)offset {
  CGPoint startPoint = CGPointMake(offset.x - _scrollView.frame.origin.x,
                                   offset.y - _scrollView.frame.origin.y);
  CGPoint endPoint = CGPointMake(startPoint.x + self.bounds.size.width,
                                 startPoint.y + self.bounds.size.height);

  switch (self.orientation) {
  case HQFlowViewOrientationHorizontal: {
    NSInteger startIndex = 0;
    for (int i = 0; i < [_cells count]; i++) {
      if (_pageSize.width * (i + 1) > startPoint.x) {
        startIndex = i;
        break;
      }
    }

    NSInteger endIndex = startIndex;
    for (NSInteger i = startIndex; i < [_cells count]; i++) {
      if ((_pageSize.width * (i + 1) < endPoint.x &&
           _pageSize.width * (i + 2) >= endPoint.x) ||
          i + 2 == [_cells count]) {
        endIndex = i + 1;
        break;
      }
    }

    startIndex = MAX(startIndex - 1, 0);
    endIndex = MIN(endIndex + 1, [_cells count] - 1);

    self.visibleRange = NSMakeRange(startIndex, endIndex - startIndex + 1);
    for (NSInteger i = startIndex; i <= endIndex; i++) {
      [self setPageAtIndex:i];
    }

    for (int i = 0; i < startIndex; i++) {
      [self removeCellAtIndex:i];
    }

    for (NSInteger i = endIndex + 1; i < [_cells count]; i++) {
      [self removeCellAtIndex:i];
    }
    break;
  }
  case HQFlowViewOrientationVertical: {
    NSInteger startIndex = 0;
    for (int i = 0; i < [_cells count]; i++) {
      if (_pageSize.height * (i + 1) > startPoint.y) {
        startIndex = i;
        break;
      }
    }

    NSInteger endIndex = startIndex;
    for (NSInteger i = startIndex; i < [_cells count]; i++) {
      if ((_pageSize.height * (i + 1) < endPoint.y &&
           _pageSize.height * (i + 2) >= endPoint.y) ||
          i + 2 == [_cells count]) {
        endIndex = i + 1;
        break;
      }
    }

    startIndex = MAX(startIndex - 1, 0);
    endIndex = MIN(endIndex + 1, [_cells count] - 1);

    _visibleRange.location = startIndex;
    _visibleRange.length = endIndex - startIndex + 1;

    for (NSInteger i = startIndex; i <= endIndex; i++) {
      [self setPageAtIndex:i];
    }

    for (NSInteger i = 0; i < startIndex; i++) {
      [self removeCellAtIndex:i];
    }

    for (NSInteger i = endIndex + 1; i < [_cells count]; i++) {
      [self removeCellAtIndex:i];
    }
    break;
  }
  default:
    break;
  }
}

#pragma mark -
#pragma mark Override Methods

- (id)initWithFrame:(CGRect)frame {
  self = [super initWithFrame:frame];
  if (self) {
    [self initialize];
  }
  return self;
}

- (id)initWithCoder:(NSCoder *)aDecoder {
  self = [super initWithCoder:aDecoder];
  if (self) {
    [self initialize];
  }
  return self;
}

#pragma mark -
#pragma mark JQFlowView API

- (void)reloadData {
  _needsReload = YES;

  for (UIView *view in self.scrollView.subviews) {
    if ([NSStringFromClass(view.class) isEqualToString:subviewClassName] ||
        [view isKindOfClass:[HQIndexBannerSubview class]]) {
      [view removeFromSuperview];
    }
  }

  [self stopTimer];

  if (_needsReload) {

    if (_dataSource &&
        [_dataSource respondsToSelector:@selector(numberOfPagesInFlowView:)]) {

      self.orginPageCount = [_dataSource numberOfPagesInFlowView:self];

      if (self.isCarousel) {
        _pageCount = self.orginPageCount == 1
                         ? 1
                         : [_dataSource numberOfPagesInFlowView:self] * 3;
      } else {
        _pageCount = self.orginPageCount == 1
                         ? 1
                         : [_dataSource numberOfPagesInFlowView:self];
      }

      if (_pageCount == 0) {

        return;
      }

      if (self.pageControl &&
          [self.pageControl respondsToSelector:@selector(setNumberOfPages:)]) {
        [self.pageControl setNumberOfPages:self.orginPageCount];
      }
    }

    _pageSize = CGSizeMake(self.bounds.size.width - 4 * self.leftRightMargin,
                           (self.bounds.size.width - 4 * self.leftRightMargin) *
                               9 / 16);
    if (self.delegate && self.delegate &&
        [self.delegate respondsToSelector:@selector(sizeForPageInFlowView:)]) {
      _pageSize = [self.delegate sizeForPageInFlowView:self];
    }

    [_reusableCells removeAllObjects];
    _visibleRange = NSMakeRange(0, 0);

    [_cells removeAllObjects];
    for (NSInteger index = 0; index < _pageCount; index++) {
      [_cells addObject:[NSNull null]];
    }

    switch (self.orientation) {
    case HQFlowViewOrientationHorizontal:
      _scrollView.frame = CGRectMake(0, 0, _pageSize.width, _pageSize.height);
      _scrollView.contentSize = CGSizeMake(_pageSize.width * _pageCount, 0);
      _scrollView.centerX = CGRectGetMidX(self.bounds);

      if (self.orginPageCount > 1) {

        if (self.isCarousel) {

          [_scrollView
              setContentOffset:CGPointMake(
                                   _pageSize.width * self.orginPageCount, 0)
                      animated:NO];

          self.page = self.orginPageCount;

          [self startTimer];

        } else {
          [_scrollView setContentOffset:CGPointMake(0, 0) animated:NO];

          self.page = self.orginPageCount;
        }
      }

      break;
    case HQFlowViewOrientationVertical: {
      _scrollView.frame = CGRectMake(0, 0, _pageSize.width, _pageSize.height);
      _scrollView.contentSize = CGSizeMake(0, _pageSize.height * _pageCount);
      _scrollView.centerX = CGRectGetMidX(self.bounds);

      if (self.orginPageCount > 1) {

        if (self.isCarousel) {
          [_scrollView setContentOffset:CGPointMake(0, _pageSize.height *
                                                           self.orginPageCount)
                               animated:NO];

          self.page = self.orginPageCount;

          [self startTimer];
        } else {
          [_scrollView setContentOffset:CGPointMake(0, 0) animated:NO];

          self.page = self.orginPageCount;
        }
      }

      break;
    }
    default:
      break;
    }

    _needsReload = NO;
  }

  [self setPagesAtContentOffset:_scrollView.contentOffset];

  [self refreshVisibleCellAppearance];
}

- (HQIndexBannerSubview *)dequeueReusableCell {
  HQIndexBannerSubview *cell = [_reusableCells lastObject];
  if (cell) {
    [_reusableCells removeLastObject];
  }

  return cell;
}

- (void)scrollToPage:(NSUInteger)pageNumber {
  if (pageNumber < _pageCount) {

    [self stopTimer];

    if (self.isCarousel) {

      self.page = pageNumber + self.orginPageCount;
      [NSObject cancelPreviousPerformRequestsWithTarget:self
                                               selector:@selector(startTimer)
                                                 object:nil];
      [self performSelector:@selector(startTimer)
                 withObject:nil
                 afterDelay:0.5];

    } else {
      self.page = pageNumber;
    }

    switch (self.orientation) {
    case HQFlowViewOrientationHorizontal:
      [_scrollView setContentOffset:CGPointMake(_pageSize.width * self.page, 0)
                           animated:YES];
      break;
    case HQFlowViewOrientationVertical:
      [_scrollView setContentOffset:CGPointMake(0, _pageSize.height * self.page)
                           animated:YES];
      break;
    }
    [self setPagesAtContentOffset:_scrollView.contentOffset];
    [self refreshVisibleCellAppearance];
  }
}

#pragma mark -
#pragma mark hitTest

- (UIView *)hitTest:(CGPoint)point withEvent:(UIEvent *)event {
  if ([self pointInside:point withEvent:event]) {
    CGPoint newPoint = CGPointZero;
    newPoint.x =
        point.x - _scrollView.frame.origin.x + _scrollView.contentOffset.x;
    newPoint.y =
        point.y - _scrollView.frame.origin.y + _scrollView.contentOffset.y;
    if ([_scrollView pointInside:newPoint withEvent:event]) {
      return [_scrollView hitTest:newPoint withEvent:event];
    }

    return _scrollView;
  }

  return nil;
}

#pragma mark -
#pragma mark UIScrollView Delegate

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {

  if (self.orginPageCount == 0) {
    return;
  }

  NSInteger pageIndex;

  switch (self.orientation) {
  case HQFlowViewOrientationHorizontal:
    pageIndex = (int)round(_scrollView.contentOffset.x / _pageSize.width) %
                self.orginPageCount;
    break;
  case HQFlowViewOrientationVertical:
    pageIndex = (int)round(_scrollView.contentOffset.y / _pageSize.height) %
                self.orginPageCount;
    break;
  default:
    break;
  }
  if (_currentPageIndex != pageIndex) {
    if (self.delegate && [self.delegate respondsToSelector:@selector
                                        (willScrollToPage:inFlowView:)]) {
      [self.delegate willScrollToPage:pageIndex inFlowView:self];
    }
  }
  if (self.isCarousel) {

    if (self.orginPageCount > 1) {
      switch (self.orientation) {
      case HQFlowViewOrientationHorizontal: {
        if (scrollView.contentOffset.x / _pageSize.width >=
            2 * self.orginPageCount) {

          [scrollView
              setContentOffset:CGPointMake(
                                   _pageSize.width * self.orginPageCount, 0)
                      animated:NO];

          self.page = self.orginPageCount;
        }

        if (scrollView.contentOffset.x / _pageSize.width <=
            self.orginPageCount - 1) {
          [scrollView
              setContentOffset:CGPointMake((2 * self.orginPageCount - 1) *
                                               _pageSize.width,
                                           0)
                      animated:NO];

          self.page = 2 * self.orginPageCount;
        }

      } break;
      case HQFlowViewOrientationVertical: {
        if (scrollView.contentOffset.y / _pageSize.height >=
            2 * self.orginPageCount) {

          [scrollView setContentOffset:CGPointMake(0, _pageSize.height *
                                                          self.orginPageCount)
                              animated:NO];

          self.page = self.orginPageCount;
        }

        if (scrollView.contentOffset.y / _pageSize.height <=
            self.orginPageCount - 1) {
          [scrollView
              setContentOffset:CGPointMake(0, (2 * self.orginPageCount - 1) *
                                                  _pageSize.height)
                      animated:NO];
          self.page = 2 * self.orginPageCount;
        }

      } break;
      default:
        break;
      }

    } else {

      pageIndex = 0;
    }
  }

  [self setPagesAtContentOffset:scrollView.contentOffset];
  [self refreshVisibleCellAppearance];

  if (self.pageControl &&
      [self.pageControl respondsToSelector:@selector(setCurrentPage:)]) {

    [self.pageControl setCurrentPage:pageIndex];
  }

  if (_delegate &&
      [_delegate respondsToSelector:@selector(didScrollToPage:inFlowView:)] &&
      _currentPageIndex != pageIndex && pageIndex >= 0) {
    [_delegate didScrollToPage:pageIndex inFlowView:self];
  }

  _currentPageIndex = pageIndex;
}

#pragma mark--将要开始拖拽
- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView {
  [self stopTimer];
}

#pragma mark--结束拖拽
- (void)scrollViewDidEndDragging:(UIScrollView *)scrollView
                  willDecelerate:(BOOL)decelerate {
  [self startTimer];
}

#pragma mark--将要结束拖拽
- (void)scrollViewWillEndDragging:(UIScrollView *)scrollView
                     withVelocity:(CGPoint)velocity
              targetContentOffset:(inout CGPoint *)targetContentOffset {

  if (self.orginPageCount > 1 && self.isOpenAutoScroll && self.isCarousel) {

    switch (self.orientation) {
    case HQFlowViewOrientationHorizontal: {
      if (self.page == floor(_scrollView.contentOffset.x / _pageSize.width)) {

        self.page = floor(_scrollView.contentOffset.x / _pageSize.width) + 1;

      } else {

        self.page = floor(_scrollView.contentOffset.x / _pageSize.width);
      }
    } break;
    case HQFlowViewOrientationVertical: {
      if (self.page == floor(_scrollView.contentOffset.y / _pageSize.height)) {

        self.page = floor(_scrollView.contentOffset.y / _pageSize.height) + 1;

      } else {

        self.page = floor(_scrollView.contentOffset.y / _pageSize.height);
      }
    } break;
    default:
      break;
    }
  }
}

- (void)singleCellTapAction:(NSInteger)selectTag
                   withCell:(HQIndexBannerSubview *)cell {

  if (self.delegate && [self.delegate respondsToSelector:@selector
                                      (didSelectCell:withSubViewIndex:)]) {

    [self.delegate didSelectCell:cell withSubViewIndex:selectTag];
  }
}

- (void)willMoveToSuperview:(UIView *)newSuperview {
  if (!newSuperview) {
    [self stopTimer];
  }
}

- (void)dealloc {
  _scrollView.delegate = nil;
}

@end
