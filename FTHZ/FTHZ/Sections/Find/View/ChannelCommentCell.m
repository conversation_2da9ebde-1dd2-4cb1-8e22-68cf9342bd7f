#import "ChannelCommentCell.h"
#import "CurrencyRootVC.h"
#import "FTHZMusicPlayer.h"
#import "FTHZMusicPlayerView.h"
#import "HZPhotoBrowser.h"
#import "HZPhotoBrowserConfig.h"
#import "HZPhotoGroup.h"
#import "LGAudioKit.h"
#import "UIImageView+AnimationCompletion.h"
#import <AVFoundation/AVFoundation.h>
#import <FLAnimatedImage/FLAnimatedImageView.h>

@interface ChannelCommentCell () <HZPhotoBrowserDelegate, LGAudioPlayerDelegate>
@property(nonatomic, strong) UIImageView *iconImage;
@property(nonatomic, strong) UILabel *nameLabel;
@property(nonatomic, strong) UILabel *timeLabel;
@property(nonatomic, strong) UILabel *toTimeLabel;
@property(nonatomic, strong) UILabel *contentTextLabel;
@property(nonatomic, strong) UIImageView *likeIV;
@property(nonatomic, strong) FlatButton *likeButton;
@property(nonatomic, strong) UIView *photoBGView;
@property(nonatomic, strong) FLAnimatedImageView *photoView;
@property(nonatomic, strong) FlatButton *playerBt;
@property(nonatomic, strong) UILabel *secLabel;
@property(nonatomic, strong) UIImageView *playAnimationIV;
@property(nonatomic, strong) ChannelCommentsReplyResult *replyModel;
@property(nonatomic, strong) UILabel *likeNumLabel;
@property(nonatomic, strong) UIView *toBGView;
@property(nonatomic, strong) UIImageView *toAimage;
@property(nonatomic, strong) UIImageView *toBimage;
@property(nonatomic, strong) UILabel *toAnameLabel;
@property(nonatomic, strong) UIImageView *withIcon;
@property(nonatomic, strong) UILabel *toBnameLabel;
@property(nonatomic, strong) UILabel *commtntTitleLabel;
@property(nonatomic, copy) NSString *commentUserID;
@property(nonatomic, copy) NSString *commentID;
@property(nonatomic, strong) dispatch_source_t countdownTimer;
@property(nonatomic, assign) int remainingSeconds;
@property(nonatomic, assign) BOOL isPlaying;
@property(nonatomic, assign) NSTimeInterval playStartTime;
@property(nonatomic, strong) UIImageView *voiceIcon;

@end

@implementation ChannelCommentCell
- (instancetype)initWithStyle:(UITableViewCellStyle)style
              reuseIdentifier:(NSString *)reuseIdentifier {
  self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
  if (self) {
    UILongPressGestureRecognizer *longpress =
        [[UILongPressGestureRecognizer alloc]
            initWithTarget:self
                    action:@selector(longPressed:)];
    [self.contentView addGestureRecognizer:longpress];
    [self loadCellView];
    [self setSelectionStyle:UITableViewCellSelectionStyleNone];
  }
  return self;
}

- (void)loadCellView {
  if (!_iconImage) {
    _iconImage = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, 30, 30)];
  }
  UIBezierPath *maskPath =
      [UIBezierPath bezierPathWithRoundedRect:_iconImage.bounds
                            byRoundingCorners:UIRectCornerAllCorners
                                  cornerRadii:_iconImage.bounds.size];
  CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
  maskLayer.frame = _iconImage.bounds;
  maskLayer.path = maskPath.CGPath;
  _iconImage.layer.mask = maskLayer;
  _iconImage.userInteractionEnabled = YES;
  [_iconImage addGestureRecognizer:[[UITapGestureRecognizer alloc]
                                       initWithTarget:self
                                               action:@selector(tapAction:)]];

  if (!_nameLabel) {
    _nameLabel = [[UILabel alloc] init];
  }
  [_nameLabel setTextColor:KColor_HighBlue];
  [_nameLabel setLineBreakMode:NSLineBreakByWordWrapping];
  [_nameLabel setNumberOfLines:0];
  _nameLabel.font = SourceHanSerifBoldFont(14 * kWidthFactor);

  _timeLabel = [UILabel new];
  _timeLabel.textColor = KColor_replyDateGray;
  _timeLabel.font = SourceHanSerifRegularFont(12 * kWidthFactor);

  _likeIV = [UIImageView new];

  _likeButton = [FlatButton buttonWithType:UIButtonTypeCustom];
  _likeButton.frame = CGRectMake(0, 0, 16, 16);
  [_likeButton addTarget:self
                  action:@selector(likeAction)
        forControlEvents:UIControlEventTouchUpInside];

  _likeNumLabel = [UILabel new];
  _likeNumLabel.font = SourceHanSerifRegularFont(12 * kWidthFactor);
  _likeNumLabel.textColor = KColor_replyDateGray;
  _likeNumLabel.textAlignment = NSTextAlignmentCenter;

  if (!_contentTextLabel) {
    _contentTextLabel = [[UILabel alloc] init];
  }
  _contentTextLabel.font = SourceHanSerifRegularFont(14 * kWidthFactor);
  [_contentTextLabel setTextColor:KColor_HighBlack];
  [_contentTextLabel setNumberOfLines:0];
  _contentTextLabel.lineBreakMode = NSLineBreakByCharWrapping;

  _photoBGView = [UIView new];

  if (!_photoView) {
    _photoView = [[FLAnimatedImageView alloc] init];
    _photoView.userInteractionEnabled = YES;
  }
  _photoView.contentMode = UIViewContentModeScaleAspectFill;
  _photoView.layer.cornerRadius = 8.0 * kWidthFactor;
  _photoView.clipsToBounds = YES;
  UITapGestureRecognizer *tap =
      [[UITapGestureRecognizer alloc] initWithTarget:self
                                              action:@selector(tap:)];
  [_photoView addGestureRecognizer:tap];

  [_photoBGView addSubview:_photoView];
  @weakify(self);
  [_photoView mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.edges.equalTo(self.photoBGView);
  }];

  _playerBt = [FlatButton buttonWithType:UIButtonTypeCustom];
  _playerBt.backgroundColor = KColor_HighBlack;
  _playerBt.layer.cornerRadius = 4.0 * kWidthFactor;
  _playerBt.layer.masksToBounds = YES;
  [_playerBt addTarget:self
                action:@selector(playAciton)
      forControlEvents:UIControlEventTouchUpInside];

  self.voiceIcon = [[UIImageView alloc] init];
  self.voiceIcon.image = [UIImage imageNamed:@"voicew"];
  [self.playerBt addSubview:self.voiceIcon];

  [self.voiceIcon mas_makeConstraints:^(MASConstraintMaker *make) {
    make.width.height.mas_equalTo(16 * kWidthFactor);
    make.left.equalTo(self.playerBt).offset(12 * kWidthFactor);
    make.centerY.equalTo(self.playerBt);
  }];

  _secLabel = [UILabel new];
  _secLabel.textColor = KColor_White;
  _secLabel.font = SourceHanSerifRegularFont(12);
  _secLabel.backgroundColor = [UIColor whiteColor];
  _secLabel.layer.cornerRadius = 10;
  _secLabel.clipsToBounds = YES;
  _secLabel.textAlignment = NSTextAlignmentCenter;
  _secLabel.textColor = KColor_HighBlack;
  _secLabel.layer.borderWidth = 1;
  _secLabel.layer.borderColor =
      [KColor_HighBlack colorWithAlphaComponent:0.1].CGColor;
  [_playerBt addSubview:_secLabel];
  [_secLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.centerY.equalTo(self.voiceIcon);
    make.right.equalTo(self.playerBt).offset(-12);
    make.width.mas_equalTo(30);
    make.height.mas_equalTo(20);
  }];

  if (!_toBGView) {
    _toBGView = [[UIView alloc] init];
  }
  _toBGView.backgroundColor = KColor_heighGray2;
  _toBGView.layer.cornerRadius = 8.0 * kWidthFactor;
  _toBGView.clipsToBounds = YES;

  if (!_toAimage) {
    _toAimage = [[UIImageView alloc] initWithFrame:CGRectMake(37, 10, 15, 15)];
  }
  UIBezierPath *maskPath2 =
      [UIBezierPath bezierPathWithRoundedRect:_toAimage.bounds
                            byRoundingCorners:UIRectCornerAllCorners
                                  cornerRadii:_toAimage.bounds.size];
  CAShapeLayer *maskLayer2 = [[CAShapeLayer alloc] init];
  maskLayer2.frame = _toAimage.bounds;
  maskLayer2.path = maskPath2.CGPath;
  _toAimage.layer.mask = maskLayer2;
  _toAimage.userInteractionEnabled = YES;

  if (!_toAnameLabel) {
    _toAnameLabel = [[UILabel alloc] init];
  }
  [_toAnameLabel setTextColor:KColor_HighBlue];
  _toAnameLabel.font = SourceHanSerifSemiBoldFont(12 * kWidthFactor);

  if (!_withIcon) {
    _withIcon =
        [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"回复"]];
  }

  if (!_toBimage) {
    _toBimage = [[UIImageView alloc] initWithFrame:CGRectMake(37, 10, 15, 15)];
  }
  UIBezierPath *maskPath3 =
      [UIBezierPath bezierPathWithRoundedRect:_toBimage.bounds
                            byRoundingCorners:UIRectCornerAllCorners
                                  cornerRadii:_toBimage.bounds.size];
  CAShapeLayer *maskLayer3 = [[CAShapeLayer alloc] init];
  maskLayer3.frame = _toBimage.bounds;
  maskLayer3.path = maskPath3.CGPath;
  _toBimage.layer.mask = maskLayer3;
  _toBimage.userInteractionEnabled = YES;
  if (!_toBnameLabel) {
    _toBnameLabel = [[UILabel alloc] init];
  }
  [_toBnameLabel setTextColor:KColor_HighBlue];
  _toBnameLabel.font = SourceHanSerifSemiBoldFont(12 * kWidthFactor);

  if (!_commtntTitleLabel) {
    _commtntTitleLabel = [[UILabel alloc] init];
  }
  _commtntTitleLabel.numberOfLines = 0;
  [_commtntTitleLabel setTextColor:KColor_HighBlack];
  _commtntTitleLabel.lineBreakMode = NSLineBreakByCharWrapping;
  _commtntTitleLabel.font = [UIFont systemFontOfSize:12];

  if (!_AnameBtn) {
    _AnameBtn = [UIButton buttonWithType:UIButtonTypeCustom];
  }
  if (!_BnameBtn) {
    _BnameBtn = [UIButton buttonWithType:UIButtonTypeCustom];
  }
}

#pragma mark - public

- (void)setReplyModel:(ChannelCommentsReplyResult *)replyModel
            withIndex:(int)index {
  [self setSelectionStyle:UITableViewCellSelectionStyleNone];
  _replyModel = replyModel;
  self.commentUserID = replyModel.userid;
  self.commentID = replyModel.commentid;

  [self clearAllSubviews];
  _likeNumLabel.hidden = NO;

  @weakify(self);
  [_iconImage
      sd_setImageWithURL:[NemoUtil getUrlWithUserSmallIcon:replyModel.avatar]
        placeholderImage:KImage_name(@"empty")];
  [self.contentView addSubview:_iconImage];
  [_iconImage mas_remakeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.equalTo(self.contentView).offset(24 * kWidthFactor);
    make.top.equalTo(self.contentView).offset(8 * kWidthFactor);
    make.size.mas_equalTo(CGSizeMake(30 * kWidthFactor, 30 * kWidthFactor));
  }];

  _nameLabel.text = replyModel.nickName;
  [self.contentView addSubview:self.nameLabel];
  [self.nameLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.equalTo(self.iconImage.mas_right).offset(8 * kWidthFactor);
    make.centerY.equalTo(self.iconImage);
    make.size.mas_lessThanOrEqualTo(CGSizeMake(kMainWidth * 0.55, 15));
  }];

  _timeLabel.text = [NemoUtil
      distanceTimeWithBeforeTime:[replyModel.create_time integerValue]];
  [self.contentView addSubview:_timeLabel];
  [_timeLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.equalTo(self.nameLabel.mas_right).offset(8 * kWidthFactor);
    make.centerY.equalTo(self.iconImage);
  }];

  [self.contentView addSubview:_likeIV];
  [_likeIV mas_remakeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.right.equalTo(self.contentView).offset(-40 * kWidthFactor);
    make.height.mas_equalTo(16 * kWidthFactor);
    make.width.mas_equalTo(16 * kWidthFactor);
    make.centerY.equalTo(self.nameLabel);
  }];

  [self.contentView addSubview:_likeButton];
  [_likeButton mas_remakeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.center.equalTo(self.likeIV);
    make.width.height.mas_equalTo(25);
  }];

  int likeRes = [replyModel.like_status intValue];
  _likeIV.image = (likeRes > 0) ? [UIImage imageNamed:@"like"]
                                : [UIImage imageNamed:@"Unlike"];

  [self.contentView addSubview:_likeNumLabel];
  [_likeNumLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.centerY.equalTo(self.likeIV);
    make.left.equalTo(self.likeIV.mas_right).offset(5 * kWidthFactor);
  }];

  int likeNum = [replyModel.likeNum intValue];
  if (likeNum > 999) {
    _likeNumLabel.text = @"999+";
  } else if (likeNum > 0) {
    _likeNumLabel.text = [NSString stringWithFormat:@"%d", likeNum];
  } else {
    _likeNumLabel.hidden = YES;
  }

  UIView *bottomView = _iconImage;
  CGFloat bottomOffset = 0;

  if (replyModel.comment.length > 0) {
    _contentTextLabel.text = replyModel.comment;
    _contentTextLabel.hidden = NO;
    CGFloat lineSpacing = 5 * kWidthFactor;
    NSMutableParagraphStyle *paragraphStyle =
        [[NSMutableParagraphStyle alloc] init];
    paragraphStyle.lineSpacing = lineSpacing;
    paragraphStyle.lineBreakMode = _contentTextLabel.lineBreakMode;
    paragraphStyle.alignment = _contentTextLabel.textAlignment;

    NSMutableAttributedString *attributedString =
        [[NSMutableAttributedString alloc]
            initWithString:_contentTextLabel.text];
    [attributedString
        addAttribute:NSParagraphStyleAttributeName
               value:paragraphStyle
               range:NSMakeRange(0, [_contentTextLabel.text length])];
    [attributedString
        addAttribute:NSFontAttributeName
               value:_contentTextLabel.font
               range:NSMakeRange(0, [_contentTextLabel.text length])];
    _contentTextLabel.attributedText = attributedString;
    [self.contentView addSubview:_contentTextLabel];
    [_contentTextLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
      @strongify(self);
      make.left.equalTo(self.nameLabel.mas_left);
      make.top.equalTo(bottomView.mas_bottom).offset(5 * kWidthFactor);
      make.right.equalTo(self.contentView).offset(-32 * kWidthFactor);
    }];
    bottomView = _contentTextLabel;
    bottomOffset = 10 * kWidthFactor;
  } else {
    _contentTextLabel.hidden = YES;
  }

  if ([NemoUtil getHZPhotoStringCount:replyModel.images] > 0) {
    [self.contentView addSubview:self.photoBGView];
    NSArray *tempCountArr =
        [replyModel.images componentsSeparatedByString:@","];
    [_photoView
        sd_setImageWithURL:[NemoUtil
                               getUrlWithUserPictaure:tempCountArr.firstObject]
          placeholderImage:KImage_name(@"empty")];
    [self.photoBGView mas_remakeConstraints:^(MASConstraintMaker *make) {
      @strongify(self);
      make.left.equalTo(self.nameLabel.mas_left);
      make.top.equalTo(bottomView.mas_bottom).offset(10 * kWidthFactor);
      make.width.height.mas_equalTo(112 * kWidthFactor);
    }];
    bottomView = self.photoBGView;
    bottomOffset = 10 * kWidthFactor;
  }

  int sec = [replyModel.voice_time intValue];
  if (replyModel.voice.length > 0 && sec > 0) {
    [[FTHZMusicPlayer shared] stop];
    self.secLabel.text = [NSString stringWithFormat:@"%ds", sec];
    int voiceWidth = (kMainWidth - 64 - 48 - 40) / 60;
    [self.contentView addSubview:_playerBt];
    [_playerBt mas_makeConstraints:^(MASConstraintMaker *make) {
      @strongify(self);
      make.left.equalTo(self.nameLabel.mas_left);
      make.width.mas_equalTo(voiceWidth * sec + 80);
      make.height.mas_equalTo(32 * kWidthFactor);
      make.top.equalTo(bottomView.mas_bottom).offset(10 * kWidthFactor);
    }];

    if ([replyModel.commentid intValue] == [LGAudioPlayer sharePlayer].index) {
      self.isPlaying = YES;
      [self startAnimating];

      NSTimeInterval currentTime = [[NSDate date] timeIntervalSince1970];
      NSTimeInterval elapsedTime = currentTime - self.playStartTime;
      int totalDuration = [replyModel.voice_time intValue];
      int remainingTime = totalDuration - (int)elapsedTime;

      if (remainingTime > 0) {
        [self startCountdown:remainingTime];
      } else {
        [self stopAnimating];
        [self stopCountdown];
        self.isPlaying = NO;
        self.voiceIcon.alpha = 1.0;
      }
    } else {
      self.isPlaying = NO;
      [self stopAnimating];
      [self stopCountdown];
      self.voiceIcon.alpha = 1.0;
    }

    bottomView = _playerBt;
    bottomOffset = 10 * kWidthFactor;
  }

  [bottomView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.bottom.equalTo(self.contentView).offset(-4 * kWidthFactor);
  }];
}

- (void)prepareForReuse {
  [super prepareForReuse];
  if (!self.isPlaying) {
    [self stopAnimating];
    [self stopCountdown];
    self.voiceIcon.alpha = 1.0;
  } else {
    [self startAnimating];
    NSTimeInterval currentTime = [[NSDate date] timeIntervalSince1970];
    NSTimeInterval elapsedTime = currentTime - self.playStartTime;
    int totalDuration = [self.replyModel.voice_time intValue];
    int remainingTime = totalDuration - (int)elapsedTime;

    if (remainingTime > 0) {
      [self startCountdown:remainingTime];
    } else {
      [self stopAnimating];
      [self stopCountdown];
      self.isPlaying = NO;
      self.voiceIcon.alpha = 1.0;
    }
  }
}

- (void)setToReplyModel:(ChannelCommentsToReplyResult *)toReplyModel {
  _toReplyModel = toReplyModel;

  self.commentUserID = toReplyModel.userid;
  self.commentID = toReplyModel.commentid;

  [self clearAllSubviews];

  if (!_toBGView) {
    _toBGView = [[UIView alloc] init];
    _toBGView.backgroundColor = KColor_heighGray2;
    _toBGView.layer.cornerRadius = 8.0 * kWidthFactor;
    _toBGView.clipsToBounds = YES;
  }
  [self.contentView addSubview:_toBGView];

  [_toBGView mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.contentView).offset(23 * kWidthFactor);
    make.top.equalTo(self.contentView).offset(5 * kWidthFactor);
    make.right.equalTo(self.contentView).offset(-24 * kWidthFactor);
  }];

  _toAimage = [self createRoundImageView];
  [_toAimage
      sd_setImageWithURL:[NemoUtil getUrlWithUserSmallIcon:toReplyModel.avatar]
        placeholderImage:KImage_name(@"empty")];
  [_toBGView addSubview:_toAimage];

  _toBimage = [self createRoundImageView];
  [_toBimage
      sd_setImageWithURL:[NemoUtil
                             getUrlWithUserSmallIcon:toReplyModel.to_avatar]
        placeholderImage:KImage_name(@"empty")];
  [_toBGView addSubview:_toBimage];

  [_toAimage mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(_toBGView).offset(14 * kWidthFactor);
    make.top.equalTo(_toBGView).offset(12 * kWidthFactor);
    make.size.mas_equalTo(CGSizeMake(20 * kWidthFactor, 20 * kWidthFactor));
  }];

  if (!_toAnameLabel) {
    _toAnameLabel = [[UILabel alloc] init];
    _toAnameLabel.font = SourceHanSerifSemiBoldFont(12 * kWidthFactor);
    [_toAnameLabel setTextColor:KColor_HighBlue];
  }
  _toAnameLabel.text = toReplyModel.nickName;
  [_toBGView addSubview:_toAnameLabel];

  [_toAnameLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(_toAimage.mas_right).offset(5 * kWidthFactor);
    make.centerY.equalTo(_toAimage);
  }];

  if (!_withIcon) {
    _withIcon =
        [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"回复"]];
  }
  [_toBGView addSubview:_withIcon];
  [_withIcon mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(_toAnameLabel.mas_right).offset(4 * kWidthFactor);
    make.centerY.equalTo(_toAimage);
  }];

  [_toBimage mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(_withIcon.mas_right).offset(6 * kWidthFactor);
    make.centerY.equalTo(_toAimage);
    make.size.mas_equalTo(CGSizeMake(20 * kWidthFactor, 20 * kWidthFactor));
  }];

  if (!_toBnameLabel) {
    _toBnameLabel = [[UILabel alloc] init];
    _toBnameLabel.font = SourceHanSerifSemiBoldFont(12 * kWidthFactor);
    [_toBnameLabel setTextColor:KColor_HighBlue];
  }
  _toBnameLabel.text = toReplyModel.to_nickName;
  [_toBGView addSubview:_toBnameLabel];
  [_toBnameLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(_toBimage.mas_right).offset(6 * kWidthFactor);
    make.centerY.equalTo(_toBimage);
  }];

  if (!_toTimeLabel) {
    _toTimeLabel = [[UILabel alloc] init];
    _toTimeLabel.textColor = KColor_replyDateGray;
    _toTimeLabel.font = SourceHanSerifRegularFont(10 * kWidthFactor);
  }
  _toTimeLabel.text = [NemoUtil
      distanceTimeWithBeforeTime:[toReplyModel.create_time integerValue]];
  [_toBGView addSubview:_toTimeLabel];
  [_toTimeLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(_toBnameLabel.mas_right).offset(8 * kWidthFactor);
    make.centerY.equalTo(_toBimage);
  }];

  if (!_commtntTitleLabel) {
    _commtntTitleLabel = [[UILabel alloc] init];
    _commtntTitleLabel.numberOfLines = 0;
    _commtntTitleLabel.lineBreakMode = NSLineBreakByCharWrapping;
    _commtntTitleLabel.font = SourceHanSerifRegularFont(12 * kWidthFactor);
    [_commtntTitleLabel
        setTextColor:[KColor_HighBlack colorWithAlphaComponent:0.65]];
  }
  _commtntTitleLabel.text = toReplyModel.comment;

  CGFloat lineSpacing = 4 * kWidthFactor;
  NSMutableParagraphStyle *paragraphStyle =
      [[NSMutableParagraphStyle alloc] init];
  paragraphStyle.lineSpacing = lineSpacing;
  paragraphStyle.lineBreakMode = _commtntTitleLabel.lineBreakMode;
  paragraphStyle.alignment = _commtntTitleLabel.textAlignment;

  NSMutableAttributedString *attributedString =
      [[NSMutableAttributedString alloc]
          initWithString:_commtntTitleLabel.text];
  [attributedString
      addAttribute:NSParagraphStyleAttributeName
             value:paragraphStyle
             range:NSMakeRange(0, [_commtntTitleLabel.text length])];
  [attributedString
      addAttribute:NSFontAttributeName
             value:_commtntTitleLabel.font
             range:NSMakeRange(0, [_commtntTitleLabel.text length])];
  _commtntTitleLabel.attributedText = attributedString;

  [_toBGView addSubview:_commtntTitleLabel];
  [_commtntTitleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(_toAnameLabel.mas_left);
    make.top.equalTo(_toAimage.mas_bottom).offset(5 * kWidthFactor);
    make.right.equalTo(_toBGView).offset(-14 * kWidthFactor);
    make.bottom.equalTo(_toBGView).offset(-10 * kWidthFactor);
  }];

  if (!_AnameBtn) {
    _AnameBtn = [UIButton buttonWithType:UIButtonTypeCustom];
  }
  if (!_BnameBtn) {
    _BnameBtn = [UIButton buttonWithType:UIButtonTypeCustom];
  }
  [_toBGView addSubview:_AnameBtn];
  [_toBGView addSubview:_BnameBtn];

  [_AnameBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.edges.equalTo(_toAimage);
  }];

  [_BnameBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.edges.equalTo(_toBimage);
  }];

  [_toBGView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.bottom.equalTo(self.contentView).offset(-5 * kWidthFactor);
  }];
}

- (void)clearAllSubviews {
  [self.toBGView removeFromSuperview];
  [self.toAimage removeFromSuperview];
  [self.toBimage removeFromSuperview];
  [self.toAnameLabel removeFromSuperview];
  [self.withIcon removeFromSuperview];
  [self.toBnameLabel removeFromSuperview];
  [self.commtntTitleLabel removeFromSuperview];
  [self.AnameBtn removeFromSuperview];
  [self.BnameBtn removeFromSuperview];
  [self.toTimeLabel removeFromSuperview];

  [self.iconImage removeFromSuperview];
  [self.nameLabel removeFromSuperview];
  [self.contentTextLabel removeFromSuperview];
  [self.timeLabel removeFromSuperview];
  [self.likeIV removeFromSuperview];
  [self.likeButton removeFromSuperview];
  [self.likeNumLabel removeFromSuperview];
  [self.photoBGView removeFromSuperview];
  [self.playerBt removeFromSuperview];
  _likeNumLabel.hidden = NO;
}

- (UIImageView *)createRoundImageView {
  UIImageView *imageView = [[UIImageView alloc] init];
  imageView.contentMode = UIViewContentModeScaleAspectFill;
  imageView.clipsToBounds = YES;
  imageView.layer.cornerRadius = 10 * kWidthFactor;
  imageView.userInteractionEnabled = YES;
  return imageView;
}

- (void)likeAnimation:(int)raiseLevel {
  int level = 0;
  if (raiseLevel > 0) {
    level = raiseLevel;
  } else {
    int likeNum = [self.replyModel.likeNum intValue];
    if (likeNum > 0 && likeNum < 4) {
      level = 2;
    } else if (likeNum > 3 && likeNum < 7) {
      level = 4;
    } else if (likeNum > 6) {
      level = 6;
    }
  }

  NSMutableArray *images = [NSMutableArray new];
  for (int i = 0 + (level - 1) * 25; i < 21 + (level - 1) * 25; i++) {
    NSString *imgN = [NSString stringWithFormat:@"heart_%d_000%d", level, i];
    if (i < 10) {
      imgN = [NSString stringWithFormat:@"heart_%d_0000%d", level, i];
    } else if (i > 99) {
      imgN = [NSString stringWithFormat:@"heart_%d_00%d", level, i];
    }
    UIImage *img = [UIImage imageNamed:imgN];
    if (img) {
      [images addObject:img];
    }
  }
  _likeIV.animationImages = images;
  _likeIV.animationDuration = 2.0;
  _likeIV.animationRepeatCount = 1;
  @weakify(self);
  [_likeIV startAnimatingWithCompletionBlock:^(BOOL success) {
    @strongify(self);
    self.likeIV.image = images.lastObject;
  }];
}

#pragma mark - Action

- (void)tap:(UIGestureRecognizer *)gesture {
  HZPhotoBrowser *browser = [[HZPhotoBrowser alloc] init];
  browser.isFullWidthForLandScape = YES;
  browser.isNeedLandscape = YES;
  browser.sourceImagesContainerView = self.photoBGView;
  browser.imageCount = 1;
  browser.delegate = self;
  [browser show];
}

- (void)likeAction {
  @weakify(self);
  [FTHZChannelLikeModel postLike:self.discussID
      comment:self.commentID
      success:^(NSDictionary *resultObject) {
        @strongify(self);
        FTHZChannelLikeModel *member =
            [FTHZChannelLikeModel mj_objectWithKeyValues:resultObject];
        if ([member.success boolValue]) {
          if ([self.replyModel.like_status intValue] > 0) {
            self.replyModel.like_status = @"0";
            self.likeIV.image = [UIImage imageNamed:@"Unlike"];
            int likeNum = [self.replyModel.likeNum intValue];
            if (likeNum > 0) {
              likeNum--;
              self.replyModel.likeNum =
                  [NSString stringWithFormat:@"%d", likeNum];

              if (likeNum > 999) {
                self.likeNumLabel.text = @"999+";
              } else if (likeNum > 0) {
                self.likeNumLabel.text =
                    [NSString stringWithFormat:@"%d", likeNum];
                self.likeNumLabel.hidden = NO;
              } else {
                self.likeNumLabel.hidden = YES;
              }
            }
          } else {
            self.replyModel.like_status = @"1";
            self.likeIV.image = [UIImage imageNamed:@"like"];
            int likeNum = [self.replyModel.likeNum intValue];
            likeNum++;
            self.replyModel.likeNum =
                [NSString stringWithFormat:@"%d", likeNum];

            if (likeNum > 999) {
              self.likeNumLabel.text = @"999+";
            } else {
              self.likeNumLabel.text =
                  [NSString stringWithFormat:@"%d", likeNum];
              self.likeNumLabel.hidden = NO;
            }
          }
        } else {
          [self.delegate likeAction:member.msg];
        }
      }
      failure:^(NSError *requestErr) {
        [self.delegate likeAction:@"数据有误,请检查网络后重试"];
      }];
}

- (void)tapAction:(UITapGestureRecognizer *)sender {
  if (self.tapIconAction) {
    self.tapIconAction(self.commentUserID);
  }
}

- (void)playAciton {
  NSString *playUrl = self.replyModel.voice;
  if (playUrl) {
    self.playStartTime = [[NSDate date] timeIntervalSince1970];
    [VoiceFileManager
        checkVoiceFile:playUrl
              complete:^(NSString *_Nonnull filePath) {
                LGAudioPlayer *player = [LGAudioPlayer sharePlayer];
                player.delegate = self;
                [player playAudioWithURLString:filePath
                                       atIndex:[self.replyModel
                                                       .commentid intValue]];
              }];
  }
}

#pragma mark - lgaudioplayer delegate
- (void)startAnimating {
  [self stopAnimating];

  [UIView animateWithDuration:1.0
                        delay:0
                      options:UIViewAnimationOptionRepeat |
                              UIViewAnimationOptionAutoreverse |
                              UIViewAnimationOptionAllowUserInteraction
                   animations:^{
                     self.voiceIcon.alpha = 0.2;
                   }
                   completion:nil];
}

- (void)stopAnimating {
  [self.voiceIcon.layer removeAllAnimations];
  self.voiceIcon.alpha = 1.0;
}

- (void)startCountdown:(int)seconds {
  [self stopCountdown];

  self.remainingSeconds = seconds;
  self.secLabel.text =
      [NSString stringWithFormat:@"%ds", self.remainingSeconds];

  self.countdownTimer = dispatch_source_create(DISPATCH_SOURCE_TYPE_TIMER, 0, 0,
                                               dispatch_get_main_queue());
  dispatch_source_set_timer(self.countdownTimer, DISPATCH_TIME_NOW,
                            1.0 * NSEC_PER_SEC, 0);

  @weakify(self);
  dispatch_source_set_event_handler(self.countdownTimer, ^{
    @strongify(self);
    if (!self.isPlaying) {
      [self stopCountdown];
      return;
    }

    self.remainingSeconds--;
    if (self.remainingSeconds >= 0) {
      self.secLabel.text =
          [NSString stringWithFormat:@"%ds", self.remainingSeconds];
    } else {
      [self stopCountdown];
    }
  });

  dispatch_resume(self.countdownTimer);
}

- (void)stopCountdown {
  if (self.countdownTimer) {
    dispatch_source_cancel(self.countdownTimer);
    self.countdownTimer = nil;
  }
  if (self.replyModel.voice_time) {
    self.secLabel.text = [NSString
        stringWithFormat:@"%ds", [self.replyModel.voice_time intValue]];
  }
}

- (void)audioPlayerStateDidChanged:(LGAudioPlayerState)audioPlayerState
                          forIndex:(NSUInteger)index {
  if (index != [self.replyModel.commentid intValue]) {
    self.isPlaying = NO;
    [self stopAnimating];
    [self stopCountdown];
    return;
  }

  if (audioPlayerState != LGAudioPlayerStatePlaying) {
    self.isPlaying = NO;
    [self stopAnimating];
    [self stopCountdown];
    self.voiceIcon.alpha = 1.0;
  } else {
    self.isPlaying = YES;
    self.playStartTime = [[NSDate date] timeIntervalSince1970];
    [self startAnimating];
    [self startCountdown:[self.replyModel.voice_time intValue]];
  }
}
#pragma mark - Delete

- (void)longPressed:(UILongPressGestureRecognizer *)gesture {
  switch (gesture.state) {
  case UIGestureRecognizerStateBegan: {
    [self deleteCommentIfNeeded];
  } break;

  default:
    break;
  }
}

- (void)deleteCommentIfNeeded {
  NSString *currentUserID = CurrentUser.userid;
  if (!currentUserID || ![self.commentUserID isEqualToString:currentUserID]) {
    return;
  }
  if (![self becomeFirstResponder]) {
    return;
  }
  [self layoutIfNeeded];
  @weakify(self);
  FTHZPopMenuView *menu = [[FTHZPopMenuView alloc] init];
  [menu setMenuItems:@[
    [FTHZPopMenuItem
        menuWithTitle:@"删除评论"
               action:^{
                 FTHZAlertDialogController *dialog =
                     [[FTHZAlertDialogController alloc]
                         initWithTitle:@"提示"
                               message:@"确认删除该评论吗？删除后将不再显示。"];
                 [dialog
                     addAction:
                         [FTHZAlertDialogAction
                             actionWithTitle:@"取消删除"
                                      action:nil
                                       style:
                                           FTHZAlertDialogActionStyleDefault]];
                 [dialog
                     addAction:
                         [FTHZAlertDialogAction
                             actionWithTitle:@"确认删除"
                                      action:^{
                                        @strongify(self);
                                        [self deleteThis];
                                      }
                                       style:
                                           FTHZAlertDialogActionStyleHighlighted]];
                 [[UIViewController topViewController]
                     presentViewController:dialog
                                  animated:YES
                                completion:nil];
               }]
  ]];
  UIView *textView =
      self.contentTextLabel.superview ? self.contentTextLabel : self.toBGView;
  [menu showInTargetRect:textView.frame
                  inView:self.contentView
          dismissHandler:^{
            @strongify(self);
            [self resignFirstResponder];
          }];
}

- (void)deleteThis {
  [FTHZDeleteChannelModel delChannelComment:self.discussID
      commentid:self.commentID
      authorid:self.commentUserID
      success:^(NSDictionary *resultObject) {
        if (self.delegate) {
          [self.delegate deleteComplete:YES];
        }
      }
      failure:^(NSError *requestErr) {
        [self.delegate deleteComplete:NO];
      }];

  [self resignFirstResponder];
}

- (BOOL)canBecomeFirstResponder {
  return YES;
}

- (BOOL)canResignFirstResponder {
  return YES;
}

- (BOOL)becomeFirstResponder {
  _contentTextLabel.backgroundColor =
      [KColor_HighBlack colorWithAlphaComponent:0.1];
  return [super becomeFirstResponder];
}

- (BOOL)resignFirstResponder {
  _contentTextLabel.backgroundColor = [UIColor clearColor];
  return [super resignFirstResponder];
}

#pragma mark - photobrowser代理方法
- (UIImage *)photoBrowser:(HZPhotoBrowser *)browser
    placeholderImageForIndex:(NSInteger)index {
  return _photoView.image;
}

- (NSURL *)photoBrowser:(HZPhotoBrowser *)browser
    highQualityImageURLForIndex:(NSInteger)index {
  NSString *urlStr =
      [self.replyModel.images stringByReplacingOccurrencesOfString:@"thumbnail"
                                                        withString:@"bmiddle"];
  return [NSURL URLWithString:urlStr];
}

- (void)dealloc {
  [self stopCountdown];
}

@end
