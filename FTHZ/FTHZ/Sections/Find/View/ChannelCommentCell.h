#import "FTHZChannelReplyModel.h"
#import "FTHZStraitModel.h"
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN
@protocol ChannelCommentCellDelgate <NSObject>

- (void)deleteComplete:(BOOL)success;

- (void)likeAction:(NSString *)errMsg;
@end

@interface ChannelCommentCell : UITableViewCell

@property(nonatomic, weak) id<ChannelCommentCellDelgate> delegate;
@property(nonatomic, copy) NSString *discussID;

@property(nonatomic, strong) UIButton *AnameBtn;
@property(nonatomic, strong) UIButton *BnameBtn;

@property(nonatomic, strong) ChannelCommentsToReplyResult *toReplyModel;

@property(nonatomic, copy) NSString *contentAuthorID;
@property(nonatomic, copy) NSString *contentID;
@property(nonatomic, copy) void (^tapIconAction)(NSString *uid);

- (void)setReplyModel:(ChannelCommentsReplyResult *)replyModel
            withIndex:(int)index;
@end

NS_ASSUME_NONNULL_END
