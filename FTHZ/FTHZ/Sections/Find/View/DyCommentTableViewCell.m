#import "DyCommentTableViewCell.h"
#import "CurrencyRootVC.h"
#import "FTHZNetworkTask+Feed.h"
#import "_2hz-Swift.h"

@interface DyCommentTableViewCell ()
@property(nonatomic, strong) UIImageView *iconImage;
@property(nonatomic, strong) UILabel *nameLabel;
@property(nonatomic, strong) UILabel *dateLabel;
@property(nonatomic, strong) UILabel *contentTextLabel;

@property(nonatomic, strong) UIView *toBGView;
@property(nonatomic, strong) UIImageView *toAimage;
@property(nonatomic, strong) UIImageView *toBimage;
@property(nonatomic, strong) UILabel *toAnameLabel;
@property(nonatomic, strong) UIImageView *withIcon;
@property(nonatomic, strong) UILabel *toBnameLabel;
@property(nonatomic, strong) UILabel *commtntTitleLabel;
@property(nonatomic, copy) NSString *commentUserID;
@property(nonatomic, copy) NSString *commentID;
@property(nonatomic, strong) UILabel *toDateLabel;
@end

@implementation DyCommentTableViewCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style
              reuseIdentifier:(NSString *)reuseIdentifier {
  self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
  if (self) {
    UILongPressGestureRecognizer *longpress =
        [[UILongPressGestureRecognizer alloc]
            initWithTarget:self
                    action:@selector(longPressed:)];
    [self.contentView addGestureRecognizer:longpress];
    [self loadCellView];
    [self setSelectionStyle:UITableViewCellSelectionStyleNone];
  }
  return self;
}

- (void)loadCellView {
  if (!_iconImage) {
    _iconImage = [[UIImageView alloc]
        initWithFrame:CGRectMake(37 * kWidthFactor, 10 * kWidthFactor,
                                 30 * kWidthFactor, 30 * kWidthFactor)];
  }
  UIBezierPath *maskPath =
      [UIBezierPath bezierPathWithRoundedRect:_iconImage.bounds
                            byRoundingCorners:UIRectCornerAllCorners
                                  cornerRadii:_iconImage.bounds.size];
  CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
  maskLayer.frame = _iconImage.bounds;
  maskLayer.path = maskPath.CGPath;
  _iconImage.layer.mask = maskLayer;
  _iconImage.userInteractionEnabled = YES;
  [_iconImage addGestureRecognizer:[[UITapGestureRecognizer alloc]
                                       initWithTarget:self
                                               action:@selector(tapAction:)]];

  if (!_nameLabel) {
    _nameLabel = [[UILabel alloc] init];
  }
  [_nameLabel setTextColor:KColor_HighBlue];
  [_nameLabel setLineBreakMode:NSLineBreakByWordWrapping];
  [_nameLabel setNumberOfLines:0];
  _nameLabel.font = SourceHanSerifBoldFont(14 * kWidthFactor);

  if (!_dateLabel) {
    _dateLabel = [[UILabel alloc] init];
  }
  [_dateLabel setTextColor:KColor_replyDateGray];
  _dateLabel.font = SourceHanSerifRegularFont(12 * kWidthFactor);

  if (!_contentTextLabel) {
    _contentTextLabel = [[UILabel alloc] init];
    _contentTextLabel.numberOfLines = 0;
  }
  _contentTextLabel.font = SourceHanSerifMediumFont(14 * kWidthFactor);
  [_contentTextLabel setTextColor:KColor_titleDarkGray];
  [_contentTextLabel setNumberOfLines:0];
  _contentTextLabel.lineBreakMode = NSLineBreakByCharWrapping;

  if (!_toBGView) {
    _toBGView = [[UIView alloc] init];
  }
  _toBGView.backgroundColor = KColor_heighGray2;
  _toBGView.layer.cornerRadius = 8 * kWidthFactor;
  _toBGView.layer.masksToBounds = YES;

  if (!_toAimage) {
    _toAimage = [[UIImageView alloc]
        initWithFrame:CGRectMake(0, 0, 20 * kWidthFactor, 20 * kWidthFactor)];
  }
  UIBezierPath *maskPath2 =
      [UIBezierPath bezierPathWithRoundedRect:_toAimage.bounds
                            byRoundingCorners:UIRectCornerAllCorners
                                  cornerRadii:_toAimage.bounds.size];
  CAShapeLayer *maskLayer2 = [[CAShapeLayer alloc] init];
  maskLayer2.frame = _toAimage.bounds;
  maskLayer2.path = maskPath2.CGPath;
  _toAimage.layer.mask = maskLayer2;
  _toAimage.userInteractionEnabled = YES;

  if (!_toAnameLabel) {
    _toAnameLabel = [[UILabel alloc] init];
  }
  [_toAnameLabel setTextColor:KColor_HighBlue];
  _toAnameLabel.font = SourceHanSerifSemiBoldFont(12 * kWidthFactor);

  if (!_withIcon) {
    _withIcon =
        [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"回复"]];
  }

  if (!_toBimage) {
    _toBimage = [[UIImageView alloc]
        initWithFrame:CGRectMake(0, 0, 20 * kWidthFactor, 20 * kWidthFactor)];
  }
  UIBezierPath *maskPath3 =
      [UIBezierPath bezierPathWithRoundedRect:_toBimage.bounds
                            byRoundingCorners:UIRectCornerAllCorners
                                  cornerRadii:_toBimage.bounds.size];
  CAShapeLayer *maskLayer3 = [[CAShapeLayer alloc] init];
  maskLayer3.frame = _toBimage.bounds;
  maskLayer3.path = maskPath3.CGPath;
  _toBimage.layer.mask = maskLayer3;
  _toBimage.userInteractionEnabled = YES;
  if (!_toBnameLabel) {
    _toBnameLabel = [[UILabel alloc] init];
  }
  [_toBnameLabel setTextColor:KColor_HighBlue];
  _toBnameLabel.font = SourceHanSerifSemiBoldFont(12 * kWidthFactor);

  if (!_toDateLabel) {
    _toDateLabel = [[UILabel alloc] init];
  }
  [_toDateLabel setTextColor:KColor_replyDateGray];
  _toDateLabel.font = SourceHanSerifRegularFont(10 * kWidthFactor);

  if (!_commtntTitleLabel) {
    _commtntTitleLabel = [[UILabel alloc] init];
    _commtntTitleLabel.numberOfLines = 0;
  }

  [_commtntTitleLabel setTextColor:KColor_replyGray];
  _commtntTitleLabel.lineBreakMode = NSLineBreakByCharWrapping;
  _commtntTitleLabel.font = SourceHanSerifRegularFont(12 * kWidthFactor);

  if (!_AnameBtn) {
    _AnameBtn = [UIButton buttonWithType:UIButtonTypeCustom];
  }
  if (!_BnameBtn) {
    _BnameBtn = [UIButton buttonWithType:UIButtonTypeCustom];
  }
}

- (void)setCommentUser:(AffairCommentsReplyResult *)Comment {
  _CommentUser = Comment;
  self.commentUserID = Comment.userid;
  self.commentID = Comment.commentid;

  [self.contentView addSubview:_iconImage];
  [self.contentView addSubview:self.nameLabel];
  [self.contentView addSubview:self.dateLabel];
  [self.contentView addSubview:_contentTextLabel];

  [self.toBGView removeFromSuperview];
  [self.toAimage removeFromSuperview];
  [self.toBimage removeFromSuperview];
  [self.toAnameLabel removeFromSuperview];
  [self.withIcon removeFromSuperview];
  [self.toBnameLabel removeFromSuperview];
  [self.commtntTitleLabel removeFromSuperview];
  [self.AnameBtn removeFromSuperview];
  [self.BnameBtn removeFromSuperview];
  [self.toDateLabel removeFromSuperview];

  @weakify(self);
  [_iconImage
      sd_setImageWithURL:[NemoUtil getUrlWithUserSmallIcon:_CommentUser.avatar]
        placeholderImage:KImage_name(@"empty")];
  [self.contentView addSubview:_iconImage];
  [_iconImage mas_remakeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.equalTo(self.contentView).offset(24 * kWidthFactor);
    make.top.equalTo(self.contentView);
    make.size.mas_equalTo(CGSizeMake(30 * kWidthFactor, 30 * kWidthFactor));
  }];

  _nameLabel.text = _CommentUser.nickName;
  _nameLabel.textColor = KColor_HighBlue;
  if (_CommentUser.colorFont != nil) {
    [_nameLabel applyGradientWithType:_CommentUser.colorFont.type
                           expireTime:_CommentUser.colorFont.expire_time];
  }
  [self.contentView addSubview:self.nameLabel];
  [self.nameLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.equalTo(self.iconImage.mas_right).offset(8 * kWidthFactor);
    make.centerY.equalTo(self.iconImage);
    make.right.lessThanOrEqualTo(self.dateLabel.mas_left)
        .offset(-8 * kWidthFactor); // 添加此行
  }];

  _dateLabel.text = [NemoUtil
      distanceTimeWithBeforeTime:[_CommentUser.create_time integerValue]];
  [self.contentView addSubview:self.dateLabel];
  [self.dateLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.equalTo(self.nameLabel.mas_right).offset(8 * kWidthFactor);
    make.centerY.equalTo(self.iconImage);
  }];

  //    CGSize tp = [NemoUtil calculateLabelHeightByText:[UIFont
  //    systemFontOfSize:13*kWidthFactor]
  //    width:366*kWidthFactor heightMax:kMainHeight*10
  //    content:_CommentUser.comment];
  _contentTextLabel.text = _CommentUser.comment;

  // 添加行间距设置代码
  NSMutableParagraphStyle *paragraphStyle =
      [[NSMutableParagraphStyle alloc] init];
  paragraphStyle.lineSpacing = 5 * kWidthFactor; // 设置行间距为5*kWidthFactor
  paragraphStyle.lineBreakMode = _contentTextLabel.lineBreakMode;
  paragraphStyle.alignment = _contentTextLabel.textAlignment;

  NSMutableAttributedString *attributedString =
      [[NSMutableAttributedString alloc] initWithString:_contentTextLabel.text];
  [attributedString
      addAttribute:NSParagraphStyleAttributeName
             value:paragraphStyle
             range:NSMakeRange(0, [_contentTextLabel.text length])];
  // 保留原来的字体
  [attributedString
      addAttribute:NSFontAttributeName
             value:_contentTextLabel.font
             range:NSMakeRange(0, [_contentTextLabel.text length])];
  _contentTextLabel.attributedText = attributedString;
  [self.contentView addSubview:_contentTextLabel];
  [_contentTextLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.equalTo(self.nameLabel.mas_left);
    make.top.equalTo(self.nameLabel.mas_bottom).offset(12 * kWidthFactor);
    make.right.equalTo(self.contentView.mas_right).offset(-24 * kWidthFactor);
    make.bottom.equalTo(self.contentView.mas_bottom);
  }];
}

- (void)loadToCommentUser:(AffairCommentsToReplyResult *)toCom {
  _toComentRs = toCom;
  [self.iconImage removeFromSuperview];
  [self.nameLabel removeFromSuperview];
  [self.contentTextLabel removeFromSuperview];
  [self.dateLabel removeFromSuperview];
  self.commentUserID = toCom.userid;
  self.commentID = toCom.commentid;

  @weakify(self);
  [_toAimage sd_setImageWithURL:[NemoUtil getUrlWithUserSmallIcon:toCom.avatar]
               placeholderImage:KImage_name(@"empty")];
  [self.toBGView addSubview:_toAimage];
  [_toAimage mas_remakeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.equalTo(self.toBGView).offset(10 * kWidthFactor);
    make.top.equalTo(self.toBGView).offset(12 * kWidthFactor);
    make.size.mas_equalTo(CGSizeMake(20 * kWidthFactor, 20 * kWidthFactor));
  }];

  _toAnameLabel.text = toCom.nickName;
  _toAnameLabel.textColor = KColor_HighBlue;

  if (toCom.colorFont != nil) {
    [_toAnameLabel applyGradientWithType:toCom.colorFont.type
                              expireTime:toCom.colorFont.expire_time];
  }
  [self.toBGView addSubview:_toAnameLabel];
  [_toAnameLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.equalTo(self.toAimage.mas_right).offset(8 * kWidthFactor);
    make.centerY.equalTo(self.toAimage);
  }];

  [self.toBGView addSubview:_withIcon];
  [_withIcon mas_remakeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.equalTo(self.toAnameLabel.mas_right).offset(4 * kWidthFactor);
    make.centerY.equalTo(self.toAimage);
  }];

  [_toBimage
      sd_setImageWithURL:[NemoUtil getUrlWithUserSmallIcon:toCom.to_avatar]
        placeholderImage:KImage_name(@"empty")];
  [self.toBGView addSubview:_toBimage];
  [_toBimage mas_remakeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.equalTo(self.withIcon.mas_right).offset(7 * kWidthFactor);
    make.centerY.equalTo(self.toAimage);
    make.size.mas_equalTo(CGSizeMake(20 * kWidthFactor, 20 * kWidthFactor));
  }];

  _toBnameLabel.text = toCom.to_nickName;
  [self.toBGView addSubview:_toBnameLabel];
  [_toBnameLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.equalTo(self.toBimage.mas_right).offset(8 * kWidthFactor);
    make.centerY.equalTo(self.toBimage);
  }];

  _toDateLabel.text =
      [NemoUtil distanceTimeWithBeforeTime:[toCom.create_time integerValue]];
  [self.toBGView addSubview:_toDateLabel];
  [_toDateLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.equalTo(self.toBnameLabel.mas_right).offset(8 * kWidthFactor);
    make.centerY.equalTo(self.toBimage);
  }];

  [_toBnameLabel mas_updateConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.right.lessThanOrEqualTo(self.toDateLabel.mas_left)
        .offset(-8 * kWidthFactor);
  }];

  _commtntTitleLabel.text = toCom.comment;
  // 添加行间距设置代码
  NSMutableParagraphStyle *paragraphStyle =
      [[NSMutableParagraphStyle alloc] init];
  paragraphStyle.lineSpacing =
      4 * kWidthFactor; // 可以设置稍小一点的行间距，因为字体较小
  paragraphStyle.lineBreakMode = _commtntTitleLabel.lineBreakMode;
  paragraphStyle.alignment = _commtntTitleLabel.textAlignment;

  NSMutableAttributedString *attributedString =
      [[NSMutableAttributedString alloc]
          initWithString:_commtntTitleLabel.text];
  [attributedString
      addAttribute:NSParagraphStyleAttributeName
             value:paragraphStyle
             range:NSMakeRange(0, [_commtntTitleLabel.text length])];
  // 保留原来的字体
  [attributedString
      addAttribute:NSFontAttributeName
             value:_commtntTitleLabel.font
             range:NSMakeRange(0, [_commtntTitleLabel.text length])];
  _commtntTitleLabel.attributedText = attributedString;

  [self.toBGView addSubview:_commtntTitleLabel];
  [_commtntTitleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.equalTo(self.toAnameLabel.mas_left);
    make.top.equalTo(self.toAimage.mas_bottom).offset(7 * kWidthFactor);
    make.right.equalTo(self.toBGView).offset(-13 * kWidthFactor);
    make.bottom.equalTo(self.toBGView.mas_bottom).offset(-10 * kWidthFactor);
  }];

  [self.toBGView addSubview:self.AnameBtn];
  [self.AnameBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.edges.equalTo(self.toAimage);
  }];

  [self.toBGView addSubview:self.BnameBtn];
  [self.BnameBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.edges.equalTo(self.toBimage);
  }];

  [self.contentView addSubview:self.toBGView];
  [self.toBGView mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.contentView).offset(24 * kWidthFactor);
    make.top.equalTo(self.contentView).offset(12 * kWidthFactor);
    make.width.mas_equalTo(366 * kWidthFactor);
    make.bottom.equalTo(self.contentView);
  }];
}

#pragma mark - Action
- (void)tapAction:(UITapGestureRecognizer *)sender {
  if (self.tapIconAction) {
    self.tapIconAction(self.CommentUser.userid);
  }
}

#pragma mark - Delete

- (void)longPressed:(UILongPressGestureRecognizer *)gesture {
  switch (gesture.state) {
  case UIGestureRecognizerStateBegan: {
    [self deleteCommentIfNeeded];
  } break;

  default:
    break;
  }
}

- (void)deleteCommentIfNeeded {
  NSString *currentUserID = CurrentUser.userid;
  if (!currentUserID) {
    return;
  }
  if (![self becomeFirstResponder]) {
    return;
  }
  [self layoutIfNeeded];
  @weakify(self);
  FTHZPopMenuView *menu = [[FTHZPopMenuView alloc] init];
  FTHZPopMenuItem *copyMenuItem =
      [FTHZPopMenuItem menuWithTitle:@"复制"
                              action:^{
                                @strongify(self);
                                if (self.CommentUser) {
                                  [UIPasteboard generalPasteboard].string =
                                      self.CommentUser.comment ?: @"";
                                } else if (self.toComentRs) {
                                  [UIPasteboard generalPasteboard].string =
                                      self.toComentRs.comment ?: @"";
                                }
                              }];

  FTHZPopMenuItem *delMenuItem = [FTHZPopMenuItem
      menuWithTitle:@"删除评论"
             action:^{
               FTHZAlertDialogController *dialog =
                   [[FTHZAlertDialogController alloc]
                       initWithTitle:@"提示"
                             message:@"确认删除该评论吗？删除后将不再显示。"];
               [dialog
                   addAction:
                       [FTHZAlertDialogAction
                           actionWithTitle:@"取消删除"
                                    action:nil
                                     style:FTHZAlertDialogActionStyleDefault]];
               [dialog
                   addAction:
                       [FTHZAlertDialogAction
                           actionWithTitle:@"确认删除"
                                    action:^{
                                      @strongify(self);
                                      [self deleteThis];
                                    }
                                     style:
                                         FTHZAlertDialogActionStyleHighlighted]];
               [[UIViewController topViewController]
                   presentViewController:dialog
                                animated:YES
                              completion:nil];
             }];
  if ([self.commentUserID isEqualToString:currentUserID] ||
      [self.contentAuthorID isEqualToString:currentUserID]) {
    [menu setMenuItems:@[ copyMenuItem, delMenuItem ]];

  } else {
    [menu setMenuItems:@[ copyMenuItem ]];
  }

  UIView *textView = self.contentTextLabel.superview ? self.contentTextLabel
                                                     : self.commtntTitleLabel;
  [menu showInTargetRect:textView.frame
                  inView:self.contentView
          dismissHandler:^{
            @strongify(self);
            [self resignFirstResponder];
          }];
}

- (void)deleteThis {
  [[FTHZNetworkTask deleteCommentWithFeedID:self.contentID
                                  commentID:self.commentID
                                   authorID:self.contentAuthorID]
      requestWithCompletion:^(BOOL isSuccess, NSURLResponse *_Nullable response,
                              id _Nullable model, NSError *_Nullable anError) {
        if (isSuccess) {
          [NOTIFICENTER postNotificationName:TacthBtnofCommentForSeaReload
                                      object:nil];
          if (self.deletedComment) {
            self.deletedComment(self);
          }
        } else {
          CurrencyRootVC *vc =
              (CurrencyRootVC *)[UIViewController topViewController];
          if ([vc isKindOfClass:[CurrencyRootVC class]]) {
            [vc showToast:anError.localizedDescription];
          }
        }
      }];
  [self resignFirstResponder];
}

- (BOOL)canBecomeFirstResponder {
  return YES;
}

- (BOOL)canResignFirstResponder {
  return YES;
}

- (BOOL)becomeFirstResponder {
  _contentTextLabel.backgroundColor =
      [KColor_HighBlack colorWithAlphaComponent:0.1];
  return [super becomeFirstResponder];
}

- (BOOL)resignFirstResponder {
  _contentTextLabel.backgroundColor = [UIColor clearColor];
  return [super resignFirstResponder];
}

@end
