#import "FTHZMusicPlayer.h"
#import "MusicInfoModel.h"
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface FTHZPlayerButton : UIButton
@property (nonatomic, assign) BOOL isLightStyle; // 新增属性

@end

@interface FTHZMusicPlayerBaseView : UIControl

@property(nonatomic, strong, readonly) FTHZPlayerButton *actionButton;

- (void)setMusicCover:(NSURL *_Nullable)cover
                 name:(NSString *_Nullable)name
               artist:(NSString *_Nullable)artist
             platform:(MusicSource)source;
@end

@interface FTHZMusicPlayerEditView : FTHZMusicPlayerBaseView

- (instancetype)init;

- (void)toAddMusicStyle:(BOOL)animated;

- (void)toDisplayMusicStyle:(BOOL)animated;

@end

@interface FTHZMusicPlayerView : FTHZMusicPlayerBaseView

- (BOOL)isPlaying;

- (BOOL)isPlayerLoadedCurrentMusic;

- (void)setMusicInfo:(MusicInfoData *_Nullable)info
                uuid:(NSString *_Nullable)uuid
           autoTrace:(BOOL)autoTrace;
- (void)applyLightStyle;
@end

@interface FTHZMusicPlayerBanner : FTHZMusicPlayerBaseView

@property(nonatomic, assign) BOOL canShow;

@end

@interface FTHZFloatMusicPlayer : UIControl // 悬浮播放器

@property(nonatomic, assign) BOOL canShow;

@end

NS_ASSUME_NONNULL_END
