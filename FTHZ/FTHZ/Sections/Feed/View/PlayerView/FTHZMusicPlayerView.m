#import "FTHZMusicPlayerView.h"
#import "FTHZMusicPlayer.h"
#import "UIView+Frame.h"
#import "_2hz-Swift.h"

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSI<PERSON><PERSON>, FTHZPlayerActionButtonType) {
  FTHZPlayerActionButtonTypeAdd,
  FTHZPlayerActionButtonTypeCancel,
  FTHZPlayerActionButtonTypePlay,
  FTHZPlayerActionButtonTypePause,
};

@implementation FTHZPlayerButton

- (instancetype)initWithFrame:(CGRect)frame {
  self = [super initWithFrame:frame];
  if (self) {
    self.imageView.contentMode = UIViewContentModeScaleAspectFit;
    self.contentHorizontalAlignment = UIControlContentHorizontalAlignmentCenter;
    self.contentVerticalAlignment = UIControlContentVerticalAlignmentCenter;
  }
  return self;
}

- (BOOL)pointInside:(CGPoint)point withEvent:(UIEvent *_Nullable)event {
  CGRect bounds = self.bounds;
  CGFloat widthDelta = MAX(24, 0);
  CGFloat heightDelta = MAX(24, 0);
  bounds = CGRectInset(bounds, -0.5 * widthDelta, -0.5 * heightDelta);
  return CGRectContainsPoint(bounds, point);
}

- (void)changeButtonType:(FTHZPlayerActionButtonType)type {
  UIImage *image;
  if (self.isLightStyle) {
    switch (type) {
    case FTHZPlayerActionButtonTypeAdd:
      image = KImage_name(@"music_add_black");
      break;
    case FTHZPlayerActionButtonTypePlay:
      image = KImage_name(@"music_play_black");
      break;
    case FTHZPlayerActionButtonTypePause:
      image = KImage_name(@"music_pause_black");
      break;
    case FTHZPlayerActionButtonTypeCancel:
      image = KImage_name(@"music_close_black");
      break;
    }
  } else {
    switch (type) {
    case FTHZPlayerActionButtonTypeAdd:
      image = KImage_name(@"music_add");
      break;
    case FTHZPlayerActionButtonTypePlay:
      image = KImage_name(@"music_play");
      break;
    case FTHZPlayerActionButtonTypePause:
      image = KImage_name(@"music_pause");
      break;
    case FTHZPlayerActionButtonTypeCancel:
      image = KImage_name(@"music_close");
      break;
    }
  }

  self.imageView.frame = CGRectMake(0, 0, 30 * kMainTemp, 30 * kMainTemp);
  self.imageView.center =
      CGPointMake(self.bounds.size.width / 2, self.bounds.size.height / 2);
  self.imageView.contentMode = UIViewContentModeScaleAspectFit;

  [self setImage:image forState:UIControlStateNormal];
}

@end

@interface FTHZCustomPlayButton : UIButton
@end

@implementation FTHZCustomPlayButton

- (void)drawRect:(CGRect)rect {
  [super drawRect:rect];

  CGContextRef context = UIGraphicsGetCurrentContext();
  CGContextSetLineWidth(context, 2.5);
  CGContextSetStrokeColorWithColor(context, [UIColor blackColor].CGColor);
  CGContextSetFillColorWithColor(context, [UIColor blackColor].CGColor);

  if (self.selected) {
    CGFloat pauseWidth = 4.0;
    CGFloat spacing = 6.0;
    CGFloat height = rect.size.height * 0.6;
    CGFloat yOffset = (rect.size.height - height) / 2;
    CGFloat xOffset = (rect.size.width - (2 * pauseWidth + spacing)) / 2;

    CGContextFillRect(context,
                      CGRectMake(xOffset, yOffset, pauseWidth, height));

    CGContextFillRect(context, CGRectMake(xOffset + pauseWidth + spacing,
                                          yOffset, pauseWidth, height));
  } else {
    CGFloat height = rect.size.height * 0.7;
    CGFloat width = height * 0.866;
    CGFloat yOffset = (rect.size.height - height) / 2;
    CGFloat xOffset = (rect.size.width - width) / 2 + 2;

    CGContextBeginPath(context);
    CGContextMoveToPoint(context, xOffset, yOffset);
    CGContextAddLineToPoint(context, xOffset + width, yOffset + height / 2);
    CGContextAddLineToPoint(context, xOffset, yOffset + height);
    CGContextClosePath(context);
    CGContextFillPath(context);
  }
}

- (void)setSelected:(BOOL)selected {
  [super setSelected:selected];
  [self setNeedsDisplay];
}

@end

@interface FTHZGramophoneRecordView : UIImageView

@end

@implementation FTHZGramophoneRecordView

- (instancetype)initWithFrame:(CGRect)frame {
  self = [super initWithFrame:frame];
  if (self) {
    self.contentMode = UIViewContentModeScaleAspectFit;
    self.clipsToBounds = YES;
    CGFloat fixedSize = 14 * kMainTemp;
    self.frame = CGRectMake(self.frame.origin.x, self.frame.origin.y, fixedSize,
                            fixedSize);

    self.image = [UIImage imageNamed:@"分享音乐"];
  }
  return self;
}

- (void)layoutSubviews {
  [super layoutSubviews];
}

@end

@interface FTHZMusicPlayerMusicInfoView : UIView

@property(nonatomic, strong, readonly) UILabel *titleLabel;

@property(nonatomic, strong, readonly) UILabel *artistLabel;

@end

@implementation FTHZMusicPlayerMusicInfoView

- (instancetype)initWithFrame:(CGRect)frame {
  self = [super initWithFrame:frame];
  if (self) {
    _titleLabel = [[UILabel alloc] init];
    _titleLabel.textColor = [UIColor whiteColor];
    _titleLabel.font = SourceHanSerifRegularFont(12);
    _titleLabel.numberOfLines = 1;
    _titleLabel.lineBreakMode = NSLineBreakByTruncatingTail;

    _artistLabel = [[UILabel alloc] init];
    _artistLabel.textColor = [[UIColor whiteColor] colorWithAlphaComponent:0.7];
    _artistLabel.font = SourceHanSerifRegularFont(10);
    _artistLabel.numberOfLines = 1;
    _artistLabel.lineBreakMode = NSLineBreakByTruncatingTail;

    [self addSubview:_titleLabel];
    [self addSubview:_artistLabel];
  }
  return self;
}

- (void)layoutSubviews {
  [super layoutSubviews];

  CGFloat labelHeight = 20;
  CGFloat spacing = 2;

  CGFloat startY = (self.bounds.size.height - (labelHeight * 2 + spacing)) / 2;

  self.titleLabel.frame =
      CGRectMake(0, startY, self.bounds.size.width, labelHeight);

  self.artistLabel.frame = CGRectMake(0, startY + labelHeight + spacing,
                                      self.bounds.size.width, labelHeight);
}

- (void)setMusicName:(NSString *_Nullable)musicName
              artist:(NSString *_Nullable)artist {
  self.titleLabel.text = musicName;
  self.artistLabel.text = artist;
  [self setNeedsLayout];
}

@end

@interface FTHZMusicPlayerBaseView ()

@property(nonatomic, strong, readonly)
    FTHZGramophoneRecordView *gramophoneRecordView;

@property(nonatomic, strong, nullable)
    FTHZMusicPlayerMusicInfoView *contentView;

@property(nonatomic, strong, readonly) UIImageView *logoView;

@end

@implementation FTHZMusicPlayerBaseView

- (instancetype)initWithFrame:(CGRect)frame {
  self = [super initWithFrame:frame];
  if (self) {
    _gramophoneRecordView = [[FTHZGramophoneRecordView alloc]
        initWithFrame:CGRectMake(0, 0, 16, 16)];
    _contentView = [[FTHZMusicPlayerMusicInfoView alloc] init];
    _contentView.userInteractionEnabled = NO;
    _logoView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, 30, 32)];
    _logoView.contentMode = UIViewContentModeScaleAspectFit;
    _actionButton = [[FTHZPlayerButton alloc] init];
    [self addSubview:_logoView];
    [self addSubview:_gramophoneRecordView];
    [self addSubview:_contentView];
    [self addSubview:_actionButton];

    self.backgroundColor = KColor_HighBlack;
    self.layer.cornerRadius = 12 * kMainTemp;
    self.layer.masksToBounds = YES;
    self.layer.borderWidth = 1.0;
    self.layer.borderColor = KColor_HighBlack.CGColor;
  }
  return self;
}

- (void)layoutSubviews {
  [super layoutSubviews];
  const CGRect bounds = self.bounds;

  self.gramophoneRecordView.left = 0;
  self.gramophoneRecordView.centerY = CGRectGetMidY(bounds);

  CGFloat buttonSize = 60 * kMainTemp;
  self.actionButton.size = CGSizeMake(buttonSize, buttonSize);
  self.actionButton.centerX = self.gramophoneRecordView.centerX;
  self.actionButton.centerY = self.gramophoneRecordView.centerY;

  CGFloat logoWidth = 30;
  CGFloat logoRightMargin = 12;
  self.logoView.centerY = CGRectGetMidY(bounds);
  self.logoView.right = CGRectGetMaxX(bounds) - logoRightMargin;

  CGFloat contentRightMargin = 8;
  CGFloat contentMaxWidth = self.logoView.left - contentRightMargin -
                            (self.gramophoneRecordView.right + kDefaultMargin);

  self.contentView.height = CGRectGetHeight(bounds) - 8;
  self.contentView.centerY = CGRectGetMidY(bounds);
  self.contentView.left = self.gramophoneRecordView.right + kDefaultMargin;
  self.contentView.width = contentMaxWidth;
}

- (void)setMusicCover:(NSURL *_Nullable)cover
                 name:(NSString *_Nullable)name
               artist:(NSString *_Nullable)artist
             platform:(MusicSource)source {

  if (cover) {
    self.gramophoneRecordView.frame =
        CGRectMake(0, 0, 60 * kMainTemp, 60 * kMainTemp);
    self.gramophoneRecordView.contentMode = UIViewContentModeScaleAspectFit;
    [self.gramophoneRecordView sd_setImageWithURL:cover];
  } else {
    self.gramophoneRecordView.frame =
        CGRectMake(0, 0, 14 * kMainTemp, 14 * kMainTemp);
    self.gramophoneRecordView.contentMode = UIViewContentModeScaleAspectFit;
    self.gramophoneRecordView.image = [UIImage imageNamed:@"分享音乐"];
  }

  [(FTHZMusicPlayerMusicInfoView *)self.contentView setMusicName:name
                                                          artist:artist];

  NSDictionary *imageMap = @{
    @(MusicSource163Music) : @"MusicCloud",
    @(MusicSourceXiami) : @"Xiami",
    @(MusicSourceQQMusic) : @"QQMusic",
    @(MusicSourceCoolgo) : @"CoolDog",
    @(MusicSourceCoolMe) : @"CoolMe",
  };
  NSString *imageName = imageMap[@(source)];
  self.logoView.image = imageName ? KImage_name(imageName) : nil;
  self.logoView.alpha = 1.0;
}

@end

@interface FTHZMusicPlayerEditView ()

@property(nonatomic, strong, readonly) UILabel *tipLabel;

@property(nonatomic, assign) BOOL hasMusic;

@end

@implementation FTHZMusicPlayerEditView

- (instancetype)init {
  return [self initWithFrame:CGRectMake(0, 0, 228, 60)];
}

- (instancetype)initWithFrame:(CGRect)frame {
  self = [super initWithFrame:frame];
  if (self) {
    _hasMusic = NO;
    _tipLabel = [[UILabel alloc] init];
    _tipLabel.text = @"分享音乐";
    _tipLabel.textColor = [KColor_HighBlack colorWithAlphaComponent:0.54];
    _tipLabel.font = SourceHanSerifRegularFont(12);
    [_tipLabel sizeToFit];
    [self addSubview:_tipLabel];
    self.contentView.hidden = YES;
    self.layer.borderColor = [UIColor clearColor].CGColor;
  }
  return self;
}

- (void)layoutSubviews {
  [super layoutSubviews];
  const CGRect bounds = self.bounds;
  self.tipLabel.centerY = CGRectGetMidY(bounds);
  self.tipLabel.left = 24 * kMainTemp;
}

- (CGSize)intrinsicContentSize {
  if (self.hasMusic) {
    self.actionButton.hidden = NO;
    return CGSizeMake(317 * kMainTemp, 60 * kMainTemp);
  } else {
    self.actionButton.hidden = YES;
    return CGSizeMake(128 * kMainTemp, 60 * kMainTemp);
  }
}

- (void)toAddMusicStyle:(BOOL)animated {
  self.hasMusic = NO;
  self.backgroundColor = UIColor.whiteColor;
  self.tipLabel.hidden = NO;
  if (animated) {
    [UIView animateWithDuration:0.25
        animations:^{
          [self invalidateIntrinsicContentSize];
          [self setNeedsLayout];
          [self layoutIfNeeded];
          self.tipLabel.alpha = 1.0;
          self.contentView.alpha = 0.0;
          [self.actionButton changeButtonType:FTHZPlayerActionButtonTypeAdd];
        }
        completion:^(BOOL finished) {
          self.contentView.hidden = YES;
        }];
  } else {
    [self invalidateIntrinsicContentSize];
    [self setNeedsLayout];
    [self layoutIfNeeded];
    self.contentView.alpha = 0;
    self.tipLabel.alpha = 1;
    [self.actionButton changeButtonType:FTHZPlayerActionButtonTypeAdd];
    self.contentView.hidden = YES;
  }
}

- (void)toDisplayMusicStyle:(BOOL)animated {
  self.hasMusic = YES;
  self.backgroundColor = KColor_HighBlack;
  if (animated) {
    self.contentView.hidden = NO;
    [UIView animateWithDuration:0.25
        animations:^{
          [self invalidateIntrinsicContentSize];
          [self setNeedsLayout];
          [self layoutIfNeeded];
          self.tipLabel.alpha = 0.0;
          self.contentView.alpha = 1.0;
          [self.actionButton changeButtonType:FTHZPlayerActionButtonTypeCancel];
        }
        completion:^(BOOL finished) {
          self.tipLabel.hidden = YES;
        }];
  } else {
    [self invalidateIntrinsicContentSize];
    [self setNeedsLayout];
    [self layoutIfNeeded];
    self.tipLabel.alpha = 0.0;
    self.contentView.alpha = 1.0;
    self.contentView.hidden = NO;
    self.tipLabel.hidden = YES;
    [self.actionButton changeButtonType:FTHZPlayerActionButtonTypeCancel];
  }
}

@end

@interface FTHZMusicPlayerView ()

@property(nonatomic, strong, nullable) MusicInfoData *info;

@property(nonatomic, copy, nullable) NSString *uuid;

@end

@implementation FTHZMusicPlayerView

- (instancetype)initWithFrame:(CGRect)frame {
  self = [super initWithFrame:frame];
  if (self) {
    [self.actionButton changeButtonType:FTHZPlayerActionButtonTypePlay];
    [self.actionButton addTarget:self
                          action:@selector(actionButtonClicked:)
                forControlEvents:UIControlEventTouchUpInside];
    [self beginTrace];
  }
  return self;
}

- (void)stopTrace {
  [[FTHZMusicPlayer shared] removeObserver:self forKeyPath:@"isPlaying"];
}

- (void)beginTrace {
  [[FTHZMusicPlayer shared] addObserver:self
                             forKeyPath:@"isPlaying"
                                options:NSKeyValueObservingOptionInitial |
                                        NSKeyValueObservingOptionNew
                                context:nil];
}

- (void)observeValueForKeyPath:(NSString *_Nullable)keyPath
                      ofObject:(id _Nullable)object
                        change:
                            (NSDictionary<NSKeyValueChangeKey, id> *_Nullable)
                                change
                       context:(void *_Nullable)context {
  [self update];
}

- (void)update {
  if (!self.uuid ||
      ![[FTHZMusicPlayer shared].uuid isEqualToString:self.uuid]) {
    [self.actionButton changeButtonType:FTHZPlayerActionButtonTypePlay];
    return;
  }
  BOOL isPlaying = [FTHZMusicPlayer shared].isPlaying;
  [self.actionButton changeButtonType:isPlaying
                                          ? FTHZPlayerActionButtonTypePause
                                          : FTHZPlayerActionButtonTypePlay];
}

- (void)setMusicInfo:(MusicInfoData *_Nullable)info
                uuid:(NSString *_Nullable)uuid
           autoTrace:(BOOL)autoTrace {
  self.info = info;
  [self setMusicCover:info.imgUrl ? [NSURL URLWithString:info.imgUrl] : nil
                 name:info.musicName
               artist:info.artists
             platform:[info.from integerValue]];
  self.uuid = uuid;
  if (autoTrace) {
    [self update];
  }
}

- (void)applyLightStyle {
  self.backgroundColor = [UIColor whiteColor];
  self.layer.cornerRadius = 12 * kMainTemp;
  self.layer.masksToBounds = YES;
  self.layer.borderColor = KColor_White.CGColor;

  if ([self.contentView isKindOfClass:[FTHZMusicPlayerMusicInfoView class]]) {
    FTHZMusicPlayerMusicInfoView *infoView =
        (FTHZMusicPlayerMusicInfoView *)self.contentView;
    infoView.titleLabel.textColor = [UIColor blackColor];
    infoView.artistLabel.textColor =
        [[UIColor blackColor] colorWithAlphaComponent:0.7];
  }

  CGFloat buttonSize = 60 * kMainTemp;
  self.actionButton.size = CGSizeMake(buttonSize, buttonSize);
  self.actionButton.centerX = self.gramophoneRecordView.centerX;
  self.actionButton.centerY = self.gramophoneRecordView.centerY;

  self.actionButton.isLightStyle = YES;
  [self.actionButton changeButtonType:self.isPlaying
                                          ? FTHZPlayerActionButtonTypePause
                                          : FTHZPlayerActionButtonTypePlay];
}

- (void)actionButtonClicked:(BOOL)clicked {
  FTHZMusicPlayer *player = [FTHZMusicPlayer shared];
  if (player.isPlaying && self.uuid &&
      [player.uuid isEqualToString:self.uuid]) {
    [player stop];
  } else if (player.faAudioState == kFsAudioStreamPaused) {
    [player pause];
  } else {
    [player playMusicWithMusicInfo:self.info uuid:self.uuid];
  }
}

- (BOOL)isPlayerLoadedCurrentMusic {
  return self.uuid && [[FTHZMusicPlayer shared].uuid isEqualToString:self.uuid];
}

- (BOOL)isPlaying {
  FTHZMusicPlayer *player = [FTHZMusicPlayer shared];
  return player.isPlaying && [self isPlayerLoadedCurrentMusic];
}

- (void)dealloc {
  [self stopTrace];
}

@end

@interface FTHZMusicPlayerBanner ()

@end

@implementation FTHZMusicPlayerBanner

- (instancetype)initWithFrame:(CGRect)frame {
  self = [super initWithFrame:frame];
  if (self) {
    _canShow = YES;
    [self.actionButton setImage:KImage_name(@"music_close_2")
                       forState:UIControlStateNormal];
    [self.actionButton addTarget:self
                          action:@selector(closeButtonClicked:)
                forControlEvents:UIControlEventTouchUpInside];
    self.alpha = 0.0;
    [self beginTrace];
  }
  return self;
}

- (void)stopTrace {
  [[FTHZMusicPlayer shared] removeObserver:self forKeyPath:@"isPlaying"];
  [[FTHZMusicPlayer shared] removeObserver:self forKeyPath:@"info"];
}

- (void)beginTrace {
  [[FTHZMusicPlayer shared] addObserver:self
                             forKeyPath:@"isPlaying"
                                options:NSKeyValueObservingOptionInitial |
                                        NSKeyValueObservingOptionNew
                                context:nil];
  [[FTHZMusicPlayer shared] addObserver:self
                             forKeyPath:@"info"
                                options:NSKeyValueObservingOptionInitial |
                                        NSKeyValueObservingOptionNew
                                context:nil];
}

- (void)observeValueForKeyPath:(NSString *_Nullable)keyPath
                      ofObject:(id _Nullable)object
                        change:
                            (NSDictionary<NSKeyValueChangeKey, id> *_Nullable)
                                change
                       context:(void *_Nullable)context {
  if ([keyPath isEqualToString:@"isPlaying"]) {
    [self updateShowingBehavior];
    ;
  } else if ([keyPath isEqualToString:@"info"]) {
    [self updateMusicInfo];
  }
}

- (void)closeButtonClicked:(UIButton *)button {
  if ([FTHZMusicPlayer shared].isPlaying) {
    [[FTHZMusicPlayer shared] stop];
  }
}

- (void)setCanShow:(BOOL)canShow {
  if (canShow == _canShow) {
    return;
  }
  _canShow = canShow;
  [self updateShowingBehavior];
}

- (void)updateMusicInfo {
  MusicInfoData *info = [FTHZMusicPlayer shared].info;
  [self setMusicCover:info.imgUrl ? [NSURL URLWithString:info.imgUrl] : nil
                 name:info.musicName
               artist:info.artists
             platform:[info.from integerValue]];
}

- (void)layoutSubviews {
  [super layoutSubviews];
  self.gramophoneRecordView.left = 24;
  self.actionButton.right -= 12;
  self.logoView.right -= 23;
  self.contentView.left = self.gramophoneRecordView.right + 8;
  self.contentView.width = self.actionButton.left - self.contentView.left;
}

- (void)updateShowingBehavior {
  if (self.canShow && [FTHZMusicPlayer shared].isPlaying) {
    if (self.alpha < 1.0) {
      [UIView animateWithDuration:0.2
                       animations:^{
                         self.alpha = 1.0;
                       }];
    }
  } else {
    if (self.alpha > 0) {
      [UIView animateWithDuration:0.2
                       animations:^{
                         self.alpha = 0.0;
                       }];
    }
  }
}

- (void)dealloc {
  [self stopTrace];
}

@end

@interface FTHZFloatMusicPlayer ()
@property(nonatomic, strong) UIImageView *stickIV;
@property(nonatomic, strong) UIImageView *coverIV;
@property(nonatomic, strong) FlatButton *playBt;
@end

@implementation FTHZFloatMusicPlayer

- (instancetype)initWithFrame:(CGRect)frame {
  self = [super initWithFrame:frame];
  if (self) {
    _coverIV = [[UIImageView alloc]
        initWithFrame:CGRectMake(4 * kWidthFactor, 3 * kWidthFactor,
                                 48 * kWidthFactor, 48 * kWidthFactor)];
    _coverIV.layer.masksToBounds = YES;
    _coverIV.layer.cornerRadius = 24 * kWidthFactor;
    _coverIV.layer.borderWidth = 1;
    _coverIV.layer.borderColor = [[UIColor lightGrayColor] CGColor];

    _stickIV = [[UIImageView alloc]
        initWithFrame:CGRectMake(0, 0, 26 * kWidthFactor, 13 * kWidthFactor)];
    _stickIV.image = [UIImage imageNamed:@"disc_tick"];

    CGFloat coverCenterX = 4 * kWidthFactor + (48 * kWidthFactor / 2);
    CGFloat coverCenterY = 3 * kWidthFactor + (48 * kWidthFactor / 2);

    CGFloat buttonSize = 24 * kWidthFactor;

    _playBt = [[FTHZCustomPlayButton alloc] init];
    _playBt.frame =
        CGRectMake(coverCenterX - (buttonSize / 2),
                   coverCenterY - (buttonSize / 2), buttonSize, buttonSize);
    _playBt.backgroundColor = [UIColor clearColor];
    [_playBt addTarget:self
                  action:@selector(playAction:)
        forControlEvents:UIControlEventTouchUpInside];
    _playBt.selected = NO;

    [self addSubview:_coverIV];
    [self addSubview:_stickIV];
    [self addSubview:_playBt];

    _canShow = YES;
    self.alpha = 0.0;
    [self beginTrace];
  }
  return self;
}

- (void)stopTrace {
  [[FTHZMusicPlayer shared] removeObserver:self forKeyPath:@"isPlaying"];
  [[FTHZMusicPlayer shared] removeObserver:self forKeyPath:@"info"];
}

- (void)beginTrace {
  [[FTHZMusicPlayer shared] addObserver:self
                             forKeyPath:@"isPlaying"
                                options:NSKeyValueObservingOptionInitial |
                                        NSKeyValueObservingOptionNew
                                context:nil];
  [[FTHZMusicPlayer shared] addObserver:self
                             forKeyPath:@"info"
                                options:NSKeyValueObservingOptionInitial |
                                        NSKeyValueObservingOptionNew
                                context:nil];
}

- (void)observeValueForKeyPath:(NSString *_Nullable)keyPath
                      ofObject:(id _Nullable)object
                        change:
                            (NSDictionary<NSKeyValueChangeKey, id> *_Nullable)
                                change
                       context:(void *_Nullable)context {
  if ([keyPath isEqualToString:@"isPlaying"]) {
    [self updateShowingBehavior];
  } else if ([keyPath isEqualToString:@"info"]) {
    [self updateMusicInfo];
  }
}

- (void)setSelected:(BOOL)selected {
}

- (void)playAction:(UIButton *)button {

  [[FTHZMusicPlayer shared] pause];
}

- (void)setCanShow:(BOOL)canShow {
  if (canShow == _canShow) {
    return;
  }
  _canShow = canShow;
  [self updateShowingBehavior];
}

- (void)updateMusicInfo {
  MusicInfoData *info = [FTHZMusicPlayer shared].info;

  [_coverIV
      sd_setImageWithURL:(info.imgUrl ? [NSURL URLWithString:info.imgUrl] : nil)
        placeholderImage:[[UIImage alloc]
                             initWithColor:KColor_HighBlack
                                      size:CGSizeMake(48 * kWidthFactor,
                                                      48 * kWidthFactor)]];
}

- (void)updateShowingBehavior {
  if ([FTHZMusicPlayer shared].isPlaying) {
    _playBt.selected = YES;
  } else {
    _playBt.selected = NO;
  }
  if (self.canShow &&
      ([FTHZMusicPlayer shared].isPlaying ||
       [FTHZMusicPlayer shared].faAudioState == kFsAudioStreamPaused)) {
    if (self.alpha < 1.0) {
      [UIView animateWithDuration:0.2
                       animations:^{
                         self.alpha = 1.0;
                       }
                       completion:^(BOOL finished){

                       }];
    }
  } else {
    if (self.alpha > 0) {
      [UIView animateWithDuration:0.2
                       animations:^{
                         self.alpha = 0.0;
                       }
                       completion:^(BOOL finished){

                       }];
    }
  }
}

- (void)dealloc {
  [self stopTrace];
}

@end

NS_ASSUME_NONNULL_END
