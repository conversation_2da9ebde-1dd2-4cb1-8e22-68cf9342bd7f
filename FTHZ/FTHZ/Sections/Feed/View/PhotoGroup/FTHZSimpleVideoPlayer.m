#import "FTHZSimpleVideoPlayer.h"
#import <AVFoundation/AVFoundation.h>

@interface FTHZSimpleVideoPlayer () <UIGestureRecognizerDelegate>

@property(nonatomic, strong) AVPlayer *player;
@property(nonatomic, strong) AVPlayerLayer *playerLayer;
@property(nonatomic, weak) UIView *originalContainerView;
@property(nonatomic, assign) CGRect originalFrame;
@property(nonatomic, strong) UIButton *playPauseButton;
@property(nonatomic, strong) UIProgressView *progressView;
@property(nonatomic, strong) UILabel *timeLabel;
@property(nonatomic, strong) id timeObserver;
@property(nonatomic, assign) BOOL isPlaying;
@property(nonatomic, strong) UIPanGestureRecognizer *panGesture;
@property(nonatomic, assign) CGPoint initialTouchPoint;
@property(nonatomic, assign) CGFloat dragStartY;

@end

@implementation FTHZSimpleVideoPlayer

+ (instancetype)sharedInstance {
  static FTHZSimpleVideoPlayer *instance = nil;
  static dispatch_once_t onceToken;
  dispatch_once(&onceToken, ^{
    instance = [[FTHZSimpleVideoPlayer alloc] init];
  });
  return instance;
}

- (instancetype)init {
  self = [super init];
  if (self) {
    self.backgroundColor = [UIColor blackColor];

    UITapGestureRecognizer *tap =
        [[UITapGestureRecognizer alloc] initWithTarget:self
                                                action:@selector(handleTap)];
    [self addGestureRecognizer:tap];

    self.panGesture =
        [[UIPanGestureRecognizer alloc] initWithTarget:self
                                                action:@selector(handlePan:)];
    self.panGesture.delegate = self;
    [self addGestureRecognizer:self.panGesture];

    self.userInteractionEnabled = YES;

    [self setupPlayPauseButton];
    [self setupProgressView];
    [self setupTimeLabel];
  }
  return self;
}

- (void)setupTimeLabel {
  self.timeLabel = [[UILabel alloc] init];
  self.timeLabel.textColor = [UIColor whiteColor];
  self.timeLabel.font = [UIFont systemFontOfSize:12];
  self.timeLabel.textAlignment = NSTextAlignmentRight;
  self.timeLabel.text = @"00:00";
  self.timeLabel.hidden = YES;
}

#pragma mark - 手势代理方法
- (BOOL)gestureRecognizerShouldBegin:(UIGestureRecognizer *)gestureRecognizer {
  if (gestureRecognizer == self.panGesture) {
    CGPoint velocity = [self.panGesture velocityInView:self];
    return fabs(velocity.y) > fabs(velocity.x) && velocity.y > 0;
  }
  return YES;
}

#pragma mark - UI 设置

- (void)setupPlayPauseButton {
  self.playPauseButton = [UIButton buttonWithType:UIButtonTypeSystem];
  self.playPauseButton.tintColor = [UIColor whiteColor];

  UIImage *playImage = [UIImage systemImageNamed:@"play.fill"];
  if (playImage) {
    [self.playPauseButton setImage:playImage forState:UIControlStateNormal];
  } else {
    [self.playPauseButton setTitle:@"播放" forState:UIControlStateNormal];
  }

  self.playPauseButton.backgroundColor = [UIColor colorWithWhite:0.0 alpha:0.5];
  self.playPauseButton.layer.cornerRadius = 20;
  self.playPauseButton.alpha = 0.8;
  [self.playPauseButton addTarget:self
                           action:@selector(playPauseButtonTapped:)
                 forControlEvents:UIControlEventTouchUpInside];

  self.playPauseButton.hidden = YES;
}

- (void)setupProgressView {
  self.progressView = [[UIProgressView alloc] init];
  self.progressView.progressTintColor = [UIColor whiteColor];
  self.progressView.trackTintColor = [UIColor colorWithWhite:0.5 alpha:0.5];

  self.progressView.hidden = YES;
}

#pragma mark - 手势处理

- (void)handlePan:(UIPanGestureRecognizer *)gesture {
  CGPoint translation = [gesture translationInView:self.superview];

  switch (gesture.state) {
  case UIGestureRecognizerStateBegan: {

    self.dragStartY = self.frame.origin.y;
    self.initialTouchPoint = [gesture locationInView:self.superview];
    break;
  }

  case UIGestureRecognizerStateChanged: {

    if (translation.y > 0) {

      self.frame =
          CGRectMake(self.frame.origin.x, self.dragStartY + translation.y,
                     self.frame.size.width, self.frame.size.height);
    }
    break;
  }

  case UIGestureRecognizerStateEnded:
  case UIGestureRecognizerStateCancelled: {

    CGFloat distance = [gesture translationInView:self.superview].y;
    UIWindow *window = [UIApplication sharedApplication].keyWindow;

    if (distance > window.bounds.size.height * 0.2) {
      [self exitPlayback];
    } else {

      [UIView animateWithDuration:0.3
                       animations:^{
                         self.frame = CGRectMake(
                             self.frame.origin.x, self.dragStartY,
                             self.frame.size.width, self.frame.size.height);
                       }];
    }
    break;
  }

  default:
    break;
  }
}

- (void)handleTap {

  CGPoint playBtnPoint =
      [self.playPauseButton.superview convertPoint:self.playPauseButton.center
                                            toView:self];

  if (CGRectContainsPoint(self.playPauseButton.frame, playBtnPoint) ||
      CGRectContainsPoint(self.progressView.frame, self.progressView.center)) {
    return;
  }

  if (self.isPlaying) {
    [self pauseVideo];
  } else {
    [self playVideo];
  }
}

#pragma mark - 控制按钮事件

- (void)playPauseButtonTapped:(UIButton *)button {
  if (self.isPlaying) {
    [self pauseVideo];
  } else {
    [self playVideo];
  }

  [[NSOperationQueue mainQueue] addOperationWithBlock:^{
    [NSObject cancelPreviousPerformRequestsWithTarget:self
                                             selector:@selector(handleTap)
                                               object:nil];
  }];
}

#pragma mark - 播放控制

- (void)playVideo {
  [self.player play];
  self.isPlaying = YES;

  [self.playPauseButton setImage:[UIImage imageNamed:@"music_pause"]
                        forState:UIControlStateNormal];
}

- (void)pauseVideo {
  [self.player pause];
  self.isPlaying = NO;

  [self.playPauseButton setImage:[UIImage imageNamed:@"music_play"]
                        forState:UIControlStateNormal];
}

- (void)reset {

  [self.player pause];
  [self removeProgressObserver];

  [self.playerLayer removeFromSuperlayer];

  self.player = nil;
  self.playerLayer = nil;
  self.originalContainerView = nil;
  self.originalFrame = CGRectZero;
  self.isPlaying = NO;

  [self.progressView setProgress:0.0 animated:NO];
  self.timeLabel.text = @"00:00";

  [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)addProgressObserver {
  __weak typeof(self) weakSelf = self;
  self.timeObserver = [self.player
      addPeriodicTimeObserverForInterval:CMTimeMake(1, 60)
                                   queue:dispatch_get_main_queue()
                              usingBlock:^(CMTime time) {
                                CMTime duration =
                                    weakSelf.player.currentItem.duration;
                                if (CMTimeGetSeconds(duration) > 0) {
                                  float progress = CMTimeGetSeconds(time) /
                                                   CMTimeGetSeconds(duration);
                                  [weakSelf.progressView setProgress:progress
                                                            animated:YES];

                                  float remainingSeconds =
                                      CMTimeGetSeconds(duration) -
                                      CMTimeGetSeconds(time);
                                  int minutes = (int)remainingSeconds / 60;
                                  int seconds = (int)remainingSeconds % 60;
                                  weakSelf.timeLabel.text = [NSString
                                      stringWithFormat:@"%02d:%02d", minutes,
                                                       seconds];
                                }
                              }];
}

- (void)removeProgressObserver {
  if (self.timeObserver) {
    [self.player removeTimeObserver:self.timeObserver];
    self.timeObserver = nil;
  }
}

- (void)playerItemDidReachEnd:(NSNotification *)notification {

  [self pauseVideo];

  [self.player seekToTime:CMTimeMake(0, 1)];
}

- (void)exitPlayback {

  [self.player pause];

  UIWindow *window = [UIApplication sharedApplication].keyWindow;
  CGFloat screenHeight = window.bounds.size.height;

  [UIView animateWithDuration:0.3
      animations:^{
        self.frame = CGRectMake(self.frame.origin.x, screenHeight,
                                self.frame.size.width, self.frame.size.height);
      }
      completion:^(BOOL finished) {
        [self stop];
      }];
}

#pragma mark - 公共方法

- (void)playVideoWithURL:(NSString *)videoURL inView:(UIView *)containerView {
  if (!videoURL || !containerView)
    return;
  [self reset];

  self.originalContainerView = containerView;
  self.originalFrame = containerView.frame;

  NSURL *url = [NSURL URLWithString:videoURL];
  AVPlayerItem *playerItem = [AVPlayerItem playerItemWithURL:url];
  self.player = [AVPlayer playerWithPlayerItem:playerItem];

  self.playerLayer = [AVPlayerLayer playerLayerWithPlayer:self.player];

  self.playerLayer.videoGravity = AVLayerVideoGravityResizeAspect;

  [self.layer addSublayer:self.playerLayer];

  [self addSubview:self.playPauseButton];
  self.playPauseButton.hidden = NO;
  [self addSubview:self.progressView];
  self.progressView.hidden = NO;
  [self addSubview:self.timeLabel];
  self.timeLabel.hidden = NO;

  UIWindow *window = [UIApplication sharedApplication].keyWindow;
  [window addSubview:self];
  self.alpha = 1.0;

  self.frame = [containerView convertRect:containerView.bounds toView:window];
  [UIView animateWithDuration:0.3
      animations:^{
        self.frame = window.bounds;
        self.playerLayer.frame = self.bounds;
      }
      completion:^(BOOL finished) {
        [self playVideo];
        [self addProgressObserver];

        [[NSNotificationCenter defaultCenter]
            addObserver:self
               selector:@selector(playerItemDidReachEnd:)
                   name:AVPlayerItemDidPlayToEndTimeNotification
                 object:self.player.currentItem];
      }];
}

- (void)stop {
  [self.player pause];
  [self removeProgressObserver];
  [self.playerLayer removeFromSuperlayer];
  self.player = nil;
  self.playerLayer = nil;

  [[NSNotificationCenter defaultCenter]
      removeObserver:self
                name:AVPlayerItemDidPlayToEndTimeNotification
              object:nil];

  self.isPlaying = NO;

  [self removeFromSuperview];
}

#pragma mark - 布局

- (void)layoutSubviews {
  [super layoutSubviews];
  self.playerLayer.frame = self.bounds;

  CGFloat progressHeight = 4;

  CGFloat progressWidth = self.bounds.size.width * 0.6;
  CGFloat bottomMargin = 40;

  CGFloat buttonSize = 40;

  CGFloat timeLabelWidth = 50;
  CGFloat timeLabelHeight = 20;

  CGFloat availableWidth = self.bounds.size.width - (buttonSize + 20);
  CGFloat maxProgressWidth = availableWidth - timeLabelWidth - 30;
  CGFloat actualProgressWidth = MIN(progressWidth, maxProgressWidth);

  CGFloat progressLeft =
      (self.bounds.size.width - actualProgressWidth - timeLabelWidth - 20) / 2 +
      20;
  CGFloat progressY = self.bounds.size.height - bottomMargin;
  self.progressView.frame =
      CGRectMake(progressLeft, progressY, actualProgressWidth, progressHeight);

  CGFloat buttonX = progressLeft - buttonSize - 10;
  CGFloat buttonY = progressY - buttonSize / 2 + progressHeight / 2;
  self.playPauseButton.frame =
      CGRectMake(buttonX, buttonY, buttonSize, buttonSize);

  CGFloat timeLabelX = progressLeft + actualProgressWidth + 10;
  CGFloat timeLabelY = progressY - timeLabelHeight / 2 + progressHeight / 2;

  CGFloat maxTimeLabelX = self.bounds.size.width - timeLabelWidth - 10;
  CGFloat finalTimeLabelX = MIN(timeLabelX, maxTimeLabelX);

  self.timeLabel.frame =
      CGRectMake(finalTimeLabelX, timeLabelY, timeLabelWidth, timeLabelHeight);
}

- (void)dealloc {
  [self removeProgressObserver];
  [[NSNotificationCenter defaultCenter] removeObserver:self];
}

@end