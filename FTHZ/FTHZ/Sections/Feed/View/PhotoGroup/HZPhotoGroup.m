#import "HZPhotoGroup.h"
#import "FLAnimatedImageView.h"
#import "HZPhotoBrowser.h"
#import "HZPhotoBrowserConfig.h"
#import "UIButton+WebCache.h"
#import <AVKit/AVKit.h>
#import <FLAnimatedImage/FLAnimatedImageView.h>

#define HZPhotoGroupImageMargin 8 * kWidthFactor

@interface HZPhotoGroup () <HZPhotoBrowserDelegate>
@property(nonatomic, assign) CGRect *tempRect;
@property(nonatomic, assign) NSInteger imageNum;
@property(nonatomic, assign) NSInteger imageIndex;

@end

@implementation HZPhotoGroup

- (instancetype)init {
  self = [super init];
  if (self) {
  }
  return self;
}

- (void)getNum:(NSInteger)num index:(NSInteger)index {
  _imageNum = num;
  _imageIndex = index;
}

- (void)setUrlArray:(NSArray<NSString *> *)urlArray {
  _urlArray = urlArray;
  [self.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];

  NSInteger maxCount = urlArray.count > 5 ? 5 : urlArray.count;
  for (NSInteger idx = 0; idx < maxCount; idx++) {
    FLAnimatedImageView *imageView = [[FLAnimatedImageView alloc] init];
    imageView.userInteractionEnabled = YES;
    imageView.contentMode = UIViewContentModeScaleAspectFill;
    imageView.clipsToBounds = YES;

    [imageView
        sd_setImageWithURL:[NemoUtil getUrlWithUserPictaure:urlArray[idx]]
          placeholderImage:KImage_name(@"empty")];

    imageView.tag = idx;
    UITapGestureRecognizer *tap =
        [[UITapGestureRecognizer alloc] initWithTarget:self
                                                action:@selector(tap:)];
    [imageView addGestureRecognizer:tap];

    [self addSubview:imageView];

    if (idx == 0 && self.isVideo) {
      UIView *playButtonBg = [[UIView alloc] init];
      playButtonBg.backgroundColor =
          [[UIColor blackColor] colorWithAlphaComponent:0.2];
      playButtonBg.layer.cornerRadius = 10 * kWidthFactor;
      [imageView addSubview:playButtonBg];

      [playButtonBg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(imageView).offset(-8 * kWidthFactor);
        make.bottom.equalTo(imageView).offset(-8 * kWidthFactor);
        make.width.height.mas_equalTo(20 * kWidthFactor);
      }];

      CAShapeLayer *circleLayer = [CAShapeLayer layer];
      UIBezierPath *circlePath =
          [UIBezierPath bezierPathWithArcCenter:CGPointMake(10 * kWidthFactor,
                                                            10 * kWidthFactor)
                                         radius:8 * kWidthFactor
                                     startAngle:0
                                       endAngle:2 * M_PI
                                      clockwise:YES];
      circleLayer.path = circlePath.CGPath;
      circleLayer.strokeColor =
          [[UIColor whiteColor] colorWithAlphaComponent:0.8].CGColor;
      circleLayer.fillColor = [UIColor clearColor].CGColor;
      circleLayer.lineWidth = 1.5;
      [playButtonBg.layer addSublayer:circleLayer];

      CAShapeLayer *triangleLayer = [CAShapeLayer layer];
      UIBezierPath *trianglePath = [UIBezierPath bezierPath];
      [trianglePath
          moveToPoint:CGPointMake(8 * kWidthFactor, 6 * kWidthFactor)];
      [trianglePath
          addLineToPoint:CGPointMake(8 * kWidthFactor, 14 * kWidthFactor)];
      [trianglePath
          addLineToPoint:CGPointMake(14 * kWidthFactor, 10 * kWidthFactor)];
      [trianglePath closePath];
      triangleLayer.path = trianglePath.CGPath;
      triangleLayer.fillColor =
          [[UIColor whiteColor] colorWithAlphaComponent:0.8].CGColor;
      [playButtonBg.layer addSublayer:triangleLayer];
    }
  }
}

- (void)layoutSubviews {
  [super layoutSubviews];
  long imageCount = self.urlArray.count;
  CGFloat totalW = 365 * kWidthFactor;
  CGFloat totalH = 174 * kWidthFactor;
  CGFloat margin = HZPhotoGroupImageMargin;

  if (_imageNum == 1) {
    [self.subviews enumerateObjectsUsingBlock:^(UIView *imageView,
                                                NSUInteger idx, BOOL *stop) {
      if (idx == 0) {
        imageView.hidden = NO;
        imageView.layer.cornerRadius = 12 * kMainTemp;
        imageView.frame = CGRectMake(0, 0, totalW, totalH);
      } else {
        imageView.hidden = YES;
      }
    }];
    return;
  }

  if (_imageNum == 2) {
    CGFloat imgW = (totalW - margin) / 2.0;
    CGFloat imgH = totalH;
    [self.subviews enumerateObjectsUsingBlock:^(UIView *imageView,
                                                NSUInteger idx, BOOL *stop) {
      if (idx < 2) {
        imageView.hidden = NO;
        imageView.layer.cornerRadius = 12 * kMainTemp;
        CGFloat x = idx * (imgW + margin);
        imageView.frame = CGRectMake(x, 0, imgW, imgH);
      } else {
        imageView.hidden = YES;
      }
    }];
    return;
  }

  if (_imageNum == 3) {
    CGFloat leftW = (totalW - margin) / 2.0;
    CGFloat leftH = totalH;
    CGFloat rightW = leftW;
    CGFloat rightH = (totalH - margin) / 2.0;
    [self.subviews enumerateObjectsUsingBlock:^(UIView *imageView,
                                                NSUInteger idx, BOOL *stop) {
      if (idx < 3) {
        imageView.hidden = NO;
        if (idx == 0) {
          imageView.layer.cornerRadius = 12 * kMainTemp;
          imageView.frame = CGRectMake(0, 0, leftW, leftH);
        } else {
          imageView.layer.cornerRadius = 10 * kMainTemp;
          CGFloat y = (idx == 1) ? 0 : (rightH + margin);
          imageView.frame = CGRectMake(leftW + margin, y, rightW, rightH);
        }
      } else {
        imageView.hidden = YES;
      }
    }];
    return;
  }

  if (_imageNum == 4) {
    CGFloat leftW = (totalW - margin) / 2.0;
    CGFloat leftH = totalH;
    CGFloat rightW = leftW;
    CGFloat topH = (totalH - margin) / 2.0;
    CGFloat botH = topH;
    CGFloat botW = (rightW - margin) / 2.0;
    [self.subviews enumerateObjectsUsingBlock:^(UIView *imageView,
                                                NSUInteger idx, BOOL *stop) {
      if (idx < 4) {
        imageView.hidden = NO;
        if (idx == 0) {
          imageView.layer.cornerRadius = 12 * kMainTemp;
          imageView.frame = CGRectMake(0, 0, leftW, leftH);
        } else if (idx == 1) {
          imageView.layer.cornerRadius = 10 * kMainTemp;
          imageView.frame = CGRectMake(leftW + margin, 0, rightW, topH);
        } else {
          imageView.layer.cornerRadius = 8 * kMainTemp;
          CGFloat x = leftW + margin + (idx - 2) * (botW + margin);
          imageView.frame = CGRectMake(x, topH + margin, botW, botH);
        }
      } else {
        imageView.hidden = YES;
      }
    }];
    return;
  }

  if (_imageNum == 5) {
    CGFloat leftW = (totalW - margin) / 2.0;
    CGFloat leftH = totalH;
    CGFloat rightW = leftW;
    CGFloat cellW = (rightW - margin) / 2.0;
    CGFloat cellH = (totalH - margin) / 2.0;
    [self.subviews enumerateObjectsUsingBlock:^(UIView *imageView,
                                                NSUInteger idx, BOOL *stop) {
      if (idx < 5) {
        imageView.hidden = NO;
        if (idx == 0) {
          imageView.layer.cornerRadius = 12 * kMainTemp;
          imageView.frame = CGRectMake(0, 0, leftW, leftH);
        } else {
          imageView.layer.cornerRadius = 8 * kMainTemp;
          CGFloat x = leftW + margin + ((idx - 1) % 2) * (cellW + margin);
          CGFloat y = ((idx - 1) / 2) * (cellH + margin);
          imageView.frame = CGRectMake(x, y, cellW, cellH);
        }
      } else {
        imageView.hidden = YES;
      }
    }];
    return;
  }

  if (_imageNum >= 6) {
    CGFloat leftW = (totalW - margin) / 2.0;
    CGFloat leftH = totalH;
    CGFloat rightW = leftW;
    CGFloat cellW = (rightW - margin) / 2.0;
    CGFloat cellH = (totalH - margin) / 2.0;
    [self.subviews enumerateObjectsUsingBlock:^(UIView *imageView,
                                                NSUInteger idx, BOOL *stop) {
      if (idx < 5) {
        imageView.hidden = NO;
        if (idx == 0) {
          imageView.layer.cornerRadius = 12 * kMainTemp;
          imageView.frame = CGRectMake(0, 0, leftW, leftH);
        } else {
          imageView.layer.cornerRadius = 8 * kMainTemp;
          CGFloat x = leftW + margin + ((idx - 1) % 2) * (cellW + margin);
          CGFloat y = ((idx - 1) / 2) * (cellH + margin);
          imageView.frame = CGRectMake(x, y, cellW, cellH);
          if (idx == 4) {
            [self addPlusLabelToImageView:imageView
                                withCount:(int)(imageCount - 5)];
          }
        }
      } else {
        imageView.hidden = YES;
      }
    }];
    return;
  }
}

- (void)tap:(UIGestureRecognizer *)gesture {
  if (self.isVideo) {
    if ([self.delegate respondsToSelector:@selector(photoGroup:
                                              didTapVideoWithVideoURL:)]) {
      [self.delegate photoGroup:self didTapVideoWithVideoURL:self.videoURL];
    }
    return;
  }
  FLAnimatedImageView *imageView = (FLAnimatedImageView *)gesture.view;

  HZPhotoBrowser *browser = [[HZPhotoBrowser alloc] init];
  browser.isFullWidthForLandScape = YES;
  browser.isNeedLandscape = YES;
  browser.sourceImagesContainerView = self;
  browser.currentImageIndex = (int)imageView.tag;
  browser.imageCount = self.urlArray.count;
  browser.delegate = self;
  [browser show];
}

- (void)addPlusLabelToImageView:(UIView *)imageView withCount:(int)count {
  for (UIView *subview in imageView.subviews) {
    if ([subview isKindOfClass:[UILabel class]] && subview.tag == 999) {
      [subview removeFromSuperview];
    }
  }

  UIView *overlayView = [[UIView alloc] init];
  overlayView.backgroundColor =
      [[UIColor blackColor] colorWithAlphaComponent:0.4];
  overlayView.layer.cornerRadius = 8 * kMainTemp;
  overlayView.tag = 998;
  [imageView addSubview:overlayView];

  UILabel *plusLabel = [[UILabel alloc] init];
  plusLabel.text = [NSString stringWithFormat:@"+%d", count];
  plusLabel.textColor = [[UIColor whiteColor] colorWithAlphaComponent:0.6];
  plusLabel.font = [UIFont boldSystemFontOfSize:30 * kWidthFactor];
  plusLabel.textAlignment = NSTextAlignmentCenter;
  plusLabel.tag = 999;
  [imageView addSubview:plusLabel];

  overlayView.frame = imageView.bounds;
  plusLabel.frame = imageView.bounds;
}

+ (CGSize)sizeWithImageCount:(NSUInteger)count displayStyle:(NSInteger)style {
  CGFloat width = 365 * kWidthFactor;
  CGFloat height = 0;

  switch (count) {
  case 1:
    height = 174 * kWidthFactor;
    break;
  case 2:
    height = 174 * kWidthFactor;
    break;
  case 3:
    height = 174 * kWidthFactor;
    break;
  case 4:
    height = 174 * kWidthFactor;
    break;
  case 5:
    height = 174 * kWidthFactor;
    break;
  default:
    height = 174 * kWidthFactor;
    break;
  }

  return CGSizeMake(width, height);
}
#pragma mark - photobrowser代理方法
- (UIImage *)photoBrowser:(HZPhotoBrowser *)browser
    placeholderImageForIndex:(NSInteger)index {
  if (index >= 0 && index < self.subviews.count) {
    FLAnimatedImageView *imageView =
        (FLAnimatedImageView *)self.subviews[index];
    return imageView.image;
  }
  return nil;
}

- (NSURL *)photoBrowser:(HZPhotoBrowser *)browser
    highQualityImageURLForIndex:(NSInteger)index {
  NSString *urlStr =
      [self.urlArray[index] stringByReplacingOccurrencesOfString:@"thumbnail"
                                                      withString:@"bmiddle"];
  return [NSURL URLWithString:urlStr];
}

@end
