#import <UIKit/UIKit.h>

@class HZPhotoGroupOld;

@protocol HZPhotoGroupOldDelegate <NSObject>
@optional
- (void)photoGroup:(HZPhotoGroupOld *)photoGroup
    didTapVideoWithVideoURL:(NSString *)videoURL;
@end

@interface HZPhotoGroupOld : UIView

@property(nonatomic, strong) NSArray<NSString *> *urlArray;
@property(nonatomic, weak) id<HZPhotoGroupOldDelegate> delegate;
@property(nonatomic, assign) BOOL isVideo;
@property(nonatomic, copy) NSString *videoURL;

- (void)getNum:(NSInteger)num index:(NSInteger)index;

+ (CGSize)sizeWithImageCount:(NSUInteger)count displayStyle:(NSInteger)style;

@end
