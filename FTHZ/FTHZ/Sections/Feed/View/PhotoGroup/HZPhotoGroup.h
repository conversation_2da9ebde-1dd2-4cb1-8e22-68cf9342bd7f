#import <UIKit/UIKit.h>

@class HZPhotoGroup;

@protocol HZPhotoGroupDelegate <NSObject>
@optional
- (void)photoGroup:(HZPhotoGroup *)photoGroup
    didTapVideoWithVideoURL:(NSString *)videoURL;
@end

@interface HZPhotoGroup : UIView

@property(nonatomic, strong) NSArray<NSString *> *urlArray;
@property(nonatomic, weak) id<HZPhotoGroupDelegate> delegate;
@property(nonatomic, assign) BOOL isVideo;
@property(nonatomic, copy) NSString *videoURL;

- (void)getNum:(NSInteger)num index:(NSInteger)index;

+ (CGSize)sizeWithImageCount:(NSUInteger)count displayStyle:(NSInteger)style;

@end
