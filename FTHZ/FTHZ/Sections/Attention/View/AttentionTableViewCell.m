#import "AttentionTableViewCell.h"
#import "FTHZMusicPlayerView.h"
#import "FTHZSimpleVideoPlayer.h"
#import "HZPhotoGroup.h"
#import "_2hz-Swift.h"
#import <AVFoundation/AVFoundation.h>
#import <AVKit/AVKit.h>

@interface AttentionTableViewCell ()
@property(nonatomic, strong) UIImageView *iconImage;
@property(nonatomic, strong) UILabel *nameLabel;
@property(nonatomic, strong) UILabel *heziLabel;
@property(nonatomic, strong) UILabel *detailsLabel;
@property(nonatomic, strong) HZPhotoGroup *photoView;
@property(nonatomic, strong) UILabel *awesomeLabel;
@property(nonatomic, strong) UILabel *vommentLabel;
@property(nonatomic, strong) UILabel *locationLabel;
@property(nonatomic, strong) UILabel *timeLabel;
@property(nonatomic, strong) FTHZMusicPlayerView *playerView;
@property(nonatomic, strong) DynamicModelResult *dynamic;
@property(nonatomic, strong) FTHZSimpleVideoPlayer *videoPlayerView;
@property(nonatomic, strong) UILabel *hideStatusLabel;
@property(nonatomic, strong) UIView *separatorLineView;
@property(nonatomic, strong) UILabel *sponsorLabel;
@property(nonatomic, strong) UIView *actionBar;
@property(nonatomic, strong) NSTimer *alternateTimer;
@property(nonatomic, assign) BOOL isShowingSignature;
@property(nonatomic, strong) UIImageView *topTriangleImageView;

@end

@implementation AttentionTableViewCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style
              reuseIdentifier:(NSString *)reuseIdentifier {
  self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
  if (self) {
    [self loadCellView];
    [self setSelectionStyle:(UITableViewCellSelectionStyleNone)];
  }
  return self;
}

- (void)loadCellView {
  _iconImage = [[UIImageView alloc] init];
  _iconImage.userInteractionEnabled = YES;
  [self.contentView addSubview:_iconImage];
  [_iconImage mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.contentView).offset(24 * kWidthFactor);
    make.top.equalTo(self.contentView).offset(20 * kWidthFactor);
    make.size.mas_equalTo(CGSizeMake(40 * kWidthFactor, 40 * kWidthFactor));
  }];
  _iconImage.layer.cornerRadius = 20 * kWidthFactor;
  _iconImage.layer.masksToBounds = YES;

  _nameLabel = [[UILabel alloc] init];
  _nameLabel.font = SourceHanSerifBoldFont(15 * kWidthFactor);
  _nameLabel.textColor = KColor_HighBlue;
  _nameLabel.numberOfLines = 1;
  [self.contentView addSubview:_nameLabel];
  [_nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(_iconImage.mas_right).offset(8 * kWidthFactor);
    make.top.equalTo(_iconImage);
    make.right.lessThanOrEqualTo(self.contentView).offset(-100 * kWidthFactor);
    make.height.mas_equalTo(20 * kWidthFactor);
  }];

  _heziLabel = [[UILabel alloc] init];
  _heziLabel.font = DinCondensedBoldFont(12 * kWidthFactor);
  _heziLabel.textColor = KColor_textTinyGray;
  [self.contentView addSubview:_heziLabel];
  [_heziLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(_nameLabel);
    make.bottom.equalTo(_iconImage.mas_bottom);
    make.width.mas_equalTo(60 * kWidthFactor);
    make.height.mas_equalTo(16 * kWidthFactor);
  }];

  _hideStatusLabel = [[UILabel alloc] init];
  _hideStatusLabel.font = SourceHanSerifRegularFont(9 * kWidthFactor);
  _hideStatusLabel.textColor = KColor_White;
  _hideStatusLabel.textAlignment = NSTextAlignmentCenter;
  _hideStatusLabel.backgroundColor = KColor_HighBlack;
  _hideStatusLabel.layer.cornerRadius = 7.5 * kWidthFactor;
  _hideStatusLabel.layer.masksToBounds = YES;
  _hideStatusLabel.hidden = YES;
  [self.contentView addSubview:_hideStatusLabel];

  _sponsorLabel = [[UILabel alloc] init];
  _sponsorLabel.text = @"赞助";
  _sponsorLabel.font = SourceHanSerifRegularFont(9 * kWidthFactor);
  _sponsorLabel.textColor = KColor_White;
  _sponsorLabel.textAlignment = NSTextAlignmentCenter;
  _sponsorLabel.backgroundColor = KColor_HighBlack;
  _sponsorLabel.layer.cornerRadius = 7.5 * kWidthFactor;
  _sponsorLabel.layer.masksToBounds = YES;
  _sponsorLabel.hidden = YES;
  [self.contentView addSubview:_sponsorLabel];

  _detailsLabel = [[UILabel alloc] init];
  _detailsLabel.font = SourceHanSerifMediumFont(14 * kWidthFactor);
  _detailsLabel.textColor = KColor_titleDarkGray;
  _detailsLabel.numberOfLines = 0;
  [self.contentView addSubview:_detailsLabel];

  _photoView = [[HZPhotoGroup alloc] init];
  [self.contentView addSubview:_photoView];

  _playerView = [[FTHZMusicPlayerView alloc] init];
  [self.contentView addSubview:_playerView];

  _tagBtn = [FlatButton buttonWithType:UIButtonTypeCustom];
  _tagBtn.backgroundColor = KColor_HighBlack;
  _tagBtn.layer.masksToBounds = YES;
  _tagBtn.layer.cornerRadius = 5 * kWidthFactor;
  [self.contentView addSubview:_tagBtn];

  _actionBar = [[UIView alloc] init];
  [self.contentView addSubview:_actionBar];

  _awesomeBtn = [FlatButton buttonWithType:UIButtonTypeCustom];
  [_actionBar addSubview:_awesomeBtn];

  _awesomeLabel = [[UILabel alloc] init];
  _awesomeLabel.font = DinCondensedFont(14 * kWidthFactor);
  _awesomeLabel.textColor = KColor_HighBlack;
  [_actionBar addSubview:_awesomeLabel];

  _commentBtn = [FlatButton buttonWithType:UIButtonTypeCustom];
  [_actionBar addSubview:_commentBtn];

  _vommentLabel = [[UILabel alloc] init];
  _vommentLabel.font = DinCondensedFont(14 * kWidthFactor);
  _vommentLabel.textColor = KColor_HighBlack;
  [_actionBar addSubview:_vommentLabel];

  _timeLabel = [[UILabel alloc] init];
  _timeLabel.font = [UIFont systemFontOfSize:12 * kWidthFactor];
  _timeLabel.textColor = KColor_detailLightGray;
  [self.contentView addSubview:_timeLabel];

  _locationLabel = [[UILabel alloc] init];
  _locationLabel.font = SourceHanSerifRegularFont(12 * kWidthFactor);
  _locationLabel.textColor = KColor_detailLightGray;
  [self.contentView addSubview:_locationLabel];

  _separatorLineView = [[UIView alloc] init];
  _separatorLineView.backgroundColor = KColor_ExtraLightGray;
  [self.contentView addSubview:_separatorLineView];

  self.topTriangleImageView =
      [[UIImageView alloc] initWithImage:KImage_name(@"zhiding")];
  self.topTriangleImageView.hidden = YES;
  [self.contentView addSubview:self.topTriangleImageView];
  [self.topTriangleImageView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(self.contentView).offset(2 * kWidthFactor);
    make.left.equalTo(self.contentView).offset(2 * kWidthFactor);
    make.width.height.mas_equalTo(24 * kWidthFactor);
  }];
}

#pragma mark - HZPhotoGroupDelegate
- (void)photoGroup:(HZPhotoGroup *)photoGroup
    didTapVideoWithVideoURL:(NSString *)videoURL {
  [[FTHZSimpleVideoPlayer sharedInstance] playVideoWithURL:videoURL
                                                    inView:photoGroup];
}

- (void)alternateText {
  if (self.isShowingSignature) {
    _heziLabel.text = [NSString stringWithFormat:@"%@Hz", _dynamic.user.hertz];
  } else {
    _heziLabel.text = _dynamic.user.signature;
  }
  self.isShowingSignature = !self.isShowingSignature;
}

- (void)prepareForReuse {
  [super prepareForReuse];
  [self.alternateTimer invalidate];
  self.alternateTimer = nil;
  [[FTHZSimpleVideoPlayer sharedInstance] stop];
  _photoView.isVideo = NO;
  _photoView.videoURL = nil;
  _photoView.urlArray = nil;
}

- (void)setDynamic:(DynamicModelResult *)dynamic showTime:(BOOL)onlyTime {
  _dynamic = dynamic;

  [_iconImage
      sd_setImageWithURL:[NemoUtil getUrlWithUserSmallIcon:_dynamic.user.avatar]
        placeholderImage:KImage_name(@"empty")];

  _nameLabel.text = _dynamic.user.nickname ?: @"";
  _nameLabel.textColor = KColor_HighBlue;
  if (_dynamic.user.colorFont != nil) {
    [_nameLabel applyGradientWithType:_dynamic.user.colorFont.type
                           expireTime:_dynamic.user.colorFont.expire_time];
  }

  _heziLabel.text = [NSString stringWithFormat:@"%@Hz", _dynamic.user.hertz];

  BOOL showSponsor = [_dynamic.affair.sponsor isEqualToString:@"1"];
  _sponsorLabel.hidden = !showSponsor;
  if (showSponsor) {
    [_sponsorLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.centerY.equalTo(_heziLabel);
      make.right.equalTo(self.contentView).offset(-24 * kWidthFactor);
      make.height.mas_equalTo(15 * kWidthFactor);
      make.width.mas_equalTo(30 * kWidthFactor);
    }];
  }

  BOOL showHideStatus = [_dynamic.affair.status isEqualToString:@"2"] ||
                        ([_dynamic.affair.status isEqualToString:@"3"] &&
                         _dynamic.affair.timing.length > 0);
  if (showHideStatus) {
    if ([_dynamic.affair.status isEqualToString:@"2"]) {
      _hideStatusLabel.text = @"隐藏";
    } else {
      NSTimeInterval timeInterval = [_dynamic.affair.timing doubleValue];
      NSDate *date = [NSDate dateWithTimeIntervalSince1970:timeInterval];
      NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
      formatter.locale = [[NSLocale alloc] initWithLocaleIdentifier:@"zh_CN"];
      [formatter setDateFormat:@"yy年M月d日H时"];
      NSString *dateString = [formatter stringFromDate:date];
      _hideStatusLabel.text = [NSString stringWithFormat:@"%@发布", dateString];
    }
    _hideStatusLabel.hidden = NO;
    CGSize textSize =
        [_hideStatusLabel.text
            boundingRectWithSize:CGSizeMake(CGFLOAT_MAX, 15 * kWidthFactor)
                         options:NSStringDrawingUsesLineFragmentOrigin
                      attributes:@{NSFontAttributeName : _hideStatusLabel.font}
                         context:nil]
            .size;
    [_hideStatusLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.centerY.equalTo(_heziLabel);
      make.height.mas_equalTo(15 * kWidthFactor);
      make.width.mas_equalTo(textSize.width + 10 * kWidthFactor);
      if (showSponsor && !_sponsorLabel.hidden) {
        make.right.equalTo(_sponsorLabel.mas_left).offset(-8 * kWidthFactor);
      } else {
        make.right.equalTo(self.contentView).offset(-24 * kWidthFactor);
      }
    }];
  } else {
    _hideStatusLabel.hidden = YES;
  }

  _detailsLabel.text =
      _dynamic.affair.content.length > 0
          ? _dynamic.affair.content
          : ([_dynamic.affair.type isEqualToString:@"4"] ? @"分享视频"
                                                         : @"分享图片");

  _detailsLabel.font = SourceHanSerifMediumFont(14 * kWidthFactor);
  _detailsLabel.textColor = KColor_titleDarkGray;
  _detailsLabel.numberOfLines = 0;
  NSMutableParagraphStyle *paragraphStyle =
      [[NSMutableParagraphStyle alloc] init];
  paragraphStyle.lineSpacing = 5 * kWidthFactor;
  paragraphStyle.lineBreakMode = _detailsLabel.lineBreakMode;
  paragraphStyle.alignment = _detailsLabel.textAlignment;
  NSMutableAttributedString *attributedString =
      [[NSMutableAttributedString alloc] initWithString:_detailsLabel.text];
  [attributedString addAttribute:NSParagraphStyleAttributeName
                           value:paragraphStyle
                           range:NSMakeRange(0, [_detailsLabel.text length])];
  [attributedString addAttribute:NSFontAttributeName
                           value:_detailsLabel.font
                           range:NSMakeRange(0, [_detailsLabel.text length])];
  _detailsLabel.attributedText = attributedString;

  UIView *lastView = _iconImage;
  CGFloat firstElementOffset = 12 * kWidthFactor;

  NSInteger photoCount =
      [NemoUtil getHZPhotoStringCount:_dynamic.affair.images];
  BOOL hasPhoto = photoCount > 0;
  if (hasPhoto) {
    _photoView.hidden = NO;
    _photoView.isVideo = [_dynamic.affair.type isEqualToString:@"4"];
    _photoView.videoURL = _photoView.isVideo ? _dynamic.affair.video : nil;
    _photoView.delegate = self;
    [_photoView getNum:photoCount
                 index:[_dynamic.affair.imageType integerValue]];
    _photoView.urlArray =
        [_dynamic.affair.images componentsSeparatedByString:@","];

    CGFloat photoHeight =
        [NemoUtil getHZPhotoHeightNew:photoCount
                                index:[_dynamic.affair.imageType integerValue]];

    [_photoView mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.top.equalTo(lastView.mas_bottom).offset(firstElementOffset);
      make.left.equalTo(_iconImage.mas_left);
      make.right.equalTo(self.contentView).offset(-24 * kWidthFactor);
      make.height.mas_equalTo(photoHeight).priority(999);
    }];
    lastView = _photoView;
    firstElementOffset = 12 * kWidthFactor;
  } else {
    _photoView.hidden = YES;
    [_photoView mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.top.equalTo(lastView.mas_bottom);
      make.left.equalTo(_iconImage.mas_left);
      make.right.equalTo(self.contentView).offset(-24 * kWidthFactor);
      make.height.mas_equalTo(0);
    }];
  }

  MusicInfoData *musicData = _dynamic.affair.musicContent;
  BOOL hasMusic = [musicData isKindOfClass:[MusicInfoData class]];
  if (hasMusic) {
    _playerView.hidden = NO;
    [_playerView setMusicInfo:musicData uuid:_dynamic.affair.aid autoTrace:YES];
    [_playerView mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.top.equalTo(lastView.mas_bottom).offset(firstElementOffset);
      make.left.equalTo(_iconImage.mas_left);
      make.width.mas_equalTo(280 * kWidthFactor);
      make.height.mas_equalTo(60 * kWidthFactor);
    }];
    lastView = _playerView;
    firstElementOffset = 12 * kWidthFactor;
  } else {
    _playerView.hidden = YES;
    [_playerView mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.top.equalTo(lastView.mas_bottom);
      make.left.equalTo(_iconImage.mas_left);
      make.width.mas_equalTo(280 * kWidthFactor);
      make.height.mas_equalTo(0);
    }];
  }

  [_detailsLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(lastView.mas_bottom).offset(firstElementOffset);
    make.left.equalTo(_iconImage.mas_left);
    make.right.equalTo(self.contentView).offset(-24 * kWidthFactor);
  }];
  lastView = _detailsLabel;

  if (_dynamic.affair.tagName.length > 0) {
    _tagBtn.hidden = NO;
    for (UIView *subview in _tagBtn.subviews) {
      [subview removeFromSuperview];
    }
    UIView *markView = [UIView new];
    if ([_dynamic.user.gender isEqualToString:@"1"]) {
      markView.backgroundColor = KColor_switchLightBlue;
    } else {
      markView.backgroundColor = KColor_switchLightPink;
    }
    [_tagBtn addSubview:markView];
    [markView mas_makeConstraints:^(MASConstraintMaker *make) {
      make.size.mas_equalTo(CGSizeMake(10 * kWidthFactor, 10 * kWidthFactor));
      make.centerY.equalTo(_tagBtn);
      make.left.equalTo(_tagBtn).offset(5 * kWidthFactor);
    }];
    markView.layer.cornerRadius = 5 * kWidthFactor;
    markView.layer.masksToBounds = YES;
    UILabel *tagTitle = [UILabel new];
    tagTitle.text = _dynamic.affair.tagName;
    tagTitle.font = SourceHanSerifRegularFont(10 * kWidthFactor);
    tagTitle.textColor = KColor_White;
    [_tagBtn addSubview:tagTitle];
    [tagTitle mas_makeConstraints:^(MASConstraintMaker *make) {
      make.centerY.equalTo(_tagBtn);
      make.left.equalTo(markView.mas_right).offset(4 * kWidthFactor);
      make.right.equalTo(_tagBtn).offset(-5 * kWidthFactor);
    }];
    [_tagBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.right.equalTo(self.contentView).offset(-24 * kWidthFactor);
      make.centerY.equalTo(_nameLabel);
      make.height.mas_equalTo(18 * kWidthFactor);
    }];
  } else {
    _tagBtn.hidden = YES;
  }

  [_actionBar mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.right.equalTo(self.contentView);
    make.height.mas_equalTo(24 * kWidthFactor);
    make.top.equalTo(lastView.mas_bottom).offset(12 * kWidthFactor);
  }];
  lastView = _actionBar;

  if (onlyTime) {
    _timeLabel.hidden = NO;
    _locationLabel.hidden = YES;
    _timeLabel.text = [NemoUtil
        distanceTimeWithBeforeTime:[_dynamic.affair.dateline doubleValue]];
    [_timeLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(_actionBar).offset(24 * kWidthFactor);
      make.centerY.equalTo(_actionBar);
      make.height.mas_equalTo(18 * kWidthFactor);
    }];
  } else {
    _timeLabel.hidden = YES;
    _locationLabel.hidden = NO;
    NSString *city = _dynamic.user.city ?: @"";
    double timestamp = [_dynamic.affair.dateline doubleValue];
    if (timestamp == 0) {
      timestamp = [_dynamic.affair.create_time doubleValue];
    }
    NSString *time = [NemoUtil distanceTimeWithBeforeTime:timestamp];
    _locationLabel.text = [NSString stringWithFormat:@"%@・%@", city, time];
    [_locationLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(_actionBar).offset(24 * kWidthFactor);
      make.centerY.equalTo(_actionBar);
      make.height.mas_equalTo(18 * kWidthFactor);
    }];
  }

  [_commentBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.right.equalTo(_actionBar).offset(-40 * kWidthFactor);
    make.centerY.equalTo(_actionBar);
    make.size.mas_equalTo(CGSizeMake(16 * kWidthFactor, 16 * kWidthFactor));
  }];

  [_vommentLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(_commentBtn.mas_right).offset(6 * kWidthFactor);
    make.centerY.equalTo(_actionBar);
  }];

  [_awesomeBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.right.equalTo(_commentBtn.mas_left).offset(-40 * kWidthFactor);
    make.centerY.equalTo(_actionBar);
    make.size.mas_equalTo(CGSizeMake(16 * kWidthFactor, 16 * kWidthFactor));
  }];

  [_awesomeLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(_awesomeBtn.mas_right).offset(6 * kWidthFactor);
    make.centerY.equalTo(_actionBar);
  }];

  if ([_dynamic.affair.likeRs isEqualToString:@"0"]) {
    [_awesomeBtn setImage:KImage_name(@"Unlike") forState:UIControlStateNormal];
  } else {
    [_awesomeBtn setImage:KImage_name(@"like") forState:UIControlStateNormal];
  }
  _awesomeLabel.text = [_dynamic.affair.likeNum isEqualToString:@"0"]
                           ? @""
                           : _dynamic.affair.likeNum;

  if ([_dynamic.affair.commentRs isEqualToString:@"0"]) {
    [_commentBtn setImage:KImage_name(@"Comment")
                 forState:UIControlStateNormal];
  } else {
    [_commentBtn setImage:KImage_name(@"CommentNum")
                 forState:UIControlStateNormal];
  }

  _vommentLabel.text = [_dynamic.affair.commentNum isEqualToString:@"0"]
                           ? @""
                           : _dynamic.affair.commentNum;

  _separatorLineView.backgroundColor = KColor_ExtraLightGray;

  [_separatorLineView mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.right.equalTo(self.contentView);
    make.top.equalTo(lastView.mas_bottom).offset(12 * kWidthFactor);
    make.height.mas_equalTo(4 * kWidthFactor);
    make.bottom.equalTo(self.contentView).priority(999);
  }];

  if ([_dynamic.affair.top isEqualToString:@"1"]) {
    self.topTriangleImageView.hidden = NO;
  } else {
    self.topTriangleImageView.hidden = YES;
  }
}

- (void)updateLikeStatus:(NSString *)likeRs likeNum:(NSString *)likeNum {
  if ([likeRs isEqualToString:@"0"]) {
    [_awesomeBtn setImage:KImage_name(@"Unlike") forState:UIControlStateNormal];
  } else {
    [_awesomeBtn setImage:KImage_name(@"like") forState:UIControlStateNormal];
  }
  _awesomeLabel.text = [likeNum isEqualToString:@"0"] ? @"" : likeNum;
}

- (void)setTapIconAction:(void (^)(NSString *_Nonnull))tapIconAction {
  _tapIconAction = tapIconAction;
  _iconImage.userInteractionEnabled = _tapIconAction != nil;
}

#pragma mark - Action
- (void)tapAction:(UITapGestureRecognizer *)sender {
  if (self.tapIconAction) {
    self.tapIconAction(self.dynamic.user.uid);
  }
}

@end