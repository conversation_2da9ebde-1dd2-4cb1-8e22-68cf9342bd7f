#import "OperationTableViewCell.h"
#import <SDWebImage/SDWebImage.h>

@implementation OperationTableViewCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style
              reuseIdentifier:(NSString *)reuseIdentifier {
  self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
  if (self) {
    self.backgroundColor = [UIColor clearColor];

    UIView *bgView = [[UIView alloc] init];
    bgView.backgroundColor = [UIColor colorWithRed:245 / 255.0
                                             green:245 / 255.0
                                              blue:245 / 255.0
                                             alpha:1.0];
    bgView.layer.cornerRadius = 12 * kWidthFactor;
    bgView.clipsToBounds = YES;
    [self.contentView addSubview:bgView];

    [bgView mas_makeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.contentView).offset(15 * kWidthFactor);
      make.right.equalTo(self.contentView).offset(-15 * kWidthFactor);
      make.top.equalTo(self.contentView).offset(10 * kWidthFactor);
      make.bottom.equalTo(self.contentView).offset(-10 * kWidthFactor);
    }];

    [self loadCellView:bgView];
  }
  return self;
}

- (void)loadCellView:(UIView *)bgView {
  if (!_titleLabel) {
    _titleLabel = [[UILabel alloc] init];
    _titleLabel.font = [UIFont boldSystemFontOfSize:18 * kWidthFactor];
    _titleLabel.textColor = KColor_HighBlack;
    [bgView addSubview:_titleLabel];
  }
  [_titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(bgView).offset(15 * kWidthFactor);
    make.left.equalTo(bgView).offset(15 * kWidthFactor);
    make.right.equalTo(bgView).offset(-15 * kWidthFactor);
  }];

  if (!_coverImageView) {
    _coverImageView = [[UIImageView alloc] init];
    _coverImageView.contentMode = UIViewContentModeScaleAspectFill;
    _coverImageView.layer.cornerRadius = 8 * kWidthFactor;
    _coverImageView.clipsToBounds = YES;
    [bgView addSubview:_coverImageView];
  }
  [_coverImageView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(bgView).offset(15 * kWidthFactor);
    make.width.height.equalTo(@80);
  }];

  if (!_contentLabel) {
    _contentLabel = [[UILabel alloc] init];
    _contentLabel.font = SourceHanSerifRegularFont(14 * kWidthFactor);
    _contentLabel.textColor = [UIColor darkGrayColor];
    _contentLabel.numberOfLines = 5 * kWidthFactor;
    _contentLabel.lineBreakMode = NSLineBreakByTruncatingTail;
    [bgView addSubview:_contentLabel];
  }
  [_contentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(self.titleLabel.mas_bottom).offset(6 * kWidthFactor);
    make.left.equalTo(self.coverImageView.mas_right).offset(12 * kWidthFactor);
    make.right.equalTo(self.titleLabel);
    make.height.equalTo(@(110));
  }];

  [_coverImageView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.centerY.equalTo(self.contentLabel);
  }];

  if (!_timeLabel) {
    _timeLabel = [[UILabel alloc] init];
    _timeLabel.font = SourceHanSerifRegularFont(12 * kWidthFactor);
    _timeLabel.textColor = KColor_Gray;
    _timeLabel.textAlignment = NSTextAlignmentRight;
    [bgView addSubview:_timeLabel];
  }
  [_timeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(self.contentLabel.mas_bottom).offset(8 * kWidthFactor);
    make.right.equalTo(self.titleLabel);
    make.bottom.equalTo(bgView).offset(-15 * kWidthFactor);
  }];
}

- (void)setupWithItem:(OperationItemResult *)item {
  self.titleLabel.text = item.title;

  self.contentLabel.text = item.content;

  NSDate *date =
      [NSDate dateWithTimeIntervalSince1970:[item.create_time doubleValue]];
  self.timeLabel.text =
      [NemoUtil distanceTimeWithBeforeTime:[item.create_time doubleValue]];

  if (item.images && ![item.images isEqualToString:@""]) {
    self.coverImageView.hidden = NO;
    [self.coverImageView
        sd_setImageWithURL:[NSURL URLWithString:item.images]
          placeholderImage:[UIImage imageNamed:@"placeholder_image"]];
    [_contentLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.top.equalTo(self.titleLabel.mas_bottom).offset(6 * kWidthFactor);
      make.left.equalTo(self.coverImageView.mas_right)
          .offset(12 * kWidthFactor);
      make.right.equalTo(self.titleLabel);
      make.height.equalTo(@(110));
    }];
  } else {
    self.coverImageView.hidden = YES;
    [_contentLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.top.equalTo(self.titleLabel.mas_bottom).offset(6 * kWidthFactor);
      make.left.equalTo(self.titleLabel);
      make.right.equalTo(self.titleLabel);
      make.height.equalTo(@(110));
    }];
  }
}

- (void)prepareForReuse {
  [super prepareForReuse];
  self.titleLabel.text = nil;
  self.contentLabel.text = nil;
  self.timeLabel.text = nil;
  self.coverImageView.image = nil;
}

@end