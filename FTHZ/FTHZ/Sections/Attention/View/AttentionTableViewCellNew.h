#import "AffairListModel.h"
#import "HZPhotoGroup.h"
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface AttentionTableViewCellNew : UITableViewCell <HZPhotoGroupDelegate>
@property(nonatomic, strong) FlatButton *awesomeBtn;
@property(nonatomic, strong) FlatButton *commentBtn;
@property(nonatomic, strong) FlatButton *tagBtn;
@property(nonatomic, strong) UILabel *topLabel;

@property(nonatomic, copy) void (^tapIconAction)(NSString *uid);

- (void)setDynamic:(DynamicModelResult *)dynamic showTime:(BOOL)onlyTime;
- (void)updateLikeStatus:(NSString *)likeRs likeNum:(NSString *)likeNum;
@end

NS_ASSUME_NONNULL_END
