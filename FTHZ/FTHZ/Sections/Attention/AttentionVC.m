#import "AttentionVC.h"
#import "AffairFollowListModel.h"
#import "AttentionDetailVC.h"
#import "AttentionTableViewCell.h"
#import "DJStatusBarHUD.h"
#import "WhaleDetailVC.h"

#import "DoLikeModel.h"

#import "TagAffairVC.h"
#import "_2hz-Swift.h"

#define AttentionTableViewCellID @"AttentionVCTableViewCellID"
#define POST_ICON @"post_icon"

#define kRealWidth(width) (width * kMainTemp)

@interface AttentionVC () <UITableViewDelegate, UITableViewDataSource,
                           MomentDelegate> {
}

@property(nonatomic, strong) NSMutableArray *cellData;
@property(nonatomic, strong) AffairListModelResult *tempMember;
@property(nonatomic, strong) UITableView *tableView;
@property(nonatomic, copy) NSString *lastId;
@property(nonatomic, assign) BOOL isFirstLoad;

@end

@implementation AttentionVC
- (void)loadData {
  if (!self.cellData) {
    self.cellData = [NSMutableArray new];
  }
  @weakify(self);

  [AffairFollowListModel getAffairFollowListNewModel:self.lastId
      success:^(NSDictionary *resultObject) {
        @strongify(self);
        AffairFollowListModel *member =
            [AffairFollowListModel mj_objectWithKeyValues:resultObject];

        if ([member.success boolValue]) {
          if (!self.lastId) {
            [self.cellData removeAllObjects];
          }

          self.tempMember = [AffairListModelResult
              mj_objectWithKeyValues:member.data.firstObject];

          for (int i = 0; i < self.tempMember.data.count; i++) {
            DynamicModelResult *dy = [DynamicModelResult
                mj_objectWithKeyValues:[self.tempMember.data objectAtIndex:i]];
            [self.cellData addObject:dy];
          }

          if (self.tempMember.data.count > 0) {
            DynamicModelResult *lastItem = [self.tempMember.data lastObject];
            if (lastItem && lastItem.affair.aid.length > 0) {
              self.lastId = lastItem.affair.aid;
            }

            self.isFirstLoad = NO;
          }

          if ([self.tempMember.count integerValue] > self.cellData.count) {
            if (!self.tableView.mj_footer) {
              MJChiBaoZiFooter *footer = [MJChiBaoZiFooter
                  footerWithRefreshingTarget:self
                            refreshingAction:@selector(loadMoreData)];
              footer.refreshingTitleHidden = YES;
              self.tableView.mj_footer = footer;
            }
          } else {
            [self.tableView.mj_footer removeFromSuperview];
            self.tableView.mj_footer = nil;
          }
        } else {
          [self showToastFast:member.msg];
        }

        [self.tableView.mj_header endRefreshing];
        [self.tableView.mj_footer endRefreshing];
        [self.tableView reloadData];
      }
      failure:^(NSError *requestErr) {
        @strongify(self);
        [self.tableView.mj_header endRefreshing];
        [self.tableView.mj_footer endRefreshing];
        [self showToastFast:@"数据有误,请检查网络后重试"];
      }];
}

- (void)viewDidLoad {
  [super viewDidLoad];
  self.tableView.estimatedRowHeight = 200;
  self.tableView.backgroundColor = [UIColor whiteColor];
  self.tableView.rowHeight = UITableViewAutomaticDimension;
  self.isFirstLoad = YES;

  if (self.mainTempScale == 0) {
    self.mainTempScale = kMainTemp;
  }

  [self.view addSubview:self.tableView];

  CGFloat headerHeight = [self isNotchScreen] ? (108 * self.mainTempScale)
                                              : (84 * self.mainTempScale);

  MJChiBaoZiHeader *header =
      [MJChiBaoZiHeader headerWithRefreshingTarget:self
                                  refreshingAction:@selector(LoadupData)];
  header.lastUpdatedTimeLabel.hidden = YES;
  header.stateLabel.hidden = YES;
  header.ignoredScrollViewContentInsetTop = -headerHeight;
  [header beginRefreshing];
  self.tableView.mj_header = header;

  [NOTIFICENTER addObserver:self
                   selector:@selector(reloadAllUI)
                       name:AllReloadUI
                     object:nil];
  [NOTIFICENTER addObserver:self
                   selector:@selector(reloadAllUI)
                       name:MyUserAffReload
                     object:nil];
  [NOTIFICENTER addObserver:self
                   selector:@selector(LoadindexData)
                       name:ReloadIndexTwo
                     object:nil];

  UIView *headerView = [[UIView alloc]
      initWithFrame:CGRectMake(0, 0, self.view.frame.size.width, headerHeight)];
  headerView.backgroundColor = [UIColor clearColor];
  self.tableView.tableHeaderView = headerView;
}

- (void)reloadAllUI {
  if (_tableView) {
    [self LoadupData];
  }
}

- (void)LoadindexData {
  [self.tableView.mj_header beginRefreshing];
}
- (void)LoadupData {
  self.lastId = nil;
  [self loadData];
}
- (void)loadMoreData {
  if (self.lastId.length > 0) {
    [self loadData];
  } else {
    [self.tableView.mj_footer endRefreshing];
    [self.view makeToast:@"没有更多内容了"
                duration:1.0
                position:CSToastPositionCenter];
  }
}

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];
  [self.navigationController setNavigationBarHidden:YES animated:YES];
  [AppConfig statusbarStyle:YES];
}

- (UITableView *)tableView {
  if (!_tableView) {
    _tableView = [[UITableView alloc] initWithFrame:CGRectZero
                                              style:UITableViewStyleGrouped];
    _tableView.delegate = self;
    _tableView.dataSource = self;
    [_tableView registerClass:[AttentionTableViewCell class]
        forCellReuseIdentifier:AttentionTableViewCellID];
    _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;

    _tableView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:_tableView];

    [_tableView.topAnchor constraintEqualToAnchor:self.view.topAnchor].active =
        YES;
    [_tableView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor]
        .active = YES;
    [_tableView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor]
        .active = YES;
    [_tableView.bottomAnchor constraintEqualToAnchor:self.view.bottomAnchor]
        .active = YES;

    if (@available(iOS 11.0, *)) {
      _tableView.contentInsetAdjustmentBehavior =
          UIScrollViewContentInsetAdjustmentNever;
    } else {
      self.automaticallyAdjustsScrollViewInsets = NO;
    }
  }
  return _tableView;
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
  CGPoint point = [scrollView.panGestureRecognizer translationInView:self.view];

  if (point.y > 0) {
    //    [UIView animateWithDuration:0.3
    //                     animations:^{
    //                       self.postBt.alpha = 1.0;
    //                     }];
  } else if (point.y < 0) {
    //    [UIView animateWithDuration:0.3
    //                     animations:^{
    //                       self.postBt.alpha = 0.0;
    //                     }];
  }
}

#pragma mark - Table view data source
- (CGFloat)tableView:(UITableView *)tableView
    heightForFooterInSection:(NSInteger)section {

  return 10.0;
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForHeaderInSection:(NSInteger)section {
  if (_cellData.count == 0) {
    return kMainHeight * 0.8;
  }
  return 0;
}

- (UIView *)tableView:(UITableView *)tableView
    viewForFooterInSection:(NSInteger)section {
  return [[UIView alloc] initWithFrame:CGRectZero];
}

- (UIView *)tableView:(UITableView *)tableView
    viewForHeaderInSection:(NSInteger)section {
  if (_cellData.count == 0) {
    UIView *bgView = [[UIView alloc]
        initWithFrame:CGRectMake(0, 0, kMainWidth, kMainHeight)];
    UIImageView *empty = [[UIImageView alloc] init];
    empty.image = KImage_name(@"Bigf");
    [bgView addSubview:empty];
    [empty mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.centerX.equalTo(bgView);
      make.centerY.equalTo(bgView).offset(-10 * kMainTemp);

      make.size.mas_equalTo(CGSizeMake(kMainWidth * 0.6, kMainWidth * 0.6));
    }];
    return bgView;
  }
  return nil;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
  return 1;
}

- (NSInteger)tableView:(UITableView *)tableView
    numberOfRowsInSection:(NSInteger)section {
  return _cellData.count + 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView
         cellForRowAtIndexPath:(NSIndexPath *)indexPath {
  if (indexPath.row == _cellData.count) {
    UITableViewCell *cell =
        [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault
                               reuseIdentifier:@"EmptyCell"];
    cell.backgroundColor = [UIColor clearColor];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    return cell;
  }

  DynamicModelResult *dy = [_cellData objectAtIndex:indexPath.row];
  AttentionTableViewCell *cell = (AttentionTableViewCell *)[tableView
      dequeueReusableCellWithIdentifier:AttentionTableViewCellID
                           forIndexPath:indexPath];
  if (!cell) {
    cell = [[AttentionTableViewCell alloc]
          initWithStyle:(UITableViewCellStyleDefault)
        reuseIdentifier:AttentionTableViewCellID];
  }
  [cell setDynamic:dy showTime:NO];
  [cell.awesomeBtn addTarget:self
                      action:@selector(onTouchBtnInCell:)
            forControlEvents:(UIControlEventTouchUpInside)];
  cell.commentBtn.tag = indexPath.row;
  [cell.commentBtn addTarget:self
                      action:@selector(onTouchContentCell:)
            forControlEvents:(UIControlEventTouchUpInside)];

  @weakify(self);
  cell.tapIconAction = ^(NSString *_Nonnull uid) {
    @strongify(self);
    WhaleDetailVC *infoVC = [[WhaleDetailVC alloc] init];
    infoVC.uid = uid;
    [self.navigationController pushViewController:infoVC animated:YES];
  };
  return cell;
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForRowAtIndexPath:(NSIndexPath *)indexPath {
  if (indexPath.row == _cellData.count) {
    return 49 * kMainTemp;
  }
  return UITableViewAutomaticDimension;
}

- (void)onTouchBtnInCell:(UIButton *)sender {
  CGPoint point = sender.center;
  point = [self.tableView convertPoint:point fromView:sender.superview];
  NSIndexPath *indexpath = [self.tableView indexPathForRowAtPoint:point];
  DynamicModelResult *dy = [_cellData objectAtIndex:indexpath.row];
  [self doLike:dy.affair.aid uid:dy.user.uid indexRow:indexpath.row];
}

- (void)onTouchBtnTagInCell:(UIButton *)sender {
  CGPoint point = sender.center;
  point = [self.tableView convertPoint:point fromView:sender.superview];
  NSIndexPath *indexpath = [self.tableView indexPathForRowAtPoint:point];
  DynamicModelResult *dy = [_cellData objectAtIndex:indexpath.row];
  TagAffairVC *ddVC = [[TagAffairVC alloc] init];
  ddVC.tagId = dy.affair.tagType;
  ddVC.tagName = dy.affair.tagName;
  [self.navigationController pushViewController:ddVC animated:YES];
}

- (void)onTouchContentCell:(UIButton *)sender {
  DynamicModelResult *dy = [_cellData objectAtIndex:sender.tag];
  [self gotoDetailVC:dy with:YES];
}

- (void)doLike:(NSString *)aid
           uid:(NSString *)uid
      indexRow:(NSInteger)indexRow {
  DynamicModelResult *dy = [_cellData objectAtIndex:indexRow];
  __weak typeof(self) wSelf = self;
  void (^action)(NSDictionary *) = ^(NSDictionary *resultObject) {
    DoLikeModel *member = [DoLikeModel mj_objectWithKeyValues:resultObject];
    if ([member.success boolValue]) {
      if ([dy.affair.likeRs isEqualToString:@"0"]) {
        NSInteger tepLike = [dy.affair.likeNum integerValue];
        tepLike++;
        dy.affair.likeNum = [NSString stringWithFormat:@"%ld", tepLike];
        dy.affair.likeRs = @"1";
        NSMutableArray *newArray = [wSelf.cellData mutableCopy];
        [newArray replaceObjectAtIndex:indexRow withObject:dy];
        wSelf.cellData = newArray;
      } else {
        NSInteger tepLike = [dy.affair.likeNum integerValue];
        tepLike--;
        dy.affair.likeNum = [NSString stringWithFormat:@"%ld", tepLike];
        dy.affair.likeRs = @"0";
        NSMutableArray *newArray = [wSelf.cellData mutableCopy];
        [newArray replaceObjectAtIndex:indexRow withObject:dy];
        wSelf.cellData = newArray;
      }
      NSIndexPath *indexPath = [NSIndexPath indexPathForRow:indexRow
                                                  inSection:0];
      [wSelf.tableView
          reloadRowsAtIndexPaths:[NSArray arrayWithObjects:indexPath, nil]
                withRowAnimation:UITableViewRowAnimationNone];
    } else if ([member.code integerValue] == FTHZErrorCodeAccountBanned) {
      ShowBanTip(member.msg);
    } else {
      [wSelf.view makeToast:member.msg
                   duration:1.0
                   position:CSToastPositionCenter];
    }
  };
  if ([dy.affair upvoted]) {
    [DoLikeModel postUnlikeModel:uid
                       contentid:aid
                         success:action
                         failure:^(NSError *requestErr) {
                           [self.view makeToast:@"数据有误,请检查网络后重试"
                                       duration:2.0
                                       position:CSToastPositionCenter];
                         }];
  } else {
    [DoLikeModel postLikeModel:uid
                     contentid:aid
                       success:action
                       failure:^(NSError *requestErr) {
                         [self.view makeToast:@"数据有误,请检查网络后重试"
                                     duration:2.0
                                     position:CSToastPositionCenter];
                       }];
  }
}

- (void)tableView:(UITableView *)tableView
    didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
  DynamicModelResult *dy = [_cellData objectAtIndex:indexPath.row];
  [self gotoDetailVC:dy with:NO];
  [tableView deselectRowAtIndexPath:indexPath animated:YES];
}

- (void)gotoDetailVC:(DynamicModelResult *)dy with:(BOOL)comment {
  FZMomentVC *momentVC = [[FZMomentVC alloc] init];
  momentVC.userId = dy.user.uid;
  momentVC.momentId = dy.affair.aid;
  momentVC.delegate = self;
  momentVC.withComment = comment;
  [self.navigationController pushViewController:momentVC animated:YES];
}

- (void)needRefreshList {
  [self LoadindexData];
}

- (BOOL)isNotchScreen {
  if (@available(iOS 11.0, *)) {
    UIWindow *window = [UIApplication sharedApplication].windows.firstObject;
    return window.safeAreaInsets.top > 20;
  }
  return NO;
}
@end
