#import "CurrencyNavVC.h"
#import "CurrencyRootVC.h"
#import "LoginBusiness.h"

NS_ASSUME_NONNULL_BEGIN

typedef void (^FTHZLoginResultCallback)(FTHZLoginResult result,
                                        UserPersonResult *_Nullable user,
                                        NSError *_Nullable error);

@protocol FTHZLoginResultDelegate <NSObject>

- (void)loginViewController:(UIViewController *)viewController
    didFinishedLoginWithResult:(FTHZLoginResult)result
                          user:(UserPersonResult *_Nullable)user
                         error:(NSError *_Nullable)error;

@end

@interface FTHZLoginViewController : CurrencyNavVC <FTHZLoginResultDelegate>

@property(nonatomic, copy, nullable) FTHZLoginResultCallback callback;

@end

@interface LoginVC : CurrencyRootVC

typedef NS_ENUM(NSInteger, LoginMode) {
  LoginModePhone = 0,
  LoginModeAccount = 1
};

@property(nonatomic, assign) LoginMode loginMode;
@property(nonatomic, strong) UITextField *accountTxd;
@property(nonatomic, strong) UITextField *passwordTxd;
@property(nonatomic, assign) BOOL goNextView;

- (void)needInvitationCode;
@end

NS_ASSUME_NONNULL_END
