#import "verifyViewController.h"
#import "VerifyTableViewCell.h"
#import "verifyModel.h"
#define VerifyDetailVCID @"VerifyDetailVCID"

#define SCREENWIDTH [UIScreen mainScreen].bounds.size.width
#define SCREENHEIGHT [UIScreen mainScreen].bounds.size.height

@interface verifyViewController () <UITableViewDelegate,
                                    UITableViewDataSource> {
  NSInteger currentPage;
}
@property(nonatomic, strong) NSMutableArray *cellData;
@property(nonatomic, strong) UITableView *tableView;
@property(nonatomic, strong) NSMutableArray *answerArray;
@property(nonatomic, strong) NSMutableArray *postArray;
@property(nonatomic, assign) NSInteger answerCount;
@end

@implementation verifyViewController

- (void)loadData {
  if (!_cellData) {
    _cellData = [NSMutableArray new];
  }

  @weakify(self);

  [VerifyNotificationModel getVerifyNotificationModel:self.phoneNum
      success:^(NSDictionary *resultObject) {
        @strongify(self);

        if (self->currentPage == 1) {
          [self.cellData removeAllObjects];
        }

        VerifyNotificationModel *memeber =
            [VerifyNotificationModel mj_objectWithKeyValues:resultObject];

        if ([memeber.success boolValue]) {
          self.answerCount = memeber.data.count;
          for (int i = 0; i < memeber.data.count; i++) {
            VerifyModel *dy = [VerifyModel
                mj_objectWithKeyValues:[memeber.data objectAtIndex:i]];
            [self.cellData addObject:dy];
          }
        }

        [self.view viewWithTag:100].hidden = (self.cellData.count == 0);

        [self.tableView.mj_header endRefreshing];
        [self.tableView.mj_footer endRefreshing];
        [self.tableView reloadData];
      }
      failure:^(NSError *requestErr) {
        [self.tableView.mj_header endRefreshing];
        [self.tableView.mj_footer endRefreshing];
        [self showToastFast:@"数据有误,请检查网络后重试"];
      }];
}

- (void)viewDidLoad {
  [super viewDidLoad];
  self.title = @"账号验证";
  self.answerArray = [[NSMutableArray alloc] init];
  self.postArray = [[NSMutableArray alloc] init];
  [[NSNotificationCenter defaultCenter] addObserver:self
                                           selector:@selector(buttonClickedV2:)
                                               name:@"VerifyTableViewCell"
                                             object:nil];
  [[NSNotificationCenter defaultCenter] addObserver:self
                                           selector:@selector(buttonClickedV3:)
                                               name:@"VerifyTableViewCellV2"
                                             object:nil];
  [self.safeContentView addSubview:self.tableView];

  [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.right.left.bottom.equalTo(self.safeContentView);
    make.top.equalTo(self.safeContentView).offset(24 * kWidthFactor);
  }];

  UIButton *btn = [[UIButton alloc] init];
  [self.safeContentView addSubview:btn];
  [btn setTitleColor:KColor_White forState:UIControlStateNormal];
  [btn setTitle:@"提交" forState:UIControlStateNormal];
  btn.contentHorizontalAlignment = UIControlContentHorizontalAlignmentCenter;
  btn.titleLabel.font = SourceHanSerifRegularFont(20);
  [btn mas_makeConstraints:^(MASConstraintMaker *make) {
    make.bottom.mas_equalTo(self.safeContentView.bottom)
        .offset(-24 * kWidthFactor);
    make.centerX.mas_equalTo(self.safeContentView);
    make.size.mas_equalTo(CGSizeMake(120 * kWidthFactor, 50 * kWidthFactor));
  }];
  btn.layer.cornerRadius = 25;
  [btn setBackgroundColor:KColor_HighBlack];
  [btn addTarget:self
                action:@selector(buttonClicked:)
      forControlEvents:UIControlEventTouchUpInside];
  btn.tag = 100;
  btn.hidden = (_cellData.count == 0);
  [self loadData];
}

- (void)cancelAction {
  [self.navigationController popViewControllerAnimated:YES];
}

- (void)loadnewMessage {
  if (_tableView) {
    [self loadData];
  }
}

- (void)loadMessageData {
  [self loadData];
}

- (void)loadMoreData {
  [self loadData];
}

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];
  [self.navigationController setNavigationBarHidden:NO animated:NO];
  self.showBackBtn = YES;
  [AppConfig statusbarStyle:YES];
  self.edgesForExtendedLayout = UIRectEdgeNone;
}

- (UITableView *)tableView {
  if (!_tableView) {
    _tableView = [[UITableView alloc] initWithFrame:CGRectZero
                                              style:(UITableViewStylePlain)];
    _tableView.delegate = self;
    _tableView.dataSource = self;
    [_tableView registerClass:[VerifyTableViewCell class]
        forCellReuseIdentifier:VerifyDetailVCID];
    _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
  }
  _tableView.rowHeight = UITableViewAutomaticDimension;
  return _tableView;
}

#pragma mark - Table view data source
- (CGFloat)tableView:(UITableView *)tableView
    heightForFooterInSection:(NSInteger)section {
  return 10.0;
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForHeaderInSection:(NSInteger)section {
  if (_cellData.count == 0) {
    return 96 * kWidthFactor;
  }

  return 0.1 * kWidthFactor;
}

- (UIView *)tableView:(UITableView *)tableView
    viewForFooterInSection:(NSInteger)section {
  return [[UIView alloc] initWithFrame:CGRectZero];
}

- (UIView *)tableView:(UITableView *)tableView
    viewForHeaderInSection:(NSInteger)section {
  if (_cellData.count == 0) {
    UIView *bgView = [[UIView alloc]
        initWithFrame:CGRectMake(0, 0, kMainWidth, 96 * kWidthFactor)];
    UILabel *tipL = [UILabel new];
    tipL.text = @"请联系官方邮箱: <EMAIL>";
    tipL.font = SourceHanSerifMediumFont(14 * kWidthFactor);
    tipL.textColor = KColor_textTinyGray;
    [bgView addSubview:tipL];
    [tipL mas_makeConstraints:^(MASConstraintMaker *make) {
      make.centerX.bottom.equalTo(bgView);
    }];
    return bgView;
  }

  UIView *bgView = [[UIView alloc]
      initWithFrame:CGRectMake(0, 0, kMainWidth, 0.1 * kWidthFactor)];
  bgView.backgroundColor = UIColor.whiteColor;
  return bgView;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
  return 1;
}

- (NSInteger)tableView:(UITableView *)tableView
    numberOfRowsInSection:(NSInteger)section {
  return _cellData.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView
         cellForRowAtIndexPath:(NSIndexPath *)indexPath {
  VerifyModel *dy = [_cellData objectAtIndex:indexPath.row];
  VerifyTableViewCell *cell = (VerifyTableViewCell *)[tableView
      dequeueReusableCellWithIdentifier:VerifyDetailVCID
                           forIndexPath:indexPath];

  if (!cell) {
    cell =
        [[VerifyTableViewCell alloc] initWithStyle:(UITableViewCellStyleDefault)
                                   reuseIdentifier:VerifyDetailVCID];
  }

  cell.messageData = dy;
  return cell;
}

- (void)tableView:(UITableView *)tableView
    didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
}

- (void)buttonClicked:(UIButton *)sender {
  if (self.answerArray.count < self.answerCount) {
    [self.view makeToast:@"请完成全部问题"
                duration:1.0
                position:CSToastPositionCenter];
  } else {
    for (UIButton *btn in _answerArray) {
      NSString *cStr = [NSString stringWithFormat:@"%ld", (long)btn.tag];
      [self.postArray addObject:cStr];
    }
    NSString *postArrayStr = [self.postArray componentsJoinedByString:@","];
    [VerifyModelQa postVerifyNotificationModel:self.phoneNum
        qa:postArrayStr
        success:^(NSDictionary *resultObject) {
          VerifyModelQa *member =
              [VerifyModelQa mj_objectWithKeyValues:resultObject];
          if ([member.success boolValue]) {
            VerifyModelQaMsg *qars = [VerifyModelQaMsg
                mj_objectWithKeyValues:member.data.firstObject];
            [self.view makeToast:qars.message
                        duration:1.0
                        position:CSToastPositionCenter];
          } else {
            [self.view makeToast:member.msg
                        duration:1.0
                        position:CSToastPositionCenter];
          }
        }
        failure:^(NSError *requestErr) {
          [self showToastFast:@"数据有误,请检查网络后重试"];
        }];
    [self.postArray removeAllObjects];

    [self performSelector:@selector(runDelayMethod)
               withObject:nil
               afterDelay:2.0];
  }
}

- (void)runDelayMethod {
  [self.navigationController popViewControllerAnimated:YES];
}

- (void)buttonClickedV2:(NSNotification *)note {
  [self.answerArray addObject:note.object];
}

- (void)buttonClickedV3:(NSNotification *)note {
  [self.answerArray removeObject:note.object];
}

- (void)dealloc {
  [[NSNotificationCenter defaultCenter] removeObserver:self
                                                  name:@"VerifyTableViewCell"
                                                object:nil];
  [[NSNotificationCenter defaultCenter] removeObserver:self
                                                  name:@"VerifyTableViewCellV2"
                                                object:nil];
}

@end
