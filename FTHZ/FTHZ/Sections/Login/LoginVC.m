#import "LoginVC.h"
#import "AccountLoginVC.h"
#import "CommonCodeModel.h"
#import "GenderVC.h"
#import "GetUserinfoModel.h"
#import "HeziChouseOneVC.h"
#import "InvitationStatusModel.h"
#import "VerificationCodeVC.h"
#import "WXLoginModel.h"
#import "XGAuthCode.h"
#import "verifyViewController.h"

@implementation FTHZLoginViewController
- (instancetype)init {
  LoginVC *vc = [[LoginVC alloc] init];

  self = [self initWithRootViewController:vc];
  return self;
}

- (void)loginViewController:(UIViewController *)viewController
    didFinishedLoginWithResult:(FTHZLoginResult)result
                          user:(UserPersonResult *)user
                         error:(NSError *)error {
  FTHZLoginResultCallback cb = self.callback;

  self.callback = nil;

  switch (result) {
  case FTHZLoginResultSuccess:
  case FTHZLoginResultAlreadyLogined: {
    [NOTIFICENTER postNotificationName:DyreloadData object:nil];
    [NOTIFICENTER postNotificationName:ChangeMyUserInfoMation object:nil];
    [NOTIFICENTER postNotificationName:AllReloadUI object:nil];
  } break;

  default:
    break;
  }
  [self dismissViewControllerAnimated:YES
                           completion:^{
                             if (cb) {
                               cb(result, user, error);
                             }
                           }];
}

@end

@interface LoginVC () {
  UIView *agreementView;
  CGFloat keyboardHeight;
}

@property(nonatomic, strong) UITextField *phoneTxd;
@property(nonatomic, strong) XGAuthCode *authcodeImage;
@property(nonatomic, strong) UITextField *codeTxd;
@property(nonatomic, strong) UIButton *agreementCheckbox;
@property(nonatomic, assign) BOOL isAgreementAccepted;
@property(nonatomic, strong) FlatButton *loginBtn;
@property(nonatomic, strong) UILabel *safeTitle;
@property(nonatomic, strong) UIView *accountBgView;
@property(nonatomic, strong) UIView *passwordBgView;
@property(nonatomic, strong) UILabel *accountTitleLabel;
@property(nonatomic, strong) UIButton *toggleLoginModeBtn;
@property(nonatomic, strong) UILabel *phonetitle;
@property(nonatomic, strong) UIView *phoneLoginContainerView;
@property(nonatomic, strong) UIView *accountLoginContainerView;
@property(nonatomic, strong) MASConstraint *phoneLoginLeftConstraint;
@property(nonatomic, strong) MASConstraint *accountLoginLeftConstraint;
@property(nonatomic, strong) FlatButton *accountLoginBtn;
@end

@implementation LoginVC

- (void)viewDidLoad {
  [super viewDidLoad];

  self.loginMode = LoginModePhone;
  self.goNextView = NO;

  [[NSNotificationCenter defaultCenter]
      addObserver:self
         selector:@selector(keyboardWillShow:)
             name:UIKeyboardWillShowNotification
           object:nil];
  [[NSNotificationCenter defaultCenter]
      addObserver:self
         selector:@selector(keyboardWillHide:)
             name:UIKeyboardWillHideNotification
           object:nil];

  UIImageView *whaleIv =
      [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"whaleLogo"]];
  [self.view addSubview:whaleIv];
  [whaleIv mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.view).offset(47 * kWidthFactor);
    make.top.equalTo(self.view).offset(92 * kWidthFactor);
    make.size.mas_equalTo(CGSizeMake(300 * kWidthFactor, 243 * kWidthFactor));
  }];
  whaleIv.hidden = YES;

  UILabel *Label_52 = [[UILabel alloc] init];
  Label_52.textColor = KColor_HighBlack;
  Label_52.text = @"52";
  Label_52.font = AvenirNextHeavyFont(56 * kWidthFactor);
  [whaleIv addSubview:Label_52];
  [Label_52 mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(whaleIv).offset(1 * kWidthFactor);
    make.top.equalTo(whaleIv).offset(26 * kWidthFactor);
  }];
  Label_52.hidden = YES;

  UILabel *Label_hz = [[UILabel alloc] init];
  Label_hz.textColor = KColor_HighBlack;
  Label_hz.text = @"HZ";
  Label_hz.font = AvenirNextHeavyFont(32 * kWidthFactor);
  [whaleIv addSubview:Label_hz];
  [Label_hz mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(Label_52).offset(1 * kWidthFactor);
    make.left.equalTo(Label_52.mas_right).offset(1 * kWidthFactor);
  }];
  Label_hz.hidden = YES;

  self.phonetitle = [[UILabel alloc] init];
  self.phonetitle.textColor = KColor_White;
  self.phonetitle.text = @"手机号码登录";
  self.phonetitle.font = SourceHanSerifRegularFont(20 * kWidthFactor);

  self.phoneLoginContainerView = [[UIView alloc] init];
  self.phoneLoginContainerView.backgroundColor = KColor_HighBlack;
  self.phoneLoginContainerView.layer.cornerRadius = 12 * kWidthFactor;
  self.phoneLoginContainerView.layer.masksToBounds = YES;
  [self.view addSubview:self.phoneLoginContainerView];
  [self.phoneLoginContainerView mas_makeConstraints:^(
                                    MASConstraintMaker *make) {
    self.phoneLoginLeftConstraint =
        make.left.equalTo(self.view).offset(20 * kWidthFactor);
    make.width.equalTo(self.view).offset(-40 * kWidthFactor);
    make.top.equalTo(self.view).offset(kMainHeight * 0.5 - 10 * kWidthFactor);
    make.height.mas_greaterThanOrEqualTo(200 * kWidthFactor);
  }];

  [self.phoneLoginContainerView addSubview:self.phonetitle];
  [self.phonetitle mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.phoneLoginContainerView).offset(18 * kWidthFactor);
    make.top.equalTo(self.phoneLoginContainerView).offset(20 * kWidthFactor);
    make.size.mas_equalTo(CGSizeMake(150 * kWidthFactor, 20 * kWidthFactor));
  }];

  UILabel *phonetop = [[UILabel alloc] init];
  phonetop.textColor = KColor_Black;
  phonetop.text = @"+86";
  phonetop.font = SourceHanSerifMediumFont(16 * kWidthFactor);
  [self.view addSubview:phonetop];
  [phonetop mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.view).offset(36 * kWidthFactor);
    make.top.equalTo(self.phonetitle.mas_bottom).offset(16 * kWidthFactor);
    make.size.mas_equalTo(CGSizeMake(37 * kWidthFactor, 20 * kWidthFactor));
  }];

  _phoneTxd = [[UITextField alloc] init];
  _phoneTxd.placeholder = @"请输入手机号";
  _phoneTxd.font = SourceHanSerifMediumFont(16 * kWidthFactor);
  _phoneTxd.keyboardType = UIKeyboardTypePhonePad;
  _phoneTxd.clearButtonMode = UITextFieldViewModeWhileEditing;
  _phoneTxd.keyboardAppearance = UIKeyboardAppearanceAlert;

  NSString *lastPhone =
      [[NSUserDefaults standardUserDefaults] objectForKey:@"LastLoginPhone"];
  if (lastPhone.length > 0) {
    _phoneTxd.text = lastPhone;
  }

  UIView *phoneBackgroundView = [[UIView alloc] init];
  phoneBackgroundView.backgroundColor = RGB(245, 246, 248);
  phoneBackgroundView.layer.cornerRadius = 8 * kWidthFactor;
  phoneBackgroundView.layer.masksToBounds = YES;
  [self.phoneLoginContainerView addSubview:phoneBackgroundView];

  [phoneBackgroundView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.phoneLoginContainerView).offset(16 * kWidthFactor);
    make.top.equalTo(self.phonetitle.mas_bottom).offset(20 * kWidthFactor);
    make.right.equalTo(self.phoneLoginContainerView).offset(-16 * kWidthFactor);
    make.height.mas_equalTo(45 * kWidthFactor);
  }];

  [phoneBackgroundView addSubview:phonetop];
  [phonetop mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(phoneBackgroundView).offset(15 * kWidthFactor);
    make.centerY.equalTo(phoneBackgroundView);
    make.size.mas_equalTo(CGSizeMake(37 * kWidthFactor, 20 * kWidthFactor));
  }];

  [phoneBackgroundView addSubview:_phoneTxd];
  [_phoneTxd addTarget:self
                action:@selector(textFieldDidChange:)
      forControlEvents:UIControlEventEditingChanged];
  [_phoneTxd mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(phonetop.mas_right).with.offset(10 * kWidthFactor);
    make.centerY.equalTo(phoneBackgroundView);
    make.right.equalTo(phoneBackgroundView).offset(-15 * kWidthFactor);
    make.height.mas_equalTo(30 * kWidthFactor);
  }];

  self.loginBtn = [FlatButton buttonWithType:UIButtonTypeCustom];
  self.loginBtn.backgroundColor = KColor_HighBlack;
  [self.loginBtn setTitle:@"登录" forState:UIControlStateNormal];
  self.loginBtn.titleLabel.font = SourceHanSerifMediumFont(16 * kWidthFactor);
  [self.loginBtn setTitleColor:KColor_White forState:UIControlStateNormal];
  self.loginBtn.layer.cornerRadius = 30 * kWidthFactor;
  self.loginBtn.layer.masksToBounds = YES;
  [self.loginBtn addTarget:self
                    action:@selector(loginin)
          forControlEvents:UIControlEventTouchUpInside];
  [self.view addSubview:self.loginBtn];
  [self.loginBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(self.phoneLoginContainerView.mas_bottom)
        .offset(40 * kWidthFactor);
    make.centerX.equalTo(self.view);
    make.size.mas_equalTo(CGSizeMake(160 * kWidthFactor, 60 * kWidthFactor));
  }];

  UILabel *Label_version = [[UILabel alloc] init];
  Label_version.textColor = KColor_detailLightGray;

  NSDictionary *infoDic = [[NSBundle mainBundle] infoDictionary];
  NSString *appVersion = [infoDic objectForKey:@"CFBundleShortVersionString"];
  Label_version.text = [NSString stringWithFormat:@"Version %@", appVersion];
  Label_version.font = SourceHanSerifRegularFont(12 * kWidthFactor);
  [whaleIv addSubview:Label_version];
  [Label_version mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.bottom.equalTo(self.view.mas_bottom).offset(-40 * kWidthFactor);
    make.centerX.equalTo(self.view);
  }];

  UILabel *Label_zoeVersion = [[UILabel alloc] init];
  Label_zoeVersion.textColor = KColor_detailLightGray;
  Label_zoeVersion.text = @"回归版";
  Label_zoeVersion.font = SourceHanSerifRegularFont(12 * kWidthFactor);
  [whaleIv addSubview:Label_zoeVersion];
  [Label_zoeVersion mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(Label_version.mas_bottom).offset(4 * kWidthFactor);
    make.centerX.equalTo(self.view);
  }];

  NSString *likeStr = @"我已阅读并同意用户协议与隐私政策";
  NSString *str001 = @"我已阅读并同意";
  NSString *str002 = @"用户协议";
  NSString *str003 = @"与";
  NSString *str004 = @"隐私政策";

  NSMutableAttributedString *str =
      [[NSMutableAttributedString alloc] initWithString:likeStr];
  NSRange range1 = [[str string] rangeOfString:str001];
  [str addAttribute:NSFontAttributeName
              value:[UIFont systemFontOfSize:10 * kWidthFactor]
              range:range1];
  NSRange range2 = [[str string] rangeOfString:str002];
  [str addAttribute:NSFontAttributeName
              value:[UIFont systemFontOfSize:10 * kWidthFactor]
              range:range2];
  NSRange range3 = [[str string] rangeOfString:str003];
  [str addAttribute:NSFontAttributeName
              value:[UIFont systemFontOfSize:10 * kWidthFactor]
              range:range3];
  NSRange range4 = [[str string] rangeOfString:str004];
  [str addAttribute:NSFontAttributeName
              value:[UIFont systemFontOfSize:10 * kWidthFactor]
              range:range4];
  [str addAttribute:NSForegroundColorAttributeName
              value:KColor_Black
              range:range1];
  [str addAttribute:NSForegroundColorAttributeName
              value:KColor_heziGreen
              range:range2];
  [str addAttribute:NSForegroundColorAttributeName
              value:KColor_Black
              range:range3];
  [str addAttribute:NSForegroundColorAttributeName
              value:KColor_heziGreen
              range:range4];

  self.safeTitle = [[UILabel alloc] init];
  self.safeTitle.attributedText = str;
  self.safeTitle.textAlignment = NSTextAlignmentLeft;
  [self.view addSubview:self.safeTitle];
  [self.safeTitle mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(self.loginBtn.mas_bottom).offset(22 * kWidthFactor);
    make.centerX.equalTo(self.view);
    make.height.mas_equalTo(15 * kWidthFactor);
  }];

  self.agreementCheckbox = [UIButton buttonWithType:UIButtonTypeCustom];
  CGFloat buttonSize = 12 * kWidthFactor;
  self.agreementCheckbox.layer.cornerRadius = buttonSize / 2;
  self.agreementCheckbox.layer.borderWidth = 1;
  self.agreementCheckbox.layer.borderColor = KColor_Black.CGColor;
  self.agreementCheckbox.clipsToBounds = YES;
  [self.agreementCheckbox addTarget:self
                             action:@selector(toggleAgreementCheckbox:)
                   forControlEvents:UIControlEventTouchUpInside];
  [self.view addSubview:self.agreementCheckbox];

  [self.agreementCheckbox mas_makeConstraints:^(MASConstraintMaker *make) {
    make.right.equalTo(self.safeTitle.mas_left).offset(-4 * kWidthFactor);
    make.centerY.equalTo(self.safeTitle);
    make.size.mas_equalTo(CGSizeMake(12 * kWidthFactor, 12 * kWidthFactor));
  }];

  UIView *loginButtonsContainer = [[UIView alloc] init];
  [self.view addSubview:loginButtonsContainer];
  [loginButtonsContainer mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(self.safeTitle.mas_bottom).offset(15 * kWidthFactor);
    make.centerX.equalTo(self.view);
    make.height.mas_equalTo(40 * kWidthFactor);
    make.width.mas_equalTo(100 * kWidthFactor);
  }];

  self.accountLoginBtn = [FlatButton buttonWithType:UIButtonTypeCustom];
  [self.accountLoginBtn setImage:KImage_name(@"alogin")
                        forState:UIControlStateNormal];
  [self.accountLoginBtn addTarget:self
                           action:@selector(toggleLoginMode)
                 forControlEvents:UIControlEventTouchUpInside];
  [loginButtonsContainer addSubview:self.accountLoginBtn];
  [self.accountLoginBtn mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(loginButtonsContainer);
    make.centerY.equalTo(loginButtonsContainer);
    make.size.mas_equalTo(CGSizeMake(40 * kWidthFactor, 40 * kWidthFactor));
  }];

  FlatButton *wxLoginBtn = [FlatButton buttonWithType:UIButtonTypeCustom];
  [wxLoginBtn setImage:KImage_name(@"wlogin") forState:UIControlStateNormal];
  [wxLoginBtn addTarget:self
                 action:@selector(wxlogin)
       forControlEvents:UIControlEventTouchUpInside];
  [loginButtonsContainer addSubview:wxLoginBtn];
  [wxLoginBtn mas_makeConstraints:^(MASConstraintMaker *make) {
    make.right.equalTo(loginButtonsContainer);
    make.centerY.equalTo(loginButtonsContainer);
    make.size.mas_equalTo(CGSizeMake(40 * kWidthFactor, 40 * kWidthFactor));
  }];

  FlatButton *ubtn = [FlatButton buttonWithType:UIButtonTypeCustom];
  [ubtn addTarget:self
                action:@selector(ubtnAction)
      forControlEvents:UIControlEventTouchUpInside];
  [self.view addSubview:ubtn];
  [ubtn mas_makeConstraints:^(MASConstraintMaker *make) {
    make.centerX.equalTo(self.safeTitle);
    make.centerY.equalTo(self.safeTitle);
    make.size.mas_equalTo(CGSizeMake(40 * kWidthFactor, 25 * kWidthFactor));
  }];
  FlatButton *ybtn = [FlatButton buttonWithType:UIButtonTypeCustom];
  [ybtn addTarget:self
                action:@selector(ybtnAction)
      forControlEvents:UIControlEventTouchUpInside];
  [self.view addSubview:ybtn];
  [ybtn mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(ubtn.mas_right).offset(10 * kWidthFactor);
    make.centerY.equalTo(self.safeTitle);
    make.size.mas_equalTo(CGSizeMake(40 * kWidthFactor, 25 * kWidthFactor));
  }];

  self.accountTitleLabel = [[UILabel alloc] init];
  self.accountTitleLabel.textColor = KColor_White;
  self.accountTitleLabel.text = @"密码登录";
  self.accountTitleLabel.font = SourceHanSerifRegularFont(20 * kWidthFactor);

  self.accountLoginContainerView = [[UIView alloc] init];
  self.accountLoginContainerView.backgroundColor = KColor_HighBlack;
  self.accountLoginContainerView.layer.cornerRadius = 12 * kWidthFactor;
  self.accountLoginContainerView.layer.masksToBounds = YES;
  [self.view addSubview:self.accountLoginContainerView];
  [self.accountLoginContainerView mas_makeConstraints:^(
                                      MASConstraintMaker *make) {
    self.accountLoginLeftConstraint = make.left.equalTo(self.view.mas_right);
    make.width.equalTo(self.view).offset(-40 * kWidthFactor);
    make.top.equalTo(self.view).offset(kMainHeight * 0.5 - 10 * kWidthFactor);
    make.height.mas_greaterThanOrEqualTo(200 * kWidthFactor);
  }];

  [self.accountLoginContainerView addSubview:self.accountTitleLabel];
  [self.accountTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.accountLoginContainerView).offset(18 * kWidthFactor);
    make.top.equalTo(self.accountLoginContainerView).offset(20 * kWidthFactor);
    make.size.mas_equalTo(CGSizeMake(150 * kWidthFactor, 20 * kWidthFactor));
  }];

  UIColor *bgColor = RGB(245, 246, 248);
  CGFloat cornerRadius = 8 * kWidthFactor;
  CGFloat leftMargin = 36 * kWidthFactor;
  CGFloat fieldHeight = 45 * kWidthFactor;

  self.accountBgView = [[UIView alloc] init];
  self.accountBgView.backgroundColor = bgColor;
  self.accountBgView.layer.cornerRadius = cornerRadius;
  self.accountBgView.layer.masksToBounds = YES;
  [self.accountLoginContainerView addSubview:self.accountBgView];
  [self.accountBgView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.accountLoginContainerView).offset(16 * kWidthFactor);
    make.right.equalTo(self.accountLoginContainerView)
        .offset(-16 * kWidthFactor);
    make.top.equalTo(self.accountTitleLabel.mas_bottom)
        .offset(20 * kWidthFactor);
    make.height.mas_equalTo(fieldHeight);
  }];

  self.accountTxd = [[UITextField alloc] init];
  self.accountTxd.placeholder = @"请输入 账号/手机号";
  self.accountTxd.font = SourceHanSerifMediumFont(16 * kWidthFactor);
  self.accountTxd.clearButtonMode = UITextFieldViewModeWhileEditing;
  self.accountTxd.keyboardAppearance = UIKeyboardAppearanceAlert;
  NSString *lastAccount =
      [[NSUserDefaults standardUserDefaults] objectForKey:@"LastLoginAccount"];
  if (lastAccount.length > 0) {
    self.accountTxd.text = lastAccount;
  }
  [self.accountBgView addSubview:self.accountTxd];
  [self.accountTxd mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.accountBgView).offset(15 * kWidthFactor);
    make.right.equalTo(self.accountBgView).offset(-15 * kWidthFactor);
    make.centerY.equalTo(self.accountBgView);
    make.height.mas_equalTo(30 * kWidthFactor);
  }];

  self.passwordBgView = [[UIView alloc] init];
  self.passwordBgView.backgroundColor = bgColor;
  self.passwordBgView.layer.cornerRadius = cornerRadius;
  self.passwordBgView.layer.masksToBounds = YES;
  [self.accountLoginContainerView addSubview:self.passwordBgView];
  [self.passwordBgView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.right.equalTo(self.accountBgView);
    make.top.equalTo(self.accountBgView.mas_bottom).offset(15 * kWidthFactor);
    make.height.equalTo(self.accountBgView);
  }];

  self.passwordTxd = [[UITextField alloc] init];
  self.passwordTxd.placeholder = @"请输入密码";
  self.passwordTxd.font = SourceHanSerifMediumFont(16 * kWidthFactor);
  self.passwordTxd.keyboardAppearance = UIKeyboardAppearanceAlert;
  self.passwordTxd.secureTextEntry = YES;
  UIButton *passwordToggleBtn = [UIButton buttonWithType:UIButtonTypeCustom];
  [passwordToggleBtn setImage:[UIImage imageNamed:@"password_hide"]
                     forState:UIControlStateNormal];
  [passwordToggleBtn setImage:[UIImage imageNamed:@"password_show"]
                     forState:UIControlStateSelected];
  passwordToggleBtn.frame = CGRectMake(0, 0, 30, 30);
  [passwordToggleBtn addTarget:self
                        action:@selector(togglePasswordVisibility:)
              forControlEvents:UIControlEventTouchUpInside];
  self.passwordTxd.rightView = passwordToggleBtn;
  self.passwordTxd.rightViewMode = UITextFieldViewModeAlways;
  [self.passwordBgView addSubview:self.passwordTxd];
  [self.passwordTxd mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.passwordBgView).offset(15 * kWidthFactor);
    make.right.equalTo(self.passwordBgView).offset(-15 * kWidthFactor);
    make.centerY.equalTo(self.passwordBgView);
    make.height.mas_equalTo(30 * kWidthFactor);
  }];

  if (lastPhone.length > 0) {
    dispatch_async(dispatch_get_main_queue(), ^{
      [self textFieldDidChange:self.phoneTxd];
    });
  }
}

- (void)dealloc {
  [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark - 键盘通知处理
- (void)keyboardWillShow:(NSNotification *)notification {
  NSDictionary *userInfo = notification.userInfo;
  NSValue *keyboardFrameValue = userInfo[UIKeyboardFrameEndUserInfoKey];
  CGRect keyboardFrame = [keyboardFrameValue CGRectValue];
  keyboardHeight = keyboardFrame.size.height;

  CGFloat offset = 0;
  if ([self.phoneTxd isFirstResponder]) {
    CGRect phoneFrame = [self.phoneTxd convertRect:self.phoneTxd.bounds
                                            toView:self.view];
    CGFloat bottomY = phoneFrame.origin.y + phoneFrame.size.height;
    offset = bottomY - (self.view.frame.size.height - keyboardHeight - 20);
  } else if ([self.codeTxd isFirstResponder]) {
    CGRect codeFrame = [self.codeTxd convertRect:self.codeTxd.bounds
                                          toView:self.view];
    CGFloat bottomY = codeFrame.origin.y + codeFrame.size.height;
    offset = bottomY - (self.view.frame.size.height - keyboardHeight - 20);
  }

  if (offset > 0) {
    [UIView animateWithDuration:0.3
                     animations:^{
                       self.view.frame =
                           CGRectMake(0, -offset, self.view.frame.size.width,
                                      self.view.frame.size.height);
                     }];
  }
}

- (void)keyboardWillHide:(NSNotification *)notification {
  [UIView animateWithDuration:0.3
                   animations:^{
                     self.view.frame =
                         CGRectMake(0, 0, self.view.frame.size.width,
                                    self.view.frame.size.height);
                   }];
}

- (void)toggleAgreementCheckbox:(UIButton *)sender {
  self.isAgreementAccepted = !self.isAgreementAccepted;
  if (self.isAgreementAccepted) {
    sender.backgroundColor = KColor_Black;
  } else {
    sender.backgroundColor = [UIColor clearColor];
  }
}

- (void)goToAccountLogin {
  AccountLoginVC *vc = [[AccountLoginVC alloc] init];
  vc.loginVC = self;
  [self.navigationController pushViewController:vc animated:YES];
}

- (void)textFieldDidChange:(UITextField *)theTextField {
  if (theTextField.text.length > 11) {
    theTextField.text = [theTextField.text substringToIndex:11];
  }

  NSString *phoneRegex = @"^[0-9]*$";
  NSPredicate *phoneTest =
      [NSPredicate predicateWithFormat:@"SELF MATCHES %@", phoneRegex];
  if (![phoneTest evaluateWithObject:theTextField.text]) {
    theTextField.text = [theTextField.text
        stringByTrimmingCharactersInSet:[[NSCharacterSet
                                            decimalDigitCharacterSet]
                                            invertedSet]];
  }

  if (theTextField.text.length == 11) {
    if (self.codeTxd.superview) {
      [self.codeTxd.superview removeFromSuperview];
    }
    if (self.authcodeImage) {
      [self.authcodeImage removeFromSuperview];
    }

    UIView *codeBackgroundView = [[UIView alloc] init];
    codeBackgroundView.backgroundColor = RGB(245, 246, 248);
    codeBackgroundView.layer.cornerRadius = 8 * kWidthFactor;
    codeBackgroundView.layer.masksToBounds = YES;
    [self.phoneLoginContainerView addSubview:codeBackgroundView];

    [codeBackgroundView mas_makeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.phoneLoginContainerView).offset(16 * kWidthFactor);
      make.top.equalTo(self.phoneTxd.superview.mas_bottom)
          .offset(15 * kWidthFactor);
      make.width.mas_equalTo(kMainWidth * 0.45);
      make.height.mas_equalTo(45 * kWidthFactor);
    }];

    self.codeTxd = [[UITextField alloc] init];
    self.codeTxd.placeholder = @"请输入验证码";
    self.codeTxd.font = SourceHanSerifMediumFont(16 * kWidthFactor);
    self.codeTxd.keyboardType = UIKeyboardTypeASCIICapable;
    self.codeTxd.clearButtonMode = UITextFieldViewModeWhileEditing;
    self.codeTxd.keyboardAppearance = UIKeyboardAppearanceAlert;
    [codeBackgroundView addSubview:self.codeTxd];

    [self.codeTxd mas_makeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(codeBackgroundView).offset(15 * kWidthFactor);
      make.centerY.equalTo(codeBackgroundView);
      make.right.equalTo(codeBackgroundView).offset(-15 * kWidthFactor);
      make.height.mas_equalTo(30 * kWidthFactor);
    }];

    self.authcodeImage = [[XGAuthCode alloc]
        initWithFrame:CGRectMake(0, 0, 80, 45 * kWidthFactor)];
    [self.phoneLoginContainerView addSubview:self.authcodeImage];
    [self.authcodeImage getAuthcode];

    [self.authcodeImage mas_makeConstraints:^(MASConstraintMaker *make) {
      make.right.equalTo(self.phoneLoginContainerView)
          .offset(-16 * kWidthFactor);
      make.centerY.equalTo(codeBackgroundView);
      make.width.mas_equalTo(80);
      make.height.equalTo(codeBackgroundView);
    }];

    [self.loginBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.top.equalTo(self.phoneLoginContainerView.mas_bottom)
          .offset(40 * kWidthFactor);
      make.centerX.equalTo(self.view);
      make.size.mas_equalTo(CGSizeMake(160 * kWidthFactor, 60 * kWidthFactor));
    }];
    [self.safeTitle mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.top.equalTo(self.loginBtn.mas_bottom).offset(22 * kWidthFactor);
      make.centerX.equalTo(self.view);
      make.height.mas_equalTo(15 * kWidthFactor);
    }];
  } else {
    if (self.codeTxd.superview) {
      [self.codeTxd.superview removeFromSuperview];
    }
    if (self.authcodeImage) {
      [self.authcodeImage removeFromSuperview];
    }
    [self.loginBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.top.equalTo(self.phoneLoginContainerView.mas_bottom)
          .offset(40 * kWidthFactor);
      make.centerX.equalTo(self.view);
      make.size.mas_equalTo(CGSizeMake(160 * kWidthFactor, 60 * kWidthFactor));
    }];
    [self.safeTitle mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.top.equalTo(self.loginBtn.mas_bottom).offset(22 * kWidthFactor);
      make.centerX.equalTo(self.view);
      make.height.mas_equalTo(15 * kWidthFactor);
    }];
  }
}

- (void)unableReceiveCode {
  if (_phoneTxd.text.length != 11) {
    [self.view makeToast:@"请输入账户原手机号"
                duration:1.0
                position:CSToastPositionCenter];
  } else {
  }
}

- (void)fillMoreInfoIfNeededWithUserInfo:(UserPersonResult *)user {
  if ([user.NOOType isEqualToString:@"0"]) {
    [self goNext:user.rank];
  } else {
    [self loginFinishedWithLoginResult:FTHZLoginResultSuccess
                                  user:user
                                 error:nil];
  }
}

- (void)wxlogin {
  UIAlertController *alertController = [UIAlertController
      alertControllerWithTitle:@"微信登录不可用"
                       message:@"请联系官方邮箱: <EMAIL>"
                preferredStyle:UIAlertControllerStyleAlert];
  UIAlertAction *okAction =
      [UIAlertAction actionWithTitle:@"确认"
                               style:UIAlertActionStyleDefault
                             handler:nil];
  [alertController addAction:okAction];
  [self presentViewController:alertController animated:YES completion:nil];
}

- (void)ubtnAction {
  [self showAgreementAction:@"0"];
}

- (void)ybtnAction {
  [self showAgreementAction:@"1"];
}

- (void)goNext:(NSString *)num {
  GenderVC *vc = [[GenderVC alloc] init];

  vc.numtype = num;
  [self.navigationController pushViewController:vc animated:YES];
}

- (void)loginFinishedWithLoginResult:(FTHZLoginResult)result
                                user:(UserPersonResult *)user
                               error:(NSError *)error {
  if (![self.navigationController
          conformsToProtocol:@protocol(FTHZLoginResultDelegate)]) {
    return;
  }

  [(id<FTHZLoginResultDelegate>)self.navigationController
             loginViewController:self
      didFinishedLoginWithResult:result
                            user:user
                           error:error];
}

- (void)loginin {
  if (self.loginMode == LoginModePhone) {
    NSString *phoneRegex = @"^1[1-9]\\d{9}$";
    NSPredicate *phoneTest =
        [NSPredicate predicateWithFormat:@"SELF MATCHES %@", phoneRegex];
    if (![phoneTest evaluateWithObject:self.phoneTxd.text]) {
      [self.view makeToast:@"请输入正确的手机号"
                  duration:1.0
                  position:CSToastPositionCenter];
      return;
    }
    if (self.phoneTxd.text.length != 11) {
      [self.view makeToast:@"请输入正确的手机号"
                  duration:1.0
                  position:CSToastPositionCenter];
      return;
    }
    NSString *authCodeStr = [self.authcodeImage.authCodeStr lowercaseString];
    NSString *code = [self.codeTxd.text lowercaseString];
    if (![authCodeStr isEqualToString:code]) {
      [self.view makeToast:@"验证码不正确"
                  duration:1.0
                  position:CSToastPositionCenter];
      return;
    }
    if (!self.isAgreementAccepted) {
      [self.view makeToast:@"请阅读并同意用户协议与隐私政策"
                  duration:1.0
                  position:CSToastPositionCenter];
      return;
    }
    @weakify(self);
    [CommonJudgeMobil getMobilJudge:self.phoneTxd.text
        success:^(NSDictionary *resultObject) {
          @strongify(self);
          CommonJudgeMobil *member =
              [CommonJudgeMobil mj_objectWithKeyValues:resultObject];
          NSString *code = member.data;
          if ([code intValue] == 0) {
            [self requestMobilVerCode];
          } else {
            FTHZGlobalConfig.invitationCode = nil;
            [self userVerify];
          }
        }
        failure:^(NSError *requestErr) {
          [self.view makeToast:@"数据有误,请检查网络后重试"
                      duration:1.0
                      position:CSToastPositionCenter];
        }];
  } else {
    if (self.accountTxd.text.length == 0) {
      [self.view makeToast:@"请输入账号"
                  duration:1.0
                  position:CSToastPositionCenter];
      return;
    }
    if (self.passwordTxd.text.length == 0) {
      [self.view makeToast:@"请输入密码"
                  duration:1.0
                  position:CSToastPositionCenter];
      return;
    }
    if (!self.isAgreementAccepted) {
      [self.view makeToast:@"请阅读并同意用户协议与隐私政策"
                  duration:1.0
                  position:CSToastPositionCenter];
      return;
    }
    if (self.goNextView) {
      return;
    }
    self.goNextView = YES;
    [self.view makeToast:@"登录中..."
                duration:1.0
                position:CSToastPositionCenter];
    @weakify(self);
    [[FTHZAccountManager shared]
        loginWithAccount:self.accountTxd.text
                password:self.passwordTxd.text
              completion:^(UserPersonResult *user, NSError *error) {
                @strongify(self);
                self.goNextView = NO;
                if (error) {
                  [self.view makeToast:error.localizedDescription
                              duration:1.0
                              position:CSToastPositionCenter];
                  return;
                }
                [[NSUserDefaults standardUserDefaults]
                    setObject:self.accountTxd.text
                       forKey:@"LastLoginAccount"];
                [[NSUserDefaults standardUserDefaults] synchronize];
                [self fillMoreInfoIfNeededWithUserInfo:user];
              }];
  }
}

- (void)userVerify {
  @weakify(self);
  [GetUserVerifyModel getUserVerify:_phoneTxd.text
      success:^(NSDictionary *resultObject) {
        @strongify(self);
        GetUserVerifyModel *member =
            [GetUserVerifyModel mj_objectWithKeyValues:resultObject];

        if ([member.success boolValue]) {
          UserVerifyModelResult *user = [UserVerifyModelResult
              mj_objectWithKeyValues:member.data.firstObject];

          if ([user.verify boolValue]) {
            [self requestMobilVerCode];
          } else {
            verifyViewController *verifyVC =
                [[verifyViewController alloc] init];
            verifyVC.phoneNum = self.phoneTxd.text;
            [self.navigationController pushViewController:verifyVC
                                                 animated:YES];
          }
        } else {
          [self.view makeToast:@"数据有误,请检查网络后重试"
                      duration:1.0
                      position:CSToastPositionCenter];
        }
      }
      failure:^(NSError *requestErr) {
        @strongify(self);
        [self.view makeToast:@"数据有误,请检查网络后重试"
                    duration:1.0
                    position:CSToastPositionCenter];
      }];
}

- (void)requestMobilVerCode {
  @weakify(self);
  [CommonCodeModel getCommonCode:_phoneTxd.text
      success:^(NSDictionary *resultObject) {
        @strongify(self);
        CommonCodeModel *member =
            [CommonCodeModel mj_objectWithKeyValues:resultObject];

        if ([member.success boolValue]) {
          [[NSUserDefaults standardUserDefaults] setObject:self.phoneTxd.text
                                                    forKey:@"LastLoginPhone"];
          [[NSUserDefaults standardUserDefaults] synchronize];

          VerificationCodeVC *vc = [[VerificationCodeVC alloc] init];
          vc.phoneNum = self.phoneTxd.text;
          vc.loginVC = self;
          [self.navigationController pushViewController:vc animated:YES];
        } else {
          [self.view makeToast:member.msg
                      duration:1.0
                      position:CSToastPositionCenter];
        }
      }
      failure:^(NSError *requestErr) {
        @strongify(self);
        [self.view makeToast:@"数据有误,请检查网络后重试"
                    duration:1.0
                    position:CSToastPositionCenter];
      }];
}

- (void)needInvitationCode {
  FTHZAlertDialogController *dialog = [[FTHZAlertDialogController alloc]
      initWithTitle:@""
            message:@"你的账号未注册过请输入邀请码哦"];

  [dialog addAction:[FTHZAlertDialogAction
                        actionWithTitle:@"好的，我知道了"
                                 action:^{
                                 }
                                  style:FTHZAlertDialogActionStyleHighlighted]];

  [[UIViewController topViewController] presentViewController:dialog
                                                     animated:YES
                                                   completion:nil];
}

- (void)hideAgreementAction:(id)sender {
  [agreementView removeFromSuperview];
  agreementView = nil;
}

- (void)showAgreementAction:(NSString *)type {
  agreementView =
      [[UIView alloc] initWithFrame:CGRectMake(0, 0, self.view.frame.size.width,
                                               self.view.frame.size.height)];

  UIView *maskView = [[UIView alloc] initWithFrame:agreementView.frame];
  maskView.backgroundColor = [UIColor blackColor];
  maskView.alpha = 0.6;
  [agreementView addSubview:maskView];

  UIView *contentView = [[UIView alloc]
      initWithFrame:CGRectMake(30, 80, self.view.frame.size.width - 60,
                               self.view.frame.size.height - 160)];
  contentView.backgroundColor = [UIColor whiteColor];
  contentView.layer.cornerRadius = 5.0;
  [agreementView addSubview:contentView];

  UILabel *titleLabel = [[UILabel alloc]
      initWithFrame:CGRectMake(15, 18, contentView.frame.size.width - 20, 18)];
  titleLabel.textColor = KColor_Black;
  titleLabel.textAlignment = NSTextAlignmentCenter;
  titleLabel.font = [UIFont systemFontOfSize:18.0];
  titleLabel.text =
      [type isEqualToString:@"0"] ? @"52赫兹服务协议" : @"52赫兹用户隐私政策";
  [contentView addSubview:titleLabel];

  UIView *lineView = [[UIView alloc]
      initWithFrame:CGRectMake(0, 46, contentView.frame.size.width, 1)];
  lineView.backgroundColor = KColor_codeGray;
  [contentView addSubview:lineView];

  UIScrollView *scrollView = [[UIScrollView alloc]
      initWithFrame:CGRectMake(10,
                               lineView.frame.origin.y +
                                   lineView.frame.size.height,
                               contentView.frame.size.width - 20,
                               contentView.frame.size.height - 55 - 55)];
  [contentView addSubview:scrollView];

  UIActivityIndicatorView *activityIndicator = [[UIActivityIndicatorView alloc]
      initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleGray];
  activityIndicator.center = scrollView.center;
  [activityIndicator startAnimating];
  [scrollView addSubview:activityIndicator];

  lineView = [[UIView alloc]
      initWithFrame:CGRectMake(0,
                               scrollView.frame.origin.y +
                                   scrollView.frame.size.height + 5,
                               contentView.frame.size.width, 1)];
  lineView.backgroundColor = KColor_loginGray;
  [contentView addSubview:lineView];

  UIButton *hideAgreementButton = [[UIButton alloc]
      initWithFrame:CGRectMake(0, contentView.frame.size.height - 65,
                               contentView.frame.size.width, 65)];
  hideAgreementButton.titleLabel.font = [UIFont systemFontOfSize:24.0];
  [hideAgreementButton setTitle:@"知道了" forState:UIControlStateNormal];
  [hideAgreementButton setTitleColor:KColor_Black
                            forState:UIControlStateNormal];
  [hideAgreementButton addTarget:self
                          action:@selector(hideAgreementAction:)
                forControlEvents:UIControlEventTouchUpInside];
  [contentView addSubview:hideAgreementButton];

  [self.view addSubview:agreementView];

  dispatch_async(
      dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        NSString *filePath =
            [type isEqualToString:@"0"]
                ? [[NSBundle mainBundle] pathForResource:@"52fuwu"
                                                  ofType:@"txt"]
                : [[NSBundle mainBundle] pathForResource:@"52yinsi"
                                                  ofType:@"txt"];

        NSString *content = [NemoUtil readFromFullPath:filePath];

        dispatch_async(dispatch_get_main_queue(), ^{
          [activityIndicator stopAnimating];
          [activityIndicator removeFromSuperview];

          if (content) {
            UILabel *contentLabel = [[UILabel alloc]
                initWithFrame:CGRectMake(5, 5, scrollView.frame.size.width - 10,
                                         scrollView.frame.size.height)];
            contentLabel.lineBreakMode = NSLineBreakByWordWrapping;
            contentLabel.numberOfLines = 0;
            contentLabel.font = [UIFont systemFontOfSize:13.0];
            contentLabel.textColor = KColor_Black;
            contentLabel.text = content;
            [scrollView addSubview:contentLabel];
            [contentLabel sizeToFit];

            [scrollView
                setContentSize:CGSizeMake(contentLabel.frame.size.width,
                                          contentLabel.frame.size.height +
                                              contentLabel.frame.origin.y)];
          } else {
            UILabel *errorLabel = [[UILabel alloc]
                initWithFrame:CGRectMake(5, 5, scrollView.frame.size.width - 10,
                                         20)];
            errorLabel.text = @"加载失败，请稍后重试";
            errorLabel.textAlignment = NSTextAlignmentCenter;
            errorLabel.textColor = KColor_Black;
            [scrollView addSubview:errorLabel];
          }
        });
      });
}

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];
  [self.navigationController setNavigationBarHidden:YES animated:YES];
  self.codeTxd.text = nil;
  [self.authcodeImage updateCode];
}

- (void)toggleLoginMode {
  [self.view layoutIfNeeded];
  if (self.loginMode == LoginModePhone) {
    self.loginMode = LoginModeAccount;
    [self.accountLoginBtn setImage:KImage_name(@"plogin")
                          forState:UIControlStateNormal];

    self.accountLoginContainerView.transform =
        CGAffineTransformMakeScale(1.2, 1.2);
    self.accountLoginContainerView.alpha = 0.0;

    [self.phoneLoginLeftConstraint uninstall];
    [self.accountLoginLeftConstraint uninstall];
    [self.phoneLoginContainerView
        mas_updateConstraints:^(MASConstraintMaker *make) {
          self.phoneLoginLeftConstraint =
              make.left.equalTo(self.view).offset(-self.view.frame.size.width);
        }];
    [self.accountLoginContainerView
        mas_updateConstraints:^(MASConstraintMaker *make) {
          self.accountLoginLeftConstraint =
              make.left.equalTo(self.view).offset(20 * kWidthFactor);
        }];

    [UIView animateWithDuration:0.3
        animations:^{
          self.phoneLoginContainerView.transform =
              CGAffineTransformMakeScale(0.8, 0.8);
          self.phoneLoginContainerView.alpha = 0.0;
          self.accountLoginContainerView.transform = CGAffineTransformIdentity;
          self.accountLoginContainerView.alpha = 1.0;
          [self.view layoutIfNeeded];
        }
        completion:^(BOOL finished) {
          self.phoneLoginContainerView.transform = CGAffineTransformIdentity;
          self.phoneLoginContainerView.alpha = 1.0;
        }];
  } else {
    self.loginMode = LoginModePhone;
    [self.accountLoginBtn setImage:KImage_name(@"alogin")
                          forState:UIControlStateNormal];

    self.phoneLoginContainerView.transform =
        CGAffineTransformMakeScale(1.2, 1.2);
    self.phoneLoginContainerView.alpha = 0.0;

    [self.phoneLoginLeftConstraint uninstall];
    [self.accountLoginLeftConstraint uninstall];
    [self.phoneLoginContainerView
        mas_updateConstraints:^(MASConstraintMaker *make) {
          self.phoneLoginLeftConstraint =
              make.left.equalTo(self.view).offset(20 * kWidthFactor);
        }];
    [self.accountLoginContainerView
        mas_updateConstraints:^(MASConstraintMaker *make) {
          self.accountLoginLeftConstraint =
              make.left.equalTo(self.view.mas_right);
        }];

    [UIView animateWithDuration:0.3
        animations:^{
          self.accountLoginContainerView.transform =
              CGAffineTransformMakeScale(0.8, 0.8);
          self.accountLoginContainerView.alpha = 0.0;
          self.phoneLoginContainerView.transform = CGAffineTransformIdentity;
          self.phoneLoginContainerView.alpha = 1.0;
          [self.view layoutIfNeeded];
        }
        completion:^(BOOL finished) {
          self.accountLoginContainerView.transform = CGAffineTransformIdentity;
          self.accountLoginContainerView.alpha = 1.0;
        }];
  }
}

- (void)togglePasswordVisibility:(UIButton *)sender {
  sender.selected = !sender.selected;
  self.passwordTxd.secureTextEntry = !sender.selected;
}

@end
