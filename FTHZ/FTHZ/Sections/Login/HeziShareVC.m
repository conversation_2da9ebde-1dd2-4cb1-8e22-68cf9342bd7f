#import "HeziShareVC.h"
#import "AppDelegate.h"
#import "GetUserinfoModel.h"
#import "LoginVC.h"

@interface HeziShareVC ()

@property(nonatomic, strong) UICountingLabel *heziLabel;
@property(nonatomic, strong) UIImageView *hzimage;
@property(nonatomic, strong) FlatButton *loginBtn;
@property(nonatomic, strong) UIButton *backButton;
@property(nonatomic, strong) FlatButton *botlabel;
@end

@implementation HeziShareVC

- (void)viewDidLoad {
  [super viewDidLoad];
  UIImageView *tagBg = [[UIImageView alloc] init];
  tagBg.image = KImage_name(self.whaleName);
  tagBg.contentMode = UIViewContentModeScaleAspectFit;
  tagBg.userInteractionEnabled = YES;
  [self.view addSubview:tagBg];
  [tagBg mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.view);
    make.top.equalTo(self.view);
    make.size.mas_equalTo(CGSizeMake(kMainWidth, kMainHeight));
  }];

  _heziLabel = [[UICountingLabel alloc] init];
  _heziLabel.method = UILabelCountingMethodEaseOut;
  _heziLabel.format = @"%.2f";
  _heziLabel.textColor = KColor_heziGreen;
  _heziLabel.textAlignment = NSTextAlignmentCenter;
  _heziLabel.font = [UIFont fontWithName:@"DINCondensed-Bold"
                                    size:32 * kWidthFactor];
  [self.view addSubview:_heziLabel];
  [_heziLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.bottom.equalTo(self.view.mas_centerY).offset(-183 * kWidthFactor);
    make.centerX.equalTo(self.view);
  }];
  [_heziLabel countFrom:0 to:self.heziNum withDuration:2];

  UILabel *titleLabel = [[UILabel alloc] init];
  titleLabel.textColor = KColor_heziGreen;
  titleLabel.text = @"Hz";
  titleLabel.font = [UIFont fontWithName:@"DINCondensed-Bold"
                                    size:32 * kWidthFactor];
  [self.view addSubview:titleLabel];
  [titleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.heziLabel.mas_right).offset(4 * kWidthFactor);
    make.centerY.equalTo(self.heziLabel);
  }];

  _loginBtn = [FlatButton buttonWithType:UIButtonTypeSystem];
  _loginBtn.backgroundColor = KColor_HighBlack;
  _loginBtn.layer.masksToBounds = YES;
  _loginBtn.layer.cornerRadius = 30 * kWidthFactor;
  [_loginBtn setTitle:@"进入海洋" forState:UIControlStateNormal];
  [_loginBtn setTitleColor:KColor_White forState:UIControlStateNormal];
  [_loginBtn addTarget:self
                action:@selector(goNext)
      forControlEvents:UIControlEventTouchUpInside];
  [self.view addSubview:_loginBtn];
  [_loginBtn mas_makeConstraints:^(MASConstraintMaker *make) {
    make.bottom.equalTo(self.view.mas_bottom).with.offset(-100 * kWidthFactor);
    make.centerX.equalTo(self.view);
    make.size.mas_equalTo(CGSizeMake(160 * kWidthFactor, 60 * kWidthFactor));
  }];

  _backButton = [UIButton buttonWithType:(UIButtonTypeCustom)];
  [_backButton setImage:[UIImage imageNamed:@"back"]
               forState:(UIControlStateNormal)];
  _backButton.frame = CGRectMake(10, 8 + STATUS_BAR_HEIGHT, 20, 30);
  _backButton.adjustsImageWhenHighlighted = YES;
  [_backButton addTarget:self
                  action:@selector(backAction)
        forControlEvents:(UIControlEventTouchUpInside)];
  [self.view addSubview:_backButton];
}

- (void)shareAction {
  _loginBtn.hidden = YES;
  _backButton.hidden = YES;
  _botlabel.hidden = YES;
  CGSize s = self.view.bounds.size;
  UIGraphicsBeginImageContextWithOptions(s, YES, [[UIScreen mainScreen] scale]);
  [self.view.layer renderInContext:UIGraphicsGetCurrentContext()];
  UIImage *imgeee = UIGraphicsGetImageFromCurrentImageContext();
  UIGraphicsEndImageContext();
  _backButton.hidden = NO;
  _botlabel.hidden = NO;
  _loginBtn.hidden = NO;

  NSData *imageData = UIImageJPEGRepresentation(imgeee, 0.7);

  NSString *filePath = [[NSBundle mainBundle] pathForResource:@"res5"
                                                       ofType:@"jpg"];
}

- (void)backAction {
}

- (void)goNext {
  @weakify(self);
  [[FTHZAccountManager shared]
      syncUserInfoWithCompletion:^(UserPersonResult *_Nullable user,
                                   NSError *_Nullable error) {
        @strongify(self);
        if (error) {
          [self showToastFast:error.localizedDescription];
          return;
        }
        if (![self.navigationController
                conformsToProtocol:@protocol(FTHZLoginResultDelegate)]) {
          return;
        }
        [(id<FTHZLoginResultDelegate>)self.navigationController
                   loginViewController:self
            didFinishedLoginWithResult:FTHZLoginResultSuccess
                                  user:user
                                 error:error];
      }];
}

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];
  [self.navigationController setNavigationBarHidden:YES animated:YES];
  [self setShowBackBtn:YES];
}
@end
