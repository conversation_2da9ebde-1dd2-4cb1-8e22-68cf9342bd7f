#import "VerificationCodeVC.h"
#import "GenderVC.h"
#import "KingIdentifyingView.h"
#import "SNSCodeCountdownButton.h"
#import "ValidateCode.h"

@interface VerificationCodeVC () <SNSCodeCountdownButtonDelegate> {
  SNSCodeCountdownButton *_sendButton;
  NSInteger timei;
  KingIdentifyingView *codeView;
  BOOL goNextView;
}
@property(nonatomic, strong) FlatButton *loginBtn;
@property(nonatomic, assign) NSInteger verifyFailCount;

@end

@implementation VerificationCodeVC

- (void)viewDidLoad {
  [super viewDidLoad];
  [self.navigationController setNavigationBarHidden:NO animated:NO];
  [self.navigationController.navigationBar
      setBackgroundImage:[[UIImage alloc] init]
           forBarMetrics:UIBarMetricsDefault];
  [self.navigationController.navigationBar
      setShadowImage:[[UIImage alloc] init]];

  timei = 0;
  self.verifyFailCount = 0;
  UILabel *titleLabel = [[UILabel alloc] init];
  titleLabel.textColor = KColor_Black;
  titleLabel.text = @"请输入短信验证码";
  titleLabel.font = [UIFont systemFontOfSize:24 * kMainTemp];
  [self.view addSubview:titleLabel];
  [titleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.view).offset(32 * kMainTemp);
    make.top.equalTo(self.view).offset(80 * kMainTemp);
    make.size.mas_equalTo(CGSizeMake(300 * kMainTemp, 24 * kMainTemp));
  }];

  UILabel *phoneLabel = [[UILabel alloc] init];
  phoneLabel.textColor = KColor_codeGray;
  phoneLabel.text =
      [NSString stringWithFormat:@"已发送至 +86 %@", self.phoneNum];
  phoneLabel.font = [UIFont systemFontOfSize:14 * kMainTemp];
  [self.view addSubview:phoneLabel];
  [phoneLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.view).offset(32 * kMainTemp);
    make.top.equalTo(titleLabel.mas_bottom).offset(12 * kMainTemp);
    make.size.mas_equalTo(CGSizeMake(300 * kMainTemp, 14 * kMainTemp));
  }];

  __weak typeof(self) wSelf = self;

  codeView = [[KingIdentifyingView alloc]
      initWithFrame:CGRectMake(0, 200 * kMainTemp, kMainWidth, 100 * kMainTemp)
            cerCode:@"0000"
            setting:@{
              value_labelSize : @(50 * kMainTemp),
              value_labelSpace : @(11.5 * kMainTemp)
            }
           isSecure:NO];
  [codeView makeRebackBlock:^(NSString *value) {
    [wSelf verificationCode:value];
  }];
  [self.view addSubview:codeView];

  _sendButton = [[SNSCodeCountdownButton alloc] init];
  _sendButton.center = CGPointMake(kMainWidth * 0.5, 14 * kMainTemp);
  _sendButton.countdownBeginNumber = 60;
  _sendButton.delegate = self;
  [_sendButton receiveCode];
  [self.view addSubview:_sendButton];
  [_sendButton mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(phoneLabel.mas_bottom).with.offset(175 * kMainTemp);
    make.centerX.equalTo(self.view);
    make.size.mas_equalTo(CGSizeMake(kMainWidth * 0.5, 14 * kMainTemp));
  }];
}

- (void)verificationCode:(NSString *)code {
  if (goNextView) {
    return;
  }
  goNextView = YES;
  __weak typeof(self) wSelf = self;
  [[FTHZAccountManager shared]
       loginWithMobile:self.phoneNum
      verificationCode:code
            completion:^(UserPersonResult *_Nullable user,
                         NSError *_Nullable error) {
              __strong typeof(wSelf) sSelf = wSelf;
              if (!sSelf)
                return;
              if (error) {
                sSelf.verifyFailCount++;
                sSelf->goNextView = NO; // 允许继续输入
                if (sSelf.verifyFailCount >= 3) {
                  [sSelf showToastFast:@"验证码错误，请重新获取"];
                  dispatch_after(dispatch_time(DISPATCH_TIME_NOW,
                                               (int64_t)(1.0 * NSEC_PER_SEC)),
                                 dispatch_get_main_queue(), ^{
                                   [sSelf.navigationController
                                       popViewControllerAnimated:YES];
                                 });
                  return;
                } else {
                  [sSelf showToastFast:error.localizedDescription];
                }
                if ([error.localizedDescription containsString:@"非老用户"]) {
                  [sSelf.navigationController popViewControllerAnimated:NO];
                  [sSelf.loginVC needInvitationCode];
                } else if ([error.localizedDescription
                               containsString:@"使用老用户"]) {
                  FTHZGlobalConfig.invitationCode = nil;
                  [sSelf.navigationController popViewControllerAnimated:NO];
                }
                return;
              }
              [sSelf fillMoreInfoIfNeededWithUserInfo:user];
            }];
}

- (void)fillMoreInfoIfNeededWithUserInfo:(UserPersonResult *)user {
  if ([user.NOOType isEqualToString:@"0"]) {
    [self goNext:user.rank];
  } else {
    if (![self.navigationController
            conformsToProtocol:@protocol(FTHZLoginResultDelegate)]) {
      return;
    }
    [(id<FTHZLoginResultDelegate>)self.navigationController
               loginViewController:self
        didFinishedLoginWithResult:FTHZLoginResultSuccess
                              user:user
                             error:nil];
  }
}

- (void)goNext:(NSString *)num {
  GenderVC *vc = [[GenderVC alloc] init];
  vc.numtype = num;
  [self.navigationController pushViewController:vc animated:YES];
}

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];
  goNextView = NO;
  [self performSelector:@selector(keybodFirst) withObject:nil afterDelay:0.6];
  [self setShowBackBtn:YES];
}

- (void)keybodFirst {
  [codeView becameFirstResponder2];
}

- (void)viewDidAppear:(BOOL)animated {
  [super viewDidAppear:animated];
  [codeView resignFirstResponder];
}

- (void)snsCodeCountdownButtonClicked {
  timei++;
  if (timei > 1) {
  }
}

@end
