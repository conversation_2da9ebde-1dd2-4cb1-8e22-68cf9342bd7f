#import "GenderVC.h"
#import "HeziChouseOneVC.h"
#import "HeziShareVC.h"
#import "InformationVC.h"
@interface GenderVC () {
  NSInteger genderType;
}
@property(nonatomic, strong) FlatButton *loginBtn;
@property(nonatomic, strong) FlatButton *genderBtn1;
@property(nonatomic, strong) FlatButton *genderBtn2;
@property(nonatomic, strong) UIImageView *manImageView;
@property(nonatomic, strong) UIImageView *womanImageView;
@end

@implementation GenderVC

- (void)viewDidLoad {
  [super viewDidLoad];
  genderType = 0;
  NSMutableAttributedString *str = [[NSMutableAttributedString alloc]
      initWithString:[NSString
                         stringWithFormat:@"欢迎你，%@的小鲸鱼", @"52Hz"]];
  NSRange range = [[str string] rangeOfString:@"52Hz"];
  [str addAttribute:NSForegroundColorAttributeName
              value:KColor_heziGreen
              range:range];

  UILabel *titleLabel = [[UILabel alloc] init];
  titleLabel.textColor = KColor_Black;
  titleLabel.attributedText = str;
  titleLabel.font = SourceHanSerifSemiBoldFont(20 * kWidthFactor);
  [self.view addSubview:titleLabel];
  [titleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.view).offset(32 * kMainTemp);
    make.top.equalTo(self.view).offset(16 * kMainTemp);
    make.size.mas_equalTo(CGSizeMake(300 * kMainTemp, 24 * kMainTemp));
  }];

  UILabel *phoneLabel = [[UILabel alloc] init];
  phoneLabel.textColor = KColor_codeGray;
  phoneLabel.text = @"请创建专属你的鲸鱼资料";
  phoneLabel.font = SourceHanSerifRegularFont(14 * kWidthFactor);
  [self.view addSubview:phoneLabel];
  [phoneLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.view).offset(32 * kMainTemp);
    make.top.equalTo(titleLabel.mas_bottom).offset(12 * kMainTemp);
    make.size.mas_equalTo(CGSizeMake(300 * kMainTemp, 14 * kMainTemp));
  }];

  UILabel *genderTitleLabel = [[UILabel alloc] init];
  genderTitleLabel.textColor = KColor_Black;
  genderTitleLabel.text = @"性别";
  genderTitleLabel.font = [UIFont systemFontOfSize:16 * kMainTemp];
  [self.view addSubview:genderTitleLabel];
  [genderTitleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.view).offset(60 * kMainTemp);
    make.top.equalTo(phoneLabel.mas_bottom).offset(40 * kMainTemp);
    make.size.mas_equalTo(CGSizeMake(40 * kMainTemp, 16 * kMainTemp));
  }];

  // 创建按钮容器
  UIView *buttonsContainer = [[UIView alloc] init];
  [self.view addSubview:buttonsContainer];
  [buttonsContainer mas_makeConstraints:^(MASConstraintMaker *make) {
    make.centerX.equalTo(self.view);
    make.top.equalTo(genderTitleLabel.mas_bottom).offset(50 * kWidthFactor);
    make.width.mas_equalTo(160 * kWidthFactor);
    make.height.mas_equalTo(340 * kWidthFactor);
  }];

  // 创建女生按钮（上方）
  _genderBtn2 = [FlatButton buttonWithType:UIButtonTypeCustom];
  _genderBtn2.clipsToBounds = NO;
  _genderBtn2.tag = 002;
  _genderBtn2.layer.cornerRadius = 70 * kWidthFactor;
  _genderBtn2.layer.masksToBounds = NO;
  [_genderBtn2 setTitle:@"" forState:UIControlStateNormal];

  _genderBtn2.layer.shadowColor = [UIColor blackColor].CGColor;
  _genderBtn2.layer.shadowOffset = CGSizeMake(0, 5);
  _genderBtn2.layer.shadowOpacity = 0.3;
  _genderBtn2.layer.shadowRadius = 5.0;
  _genderBtn2.backgroundColor = [UIColor clearColor];

  UIView *innerView2 = [[UIView alloc] init];
  innerView2.layer.cornerRadius = 70 * kWidthFactor;
  innerView2.layer.masksToBounds = YES;
  innerView2.backgroundColor = [UIColor colorWithRed:0.85
                                               green:0.85
                                                blue:0.85
                                               alpha:1.0];
  innerView2.userInteractionEnabled = NO;
  [_genderBtn2 addSubview:innerView2];
  [innerView2 mas_makeConstraints:^(MASConstraintMaker *make) {
    make.edges.equalTo(_genderBtn2);
  }];

  // 添加女生图标
  _womanImageView =
      [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"woman"]];
  _womanImageView.contentMode = UIViewContentModeScaleAspectFit;
  _womanImageView.layer.zPosition = 999;
  _womanImageView.backgroundColor = [UIColor clearColor];
  _womanImageView.userInteractionEnabled = NO;
  [_genderBtn2 addSubview:_womanImageView];
  [_womanImageView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.center.equalTo(_genderBtn2);
    make.size.mas_equalTo(CGSizeMake(50 * kWidthFactor, 50 * kWidthFactor));
  }];

  _genderBtn2.layer.borderWidth = 0.5;
  _genderBtn2.layer.borderColor =
      [UIColor colorWithWhite:1.0 alpha:0.2].CGColor;
  [_genderBtn2 addTarget:self
                  action:@selector(chouse:)
        forControlEvents:UIControlEventTouchUpInside];

  [buttonsContainer addSubview:_genderBtn2];
  [_genderBtn2 mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(buttonsContainer);
    make.centerX.equalTo(buttonsContainer);
    make.size.mas_equalTo(CGSizeMake(140 * kWidthFactor, 140 * kWidthFactor));
  }];

  // 创建男生按钮（下方）
  _genderBtn1 = [FlatButton buttonWithType:UIButtonTypeCustom];
  _genderBtn1.clipsToBounds = NO;
  _genderBtn1.tag = 001;
  _genderBtn1.layer.cornerRadius = 70 * kWidthFactor;
  _genderBtn1.layer.masksToBounds = NO;
  [_genderBtn1 setTitle:@"" forState:UIControlStateNormal];

  _genderBtn1.layer.shadowColor = [UIColor blackColor].CGColor;
  _genderBtn1.layer.shadowOffset = CGSizeMake(0, 5);
  _genderBtn1.layer.shadowOpacity = 0.3;
  _genderBtn1.layer.shadowRadius = 5.0;
  _genderBtn1.backgroundColor = [UIColor clearColor];

  UIView *innerView1 = [[UIView alloc] init];
  innerView1.layer.cornerRadius = 70 * kWidthFactor;
  innerView1.layer.masksToBounds = YES;
  innerView1.backgroundColor = [UIColor colorWithRed:0.85
                                               green:0.85
                                                blue:0.85
                                               alpha:1.0];
  innerView1.userInteractionEnabled = NO;
  [_genderBtn1 addSubview:innerView1];
  [innerView1 mas_makeConstraints:^(MASConstraintMaker *make) {
    make.edges.equalTo(_genderBtn1);
  }];

  // 添加男生图标
  _manImageView =
      [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"man"]];
  _manImageView.contentMode = UIViewContentModeScaleAspectFit;
  _manImageView.layer.zPosition = 999;
  _manImageView.backgroundColor = [UIColor clearColor];
  _manImageView.userInteractionEnabled = NO;
  [_genderBtn1 addSubview:_manImageView];
  [_manImageView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.center.equalTo(_genderBtn1);
    make.size.mas_equalTo(CGSizeMake(50 * kWidthFactor, 50 * kWidthFactor));
  }];

  _genderBtn1.layer.borderWidth = 0.5;
  _genderBtn1.layer.borderColor =
      [UIColor colorWithWhite:1.0 alpha:0.2].CGColor;
  [_genderBtn1 addTarget:self
                  action:@selector(chouse:)
        forControlEvents:UIControlEventTouchUpInside];

  [buttonsContainer addSubview:_genderBtn1];
  [_genderBtn1 mas_makeConstraints:^(MASConstraintMaker *make) {
    make.bottom.equalTo(buttonsContainer);
    make.centerX.equalTo(buttonsContainer);
    make.size.mas_equalTo(CGSizeMake(140 * kWidthFactor, 140 * kWidthFactor));
  }];

  _loginBtn = [FlatButton buttonWithType:UIButtonTypeCustom];

  _loginBtn.backgroundColor = KColor_HighBlack;
  _loginBtn.layer.masksToBounds = YES;
  _loginBtn.layer.cornerRadius = 30 * kWidthFactor;
  [_loginBtn setTitle:@"确定" forState:UIControlStateNormal];
  [_loginBtn setTitleColor:KColor_White forState:UIControlStateNormal];
  [_loginBtn addTarget:self
                action:@selector(goNext)
      forControlEvents:UIControlEventTouchUpInside];
  [self.view addSubview:_loginBtn];
  [_loginBtn mas_makeConstraints:^(MASConstraintMaker *make) {
    make.bottom.equalTo(self.view.mas_bottom).with.offset(-100 * kMainTemp);
    make.centerX.equalTo(self.view);
    make.size.mas_equalTo(CGSizeMake(160 * kWidthFactor, 60 * kWidthFactor));
  }];

  self.loginBtn.hidden = YES;
}

- (void)chouse:(FlatButton *)btn {
  UIColor *defaultColor = [UIColor colorWithRed:0.85
                                          green:0.85
                                           blue:0.85
                                          alpha:1.0];
  UIColor *maleColor = KColor_switchLightBlue;
  UIColor *femaleColor = KColor_switchLightPink;

  if (btn.tag == 001) {
    genderType = 1;

    // 更新男生按钮背景颜色
    for (UIView *view in _genderBtn1.subviews) {
      if (view.layer.cornerRadius == 70 * kWidthFactor &&
          view != _manImageView) {
        view.backgroundColor = maleColor;
        break;
      }
    }

    // 重置女生按钮背景颜色
    for (UIView *view in _genderBtn2.subviews) {
      if (view.layer.cornerRadius == 70 * kWidthFactor &&
          view != _womanImageView) {
        view.backgroundColor = defaultColor;
        break;
      }
    }
  }

  if (btn.tag == 002) {
    genderType = 2;

    // 更新女生按钮背景颜色
    for (UIView *view in _genderBtn2.subviews) {
      if (view.layer.cornerRadius == 70 * kWidthFactor &&
          view != _womanImageView) {
        view.backgroundColor = femaleColor;
        break;
      }
    }

    // 重置男生按钮背景颜色
    for (UIView *view in _genderBtn1.subviews) {
      if (view.layer.cornerRadius == 70 * kWidthFactor &&
          view != _manImageView) {
        view.backgroundColor = defaultColor;
        break;
      }
    }
  }

  [_genderBtn1 setNeedsDisplay];
  [_genderBtn2 setNeedsDisplay];

  if (genderType > 0) {
    self.loginBtn.hidden = NO;
  } else {
    self.loginBtn.hidden = YES;
  }
}

- (void)goNext {
  //    HeziShareVC *vc = [[HeziShareVC alloc] init];
  //    vc.whaleName = @"北瓶鼻鲸";
  //    vc.heziNum = 65.6;
  //    [self.navigationController pushViewController:vc animated:YES];
  //       return;
  if (genderType == 0) {
    [self showToast:@"请选择性别"];
  } else {
    InformationVC *vc = [[InformationVC alloc] init];
    vc.genderType = [NSString stringWithFormat:@"%ld", genderType];
    [self.navigationController pushViewController:vc animated:YES];
  }
}

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];
  [self setShowBackBtn:YES];
  [AppConfig statusbarStyle:YES];

  [UINavigationBar appearance].translucent = NO;
  [self.navigationController setNavigationBarHidden:NO animated:NO];
  [self.navigationController.navigationBar
      setShadowImage:[[UIImage alloc] init]];
}

@end
