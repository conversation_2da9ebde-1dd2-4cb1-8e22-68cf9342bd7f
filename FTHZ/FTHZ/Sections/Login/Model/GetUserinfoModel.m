#import "GetUserinfoModel.h"
@implementation TagModelResult
@end

@implementation UserPersonResult

+ (NSDictionary *)objectClassInArray {
  return @{
    @"badge" : [TagModelResult class],
    @"tag" : [TagModelResult class],
    @"likeTag" : [TagModelResult class],
    @"paoLikeTag" : [TagModelResult class],
  };
}

- (id)copyWithZone:(NSZone *)zone {
  id json = [self mj_JSONObject];
  return [[self class] mj_objectWithKeyValues:json];
}

@end

@implementation UserVerifyModelResult
// return @{
//@"verify":
// }
@end

@implementation GetUserinfoModel
+ (void)getUserUserinfo:(success)_success failure:(failure)_failure {
  NSMutableDictionary *param = [NSMutableDictionary new];
  [Http getAsynRequestWithUrl:KURLUser_userinfo
                       params:param
                      success:_success
                      failure:_failure];
}

+ (void)updateSSIDWithSuccess:(success)success failure:(failure)failure {
  [Http postAsynRequestWithUrl:KURLUpdateSSID
                        params:@{}
                       success:success
                       failure:failure];
}
@end

@implementation GetUserVerifyModel
+ (void)getUserVerify:(NSString *)mobile
              success:(success)_success
              failure:(failure)_failure {
  NSMutableDictionary *param = [NSMutableDictionary new];

  [param setObject:mobile forKey:@"phone"];
  [Http getAsynRequestWithUrl:KURLUserVerify
                       params:param
                      success:_success
                      failure:_failure];
}
@end

@implementation UserAFLResult
@end

@implementation GetUserAFLModel
+ (void)getUserAFL:(success)_success failure:(failure)_failure {
  NSMutableDictionary *param = [NSMutableDictionary new];
  [Http getAsynRequestWithUrl:KURLGetAFL
                       params:param
                      success:_success
                      failure:_failure];
}
@end
