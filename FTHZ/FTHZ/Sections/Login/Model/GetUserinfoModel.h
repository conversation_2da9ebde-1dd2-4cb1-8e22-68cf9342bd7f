#import "BaseJsonModel.h"

NS_ASSUME_NONNULL_BEGIN
@protocol TagModelResult <NSObject>
@end
@interface TagModelResult : BaseJsonModel
@property(nonatomic, strong) NSString *tagId;
@property(nonatomic, strong) NSString *type_name;
@property(nonatomic, strong) NSString *name;
@property(nonatomic, strong) NSString *type;
@property(nonatomic, strong) NSString *introduction;
@property(nonatomic, strong) NSString *icon;

@end

@protocol UserVerifyModelResult <NSObject, NSCopying>
@end
@interface UserVerifyModelResult : BaseJsonModel
@property(nonatomic, strong) NSString *verify;

@end

@protocol UserPersonResult <NSObject, NSCopying>
@end
@interface UserPersonResult : BaseJsonModel
@property(nonatomic, strong) NSString *mobile;
@property(nonatomic, strong) NSString *NOOType;
@property(nonatomic, strong) NSString *ssid;
@property(nonatomic, strong) NSString *userid;
@property(nonatomic, strong) NSString *nickname;
@property(nonatomic, strong) NSString *account;
@property(nonatomic, strong) NSString *avatar;
@property(nonatomic, strong) NSString *signature;
@property(nonatomic, strong) NSString *birth;
@property(nonatomic, strong) NSString *gender;
@property(nonatomic, strong) NSString *city;
@property(nonatomic, strong) NSString *province;
@property(nonatomic, strong) NSString *country;
@property(nonatomic, strong) NSString *usersig;
@property(nonatomic, strong) NSString *rank;
@property(nonatomic, strong) NSString *hertz;
@property(nonatomic, strong) NSString *fansNum;
@property(nonatomic, strong) NSString *attentionNum;
@property(nonatomic, strong) NSString *likeCount;
@property(nonatomic, strong) NSString *contentNum;
@property(nonatomic, strong) NSString *whale;
@property(nonatomic, strong) NSString *codeRemainTime;
@property(nonatomic, strong) NSArray<TagModelResult *> *badge;
@property(nonatomic, strong) NSArray<TagModelResult *> *tag;
@property(nonatomic, strong) NSArray<TagModelResult *> *likeTag;
@property(nonatomic, strong) NSArray<TagModelResult *> *paoLikeTag;

@end

@interface GetUserinfoModel : BaseJsonModel
@property(nonatomic, strong) NSArray<UserPersonResult *> *data;

+ (void)getUserUserinfo:(success)_success failure:(failure)_failure;
+ (void)updateSSIDWithSuccess:(success _Nullable)success
                      failure:(failure _Nullable)failure;
@end

@interface GetUserVerifyModel : BaseJsonModel
@property(nonatomic, strong) NSArray<UserVerifyModelResult *> *data;
+ (void)getUserVerify:(NSString *)mobile
              success:(success)_success
              failure:(failure)_failure;
@end

@protocol UserAFLResult <NSObject>
@end
@interface UserAFLResult : BaseJsonModel
@property(nonatomic, strong) NSString *fansNum;
@property(nonatomic, strong) NSString *attentionNum;
@property(nonatomic, strong) NSString *likeNum;
@end

@interface GetUserAFLModel : BaseJsonModel
@property(nonatomic, strong) UserAFLResult *data;
+ (void)getUserAFL:(success)_success failure:(failure)_failure;
@end

NS_ASSUME_NONNULL_END
