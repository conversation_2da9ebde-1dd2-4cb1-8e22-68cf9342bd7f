#import "VerifyTableViewCell.h"
#import "_2hz-Swift.h"

@interface VerifyTableViewCell ()
@property(nonatomic, strong) UIButton *createBt;
@end

@implementation VerifyTableViewCell
- (instancetype)initWithStyle:(UITableViewCellStyle)style
              reuseIdentifier:(NSString *)reuseIdentifier {
  self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];

  if (self) {
    [self loadCellView];
    [self setSelectionStyle:(UITableViewCellSelectionStyleNone)];
  }
  return self;
}

- (void)loadCellView {
  self.btnArray = [[NSMutableArray alloc] init];
}

- (void)setMessageData:(VerifyModel *)messageData {
  _messageData = messageData;
  NSArray *arr = [NSArray arrayWithObjects:@"Ⓐ", @"Ⓑ", @"Ⓒ", @"Ⓓ", nil];
  UILabel *questionLabel = [[UILabel alloc] init];
  [self.contentView addSubview:questionLabel];
  [questionLabel setTextColor:KColor_HighBlack];
  questionLabel.font = DinCondensedBoldFont(16 * kWidthFactor);
  questionLabel.text = _messageData.question;
  [questionLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.mas_equalTo(self.mas_left).offset(24 * kWidthFactor);
    make.top.mas_equalTo(self.contentView.mas_top).offset(10 * kWidthFactor);
    make.size.mas_equalTo(CGSizeMake(200 * kWidthFactor, 20 * kWidthFactor));
  }];

  NSInteger count = _messageData.answer.count;
  if ([_messageData.type isEqual:@"1"]) {
    UILabel *lastlab = nil;
    for (int i = 0; i < count; i++) {
      UILabel *lab = [[UILabel alloc] init];
      [self.contentView addSubview:lab];
      [lab setTextColor:KColor_Black];
      lab.font = SourceHanSerifRegularFont(14 * kWidthFactor);
      lab.text = _messageData.answer[i];
      [lab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(self.mas_left).offset(80 * kWidthFactor);
        if (lastlab) {
          make.top.mas_equalTo(lastlab.mas_bottom).offset(10 * kWidthFactor);
        } else {
          make.top.mas_equalTo(questionLabel.mas_bottom)
              .offset(10 * kWidthFactor);
        }
        if ((count - i) == 1) {
          make.bottom.mas_equalTo(self.contentView.mas_bottom)
              .offset(-20 * kWidthFactor);
        }
        make.size.mas_equalTo(
            CGSizeMake(200 * kWidthFactor, 20 * kWidthFactor));
      }];
      lastlab = lab;

      UIButton *btn = [[UIButton alloc] init];
      btn.tag = _messageData.qid + i;
      [_btnArray addObject:btn];
      [self.contentView addSubview:btn];
      [btn setTitleColor:KColor_Black forState:UIControlStateNormal];
      [btn setTitle:arr[i] forState:UIControlStateNormal];
      btn.contentHorizontalAlignment =
          UIControlContentHorizontalAlignmentCenter;
      btn.titleLabel.font = SourceHanSerifRegularFont(18);
      [btn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(lab.mas_left).offset(-10 * kWidthFactor);
        make.centerY.mas_equalTo(lab);
        make.size.mas_equalTo(CGSizeMake(24 * kWidthFactor, 24 * kWidthFactor));
      }];
      btn.layer.cornerRadius = 12;
      [btn addTarget:self
                    action:@selector(buttonClicked:)
          forControlEvents:UIControlEventTouchUpInside];
    }
  } else if ([_messageData.type isEqual:@"2"]) {
    UIImageView *lastImage = nil;
    for (int i = 0; i < count; i++) {
      UIImageView *img = [[UIImageView alloc] init];
      [self.contentView addSubview:img];
      _messageData = messageData;
      [img
          sd_setImageWithURL:[NemoUtil
                                 getUrlWithUserSmallIcon:_messageData.answer[i]]
            placeholderImage:KImage_name(@"empty")];
      [img mas_makeConstraints:^(MASConstraintMaker *make) {
        if (i == 0) {
          make.left.mas_equalTo(self.mas_left).offset(80 * kWidthFactor);
          make.top.mas_equalTo(questionLabel.mas_bottom)
              .offset(10 * kWidthFactor);
        } else if (i == 1) {
          make.left.mas_equalTo(lastImage.mas_right).offset(100 * kWidthFactor);
          make.top.mas_equalTo(questionLabel.mas_bottom)
              .offset(10 * kWidthFactor);
        } else if (i == 2) {
          make.left.mas_equalTo(self.mas_left).offset(80 * kWidthFactor);
          make.top.mas_equalTo(questionLabel.mas_bottom)
              .offset(120 * kWidthFactor);
          make.bottom.mas_equalTo(self.contentView.mas_bottom)
              .offset(-20 * kWidthFactor);
        } else if (i == 3) {
          make.left.mas_equalTo(lastImage.mas_right).offset(100 * kWidthFactor);
          make.top.mas_equalTo(questionLabel.mas_bottom)
              .offset(120 * kWidthFactor);
          make.bottom.mas_equalTo(self.contentView.mas_bottom)
              .offset(-20 * kWidthFactor);
        }
        make.size.mas_equalTo(
            CGSizeMake(100 * kWidthFactor, 100 * kWidthFactor));
      }];
      lastImage = img;
      UIButton *btn = [[UIButton alloc] init];
      btn.tag = _messageData.qid + i;
      [_btnArray addObject:btn];
      [self.contentView addSubview:btn];
      [btn setTitleColor:KColor_Black forState:UIControlStateNormal];
      [btn setTitle:arr[i] forState:UIControlStateNormal];
      btn.contentHorizontalAlignment =
          UIControlContentHorizontalAlignmentCenter;
      btn.titleLabel.font = SourceHanSerifRegularFont(18);
      [btn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(img.mas_left).offset(-10 * kWidthFactor);
        make.centerY.mas_equalTo(img);
        make.size.mas_equalTo(CGSizeMake(24 * kWidthFactor, 24 * kWidthFactor));
      }];
      btn.layer.cornerRadius = 12;
      [btn addTarget:self
                    action:@selector(buttonClicked:)
          forControlEvents:UIControlEventTouchUpInside];
    }
  }
}
- (void)awakeFromNib {
  [super awakeFromNib];
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
  [super setSelected:selected animated:animated];
}

- (void)buttonClicked:(UIButton *)sender {
  for (UIButton *btn in _btnArray) {
    if (btn.tag == sender.tag) {
      [[NSNotificationCenter defaultCenter]
          postNotificationName:@"VerifyTableViewCell"
                        object:btn
                      userInfo:nil];
      [btn setBackgroundColor:KColor_HighBlack];
      [btn setTitleColor:KColor_White forState:UIControlStateNormal];
    } else {
      [[NSNotificationCenter defaultCenter]
          postNotificationName:@"VerifyTableViewCellV2"
                        object:btn
                      userInfo:nil];
      [btn setBackgroundColor:KColor_White];
      [btn setTitleColor:KColor_Black forState:UIControlStateNormal];
    }
  }
}

@end
