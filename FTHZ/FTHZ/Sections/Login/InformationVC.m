#import "InformationVC.h"
#import "CertificateModel.h"
#import "HeziChouseOneVC.h"
#import "TZImagePickerManager.h"
#import "UIImage+Wechat.h"
#import "UserUserinfoModel.h"
#import <Photos/Photos.h>
#import <QiniuSDK.h>
#import <objc/runtime.h>

@interface InformationVC () <UIImagePickerControllerDelegate> {
  NSString *selectBirthday;
  NSString *selectImage;
}
@property(nonatomic, strong) UITextField *nameTxd;
@property(nonatomic, strong) UILabel *oldLabel;
@property(nonatomic, strong) UIButton *imgBtn;
@property(nonatomic, strong) FlatButton *loginBtn;
@property(nonatomic, strong) NSString *imageToken;
@property(nonatomic, strong) UIImageView *imageView;
@property(assign, nonatomic) BOOL isChouse;

@end

@implementation InformationVC

- (void)loginInfo {
  @weakify(self);
  [[FTHZAccountManager shared]
      updateUserInfoWithName:_nameTxd.text
                      gender:self.genderType
                      avatar:selectImage
                   signature:@"连接那些遥远的相似性。"
                       brith:[NemoUtil timeSwitchTimestamp:selectBirthday
                                              andFormatter:@"YYYY-MM-dd"]
                  completion:^(UserPersonResult *_Nullable user,
                               NSError *_Nullable error) {
                    @strongify(self);
                    if (error) {
                      [self showToast:error.localizedDescription];
                      return;
                    }
                    [self goToNextView];
                  }];
}

- (void)loadQNtoken {
  __weak typeof(self) wSelf = self;
  [CertificateModel
      getCertificateModel:@"1"
               image_name:@""
                  success:^(NSDictionary *resultObject) {
                    CertificateModel *member =
                        [CertificateModel mj_objectWithKeyValues:resultObject];
                    if ([member.success boolValue]) {
                      CertificateModelResult *da = [CertificateModelResult
                          mj_objectWithKeyValues:[member.data objectAtIndex:0]];
                      wSelf.imageToken = da.token;
                      [wSelf uploadImages:wSelf.imageView.image];
                    }
                  }
                  failure:^(NSError *requestErr){

                  }];
}

- (void)viewDidLoad {
  [super viewDidLoad];
  _isChouse = NO;
  selectBirthday = @"";
  @weakify(self);
  UIView *bgView = [[UIView alloc] init];
  bgView.backgroundColor = KColor_White;
  bgView.layer.cornerRadius = 8;
  bgView.layer.shadowColor = [UIColor colorWithWhite:0 alpha:1].CGColor;
  bgView.layer.shadowOffset = CGSizeMake(0, 10);
  bgView.layer.shadowOpacity = 0.2;
  bgView.layer.shadowRadius = 15;
  [self.view addSubview:bgView];
  [bgView mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.view).offset(16 * kMainTemp);
    make.top.equalTo(self.view).offset(136 * kMainTemp);
    make.size.mas_equalTo(
        CGSizeMake(kMainWidth - 32 * kMainTemp, 243 * kMainTemp));
  }];

  UILabel *nameLabel = [[UILabel alloc] init];
  nameLabel.textColor = KColor_titleDarkGray;
  nameLabel.text = @"昵称";
  nameLabel.font = SourceHanSerifSemiBoldFont(16 * kWidthFactor);
  [self.view addSubview:nameLabel];
  [nameLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(bgView).offset(32 * kMainTemp);
    make.top.equalTo(bgView).offset(117.5 * kMainTemp);
    make.size.mas_equalTo(CGSizeMake(40 * kMainTemp, 16 * kMainTemp));
  }];

  //    UIView  *lineLabel = [[UIView alloc] init];
  //    lineLabel.backgroundColor = KColor_loginGray ;
  //    [bgView addSubview:lineLabel];
  //    [lineLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
  //        make.left.equalTo(bgView).offset(88*kMainTemp);
  //        make.top.equalTo(bgView).offset(140.5*kMainTemp);
  //        make.size.mas_equalTo(CGSizeMake(kMainWidth - 150*kMainTemp,
  //        0.5*kMainTemp));
  //    }];

  _nameTxd = [[UITextField alloc] init];
  _nameTxd.placeholder = @"请输入";
  _nameTxd.textColor = KColor_HighBlack;
  _nameTxd.font = SourceHanSerifRegularFont(14 * kWidthFactor);
  [bgView addSubview:_nameTxd];
  [_nameTxd mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(bgView).with.offset(88 * kMainTemp);
    make.top.equalTo(nameLabel);
    make.size.mas_equalTo(CGSizeMake(225 * kMainTemp, 16 * kMainTemp));
  }];
  [self.nameTxd addTarget:self
                   action:@selector(textField1TextChange:)
         forControlEvents:UIControlEventEditingChanged];

  UILabel *nameRuleLabel = [[UILabel alloc] init];
  nameRuleLabel.numberOfLines = 0;
  nameRuleLabel.font = [UIFont systemFontOfSize:12 * kWidthFactor];
  nameRuleLabel.textColor = [UIColor grayColor];
  nameRuleLabel.text =
      @"昵称要求：只能包含中文、英文字母和数字，长度不能超过8个字符";
  [bgView addSubview:nameRuleLabel];
  [nameRuleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.equalTo(bgView).with.offset(88 * kMainTemp);
    make.top.equalTo(self.nameTxd.mas_bottom).offset(5 * kWidthFactor);
    make.right.equalTo(bgView).with.offset(-20 * kMainTemp);
  }];

  UILabel *ageLabel = [[UILabel alloc] init];
  ageLabel.textColor = KColor_titleDarkGray;
  ageLabel.text = @"生日";
  ageLabel.font = SourceHanSerifSemiBoldFont(16 * kWidthFactor);
  [bgView addSubview:ageLabel];
  [ageLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(bgView).offset(32 * kMainTemp);
    make.top.equalTo(bgView).offset(191 * kMainTemp);
    make.size.mas_equalTo(CGSizeMake(90 * kMainTemp, 16 * kMainTemp));
  }];

  _oldLabel = [[UILabel alloc] init];
  _oldLabel.textColor = KColor_codeGray;
  _oldLabel.text = @"请选择";
  //    _oldLabel.textAlignment = NSTextAlignmentRight;
  _oldLabel.font = SourceHanSerifRegularFont(14 * kWidthFactor);
  [bgView addSubview:_oldLabel];
  [_oldLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(bgView).offset(88 * kMainTemp);
    make.centerY.equalTo(ageLabel);
    make.right.equalTo(bgView.mas_right).offset(-40 * kWidthFactor);
    //        make.size.mas_equalTo(CGSizeMake(90*kMainTemp, 14*kMainTemp));
  }];

  UIButton *oldBtn = [[UIButton alloc] init];
  [oldBtn addTarget:self
                action:@selector(chickBirthday)
      forControlEvents:UIControlEventTouchUpInside];
  [bgView addSubview:oldBtn];
  [oldBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.edges.equalTo(self.oldLabel);
    //        make.centerY.equalTo(ageLabel);
    //        make.size.mas_equalTo(CGSizeMake(150*kMainTemp, 40*kMainTemp));
  }];

  UIImageView *imageBGView = [[UIImageView alloc] init];
  imageBGView.image = KImage_name(@"Camera");
  imageBGView.userInteractionEnabled = YES;
  [self.view addSubview:imageBGView];
  [imageBGView mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.centerX.equalTo(self.view);
    make.top.equalTo(self.view).offset(56 * kMainTemp);
    make.size.mas_equalTo(CGSizeMake(152 * kMainTemp, 152 * kMainTemp));
  }];

  _imageView = [[UIImageView alloc] init];
  _imageView.layer.masksToBounds = YES;
  _imageView.layer.cornerRadius = 60 * kMainTemp;
  [imageBGView addSubview:_imageView];
  [_imageView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.center.equalTo(imageBGView);
    make.size.mas_equalTo(CGSizeMake(120 * kMainTemp, 120 * kMainTemp));
  }];

  _imgBtn = [UIButton buttonWithType:UIButtonTypeSystem];
  _imgBtn.layer.masksToBounds = YES;
  _imgBtn.layer.cornerRadius = 60 * kMainTemp;
  [_imgBtn addTarget:self
                action:@selector(takePhoto)
      forControlEvents:UIControlEventTouchUpInside];
  [imageBGView addSubview:_imgBtn];
  [_imgBtn mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(imageBGView).offset(19 * kMainTemp);
    make.left.equalTo(imageBGView).offset(19 * kMainTemp);
    make.size.mas_equalTo(CGSizeMake(120 * kMainTemp, 120 * kMainTemp));
  }];

  //    UILabel  *titleLabel = [[UILabel alloc] init];
  //    titleLabel.textColor = KColor_Black ;
  //    titleLabel.text = @"设置你的形象";
  //    titleLabel.textAlignment = NSTextAlignmentCenter;
  //    titleLabel.font = [UIFont fontWithName:@"SourceHanSansCN-Normal"
  //    size:16*kMainTemp]; [self.view addSubview:titleLabel]; [titleLabel
  //    mas_remakeConstraints:^(MASConstraintMaker *make) {
  //        make.centerX.equalTo(self.view);
  //        make.top.equalTo(imageBGView.mas_bottom);
  //        make.size.mas_equalTo(CGSizeMake(150*kMainTemp, 16*kMainTemp));
  //    }];

  _loginBtn = [FlatButton buttonWithType:UIButtonTypeCustom];

  _loginBtn.backgroundColor = KColor_HighBlack;
  _loginBtn.layer.masksToBounds = YES;
  _loginBtn.layer.cornerRadius = 30 * kWidthFactor;
  [_loginBtn setTitle:@"确定" forState:UIControlStateNormal];
  [_loginBtn setTitleColor:KColor_White forState:UIControlStateNormal];
  [_loginBtn addTarget:self
                action:@selector(goNext)
      forControlEvents:UIControlEventTouchUpInside];
  [self.view addSubview:_loginBtn];
  [_loginBtn mas_makeConstraints:^(MASConstraintMaker *make) {
    make.bottom.equalTo(self.view.mas_bottom).with.offset(-100 * kMainTemp);
    make.centerX.equalTo(self.view);
    make.size.mas_equalTo(CGSizeMake(160 * kWidthFactor, 60 * kWidthFactor));
  }];

  _loginBtn.enabled = NO;
}

- (void)takePhoto {
  [[TZImagePickerManager sharedManager]
      presentSinglePhotoPickerWithViewController:self
                                       allowCrop:YES
                                        delegate:nil
                               completionHandler:^(
                                   NSArray<UIImage *> *_Nullable images,
                                   NSArray<PHAsset *> *_Nullable assets,
                                   BOOL isOriginal) {
                                 if (images.count > 0) {
                                   self.imageView.image = images.firstObject;
                                   self->_isChouse = YES;
                                   [self loadBtnColor];
                                 }
                               }
                                   cancelHandler:^{
                                       // 用户取消选择
                                   }];
}

- (void)chickBirthday {
  if ([_nameTxd isFirstResponder]) {
    [_nameTxd resignFirstResponder];
  }

  [self showBirthdayPicker];
}

- (void)showBirthdayPicker {
  UIViewController *pickerVC = [[UIViewController alloc] init];
  pickerVC.modalPresentationStyle = UIModalPresentationCustom;

  UIView *containerView = [[UIView alloc] init];
  containerView.backgroundColor = KColor_DarkKeyboard;
  containerView.frame = CGRectMake(0, self.view.bounds.size.height - 280,
                                   self.view.bounds.size.width, 280);
  pickerVC.view = containerView;

  UIToolbar *toolbar = [[UIToolbar alloc] init];
  toolbar.barStyle = UIBarStyleDefault;
  toolbar.translucent = NO;
  toolbar.barTintColor = KColor_DarkKeyboard;
  toolbar.frame = CGRectMake(0, 0, containerView.frame.size.width, 44);

  UIBarButtonItem *cancelButton =
      [[UIBarButtonItem alloc] initWithTitle:@"取消"
                                       style:UIBarButtonItemStylePlain
                                      target:self
                                      action:@selector(dismissBirthdayPicker:)];
  [cancelButton
      setTitleTextAttributes:@{NSForegroundColorAttributeName : KColor_White}
                    forState:UIControlStateNormal];

  UIBarButtonItem *doneButton =
      [[UIBarButtonItem alloc] initWithTitle:@"确定"
                                       style:UIBarButtonItemStyleDone
                                      target:self
                                      action:@selector(doneBirthdayPicker:)];
  [doneButton
      setTitleTextAttributes:@{NSForegroundColorAttributeName : KColor_White}
                    forState:UIControlStateNormal];

  UIBarButtonItem *flexibleSpace = [[UIBarButtonItem alloc]
      initWithBarButtonSystemItem:UIBarButtonSystemItemFlexibleSpace
                           target:nil
                           action:nil];
  UILabel *titleLabel =
      [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 200, 44)];
  titleLabel.text = @"选择生日";
  titleLabel.textAlignment = NSTextAlignmentCenter;
  titleLabel.textColor = KColor_White;
  titleLabel.font = [UIFont systemFontOfSize:16];
  UIBarButtonItem *titleButton =
      [[UIBarButtonItem alloc] initWithCustomView:titleLabel];

  toolbar.items =
      @[ cancelButton, flexibleSpace, titleButton, flexibleSpace, doneButton ];
  [containerView addSubview:toolbar];

  UIDatePicker *datePicker = [[UIDatePicker alloc] init];
  if (@available(iOS 13.4, *)) {
    datePicker.preferredDatePickerStyle = UIDatePickerStyleWheels;
  }
  datePicker.datePickerMode = UIDatePickerModeDate;
  datePicker.backgroundColor = KColor_DarkKeyboard;

  if (@available(iOS 14.0, *)) {
    datePicker.tintColor = UIColor.whiteColor;
    datePicker.overrideUserInterfaceStyle = UIUserInterfaceStyleDark;
  } else {
    datePicker.overrideUserInterfaceStyle = UIUserInterfaceStyleDark;
  }

  NSCalendar *calendar = [NSCalendar currentCalendar];
  NSDate *now = [NSDate date];
  NSDateComponents *minComponents = [[NSDateComponents alloc] init];
  [minComponents setYear:-100];
  NSDate *minDate = [calendar dateByAddingComponents:minComponents
                                              toDate:now
                                             options:0];
  datePicker.minimumDate = minDate;
  datePicker.maximumDate = now;

  if (selectBirthday.length > 0) {
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
    [dateFormatter setDateFormat:@"yyyy-MM-dd"];
    NSDate *birthdayDate = [dateFormatter dateFromString:selectBirthday];
    if (birthdayDate) {
      datePicker.date = birthdayDate;
    }
  }

  datePicker.frame = CGRectMake(0, 44, containerView.frame.size.width, 236);
  [containerView addSubview:datePicker];

  objc_setAssociatedObject(pickerVC, "datePicker", datePicker,
                           OBJC_ASSOCIATION_RETAIN_NONATOMIC);

  UIView *backgroundView = [[UIView alloc] initWithFrame:self.view.bounds];
  backgroundView.backgroundColor = [UIColor blackColor];
  backgroundView.alpha = 0;
  [self.view addSubview:backgroundView];

  UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc]
      initWithTarget:self
              action:@selector(dismissBirthdayPicker:)];
  [backgroundView addGestureRecognizer:tapGesture];

  objc_setAssociatedObject(pickerVC, "backgroundView", backgroundView,
                           OBJC_ASSOCIATION_RETAIN_NONATOMIC);

  [self addChildViewController:pickerVC];
  [self.view addSubview:pickerVC.view];

  pickerVC.view.transform = CGAffineTransformMakeTranslation(0, 280);
  [UIView animateWithDuration:0.3
                   animations:^{
                     pickerVC.view.transform = CGAffineTransformIdentity;
                     backgroundView.alpha = 0.4;
                   }];
}

- (void)dismissBirthdayPicker:(id)sender {
  UIViewController *pickerVC = self.childViewControllers.lastObject;
  UIView *backgroundView = objc_getAssociatedObject(pickerVC, "backgroundView");

  [UIView animateWithDuration:0.3
      animations:^{
        pickerVC.view.transform = CGAffineTransformMakeTranslation(0, 280);
        backgroundView.alpha = 0;
      }
      completion:^(BOOL finished) {
        [backgroundView removeFromSuperview];
        [pickerVC.view removeFromSuperview];
        [pickerVC removeFromParentViewController];
      }];
}

- (void)doneBirthdayPicker:(id)sender {
  UIViewController *pickerVC = self.childViewControllers.lastObject;
  UIDatePicker *datePicker = objc_getAssociatedObject(pickerVC, "datePicker");
  UIView *backgroundView = objc_getAssociatedObject(pickerVC, "backgroundView");

  NSDate *selectedDate = datePicker.date;
  NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
  [dateFormatter setDateFormat:@"yyyy-MM-dd"];
  NSString *birthdayString = [dateFormatter stringFromDate:selectedDate];

  selectBirthday = birthdayString;
  _oldLabel.textColor = KColor_Black;
  _oldLabel.text = selectBirthday;
  [self loadBtnColor];

  [UIView animateWithDuration:0.3
      animations:^{
        pickerVC.view.transform = CGAffineTransformMakeTranslation(0, 280);
        backgroundView.alpha = 0;
      }
      completion:^(BOOL finished) {
        [backgroundView removeFromSuperview];
        [pickerVC.view removeFromSuperview];
        [pickerVC removeFromParentViewController];
      }];
}

- (void)goNext {
  if (!_isChouse) {
    [self showToast:@"请设置头像"];
  }
  if (_nameTxd.text.length < 1) {
    [self showToast:@"请输入昵称"];
  }
  if (_nameTxd.text.length > 8) {
    [self showToast:@"昵称不能超过8个字"];
  }
  if (![self isValidNickname:_nameTxd.text]) {
    [self showToast:@"昵称只能包含中文、英文字母和数字"];
    return;
  }
  if (![_oldLabel.text isTheStringDate]) {
    [self showToast:@"请选择您的生日"];
  }
  if (_nameTxd.text.length < 11 && _nameTxd.text.length > 0 &&
      [_oldLabel.text isTheStringDate] && _isChouse) {
    [HUD show];
    [self loadQNtoken];
  }
}

- (BOOL)isValidNickname:(NSString *)nickname {
  NSString *pattern = @"^[\\u4e00-\\u9fa5a-zA-Z0-9]+$";
  NSPredicate *pred =
      [NSPredicate predicateWithFormat:@"SELF MATCHES %@", pattern];
  return [pred evaluateWithObject:nickname];
}

- (void)loadBtnColor {
  if ([_oldLabel.text isTheStringDate] && _nameTxd.text.length > 0 &&
      _isChouse) {
    _loginBtn.enabled = YES;
  } else {
    _loginBtn.enabled = NO;
  }
}

- (void)goToNextView {
  HeziChouseOneVC *vc = [[HeziChouseOneVC alloc] init];
  [self.navigationController pushViewController:vc animated:YES];
}

#pragma mark BirthdayPickerDelegate

- (void)addTargetMethod {
}
- (void)textField1TextChange:(UITextField *)textField {
  [self loadBtnColor];
}

- (void)uploadImages:(UIImage *)scaledImage {
  UIGraphicsBeginImageContext(CGSizeMake(512, 512));
  [scaledImage drawInRect:CGRectMake(0, 0, 512, 512)];

  __weak typeof(self) wSelf = self;
  QNConfiguration *config =
      [QNConfiguration build:^(QNConfigurationBuilder *builder) {
        builder.zone = [QNFixedZone zone0];
      }];
  QNUploadManager *upManager =
      [[QNUploadManager alloc] initWithConfiguration:config];
  QNUploadOption *option = [[QNUploadOption alloc]
      initWithProgressHandler:^(NSString *key, float percent){
      }];
  NSString *imgKey =
      [NSString stringWithFormat:@"%@%@", [NemoUtil randomString],
                                 [NemoUtil getNowTimeIntervalStr]];
  [upManager
       putFile:[self getImagePath:scaledImage]
           key:imgKey
         token:self.imageToken
      complete:^(QNResponseInfo *info, NSString *key, NSDictionary *resp) {
        if (info.ok) {
          self->selectImage = imgKey;
          [self loginInfo];
        } else {
          [wSelf showToastFast:@"头像上传失败,请重试"];
        }
        [HUD dissmiss];
      }
        option:option];
}

- (NSString *)getImagePath:(UIImage *)Image {
  NSString *filePath = nil;
  NSData *data = nil;
  data = [Image wcTimelineCompress];

  NSString *DocumentsPath =
      [NSHomeDirectory() stringByAppendingPathComponent:@"Documents"];

  NSFileManager *fileManager = [NSFileManager defaultManager];

  [fileManager createDirectoryAtPath:DocumentsPath
         withIntermediateDirectories:YES
                          attributes:nil
                               error:nil];
  NSString *ImagePath = [[NSString alloc] initWithFormat:@"/theFirstImage.png"];
  [fileManager
      createFileAtPath:[DocumentsPath stringByAppendingString:ImagePath]
              contents:data
            attributes:nil];

  filePath =
      [[NSString alloc] initWithFormat:@"%@%@", DocumentsPath, ImagePath];
  return filePath;
}

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];
  [self setShowBackBtn:YES];
  [AppConfig statusbarStyle:YES];

  [UINavigationBar appearance].translucent = NO;
  [self.navigationController setNavigationBarHidden:NO animated:NO];
  [self.navigationController.navigationBar
      setShadowImage:[[UIImage alloc] init]];
}

#pragma mark - ImagePickerDelegate

- (void)imagePickerController:(UIImagePickerController *)picker
    didFinishPickingMediaWithInfo:(NSDictionary<NSString *, id> *)info {
  @weakify(self);
  [picker dismissViewControllerAnimated:YES
                             completion:^{
                               @strongify(self);
                               UIImage *image;

                               if (picker.allowsEditing) {
                                 image = [info
                                     objectForKey:
                                         UIImagePickerControllerEditedImage];
                               } else {
                                 image = [info
                                     objectForKey:
                                         UIImagePickerControllerOriginalImage];
                               }

                               self.imageView.image = image;
                               self.isChouse = YES;
                               [self loadBtnColor];
                             }];
}

@end
