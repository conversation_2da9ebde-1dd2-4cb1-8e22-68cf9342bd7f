#import "AccountLoginVC.h"
#import "GenderVC.h"
#import "LoginVC.h"

@interface AccountLoginVC () {
  UIView *agreementView;
  CGFloat keyboardHeight;
}

@property(nonatomic, strong) UITextField *accountTxd;
@property(nonatomic, strong) UITextField *passwordTxd;
@property(nonatomic, strong) UITextField *codeTxd;
@property(nonatomic, assign) BOOL goNextView;
@property(nonatomic, assign) BOOL keyboardHasShown;
@property(nonatomic, strong) UIButton *agreementCheckbox;
@property(nonatomic, assign) BOOL isAgreementAccepted;

@end

@implementation AccountLoginVC

- (void)viewDidLoad {
  [super viewDidLoad];
  self.view.backgroundColor = [UIColor whiteColor];
  [self.navigationController setNavigationBarHidden:YES];
  self.keyboardHasShown = NO;

  NSString *lastAccount =
      [[NSUserDefaults standardUserDefaults] objectForKey:@"LastLoginAccount"];
  if (lastAccount.length > 0) {
    self.accountTxd.text = lastAccount;
  }

  [self setupUI];
  [[NSNotificationCenter defaultCenter]
      addObserver:self
         selector:@selector(keyboardWillShow:)
             name:UIKeyboardWillShowNotification
           object:nil];
  [[NSNotificationCenter defaultCenter]
      addObserver:self
         selector:@selector(keyboardWillHide:)
             name:UIKeyboardWillHideNotification
           object:nil];
}

- (void)dealloc {
  [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)setupUI {
  UIButton *button = [[UIButton alloc] initWithFrame:CGRectMake(0, 0, 10, 15)];
  [button setImage:KImage_name(@"back") forState:UIControlStateNormal];
  [button setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
  [button addTarget:self
                action:@selector(backAction)
      forControlEvents:UIControlEventTouchUpInside];
  [self.view addSubview:button];
  [button mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.view).offset(24 * kWidthFactor);
    make.top.equalTo(self.view).offset(60 * kWidthFactor);
  }];

  UIImageView *whaleIv =
      [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"whaleLogo"]];
  [self.view addSubview:whaleIv];
  [whaleIv mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.view).offset(47 * kWidthFactor);
    make.top.equalTo(self.view).offset(92 * kWidthFactor);
    make.size.mas_equalTo(CGSizeMake(300 * kWidthFactor, 243 * kWidthFactor));
  }];
  whaleIv.hidden = YES;

  UILabel *Label_52 = [[UILabel alloc] init];
  Label_52.textColor = KColor_HighBlack;
  Label_52.text = @"52";
  Label_52.font = AvenirNextHeavyFont(56 * kWidthFactor);
  [whaleIv addSubview:Label_52];
  [Label_52 mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(whaleIv).offset(1 * kWidthFactor);
    make.top.equalTo(whaleIv).offset(26 * kWidthFactor);
  }];
  Label_52.hidden = YES;

  UILabel *Label_hz = [[UILabel alloc] init];
  Label_hz.textColor = KColor_HighBlack;
  Label_hz.text = @"HZ";
  Label_hz.font = AvenirNextHeavyFont(32 * kWidthFactor);
  [whaleIv addSubview:Label_hz];
  [Label_hz mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(Label_52).offset(1 * kWidthFactor);
    make.left.equalTo(Label_52.mas_right).offset(1 * kWidthFactor);
  }];
  Label_hz.hidden = YES;

  UILabel *titleLabel = [[UILabel alloc] init];
  titleLabel.textColor = KColor_titleDarkGray;
  titleLabel.text = @"密码登录";
  titleLabel.font = SourceHanSerifRegularFont(20 * kWidthFactor);
  [self.view addSubview:titleLabel];
  [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.view).offset(38 * kWidthFactor);
    make.top.equalTo(self.view).offset(kMainHeight * 0.5);
    make.size.mas_equalTo(CGSizeMake(150 * kWidthFactor, 20 * kWidthFactor));
  }];

  _accountTxd.delegate = self;
  _passwordTxd.delegate = self;

  UIColor *bgColor = RGB(245, 246, 248);
  CGFloat cornerRadius = 8 * kWidthFactor;
  CGFloat leftMargin = 36 * kWidthFactor;
  CGFloat fieldHeight = 45 * kWidthFactor;

  UIView *accountBgView = [[UIView alloc] init];
  accountBgView.backgroundColor = bgColor;
  accountBgView.layer.cornerRadius = cornerRadius;
  accountBgView.layer.masksToBounds = YES;
  [self.view addSubview:accountBgView];
  [accountBgView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.view).offset(leftMargin);
    make.right.equalTo(self.view).offset(-leftMargin);
    make.top.equalTo(titleLabel.mas_bottom).offset(20 * kWidthFactor);
    make.height.mas_equalTo(fieldHeight);
  }];

  _accountTxd = [[UITextField alloc] init];
  _accountTxd.placeholder = @"请输入 账号/手机号";
  _accountTxd.font = SourceHanSerifMediumFont(16 * kWidthFactor);
  _accountTxd.clearButtonMode = UITextFieldViewModeWhileEditing;
  _accountTxd.keyboardAppearance = UIKeyboardAppearanceAlert;
  NSString *lastAccount =
      [[NSUserDefaults standardUserDefaults] objectForKey:@"LastLoginAccount"];
  if (lastAccount.length > 0) {
    _accountTxd.text = lastAccount;
  }
  [accountBgView addSubview:_accountTxd];
  [_accountTxd mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(accountBgView).offset(15 * kWidthFactor);
    make.right.equalTo(accountBgView).offset(-15 * kWidthFactor);
    make.centerY.equalTo(accountBgView);
    make.height.mas_equalTo(30 * kWidthFactor);
  }];

  UIView *passwordBgView = [[UIView alloc] init];
  passwordBgView.backgroundColor = bgColor;
  passwordBgView.layer.cornerRadius = cornerRadius;
  passwordBgView.layer.masksToBounds = YES;
  [self.view addSubview:passwordBgView];
  [passwordBgView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.right.equalTo(accountBgView);
    make.top.equalTo(accountBgView.mas_bottom).offset(15 * kWidthFactor);
    make.height.equalTo(accountBgView);
  }];

  _passwordTxd = [[UITextField alloc] init];
  _passwordTxd.placeholder = @"请输入密码";
  _passwordTxd.font = SourceHanSerifMediumFont(16 * kWidthFactor);
  _passwordTxd.keyboardAppearance = UIKeyboardAppearanceAlert;
  _passwordTxd.secureTextEntry = YES;

  UIButton *passwordToggleBtn = [UIButton buttonWithType:UIButtonTypeCustom];
  [passwordToggleBtn setImage:[UIImage imageNamed:@"password_hide"]
                     forState:UIControlStateNormal];
  [passwordToggleBtn setImage:[UIImage imageNamed:@"password_show"]
                     forState:UIControlStateSelected];
  passwordToggleBtn.frame = CGRectMake(0, 0, 30, 30);
  [passwordToggleBtn addTarget:self
                        action:@selector(togglePasswordVisibility:)
              forControlEvents:UIControlEventTouchUpInside];
  _passwordTxd.rightView = passwordToggleBtn;
  _passwordTxd.rightViewMode = UITextFieldViewModeAlways;

  [passwordBgView addSubview:_passwordTxd];
  [_passwordTxd mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(passwordBgView).offset(15 * kWidthFactor);
    make.right.equalTo(passwordBgView).offset(-15 * kWidthFactor);
    make.centerY.equalTo(passwordBgView);
    make.height.mas_equalTo(30 * kWidthFactor);
  }];

  FlatButton *loginBtn = [FlatButton buttonWithType:UIButtonTypeCustom];
  [loginBtn setImage:KImage_name(@"loginBtn") forState:UIControlStateNormal];
  [loginBtn addTarget:self
                action:@selector(loginAction)
      forControlEvents:UIControlEventTouchUpInside];
  [self.view addSubview:loginBtn];
  [loginBtn mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(_passwordTxd.superview.mas_bottom)
        .offset(80 * kWidthFactor);
    make.centerX.equalTo(self.view);
    make.size.mas_equalTo(CGSizeMake(160 * kWidthFactor, 60 * kWidthFactor));
  }];

  NSString *likeStr = @"我已阅读并同意用户协议与隐私政策";
  NSString *str001 = @"我已阅读并同意";
  NSString *str002 = @"用户协议";
  NSString *str003 = @"与";
  NSString *str004 = @"隐私政策";

  NSMutableAttributedString *str =
      [[NSMutableAttributedString alloc] initWithString:likeStr];
  NSRange range1 = [[str string] rangeOfString:str001];
  [str addAttribute:NSFontAttributeName
              value:[UIFont systemFontOfSize:10 * kWidthFactor]
              range:range1];
  NSRange range2 = [[str string] rangeOfString:str002];
  [str addAttribute:NSFontAttributeName
              value:[UIFont systemFontOfSize:10 * kWidthFactor]
              range:range2];
  NSRange range3 = [[str string] rangeOfString:str003];
  [str addAttribute:NSFontAttributeName
              value:[UIFont systemFontOfSize:10 * kWidthFactor]
              range:range3];
  NSRange range4 = [[str string] rangeOfString:str004];
  [str addAttribute:NSFontAttributeName
              value:[UIFont systemFontOfSize:10 * kWidthFactor]
              range:range4];
  [str addAttribute:NSForegroundColorAttributeName
              value:KColor_Black
              range:range1];
  [str addAttribute:NSForegroundColorAttributeName
              value:KColor_heziGreen
              range:range2];
  [str addAttribute:NSForegroundColorAttributeName
              value:KColor_Black
              range:range3];
  [str addAttribute:NSForegroundColorAttributeName
              value:KColor_heziGreen
              range:range4];

  UILabel *safeTitle = [[UILabel alloc] init];
  safeTitle.attributedText = str;
  safeTitle.textAlignment = NSTextAlignmentLeft;
  [self.view addSubview:safeTitle];
  [safeTitle mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(loginBtn.mas_bottom).offset(22 * kWidthFactor);
    make.centerX.equalTo(self.view);
    make.height.mas_equalTo(15 * kWidthFactor);
  }];

  self.agreementCheckbox = [UIButton buttonWithType:UIButtonTypeCustom];
  CGFloat buttonSize = 12 * kWidthFactor;
  self.agreementCheckbox.layer.cornerRadius = buttonSize / 2;
  self.agreementCheckbox.layer.borderWidth = 1;
  self.agreementCheckbox.layer.borderColor = KColor_Black.CGColor;
  self.agreementCheckbox.clipsToBounds = YES;
  [self.agreementCheckbox addTarget:self
                             action:@selector(toggleAgreementCheckbox:)
                   forControlEvents:UIControlEventTouchUpInside];
  [self.view addSubview:self.agreementCheckbox];

  [self.agreementCheckbox mas_makeConstraints:^(MASConstraintMaker *make) {
    make.right.equalTo(safeTitle.mas_left).offset(-4 * kWidthFactor);
    make.centerY.equalTo(safeTitle);
    make.size.mas_equalTo(CGSizeMake(12 * kWidthFactor, 12 * kWidthFactor));
  }];

  FlatButton *ubtn = [FlatButton buttonWithType:UIButtonTypeCustom];
  [ubtn addTarget:self
                action:@selector(ubtnAction)
      forControlEvents:UIControlEventTouchUpInside];
  [self.view addSubview:ubtn];
  [ubtn mas_makeConstraints:^(MASConstraintMaker *make) {
    make.centerX.equalTo(safeTitle);
    make.centerY.equalTo(safeTitle);
    make.size.mas_equalTo(CGSizeMake(40 * kWidthFactor, 25 * kWidthFactor));
  }];

  FlatButton *ybtn = [FlatButton buttonWithType:UIButtonTypeCustom];
  [ybtn addTarget:self
                action:@selector(ybtnAction)
      forControlEvents:UIControlEventTouchUpInside];
  [self.view addSubview:ybtn];
  [ybtn mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(ubtn.mas_right).offset(10 * kWidthFactor);
    make.centerY.equalTo(safeTitle);
    make.size.mas_equalTo(CGSizeMake(40 * kWidthFactor, 25 * kWidthFactor));
  }];
}

- (void)togglePasswordVisibility:(UIButton *)sender {
  sender.selected = !sender.selected;
  _passwordTxd.secureTextEntry = !sender.selected;
}

#pragma mark - 键盘通知处理
- (void)keyboardWillShow:(NSNotification *)notification {
  self.keyboardHasShown = YES;

  NSDictionary *userInfo = notification.userInfo;
  NSValue *keyboardFrameValue = userInfo[UIKeyboardFrameEndUserInfoKey];
  CGRect keyboardFrame = [keyboardFrameValue CGRectValue];
  keyboardHeight = keyboardFrame.size.height;

  CGFloat offset = 0;
  if ([self.accountTxd isFirstResponder]) {
    CGRect accountFrame = [self.accountTxd convertRect:self.accountTxd.bounds
                                                toView:self.view];
    CGFloat bottomY = accountFrame.origin.y + accountFrame.size.height;
    offset = bottomY - (self.view.frame.size.height - keyboardHeight - 20);
  } else if ([self.passwordTxd isFirstResponder]) {
    CGRect passwordFrame = [self.passwordTxd convertRect:self.passwordTxd.bounds
                                                  toView:self.view];
    CGFloat bottomY = passwordFrame.origin.y + passwordFrame.size.height;
    offset = bottomY - (self.view.frame.size.height - keyboardHeight - 20);
  }

  if (offset > 0) {
    [UIView animateWithDuration:0.3
                     animations:^{
                       self.view.frame =
                           CGRectMake(0, -offset, self.view.frame.size.width,
                                      self.view.frame.size.height);
                     }];
  }
}

- (void)keyboardWillHide:(NSNotification *)notification {
  [UIView animateWithDuration:0.3
                   animations:^{
                     self.view.frame =
                         CGRectMake(0, 0, self.view.frame.size.width,
                                    self.view.frame.size.height);
                   }];
}

- (void)toggleAgreementCheckbox:(UIButton *)sender {
  self.isAgreementAccepted = !self.isAgreementAccepted;
  if (self.isAgreementAccepted) {
    sender.backgroundColor = KColor_Black;
  } else {
    sender.backgroundColor = [UIColor clearColor];
  }
}

- (void)ubtnAction {
  [self showAgreementAction:@"0"];
}

- (void)ybtnAction {
  [self showAgreementAction:@"1"];
}

- (void)showAgreementAction:(NSString *)type {
  agreementView =
      [[UIView alloc] initWithFrame:CGRectMake(0, 0, self.view.frame.size.width,
                                               self.view.frame.size.height)];

  UIView *maskView = [[UIView alloc] initWithFrame:agreementView.frame];
  maskView.backgroundColor = [UIColor blackColor];
  maskView.alpha = 0.6;
  [agreementView addSubview:maskView];

  UIView *contentView = [[UIView alloc]
      initWithFrame:CGRectMake(30, 80, self.view.frame.size.width - 60,
                               self.view.frame.size.height - 160)];
  contentView.backgroundColor = [UIColor whiteColor];
  contentView.layer.cornerRadius = 5.0;
  [agreementView addSubview:contentView];

  UILabel *titleLabel = [[UILabel alloc]
      initWithFrame:CGRectMake(15, 18, contentView.frame.size.width - 20, 18)];
  titleLabel.textColor = KColor_Black;
  titleLabel.textAlignment = NSTextAlignmentCenter;
  titleLabel.font = [UIFont systemFontOfSize:18.0];
  titleLabel.text =
      [type isEqualToString:@"0"] ? @"52赫兹服务协议" : @"52赫兹用户隐私政策";
  [contentView addSubview:titleLabel];

  UIView *lineView = [[UIView alloc]
      initWithFrame:CGRectMake(0, 46, contentView.frame.size.width, 1)];
  lineView.backgroundColor = KColor_codeGray;
  [contentView addSubview:lineView];

  UIScrollView *scrollView = [[UIScrollView alloc]
      initWithFrame:CGRectMake(10,
                               lineView.frame.origin.y +
                                   lineView.frame.size.height,
                               contentView.frame.size.width - 20,
                               contentView.frame.size.height - 55 - 55)];
  [contentView addSubview:scrollView];

  UIActivityIndicatorView *activityIndicator = [[UIActivityIndicatorView alloc]
      initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleGray];
  activityIndicator.center = scrollView.center;
  [activityIndicator startAnimating];
  [scrollView addSubview:activityIndicator];

  lineView = [[UIView alloc]
      initWithFrame:CGRectMake(0,
                               scrollView.frame.origin.y +
                                   scrollView.frame.size.height + 5,
                               contentView.frame.size.width, 1)];
  lineView.backgroundColor = KColor_loginGray;
  [contentView addSubview:lineView];

  UIButton *hideAgreementButton = [[UIButton alloc]
      initWithFrame:CGRectMake(0, contentView.frame.size.height - 65,
                               contentView.frame.size.width, 65)];
  hideAgreementButton.titleLabel.font = [UIFont systemFontOfSize:24.0];
  [hideAgreementButton setTitle:@"知道了" forState:UIControlStateNormal];
  [hideAgreementButton setTitleColor:KColor_Black
                            forState:UIControlStateNormal];
  [hideAgreementButton addTarget:self
                          action:@selector(hideAgreementAction:)
                forControlEvents:UIControlEventTouchUpInside];
  [contentView addSubview:hideAgreementButton];

  [self.view addSubview:agreementView];

  dispatch_async(
      dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        NSString *filePath =
            [type isEqualToString:@"0"]
                ? [[NSBundle mainBundle] pathForResource:@"52fuwu"
                                                  ofType:@"txt"]
                : [[NSBundle mainBundle] pathForResource:@"52yinsi"
                                                  ofType:@"txt"];

        NSString *content = [NemoUtil readFromFullPath:filePath];

        dispatch_async(dispatch_get_main_queue(), ^{
          [activityIndicator stopAnimating];
          [activityIndicator removeFromSuperview];

          if (content) {
            UILabel *contentLabel = [[UILabel alloc]
                initWithFrame:CGRectMake(5, 5, scrollView.frame.size.width - 10,
                                         scrollView.frame.size.height)];
            contentLabel.lineBreakMode = NSLineBreakByWordWrapping;
            contentLabel.numberOfLines = 0;
            contentLabel.font = [UIFont systemFontOfSize:13.0];
            contentLabel.textColor = KColor_Black;
            contentLabel.text = content;
            [scrollView addSubview:contentLabel];
            [contentLabel sizeToFit];

            [scrollView
                setContentSize:CGSizeMake(contentLabel.frame.size.width,
                                          contentLabel.frame.size.height +
                                              contentLabel.frame.origin.y)];
          } else {
            UILabel *errorLabel = [[UILabel alloc]
                initWithFrame:CGRectMake(5, 5, scrollView.frame.size.width - 10,
                                         20)];
            errorLabel.text = @"加载失败，请稍后重试";
            errorLabel.textAlignment = NSTextAlignmentCenter;
            errorLabel.textColor = KColor_Black;
            [scrollView addSubview:errorLabel];
          }
        });
      });
}

- (void)hideAgreementAction:(id)sender {
  [agreementView removeFromSuperview];
  agreementView = nil;
}

#pragma mark - UITextFieldDelegate
- (BOOL)textFieldShouldReturn:(UITextField *)textField {
  if (textField == self.accountTxd) {
    [self.passwordTxd becomeFirstResponder];
  } else if (textField == self.passwordTxd) {
    [textField resignFirstResponder];
    [self loginAction];
  }
  return YES;
}

#pragma mark - Actions
- (void)loginAction {
  if (_accountTxd.text.length == 0) {
    [self showToastFast:@"请输入账号"];
    return;
  }

  if (_passwordTxd.text.length == 0) {
    [self showToastFast:@"请输入密码"];
    return;
  }

  if (!self.isAgreementAccepted) {
    [self showToastFast:@"请阅读并同意用户协议与隐私政策"];
    return;
  }

  [self requestAccountLogin];
}

- (void)requestAccountLogin {
  if (self.goNextView) {
    return;
  }
  self.goNextView = YES;

  [self showToastFast:@"登录中..."];

  @weakify(self);
  [[FTHZAccountManager shared]
      loginWithAccount:self.accountTxd.text
              password:self.passwordTxd.text
            completion:^(UserPersonResult *user, NSError *error) {
              @strongify(self);
              self.goNextView = NO;

              if (error) {
                [self showToastFast:error.localizedDescription];
                return;
              }
              [[NSUserDefaults standardUserDefaults]
                  setObject:self.accountTxd.text
                     forKey:@"LastLoginAccount"];
              [[NSUserDefaults standardUserDefaults] synchronize];
              [self fillMoreInfoIfNeededWithUserInfo:user];
            }];
}

- (void)fillMoreInfoIfNeededWithUserInfo:(UserPersonResult *)user {
  if ([user.NOOType isEqualToString:@"0"]) {
    GenderVC *vc = [[GenderVC alloc] init];
    vc.numtype = user.rank;
    [self.navigationController pushViewController:vc animated:YES];
  } else {
    if ([self.navigationController
            conformsToProtocol:@protocol(FTHZLoginResultDelegate)]) {
      [(id<FTHZLoginResultDelegate>)self.navigationController
                 loginViewController:self
          didFinishedLoginWithResult:FTHZLoginResultSuccess
                                user:user
                               error:nil];
    }
  }
}

- (void)backAction {
  [self.navigationController popViewControllerAnimated:YES];
}

#pragma mark - View Lifecycle
- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];
  [self.navigationController setNavigationBarHidden:YES animated:NO];
  self.goNextView = NO;
}

@end