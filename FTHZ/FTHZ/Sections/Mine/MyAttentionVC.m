#import "MyAttentionVC.h"
#import "MyAttentionModel.h"
#import "MyAttentionPageModel.h"
#import "MyAttentionTableViewCell.h"
#import "ReleaseAttentionModel.h"
#import "WhaleDetailVC.h"
#import "_2hz-Swift.h"

#define MyAttentionTableViewCellID @"MyAttentionTableViewCellID"

@interface MyAttentionVC () <UITableViewDelegate, UITableViewDataSource>
@property(nonatomic, strong) UITableView *tableView;
@property(nonatomic, assign) NSInteger page;
@property(nonatomic, assign) BOOL isHeader;
@property(nonatomic, strong) NSMutableArray *cellData;
@property(nonatomic, assign) NSInteger lastId;
@end

@implementation MyAttentionVC
- (void)loadData {
  __weak typeof(self) wSelf = self;
  if (!_cellData) {
    _cellData = [NSMutableArray new];
  }

  NSString *lastIdStr =
      self.isHeader ? nil
                    : [NSString stringWithFormat:@"%ld", (long)self.lastId];
  [MyAttentionModel getMyAttentionModel:lastIdStr
      success:^(NSDictionary *resultObject) {
        MyAttentionModel *member =
            [MyAttentionModel mj_objectWithKeyValues:resultObject];
        if ([member.success boolValue]) {
          if (wSelf.isHeader) {
            [wSelf.cellData removeAllObjects];
          }

          if (member.data.count > 0) {
            for (int i = 0; i < member.data.count; i++) {
              AttentionUserResult *userMember = [AttentionUserResult
                  mj_objectWithKeyValues:[member.data objectAtIndex:i]];
              [wSelf.cellData addObject:userMember];
            }
          }

          wSelf.lastId = member.lastId;

          if (member.lastId == 0) {
            [wSelf.tableView.mj_footer endRefreshingWithNoMoreData];
          } else {
            [wSelf.tableView.mj_footer resetNoMoreData];
          }

          [wSelf.tableView reloadData];
          [wSelf.tableView.mj_header endRefreshing];
        }
      }
      failure:^(NSError *requestErr) {
        [wSelf.tableView.mj_header endRefreshing];
        [wSelf.tableView.mj_footer endRefreshing];
      }];
}

- (void)loadMoreData {
  self.isHeader = NO;
  [self loadData];
}

- (void)refreshData {
  self.isHeader = YES;
  [self loadData];
}

- (void)viewDidLoad {
  [super viewDidLoad];
  [self.safeContentView addSubview:self.tableView];
  @weakify(self);
  [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.top.right.left.equalTo(self.safeContentView);
    make.bottom.equalTo(self.view);
  }];
  self.isHeader = YES;
  [self loadData];
}
- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];
  [AppConfig statusbarStyle:YES];
}

- (UITableView *)tableView {
  if (!_tableView) {
    _tableView = [[UITableView alloc] init];
    _tableView.delegate = self;
    _tableView.dataSource = self;
    _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    [_tableView registerClass:[MyAttentionTableViewCell class]
        forCellReuseIdentifier:MyAttentionTableViewCellID];

    MJRefreshAutoNormalFooter *footer = [MJRefreshAutoNormalFooter
        footerWithRefreshingTarget:self
                  refreshingAction:@selector(loadMoreData)];
    [footer setTitle:@"" forState:MJRefreshStateIdle];
    [footer setTitle:@"" forState:MJRefreshStateNoMoreData];
    self.tableView.mj_footer = footer;

    MJChiBaoZiHeader *header =
        [MJChiBaoZiHeader headerWithRefreshingTarget:self
                                    refreshingAction:@selector(refreshData)];
    header.lastUpdatedTimeLabel.hidden = YES;
    header.stateLabel.hidden = YES;
    self.tableView.mj_header = header;
  }
  return _tableView;
}

#pragma mark - Table view data source
- (CGFloat)tableView:(UITableView *)tableView
    heightForFooterInSection:(NSInteger)section {
  return 50.0;
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForHeaderInSection:(NSInteger)section {
  return 0.01f;
}

- (UIView *)tableView:(UITableView *)tableView
    viewForFooterInSection:(NSInteger)section {
  return [[UIView alloc] initWithFrame:CGRectZero];
}

- (UIView *)tableView:(UITableView *)tableView
    viewForHeaderInSection:(NSInteger)section {
  return [[UIView alloc] initWithFrame:CGRectZero];
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForRowAtIndexPath:(NSIndexPath *)indexPath {
  return 72 * kWidthFactor;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
  return 1;
}

- (NSInteger)tableView:(UITableView *)tableView
    numberOfRowsInSection:(NSInteger)section {
  return self.cellData.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView
         cellForRowAtIndexPath:(NSIndexPath *)indexPath {
  AttentionUserResult *userMember = [_cellData objectAtIndex:indexPath.row];
  MyAttentionTableViewCell *cell = (MyAttentionTableViewCell *)[tableView
      dequeueReusableCellWithIdentifier:MyAttentionTableViewCellID
                           forIndexPath:indexPath];
  if (!cell) {
    cell = [[MyAttentionTableViewCell alloc]
          initWithStyle:(UITableViewCellStyleDefault)
        reuseIdentifier:MyAttentionTableViewCellID];
  }

  [cell prepareForReuse];

  [cell.iconImage
      sd_setImageWithURL:[NemoUtil getUrlWithUserSmallIcon:userMember.avatar]
        placeholderImage:KImage_name(@"empty")];
  cell.nameLabel.text = userMember.nickname;
  cell.signLabel.text = userMember.signature;
  cell.nameLabel.textColor = KColor_HighBlue;

  if (userMember.colorFont != nil) {
    [cell.nameLabel applyGradientWithType:userMember.colorFont.type
                               expireTime:userMember.colorFont.expire_time];
  }

  if (userMember.create_time && userMember.create_time.length > 0) {
    NSTimeInterval timeInterval = [userMember.create_time doubleValue];
    cell.timeLabel.text = [NemoUtil distanceTimeWithBeforeTime:timeInterval];
  }

  if ([userMember.type isEqualToString:@"1"] ||
      [userMember.type isEqualToString:@"2"]) {
    cell.attentionBtn.hidden = NO;
    NSString *imageName =
        [userMember.type isEqualToString:@"1"] ? @"已经关注" : @"互相关注";
    [cell.attentionBtn setBackgroundImage:KImage_name(imageName)
                                 forState:UIControlStateNormal];
    cell.attentionBtn.tag = [userMember.userid integerValue];
    [cell.attentionBtn addTarget:self
                          action:@selector(releaseAtt:)
                forControlEvents:UIControlEventTouchUpInside];
  } else {
    cell.attentionBtn.hidden = YES;
  }

  return cell;
}

- (void)tableView:(UITableView *)tableView
    didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
  [tableView deselectRowAtIndexPath:indexPath animated:YES];
  AttentionUserResult *userMember = [_cellData objectAtIndex:indexPath.row];
  WhaleDetailVC *ddVC = [[WhaleDetailVC alloc] init];
  ddVC.uid = userMember.userid;
  [self.navigationController pushViewController:ddVC animated:YES];
}

- (void)releaseAtt:(UIButton *)btn {
  __weak typeof(self) wSelf = self;
  UIAlertController *alertController =
      [UIAlertController alertControllerWithTitle:@"提示"
                                          message:@"确认取消关注？"
                                   preferredStyle:UIAlertControllerStyleAlert];
  UIAlertAction *cancelAction =
      [UIAlertAction actionWithTitle:@"取消"
                               style:UIAlertActionStyleCancel
                             handler:nil];
  UIAlertAction *okAction = [UIAlertAction
      actionWithTitle:@"确定"
                style:UIAlertActionStyleDefault
              handler:^(UIAlertAction *_Nonnull action) {
                [ReleaseAttentionModel
                    postReleaseAttentionModel:[NSString
                                                  stringWithFormat:@"%ld",
                                                                   btn.tag]
                                      success:^(NSDictionary *resultObject) {
                                        ReleaseAttentionModel *member =
                                            [ReleaseAttentionModel
                                                mj_objectWithKeyValues:
                                                    resultObject];
                                        if ([member.success boolValue]) {
                                          [wSelf loadData];
                                          [NOTIFICENTER
                                              postNotificationName:MyAttToAttMy
                                                            object:nil];
                                        } else {
                                        }
                                      }
                                      failure:^(NSError *requestErr){

                                      }];
              }];
  [alertController addAction:cancelAction];
  [alertController addAction:okAction];
  [self presentViewController:alertController animated:YES completion:nil];
}

- (void)dealloc {
  [NOTIFICENTER removeObserver:self];
}

@end
