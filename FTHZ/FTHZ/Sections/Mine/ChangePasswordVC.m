
#import "ChangePasswordVC.h"
#import "ChangeAccountVC.h"
#import "ChangePasswordModel.h"
#import "VerifyCodeVC.h"

@interface ChangePasswordVC ()

@property(nonatomic, strong) UITextField *oldPwdTxd;
@property(nonatomic, strong) UITextField *pwdTxd;
@property(nonatomic, strong) UITextField *confirmPwdTxd;
@property(nonatomic, strong) UIButton *submitBtn;
@property(nonatomic, assign) BOOL needOldPassword;
@property(nonatomic, assign) BOOL isNavigatingToAccountVC;
@property(nonatomic, assign) BOOL hasCheckedStatus;
@property(nonatomic, strong) UIButton *forgotPasswordBtn;
@property(nonatomic, assign) BOOL skipOldPassword;

@end

@implementation ChangePasswordVC

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];

  [self.navigationController setNavigationBarHidden:NO animated:NO];
  self.showBackBtn = YES;
  [AppConfig statusbarStyle:YES];
}

- (void)viewWillDisappear:(BOOL)animated {
  [super viewWillDisappear:animated];

  if (self.isNavigatingToAccountVC) {
    [self.navigationController setNavigationBarHidden:YES animated:NO];
  }
}

- (void)viewDidDisappear:(BOOL)animated {
  [super viewDidDisappear:animated];

  if (!self.isNavigatingToAccountVC) {
    [self.navigationController setNavigationBarHidden:NO animated:NO];
  }
}

- (void)viewDidLoad {
  [super viewDidLoad];
  self.title = @"修改密码";
  [self checkPasswordStatus];
}

- (void)checkPasswordStatus {
  if (self.hasCheckedStatus) {
    return;
  }

  __weak typeof(self) wSelf = self;
  [ChangePasswordModel
      checkPasswordStatus:^(NSDictionary *resultObject) {
        ChangePasswordModel *member =
            [ChangePasswordModel mj_objectWithKeyValues:resultObject];

        dispatch_async(dispatch_get_main_queue(), ^{
          wSelf.hasCheckedStatus = YES;

          if ([member.code intValue] == 3500) {
            [wSelf showToastFast:member.msg];

            dispatch_after(
                dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)),
                dispatch_get_main_queue(), ^{
                  ChangeAccountVC *accountVC = [[ChangeAccountVC alloc] init];
                  accountVC.didSetAccountSuccess = ^{
                    [wSelf.navigationController popViewControllerAnimated:YES];
                  };

                  wSelf.isNavigatingToAccountVC = YES;
                  [wSelf.navigationController setNavigationBarHidden:YES
                                                            animated:NO];
                  [wSelf.navigationController pushViewController:accountVC
                                                        animated:YES];
                });
          } else if ([member.code intValue] == 3501) {
            [wSelf showToastFast:member.msg];
            wSelf.needOldPassword = NO;
            [wSelf setupUI];
          } else if ([member.code intValue] == 0) {
            wSelf.needOldPassword = YES;
            [wSelf setupUI];
          }
        });
      }
      failure:^(NSError *requestErr) {
        dispatch_async(dispatch_get_main_queue(), ^{
          wSelf.hasCheckedStatus = YES;
          [wSelf showToastFast:@"数据有误,请检查网络后重试"];
        });
      }];
}

- (void)setupUI {
  self.oldPwdTxd = nil;
  self.pwdTxd = nil;
  self.confirmPwdTxd = nil;
  self.submitBtn = nil;
  self.forgotPasswordBtn = nil;

  self.pwdTxd = [[UITextField alloc] init];
  self.pwdTxd.placeholder = @"请输入新密码";
  self.pwdTxd.secureTextEntry = YES;
  self.pwdTxd.backgroundColor = [UIColor colorWithRed:0.95
                                                green:0.95
                                                 blue:0.95
                                                alpha:1.0];
  self.pwdTxd.layer.cornerRadius = 8 * kMainTemp;
  self.pwdTxd.layer.masksToBounds = YES;
  UIView *leftView =
      [[UIView alloc] initWithFrame:CGRectMake(0, 0, 15, 44 * kMainTemp)];
  self.pwdTxd.leftView = leftView;
  self.pwdTxd.leftViewMode = UITextFieldViewModeAlways;
  [self.view addSubview:self.pwdTxd];

  UILabel *pwdRuleLabel = [[UILabel alloc] init];
  pwdRuleLabel.numberOfLines = 0;
  pwdRuleLabel.font = [UIFont systemFontOfSize:12];
  pwdRuleLabel.textColor = [UIColor grayColor];
  NSString *ruleText = @"密码必须包含：\n• 小写字母\n• 大写字母\n• 数字\n• "
                       @"英文标点符号(,.!@#$%^&*)";
  NSMutableParagraphStyle *paragraphStyle =
      [[NSMutableParagraphStyle alloc] init];
  paragraphStyle.lineSpacing = 4;
  paragraphStyle.lineBreakMode = NSLineBreakByWordWrapping;

  NSMutableAttributedString *attributedString =
      [[NSMutableAttributedString alloc] initWithString:ruleText];
  [attributedString addAttribute:NSParagraphStyleAttributeName
                           value:paragraphStyle
                           range:NSMakeRange(0, ruleText.length)];
  [attributedString addAttribute:NSFontAttributeName
                           value:[UIFont systemFontOfSize:12]
                           range:NSMakeRange(0, ruleText.length)];
  [attributedString addAttribute:NSForegroundColorAttributeName
                           value:[UIColor grayColor]
                           range:NSMakeRange(0, ruleText.length)];

  pwdRuleLabel.attributedText = attributedString;
  [self.view addSubview:pwdRuleLabel];

  self.confirmPwdTxd = [[UITextField alloc] init];
  self.confirmPwdTxd.placeholder = @"请确认新密码";
  self.confirmPwdTxd.secureTextEntry = YES;
  self.confirmPwdTxd.backgroundColor = [UIColor colorWithRed:0.95
                                                       green:0.95
                                                        blue:0.95
                                                       alpha:1.0];
  self.confirmPwdTxd.layer.cornerRadius = 8 * kMainTemp;
  self.confirmPwdTxd.layer.masksToBounds = YES;
  self.confirmPwdTxd.leftView =
      [[UIView alloc] initWithFrame:CGRectMake(0, 0, 15, 44 * kMainTemp)];
  self.confirmPwdTxd.leftViewMode = UITextFieldViewModeAlways;
  [self.view addSubview:self.confirmPwdTxd];

  self.submitBtn = [FlatButton buttonWithType:UIButtonTypeSystem];
  self.submitBtn.backgroundColor = KColor_HighBlack;
  [self.submitBtn setTitle:@"确认修改" forState:UIControlStateNormal];
  self.submitBtn.titleLabel.font = SourceHanSerifMediumFont(16 * kMainTemp);
  [self.submitBtn setTitleColor:KColor_White forState:UIControlStateNormal];
  self.submitBtn.layer.cornerRadius = 30 * kMainTemp;
  self.submitBtn.layer.masksToBounds = YES;
  [self.submitBtn addTarget:self
                     action:@selector(submitButtonTapped)
           forControlEvents:UIControlEventTouchUpInside];
  [self.view addSubview:self.submitBtn];

  if (self.needOldPassword && !self.skipOldPassword) {
    self.oldPwdTxd = [[UITextField alloc] init];
    self.oldPwdTxd.placeholder = @"请输入原密码";
    self.oldPwdTxd.secureTextEntry = YES;
    self.oldPwdTxd.backgroundColor = [UIColor colorWithRed:0.95
                                                     green:0.95
                                                      blue:0.95
                                                     alpha:1.0];
    self.oldPwdTxd.layer.cornerRadius = 8 * kMainTemp;
    self.oldPwdTxd.layer.masksToBounds = YES;
    self.oldPwdTxd.leftView =
        [[UIView alloc] initWithFrame:CGRectMake(0, 0, 15, 44 * kMainTemp)];
    self.oldPwdTxd.leftViewMode = UITextFieldViewModeAlways;
    [self.view addSubview:self.oldPwdTxd];

    self.forgotPasswordBtn = [UIButton buttonWithType:UIButtonTypeSystem];
    [self.forgotPasswordBtn setTitle:@"忘记原密码？"
                            forState:UIControlStateNormal];
    [self.forgotPasswordBtn setTitleColor:[UIColor lightGrayColor]
                                 forState:UIControlStateNormal];
    self.forgotPasswordBtn.titleLabel.font = [UIFont systemFontOfSize:14];
    [self.forgotPasswordBtn addTarget:self
                               action:@selector(forgotPasswordTapped)
                     forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.forgotPasswordBtn];
  }

  [self setupConstraints:pwdRuleLabel];
}

- (void)setupConstraints:(UILabel *)pwdRuleLabel {
  UIView *firstField = self.needOldPassword && !self.skipOldPassword
                           ? self.oldPwdTxd
                           : self.pwdTxd;

  [firstField mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(self.view).offset(20 * kMainTemp);
    make.left.equalTo(self.view).offset(20 * kMainTemp);
    make.right.equalTo(self.view).offset(-20 * kMainTemp);
    make.height.equalTo(@(44 * kMainTemp));
  }];

  if (self.needOldPassword && !self.skipOldPassword) {
    [self.pwdTxd mas_makeConstraints:^(MASConstraintMaker *make) {
      make.top.equalTo(self.oldPwdTxd.mas_bottom).offset(20 * kMainTemp);
      make.left.right.height.equalTo(self.oldPwdTxd);
    }];
  }

  [pwdRuleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(self.pwdTxd.mas_bottom).offset(15 * kMainTemp);
    make.left.equalTo(self.pwdTxd).offset(15 * kMainTemp);
    make.right.equalTo(self.pwdTxd);
  }];

  [self.confirmPwdTxd mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(pwdRuleLabel.mas_bottom).offset(15 * kMainTemp);
    make.left.right.height.equalTo(self.pwdTxd);
  }];

  [self.submitBtn mas_makeConstraints:^(MASConstraintMaker *make) {
    make.bottom.equalTo(self.view.mas_bottom).offset(-100 * kMainTemp);
    make.centerX.equalTo(self.view);
    make.size.mas_equalTo(CGSizeMake(160 * kMainTemp, 60 * kMainTemp));
  }];

  if (self.needOldPassword && !self.skipOldPassword) {
    [self.forgotPasswordBtn mas_makeConstraints:^(MASConstraintMaker *make) {
      make.top.equalTo(self.submitBtn.mas_bottom).offset(15 * kMainTemp);
      make.centerX.equalTo(self.view);
    }];
  }
}

- (void)forgotPasswordTapped {
  VerifyCodeVC *verifyVC = [[VerifyCodeVC alloc] init];
  __weak typeof(self) weakSelf = self;
  verifyVC.didVerifySuccess = ^{
    dispatch_async(dispatch_get_main_queue(), ^{
      weakSelf.skipOldPassword = YES;

      for (UIView *subview in weakSelf.view.subviews) {
        [subview removeFromSuperview];
      }

      [weakSelf setupUI];

      [weakSelf showToastFast:@"请设置新密码"];
    });
  };
  [self.navigationController pushViewController:verifyVC animated:YES];
}

- (void)submitButtonTapped {
  if (self.needOldPassword && !self.skipOldPassword) {
    if (self.oldPwdTxd.text.length == 0) {
      [self showToastFast:@"请输入原密码"];
      return;
    }
  }

  if (self.pwdTxd.text.length == 0) {
    [self showToastFast:@"请输入新密码"];
    return;
  }

  if (self.confirmPwdTxd.text.length == 0) {
    [self showToastFast:@"请确认新密码"];
    return;
  }

  NSString *passwordRegex =
      @"^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[,.!@#$%^&*]).+$";
  NSPredicate *passwordTest =
      [NSPredicate predicateWithFormat:@"SELF MATCHES %@", passwordRegex];

  if (![passwordTest evaluateWithObject:self.pwdTxd.text]) {
    [self showToastFast:@"密码必须包含大小写字母、数字和标点符号"];
    return;
  }

  if (![self.pwdTxd.text isEqualToString:self.confirmPwdTxd.text]) {
    [self showToastFast:@"两次输入的新密码不一致"];
    return;
  }

  [self changePassword];
}

- (void)changePassword {
  __weak typeof(self) wSelf = self;
  [ChangePasswordModel
      postChangePasswordModel:self.needOldPassword ? self.oldPwdTxd.text : @""
      newPwd:self.pwdTxd.text
      success:^(NSDictionary *resultObject) {
        ChangePasswordModel *member =
            [ChangePasswordModel mj_objectWithKeyValues:resultObject];
        if ([member.success boolValue]) {
          [wSelf showToastFast:@"密码修改成功"];
          dispatch_after(
              dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)),
              dispatch_get_main_queue(), ^{
                [wSelf.navigationController popViewControllerAnimated:YES];
              });
        } else {
          [wSelf showToastFast:member.msg];
        }
      }
      failure:^(NSError *requestErr) {
        [wSelf showToastFast:@"数据有误,请检查网络后重试"];
      }];
}

@end