#import "MyDynamicVC.h"
#import "AttentionDetailVC.h"
#import "AttentionTableViewCell.h"
#import "UserAffairListModel.h"

#import "DoLikeModel.h"
#import "TagAffairVC.h"

#import "_2hz-Swift.h"

#define MyDynamicVCTableViewCellID @"MyDynamicVCTableViewCellID"

@interface MyDynamicVC () <UITableViewDelegate, UITableViewDataSource,
                           MomentDelegate> {
  NSInteger currentPage;
}
@property(nonatomic, strong) multiColorTableView *tableView;
@property(nonatomic, assign) BOOL isHeader;

@property(nonatomic, strong) NSArray *cellData;
@property(nonatomic, strong) AffairListModelResult *tempMember;
@property(nonatomic, strong) homepageHeadView *headView;
@end

@implementation MyDynamicVC

- (void)loadData {
  @weakify(self);
  if (!self.cellData) {
    self.cellData = [NSArray new];
  }

  [UserAffairListModel getUserAffairListModel:CurrentUser.userid
      page:[NSString stringWithFormat:@"%ld", currentPage]
      size:@"20"
      success:^(NSDictionary *resultObject) {
        @strongify(self);
        AffairListModel *member =
            [AffairListModel mj_objectWithKeyValues:resultObject];
        if ([member.success boolValue]) {
          if (self->currentPage == 1) {
            self.cellData = [NSArray new];
          }

          NSMutableArray *tempArr =
              [NSMutableArray arrayWithArray:self.cellData];
          AffairListModelResult *tempMember = [AffairListModelResult
              mj_objectWithKeyValues:member.data.firstObject];
          for (int i = 0; i < tempMember.data.count; i++) {
            DynamicModelResult *dy = [DynamicModelResult
                mj_objectWithKeyValues:[tempMember.data objectAtIndex:i]];
            [tempArr addObject:dy];
          }
          self.cellData = tempArr.copy;
          if ([tempMember.count integerValue] > self.cellData.count) {
            if (!self.tableView.mj_footer) {
              MJChiBaoZiFooter *footer = [MJChiBaoZiFooter
                  footerWithRefreshingTarget:self
                            refreshingAction:@selector(loadMoreData)];
              footer.refreshingTitleHidden = YES;
              footer.stateLabel.hidden = YES;
              [footer setTitle:@"" forState:MJRefreshStateIdle];
              self.tableView.mj_footer = footer;
            }
          } else {
            [self.tableView.mj_footer removeFromSuperview];
            self.tableView.mj_footer = nil;
          }
        }
        [self.tableView.mj_footer endRefreshing];
        [self.tableView reloadData];
      }
      failure:^(NSError *requestErr) {
        [self.tableView.mj_footer endRefreshing];
      }];
}

- (void)viewDidLoad {
  [super viewDidLoad];
  self.tableView.estimatedRowHeight = 200;
  self.tableView.rowHeight = UITableViewAutomaticDimension;
  currentPage = 1;
  @weakify(self);
  self.safeContentView.backgroundColor = KColor_HighBlack;

  [self.safeContentView addSubview:self.tableView];
  [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.edges.equalTo(self.safeContentView);
  }];

  _headView = [[homepageHeadView alloc]
      initWithFrame:CGRectMake(0, 0, kMainWidth, 520 * kWidthFactor)];
  _headView.backgroundColor = UIColor.clearColor;
  self.tableView.tableHeaderView = _headView;

  [self loadData];

  [NOTIFICENTER addObserver:self
                   selector:@selector(sendSussessTost)
                       name:DySendSussreloadData
                     object:nil];
  [NOTIFICENTER addObserver:self
                   selector:@selector(sendSussessTost)
                       name:AllReloadUI
                     object:nil];
  [NOTIFICENTER addObserver:self
                   selector:@selector(sendSussessTost)
                       name:UserHomeReload
                     object:nil];
}

- (void)loadMoreData {
  currentPage += 1;
  [self loadData];
}

- (void)loadAData {
  [self sendSussessTost];
}

- (void)sendSussessTost {
  if (_tableView) {
    currentPage = 1;
    [self loadData];
  }
}

- (UITableView *)tableView {
  if (!_tableView) {
    _tableView = [[multiColorTableView alloc] initWithFrame:CGRectZero];
    _tableView.delegate = self;
    _tableView.dataSource = self;
    _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    [_tableView registerClass:[AttentionTableViewCell class]
        forCellReuseIdentifier:MyDynamicVCTableViewCellID];
  }
  return _tableView;
}
#pragma mark - Table view data source
- (CGFloat)tableView:(UITableView *)tableView
    heightForFooterInSection:(NSInteger)section {
  return 50.0;
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForHeaderInSection:(NSInteger)section {
  if (_cellData.count == 0) {
    return 96 * kWidthFactor;
  }
  return 12 * kWidthFactor;
}

- (UIView *)tableView:(UITableView *)tableView
    viewForFooterInSection:(NSInteger)section {
  return [[UIView alloc] initWithFrame:CGRectZero];
}

- (UIView *)tableView:(UITableView *)tableView
    viewForHeaderInSection:(NSInteger)section {
  if (_cellData.count == 0) {
    UIView *bgView = [[UIView alloc]
        initWithFrame:CGRectMake(0, 0, kMainWidth, 96 * kWidthFactor)];
    UILabel *tipL = [UILabel new];
    tipL.text = @"这是一只沉默的小鲸鱼";
    tipL.font = SourceHanSerifMediumFont(12 * kWidthFactor);
    tipL.textColor = KColor_textTinyGray;
    [bgView addSubview:tipL];
    [tipL mas_makeConstraints:^(MASConstraintMaker *make) {
      make.centerX.bottom.equalTo(bgView);
    }];
    return bgView;
  }
  return [[UIView alloc] initWithFrame:CGRectZero];
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
  return 1;
}

- (NSInteger)tableView:(UITableView *)tableView
    numberOfRowsInSection:(NSInteger)section {
  return _cellData.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView
         cellForRowAtIndexPath:(NSIndexPath *)indexPath {
  DynamicModelResult *dy = [_cellData objectAtIndex:indexPath.row];
  AttentionTableViewCell *cell = (AttentionTableViewCell *)[tableView
      dequeueReusableCellWithIdentifier:MyDynamicVCTableViewCellID
                           forIndexPath:indexPath];
  if (!cell) {
    cell = [[AttentionTableViewCell alloc]
          initWithStyle:(UITableViewCellStyleDefault)
        reuseIdentifier:MyDynamicVCTableViewCellID];
  }
  [cell setDynamic:dy showTime:YES];

  [cell.awesomeBtn addTarget:self
                      action:@selector(onTouchBtnInCell:)
            forControlEvents:(UIControlEventTouchUpInside)];
  cell.commentBtn.tag = indexPath.row;
  [cell.commentBtn addTarget:self
                      action:@selector(onTouchContentCell:)
            forControlEvents:(UIControlEventTouchUpInside)];

  return cell;
}

- (void)onTouchBtnTagInCell:(UIButton *)sender {
  CGPoint point = sender.center;
  point = [self.tableView convertPoint:point fromView:sender.superview];
  NSIndexPath *indexpath = [self.tableView indexPathForRowAtPoint:point];
  DynamicModelResult *dy = [_cellData objectAtIndex:indexpath.row];
  TagAffairVC *ddVC = [[TagAffairVC alloc] init];
  ddVC.tagId = dy.affair.tagType;
  ddVC.tagName = dy.affair.tagName;
  [self.navigationController pushViewController:ddVC animated:YES];
}

- (void)onTouchBtnInCell:(UIButton *)sender {
  CGPoint point = sender.center;
  point = [self.tableView convertPoint:point fromView:sender.superview];
  NSIndexPath *indexpath = [self.tableView indexPathForRowAtPoint:point];
  DynamicModelResult *dy = [_cellData objectAtIndex:indexpath.row];
  [self doLike:dy.affair.aid uid:dy.user.uid indexRow:indexpath.row];
}

- (void)onTouchContentCell:(UIButton *)sender {
  DynamicModelResult *dy = [_cellData objectAtIndex:sender.tag];
  AttentionDetailVC *ddVC = [[AttentionDetailVC alloc] init];
  ddVC.ddy = dy;
  ddVC.isTouchComment = YES;
  [self.navigationController pushViewController:ddVC animated:YES];
}

- (void)doLike:(NSString *)aid
           uid:(NSString *)uid
      indexRow:(NSInteger)indexRow {
  DynamicModelResult *dy = [_cellData objectAtIndex:indexRow];
  __weak typeof(self) wSelf = self;
  void (^action)(NSDictionary *) = ^(NSDictionary *resultObject) {
    DoLikeModel *member = [DoLikeModel mj_objectWithKeyValues:resultObject];
    if ([member.success boolValue]) {
      if ([dy.affair.likeRs isEqualToString:@"0"]) {
        NSInteger tepLike = [dy.affair.likeNum integerValue];
        tepLike++;
        dy.affair.likeNum = [NSString stringWithFormat:@"%ld", tepLike];
        dy.affair.likeRs = @"1";
        NSMutableArray *newArray = [wSelf.cellData mutableCopy];
        [newArray replaceObjectAtIndex:indexRow withObject:dy];
        wSelf.cellData = newArray;
      } else {
        NSInteger tepLike = [dy.affair.likeNum integerValue];
        tepLike--;
        dy.affair.likeNum = [NSString stringWithFormat:@"%ld", tepLike];
        dy.affair.likeRs = @"0";
        NSMutableArray *newArray = [wSelf.cellData mutableCopy];
        [newArray replaceObjectAtIndex:indexRow withObject:dy];
        wSelf.cellData = newArray;
      }
      NSIndexPath *indexPath = [NSIndexPath indexPathForRow:indexRow
                                                  inSection:0];
      [wSelf.tableView
          reloadRowsAtIndexPaths:[NSArray arrayWithObjects:indexPath, nil]
                withRowAnimation:UITableViewRowAnimationNone];
    } else if ([member.code integerValue] == FTHZErrorCodeAccountBanned) {
      ShowBanTip(member.msg);
    } else {
      [wSelf.view makeToast:member.msg
                   duration:1.0
                   position:CSToastPositionCenter];
    }
  };
  if ([dy.affair upvoted]) {
    [DoLikeModel postUnlikeModel:uid
                       contentid:aid
                         success:action
                         failure:^(NSError *requestErr) {
                           [self.view makeToast:@"数据有误,请检查网络后重试"
                                       duration:2.0
                                       position:CSToastPositionCenter];
                         }];
  } else {
    [DoLikeModel postLikeModel:uid
                     contentid:aid
                       success:action
                       failure:^(NSError *requestErr) {
                         [self.view makeToast:@"数据有误,请检查网络后重试"
                                     duration:2.0
                                     position:CSToastPositionCenter];
                       }];
  }
}

#pragma mark - UITableViewDelegate
- (void)tableView:(UITableView *)tableView
    didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
  DynamicModelResult *dy = [_cellData objectAtIndex:indexPath.row];

  FZMomentVC *ddVC = [[FZMomentVC alloc] init];
  ddVC.userId = dy.user.uid;
  ddVC.momentId = dy.affair.aid;
  ddVC.delegate = self;
  ddVC.indexIntable = indexPath;

  [self.navigationController pushViewController:ddVC animated:YES];
  [tableView deselectRowAtIndexPath:indexPath animated:YES];
}

- (void)momentDeletedWithIndex:(NSIndexPath *)index {
  NSMutableArray *tempArr = [NSMutableArray arrayWithArray:_cellData];
  if (index.row < tempArr.count) {
    [tempArr removeObjectAtIndex:index.row];
    _cellData = tempArr.copy;
    if (_cellData.count > 0) {
      [_tableView beginUpdates];
      [_tableView deleteRowsAtIndexPaths:@[ index ]
                        withRowAnimation:UITableViewRowAnimationFade];
      [_tableView endUpdates];
    } else {
      [_tableView reloadData];
    }
  }
}

- (void)dealloc {
  [NOTIFICENTER removeObserver:self];
}

@end
