#import "ChangeNameVC.h"
#import "UserUserinfoModel.h"

@interface ChangeNameVC () {
}
@property(nonatomic, strong) FlatButton *loginBtn;
@property(nonatomic, strong) UITextField *nameTxd;

@end

@implementation ChangeNameVC
- (void)saveData {
  @weakify(self);
  [HUD show];
  [AccountManager
      updateUserInfoWithName:self.nameTxd.text
                      gender:CurrentUser.gender
                      avatar:CurrentUser.avatar
                   signature:CurrentUser.signature
                       brith:CurrentUser.birth
                  completion:^(UserPersonResult *_Nullable user,
                               NSError *_Nullable error) {
                    [HUD dissmiss];
                    @strongify(self);
                    if (error) {
                      [self showToastFast:error.localizedDescription];
                      return;
                    }
                    [self dismissView];
                  }];
}

- (void)viewDidLoad {
  [super viewDidLoad];
  @weakify(self);
  MASViewAttribute *bottom = [self setTitleForV199:@"昵称"];
  UIButton *backButton = [self.safeContentView.subviews firstObject];

  _nameTxd = [[UITextField alloc] init];
  _nameTxd.placeholder = @"请输入昵称";
  _nameTxd.text = CurrentUser.nickname;
  // 添加输入框样式
  _nameTxd.backgroundColor = [UIColor colorWithRed:0.95
                                             green:0.95
                                              blue:0.95
                                             alpha:1.0];
  _nameTxd.layer.cornerRadius = 8 * kMainTemp;
  _nameTxd.layer.masksToBounds = YES;
  // 添加左边距
  UIView *leftView =
      [[UIView alloc] initWithFrame:CGRectMake(0, 0, 15, 44 * kMainTemp)];
  _nameTxd.leftView = leftView;
  _nameTxd.leftViewMode = UITextFieldViewModeAlways;
  [self.safeContentView addSubview:_nameTxd];
  [_nameTxd mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.equalTo(self.safeContentView).with.offset(20 * kMainTemp);
    make.right.equalTo(self.safeContentView).with.offset(-20 * kMainTemp);
    make.top.equalTo(bottom).with.offset(30 * kMainTemp);
    make.height.equalTo(@(44 * kMainTemp));
  }];
  //    [self.nameTxd addTarget:self action:@selector(textField1TextChange:)
  //    forControlEvents:UIControlEventEditingChanged];
  UILabel *nameRuleLabel = [[UILabel alloc] init];
  nameRuleLabel.numberOfLines = 0;
  nameRuleLabel.font = [UIFont systemFontOfSize:12];
  nameRuleLabel.textColor = [UIColor grayColor];
  nameRuleLabel.text =
      @"昵称要求：\n• 只能包含中文、英文字母和数字\n• 长度不能超过8个字符";
  [self.safeContentView addSubview:nameRuleLabel];
  [nameRuleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.equalTo(self.nameTxd).offset(15 * kMainTemp);
    make.top.equalTo(self.nameTxd.mas_bottom).offset(10);
    make.right.equalTo(self.nameTxd);
  }];
  UIView *lineLabel = [[UIView alloc] init];
  lineLabel.backgroundColor = KColor_loginGray;
  [self.safeContentView addSubview:lineLabel];
  [lineLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.equalTo(self.safeContentView).with.offset(20 * kMainTemp);
    make.right.equalTo(self.safeContentView).with.offset(-20 * kMainTemp);
    make.top.equalTo(nameRuleLabel.mas_bottom).offset(10);
    make.height.mas_equalTo(@(0.5 * kMainTemp));
  }];

  // UIButton *saveButton = [UIButton buttonWithType:(UIButtonTypeCustom)];
  // saveButton.frame =
  //     CGRectMake(kMainWidth - 36 * kWidthFactor, 29 * kWidthFactor,
  //                30 * kWidthFactor, 20 * kWidthFactor);
  // //    _backButton.adjustsImageWhenHighlighted = YES;
  // [saveButton setTitleColor:KColor_HighBlack forState:UIControlStateNormal];
  // [saveButton setTitle:@"完成" forState:UIControlStateNormal];
  // saveButton.titleLabel.font = SourceHanSerifMediumFont(14 * kWidthFactor);
  // [saveButton addTarget:self
  //                action:@selector(saveAction)
  //      forControlEvents:(UIControlEventTouchUpInside)];
  // [self.safeContentView addSubview:saveButton];
  // [saveButton mas_makeConstraints:^(MASConstraintMaker *make) {
  //   @strongify(self);
  //   make.right.equalTo(self.safeContentView).offset(-36 * kWidthFactor);
  //   make.centerY.equalTo(backButton);
  // }];

  _loginBtn = [FlatButton buttonWithType:UIButtonTypeCustom];
  // [_loginBtn setImage:KImage_name(@"loginBtn")
  // forState:UIControlStateNormal];
  _loginBtn.backgroundColor = KColor_HighBlack;
  _loginBtn.layer.masksToBounds = YES;
  _loginBtn.layer.cornerRadius = 30 * kWidthFactor;
  [_loginBtn setTitle:@"确定" forState:UIControlStateNormal];
  [_loginBtn setTitleColor:KColor_White forState:UIControlStateNormal];
  [_loginBtn addTarget:self
                action:@selector(saveAction)
      forControlEvents:UIControlEventTouchUpInside];
  [self.view addSubview:_loginBtn];
  [_loginBtn mas_makeConstraints:^(MASConstraintMaker *make) {
    make.bottom.equalTo(self.view.mas_bottom).with.offset(-100 * kWidthFactor);
    make.centerX.equalTo(self.view);
    make.size.mas_equalTo(CGSizeMake(160 * kWidthFactor, 60 * kWidthFactor));
  }];
}

- (void)saveAction {
  if (self.nameTxd.text.length == 0) {
    [self showToastFast:@"请输入昵称"];
  } else if (self.nameTxd.text.length > 8) {
    [self showToastFast:@"昵称不能超过8个字"];
  } else if (![self isValidNickname:self.nameTxd.text]) {
    [self showToastFast:@"昵称只能包含中文、英文字母和数字"];
  } else {
    [self saveData];
  }
}

- (BOOL)isValidNickname:(NSString *)nickname {
  // 正则表达式：匹配中文、英文字母和数字
  NSString *pattern = @"^[\\u4e00-\\u9fa5a-zA-Z0-9]+$";
  NSPredicate *pred =
      [NSPredicate predicateWithFormat:@"SELF MATCHES %@", pattern];
  return [pred evaluateWithObject:nickname];
}

- (void)dismissView {
  [NOTIFICENTER postNotificationName:ChangeMyUserInfoMation object:@(0)];
  [self.navigationController popViewControllerAnimated:YES];
}

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];
  [self setShowBackBtn:YES];
}
@end
