#import "ChangeAccountVC.h"
#import "ChangeAccountModel.h"
@interface ChangeAccountVC ()
@property(nonatomic, strong) UITextField *accountTxd;
@property(nonatomic, strong) FlatButton *loginBtn;

@end

@implementation ChangeAccountVC

- (void)saveData {
  __weak typeof(self) wSelf = self;
  [ChangeAccountModel postChangeAccount:self.accountTxd.text
      success:^(NSDictionary *resultObject) {
        ChangeAccountModel *member =
            [ChangeAccountModel mj_objectWithKeyValues:resultObject];
        if ([member.success boolValue]) {
          [wSelf showToastFast:@"账号名修改成功"];
          [[FTHZAccountManager shared]
              syncUserInfoWithCompletion:^(UserPersonResult *_Nullable user,
                                           NSError *_Nullable error) {
                if (error) {
                  [wSelf showToastFast:error.localizedDescription];
                  return;
                }
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW,
                                             (int64_t)(1.0 * NSEC_PER_SEC)),
                               dispatch_get_main_queue(), ^{
                                 [wSelf dismissView];
                               });
              }];
        } else {
          [wSelf showToastFast:member.msg];
        }
      }
      failure:^(NSError *requestErr) {
        [wSelf showToastFast:@"数据有误,请检查网络后重试"];
      }];
}

- (void)viewDidLoad {
  [super viewDidLoad];
  @weakify(self);
  MASViewAttribute *bottom = [self setTitleForV199:@"账号名"];
  UIButton *backButton = [self.safeContentView.subviews firstObject];

  _accountTxd = [[UITextField alloc] init];
  _accountTxd.placeholder = @"请输入账号名";
  _accountTxd.text = CurrentUser.account;
  _accountTxd.backgroundColor = [UIColor colorWithRed:0.95
                                                green:0.95
                                                 blue:0.95
                                                alpha:1.0];
  _accountTxd.layer.cornerRadius = 8 * kMainTemp;
  _accountTxd.layer.masksToBounds = YES;
  UIView *leftView =
      [[UIView alloc] initWithFrame:CGRectMake(0, 0, 15, 44 * kMainTemp)];
  _accountTxd.leftView = leftView;
  _accountTxd.leftViewMode = UITextFieldViewModeAlways;
  [self.safeContentView addSubview:_accountTxd];
  [_accountTxd mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.equalTo(self.safeContentView).with.offset(20 * kMainTemp);
    make.right.equalTo(self.safeContentView).with.offset(-20 * kMainTemp);
    make.top.equalTo(bottom).with.offset(30 * kMainTemp);
    make.height.equalTo(@(44 * kMainTemp));
  }];

  UILabel *accountRuleLabel = [[UILabel alloc] init];
  accountRuleLabel.numberOfLines = 0;
  accountRuleLabel.font = [UIFont systemFontOfSize:12];
  accountRuleLabel.textColor = [UIColor grayColor];
  accountRuleLabel.text =
      @"用户名要求：\n• 必须以字母开头\n• 可包含字母、数字、下划线\n• "
      @"必须包含至少一个字母\n• 长度为6-20个字符";
  [self.safeContentView addSubview:accountRuleLabel];
  [accountRuleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.equalTo(self.accountTxd).offset(15 * kMainTemp);
    make.top.equalTo(self.accountTxd.mas_bottom).offset(10);
    make.right.equalTo(self.accountTxd);
  }];

  UIView *lineLabel = [[UIView alloc] init];
  lineLabel.backgroundColor = KColor_loginGray;
  [self.safeContentView addSubview:lineLabel];
  [lineLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.equalTo(self.safeContentView).with.offset(20 * kMainTemp);
    make.right.equalTo(self.safeContentView).with.offset(-20 * kMainTemp);
    make.top.equalTo(accountRuleLabel.mas_bottom).offset(10);
    make.height.mas_equalTo(@(0.5 * kMainTemp));
  }];

  _loginBtn = [FlatButton buttonWithType:UIButtonTypeCustom];
  _loginBtn.backgroundColor = KColor_HighBlack;
  _loginBtn.layer.masksToBounds = YES;
  _loginBtn.layer.cornerRadius = 30 * kWidthFactor;
  [_loginBtn setTitle:@"确定" forState:UIControlStateNormal];
  [_loginBtn setTitleColor:KColor_White forState:UIControlStateNormal];
  [_loginBtn addTarget:self
                action:@selector(saveAction)
      forControlEvents:UIControlEventTouchUpInside];
  [self.view addSubview:_loginBtn];
  [_loginBtn mas_makeConstraints:^(MASConstraintMaker *make) {
    make.bottom.equalTo(self.view.mas_bottom).with.offset(-100 * kWidthFactor);
    make.centerX.equalTo(self.view);
    make.size.mas_equalTo(CGSizeMake(160 * kWidthFactor, 60 * kWidthFactor));
  }];
}

- (void)saveAction {
  if (self.accountTxd.text.length == 0) {
    [self showToastFast:@"请输入用户名"];
  } else if (self.accountTxd.text.length > 20) {
    [self showToastFast:@"用户名长度必须在6-20个字符之间"];
  } else if (![self isValidUsername:self.accountTxd.text]) {
    [self showToastFast:@"用户名必须以字母开头，只能包含字母、数字和下划线，且"
                        @"必须包含至少一个字母"];
  } else {
    [self saveData];
  }
}

- (BOOL)isValidUsername:(NSString *)username {
  NSString *pattern = @"^[a-zA-Z][a-zA-Z0-9_]{5,19}$";
  NSPredicate *formatPred =
      [NSPredicate predicateWithFormat:@"SELF MATCHES %@", pattern];

  NSString *letterPattern = @".*[a-zA-Z].*";
  NSPredicate *letterPred =
      [NSPredicate predicateWithFormat:@"SELF MATCHES %@", letterPattern];

  return [formatPred evaluateWithObject:username] &&
         [letterPred evaluateWithObject:username];
}

- (void)dismissView {
  if (self.didSetAccountSuccess) {
    self.didSetAccountSuccess();
  }
  [NOTIFICENTER postNotificationName:ChangeMyUserInfoMation object:@(1)];
  [self.navigationController popViewControllerAnimated:YES];
}

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];
  [self setShowBackBtn:YES];
}

@end