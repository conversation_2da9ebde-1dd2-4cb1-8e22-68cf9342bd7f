#import "ChangeSigelVC.h"
#import "UserUserinfoModel.h"

@interface ChangeSigelVC () <UITextViewDelegate>
@property(nonatomic, strong) UITextView *sigelTxd;
@property(nonatomic, strong) FlatButton *loginBtn;
@property(nonatomic, strong) UILabel *characterCountLabel;
@end

@implementation ChangeSigelVC
- (void)saveData {
  @weakify(self);
  [HUD show];
  [AccountManager
      updateUserInfoWithName:CurrentUser.nickname
                      gender:CurrentUser.gender
                      avatar:CurrentUser.avatar
                   signature:self.sigelTxd.text
                       brith:CurrentUser.birth
                  completion:^(UserPersonResult *_Nullable user,
                               NSError *_Nullable error) {
                    @strongify(self);
                    [HUD dissmiss];
                    if (error) {
                      [self showToastFast:error.localizedDescription];
                      return;
                    }

                    [self dismissView];
                  }];
}

- (void)viewDidLoad {
  [super viewDidLoad];
  @weakify(self);
  MASViewAttribute *bottom = [self setTitleForV199:@"签名"];
  UIButton *backButton = [self.safeContentView.subviews firstObject];

  self.sigelTxd = [[UITextView alloc] init];
  self.sigelTxd.text = CurrentUser.signature;
  self.sigelTxd.font = SourceHanSerifMediumFont(16 * kWidthFactor);
  self.sigelTxd.backgroundColor = [UIColor colorWithRed:0.95
                                                  green:0.95
                                                   blue:0.95
                                                  alpha:1.0];
  self.sigelTxd.layer.cornerRadius = 8 * kMainTemp;
  self.sigelTxd.layer.masksToBounds = YES;
  self.sigelTxd.delegate = self;
  self.sigelTxd.textContainerInset = UIEdgeInsetsMake(12, 10, 12, 10);

  [self.safeContentView addSubview:self.sigelTxd];
  [self.sigelTxd mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.equalTo(self.safeContentView).with.offset(20 * kMainTemp);
    make.right.equalTo(self.safeContentView).with.offset(-20 * kMainTemp);
    make.top.equalTo(bottom).with.offset(40 * kMainTemp);
    make.height.mas_equalTo(100 * kMainTemp);
  }];

  UIView *countContainer = [[UIView alloc] init];
  countContainer.backgroundColor = [UIColor clearColor];
  countContainer.userInteractionEnabled = NO;
  [self.safeContentView addSubview:countContainer];

  [countContainer mas_makeConstraints:^(MASConstraintMaker *make) {
    make.right.equalTo(self.sigelTxd).offset(-10 * kMainTemp);
    make.bottom.equalTo(self.sigelTxd).offset(-10 * kMainTemp);
    make.width.mas_equalTo(60 * kMainTemp);
    make.height.mas_equalTo(20 * kMainTemp);
  }];

  self.characterCountLabel = [[UILabel alloc] init];
  NSInteger initialLength = CurrentUser.signature.length;
  self.characterCountLabel.text =
      [NSString stringWithFormat:@"%ld/50", (long)initialLength];
  self.characterCountLabel.font = [UIFont systemFontOfSize:12];
  self.characterCountLabel.textColor = [UIColor grayColor];
  self.characterCountLabel.textAlignment = NSTextAlignmentRight;
  [countContainer addSubview:self.characterCountLabel];

  [self.characterCountLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.edges.equalTo(countContainer);
  }];

  self.loginBtn = [FlatButton buttonWithType:UIButtonTypeCustom];
  self.loginBtn.backgroundColor = KColor_HighBlack;
  self.loginBtn.layer.masksToBounds = YES;
  self.loginBtn.layer.cornerRadius = 30 * kWidthFactor;
  [self.loginBtn setTitle:@"确定" forState:UIControlStateNormal];
  [self.loginBtn setTitleColor:KColor_White forState:UIControlStateNormal];
  [self.loginBtn addTarget:self
                    action:@selector(saveAction)
          forControlEvents:UIControlEventTouchUpInside];
  [self.view addSubview:self.loginBtn];
  [self.loginBtn mas_makeConstraints:^(MASConstraintMaker *make) {
    make.bottom.equalTo(self.view.mas_bottom).with.offset(-100 * kWidthFactor);
    make.centerX.equalTo(self.view);
    make.size.mas_equalTo(CGSizeMake(160 * kWidthFactor, 60 * kWidthFactor));
  }];
}

- (void)textViewDidChange:(UITextView *)textView {
  NSInteger characterCount = textView.text.length;
  self.characterCountLabel.text =
      [NSString stringWithFormat:@"%ld/50", (long)characterCount];

  if (characterCount > 50) {
    self.characterCountLabel.textColor = [UIColor redColor];
  } else {
    self.characterCountLabel.textColor = [UIColor grayColor];
  }
}

- (void)saveAction {
  if (self.sigelTxd.text.length == 0) {
    [self showToastFast:@"请输入签名"];
  } else if (self.sigelTxd.text.length > 50) {
    [self showToastFast:@"签名不能超过50个字"];
  } else {
    [self saveData];
  }
}

- (void)dismissView {
  [NOTIFICENTER postNotificationName:ChangeMyUserInfoMation object:@(8)];
  [self.navigationController popViewControllerAnimated:YES];
}

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];
  [self setShowBackBtn:YES];
}
@end