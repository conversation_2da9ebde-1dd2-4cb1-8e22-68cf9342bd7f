#import "ChangeUserVC.h"
#import "PaoPaoCollectionViewCell.h"
#import "RateAttributeModel.h"
#import "RateCalculateModel.h"
#import <ImageIO/ImageIO.h>

#define HeziChouseOneVCID @"HeziChouseOneVCID"

@interface ChangeUserVC () <UICollectionViewDataSource,
                            UICollectionViewDelegate,
                            UICollectionViewDelegateFlowLayout> {
  UICollectionView *paoCollectionView;
  NSInteger tagnum;
  NSString *dataattribute;
  NSString *datarate;
}
@property(nonatomic, strong) FlatButton *loginBtn;
@property(nonatomic, strong) UICountingLabel *heziLabel;
@property(nonatomic, assign) CGFloat hezinum;
@property(nonatomic, strong) NSMutableArray *tagIdstr;
@property(nonatomic, strong) NSMutableArray *urlHezistr;
@property(nonatomic, strong) NSMutableArray *loadurlHezistr;
@property(nonatomic, strong) NSMutableArray *paoTagArray;

@end

@implementation ChangeUserVC
- (void)loadTagInfo {
  if (!_paoTagArray) {
    _paoTagArray = [NSMutableArray new];
  } else {
    [_paoTagArray removeAllObjects];
  }
  __weak typeof(self) wSelf = self;
  [RateAttributeModel
      getRateAttributeModel:@"0"
                    success:^(NSDictionary *resultObject) {
                      RateAttributeModel *member = [RateAttributeModel
                          mj_objectWithKeyValues:resultObject];
                      if ([member.success boolValue]) {
                        for (int i = 0; i < member.data.count; i++) {
                          TagModelResult *dy = [TagModelResult
                              mj_objectWithKeyValues:[member.data
                                                         objectAtIndex:i]];
                          dy.isChouseTag = @"0";
                          [wSelf.paoTagArray addObject:dy];
                        }
                        for (TagModelResult *lp in self.likePao) {
                          for (int i = 0; i < wSelf.paoTagArray.count; i++) {
                            TagModelResult *paoa =
                                [wSelf.paoTagArray objectAtIndex:i];
                            if ([paoa.tagId isEqualToString:lp.tagId]) {
                              if ([paoa.isChouseTag isEqualToString:@"0"]) {
                                paoa.isChouseTag = @"1";
                                self->tagnum++;
                                NSMutableArray *newArray =
                                    [wSelf.paoTagArray mutableCopy];
                                [newArray replaceObjectAtIndex:i
                                                    withObject:paoa];
                                self.paoTagArray = newArray;
                              } else if ([paoa.isChouseTag
                                             isEqualToString:@"1"]) {
                                paoa.isChouseTag = @"2";
                                NSMutableArray *newArray =
                                    [wSelf.paoTagArray mutableCopy];
                                [newArray replaceObjectAtIndex:i
                                                    withObject:paoa];
                                self.paoTagArray = newArray;
                              }
                            }
                          }
                        }

                        [self->paoCollectionView reloadData];
                      }
                    }
                    failure:^(NSError *requestErr){

                    }];
}

- (void)viewDidLoad {
  [super viewDidLoad];
  MASViewAttribute *bottom = [self setTitleForV199:@""];

  [self loadTagInfo];
  _hezinum = self.heziNum;

  tagnum = 0;
  dataattribute = @"";
  datarate = @"";

  UILabel *titleLabel = [[UILabel alloc] init];
  titleLabel.textColor = KColor_Black;
  titleLabel.text = @"开始测算你的频率";
  titleLabel.font = SourceHanSerifSemiBoldFont(20 * kWidthFactor);
  [self.safeContentView addSubview:titleLabel];
  [titleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.view).offset(24 * kWidthFactor);
    make.top.equalTo(self.safeContentView).offset(60 * kWidthFactor);
    make.size.mas_equalTo(CGSizeMake(300 * kWidthFactor, 24 * kWidthFactor));
  }];

  // 创建圆形背景视图
  UIView *circleBackgroundView = [[UIView alloc] init];
  circleBackgroundView.backgroundColor = KColor_HighBlack;
  circleBackgroundView.layer.cornerRadius = 40 * kWidthFactor;
  circleBackgroundView.layer.masksToBounds = YES;
  [self.safeContentView addSubview:circleBackgroundView];
  [circleBackgroundView mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.view).offset(24 * kWidthFactor);
    make.top.equalTo(titleLabel.mas_bottom).offset(12 * kWidthFactor);
    make.size.mas_equalTo(CGSizeMake(80 * kWidthFactor, 80 * kWidthFactor));
  }];

  _heziLabel = [[UICountingLabel alloc] init];
  _heziLabel.method = UILabelCountingMethodEaseOut;
  _heziLabel.format = @"%.2f";
  _heziLabel.textColor = KColor_White;
  _heziLabel.font = [UIFont fontWithName:@"DINCondensed-Bold"
                                    size:24 * kWidthFactor];
  [circleBackgroundView addSubview:_heziLabel];
  [_heziLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.centerX.equalTo(circleBackgroundView).offset(-8 * kWidthFactor);
    make.centerY.equalTo(circleBackgroundView);
    make.size.mas_equalTo(CGSizeMake(42 * kWidthFactor, 24 * kWidthFactor));
  }];
  [_heziLabel countFrom:0.00 to:_hezinum withDuration:1.5];

  UIImageView *hzimage = [[UIImageView alloc] init];
  hzimage.image = [UIImage imageNamed:@"heziicon"];

  // 根据用户性别设置Hz单位颜色
  NSString *gender = [CurrentUser.gender isEqualToString:@"1"] ? @"1" : @"2";
  UIColor *hzColor = [gender isEqualToString:@"1"] ? KColor_switchLightBlue
                                                   : KColor_switchLightPink;
  hzimage.tintColor = hzColor;

  // 如果图片是模板图片，启用tintColor
  hzimage.image =
      [hzimage.image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];

  [circleBackgroundView addSubview:hzimage];
  [hzimage mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.heziLabel.mas_right).offset(4 * kWidthFactor);
    make.centerY.equalTo(self.heziLabel);
    make.size.mas_equalTo(CGSizeMake(10 * kWidthFactor, 10 * kWidthFactor));
  }];

  _loginBtn = [FlatButton buttonWithType:UIButtonTypeSystem];
  _loginBtn.backgroundColor = KColor_HighBlack;
  _loginBtn.layer.masksToBounds = YES;
  _loginBtn.layer.cornerRadius = 30 * kWidthFactor;
  [_loginBtn setTitle:@"确定" forState:UIControlStateNormal];
  [_loginBtn setTitleColor:KColor_White forState:UIControlStateNormal];
  [_loginBtn addTarget:self
                action:@selector(goNext)
      forControlEvents:UIControlEventTouchUpInside];
  [self.safeContentView addSubview:_loginBtn];
  [_loginBtn mas_makeConstraints:^(MASConstraintMaker *make) {
    make.bottom.equalTo(self.safeContentView.mas_bottom)
        .with.offset(-100 * kWidthFactor);
    make.centerX.equalTo(self.safeContentView);
    make.size.mas_equalTo(CGSizeMake(160 * kWidthFactor, 60 * kWidthFactor));
  }];

  [self loadCollectionView];
}

- (void)loadCollectionView {
  UICollectionViewFlowLayout *layout =
      [[UICollectionViewFlowLayout alloc] init];
  layout.itemSize = CGSizeMake(105 * kWidthFactor, 105 * kWidthFactor);
  layout.minimumLineSpacing = 0 * kWidthFactor;
  layout.minimumInteritemSpacing = 0 * kWidthFactor;
  layout.sectionInset =
      UIEdgeInsetsMake(0, 10 * kWidthFactor, 0, 10 * kWidthFactor);
  layout.scrollDirection = UICollectionViewScrollDirectionHorizontal;
  paoCollectionView = [[UICollectionView alloc]
             initWithFrame:CGRectMake(0, 200 * kWidthFactor, kMainWidth,
                                      316 * kWidthFactor)
      collectionViewLayout:layout];
  paoCollectionView.backgroundColor = [UIColor whiteColor];
  paoCollectionView.dataSource = self;
  paoCollectionView.delegate = self;
  paoCollectionView.pagingEnabled = YES;
  paoCollectionView.showsHorizontalScrollIndicator = NO;
  [paoCollectionView registerClass:[PaoPaoCollectionViewCell class]
        forCellWithReuseIdentifier:HeziChouseOneVCID];
  [self.safeContentView addSubview:paoCollectionView];

  UILabel *goNextTopLabel = [[UILabel alloc] init];
  goNextTopLabel.textColor = KColor_textTinyGray;
  goNextTopLabel.text = @"感兴趣的内容点击一下，喜欢的内容点击两下。";
  goNextTopLabel.textAlignment = NSTextAlignmentCenter;
  goNextTopLabel.font = [UIFont systemFontOfSize:12 * kWidthFactor];
  [self.view addSubview:goNextTopLabel];
  [goNextTopLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.view);
    make.bottom.equalTo(paoCollectionView.mas_bottom).offset(40 * kWidthFactor);
    make.size.mas_equalTo(CGSizeMake(kMainWidth, 12 * kWidthFactor));
  }];
}

#pragma mark - collectionView  data source

- (NSInteger)collectionView:(UICollectionView *)collectionView
     numberOfItemsInSection:(NSInteger)section {
  return self.paoTagArray.count;
}

- (NSInteger)numberOfSectionsInCollectionView:
    (UICollectionView *)collectionView {
  return 1;
}

- (void)collectionView:(UICollectionView *)collectionView
    didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
  __weak typeof(self) wSelf = self;

  TagModelResult *dy = [self.paoTagArray objectAtIndex:indexPath.row];
  if (tagnum > 10 && [dy.isChouseTag isEqualToString:@"0"]) {
    [self showToastFast:@"最多只能选择10个"];
  } else {
    if ([dy.isChouseTag isEqualToString:@"0"]) {
      dy.isChouseTag = @"1";
      self->tagnum++;
    } else if ([dy.isChouseTag isEqualToString:@"1"]) {
      dy.isChouseTag = @"2";
    } else if ([dy.isChouseTag isEqualToString:@"2"]) {
      dy.isChouseTag = @"0";
      self->tagnum = tagnum - 1;
    }
    NSMutableArray *newArray = [self.paoTagArray mutableCopy];
    [newArray replaceObjectAtIndex:indexPath.row withObject:dy];
    self.paoTagArray = newArray;

    if (!_loadurlHezistr) {
      _loadurlHezistr = [NSMutableArray new];
    } else {
      [_loadurlHezistr removeAllObjects];
    }
    for (TagModelResult *tt in self.paoTagArray) {
      if ([tt.isChouseTag isEqualToString:@"1"]) {
        [_loadurlHezistr addObject:tt];
      }
      if ([tt.isChouseTag isEqualToString:@"2"]) {
        [_loadurlHezistr addObject:tt];
        [_loadurlHezistr addObject:tt];
      }
    }
    if (_loadurlHezistr.count > 0) {
      [RateCalculateModel postRateCalculateModel:@"0"
          tagArrStr:[NemoUtil arrayChangeString:self.loadurlHezistr]
          likeTagArrStr:@""
          attribute:dataattribute
          rate:datarate
          success:^(NSDictionary *resultObject) {
            RateCalculateModel *member =
                [RateCalculateModel mj_objectWithKeyValues:resultObject];
            if ([member.success boolValue]) {
              RateCalculateModelResult *useMember = [RateCalculateModelResult
                  mj_objectWithKeyValues:member.data.firstObject];
              self->dataattribute = useMember.attribute;
              self->datarate = useMember.rate;
              [wSelf.heziLabel countFrom:wSelf.hezinum
                                      to:[useMember.rate doubleValue]
                            withDuration:1.5];
              wSelf.hezinum = [useMember.rate doubleValue];
              [self->paoCollectionView
                  reloadItemsAtIndexPaths:
                      [NSArray
                          arrayWithObjects:[NSIndexPath
                                               indexPathForRow:indexPath.row
                                                     inSection:0],
                                           nil]];

            } else {
              if ([dy.isChouseTag isEqualToString:@"0"]) {
                dy.isChouseTag = @"2";
                self->tagnum = self->tagnum - 1;
              } else if ([dy.isChouseTag isEqualToString:@"1"]) {
                dy.isChouseTag = @"0";
                self->tagnum++;
              } else if ([dy.isChouseTag isEqualToString:@"2"]) {
                dy.isChouseTag = @"1";
              }
              NSMutableArray *newArray = [self.paoTagArray mutableCopy];
              [newArray replaceObjectAtIndex:indexPath.row withObject:dy];
              self.paoTagArray = newArray;
              [self showToastFast:member.msg];
            }
          }
          failure:^(NSError *requestErr) {
            if ([dy.isChouseTag isEqualToString:@"0"]) {
              dy.isChouseTag = @"2";
              self->tagnum = self->tagnum - 1;
            } else if ([dy.isChouseTag isEqualToString:@"1"]) {
              dy.isChouseTag = @"0";
              self->tagnum++;
            } else if ([dy.isChouseTag isEqualToString:@"2"]) {
              dy.isChouseTag = @"1";
            }
            NSMutableArray *newArray = [self.paoTagArray mutableCopy];
            [newArray replaceObjectAtIndex:indexPath.row withObject:dy];
            self.paoTagArray = newArray;

            [self showToastFast:@"数据有误,请检查网络后重试"];
          }];
    } else {
      [wSelf.heziLabel countFrom:wSelf.hezinum
                              to:[@"52.00" doubleValue]
                    withDuration:1.5];
      wSelf.hezinum = [@"52.00" doubleValue];
      [self->paoCollectionView
          reloadItemsAtIndexPaths:
              [NSArray
                  arrayWithObjects:[NSIndexPath indexPathForRow:indexPath.row
                                                      inSection:0],
                                   nil]];
    }
  }
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView
                  cellForItemAtIndexPath:(NSIndexPath *)indexPath {
  TagModelResult *dy = [self.paoTagArray objectAtIndex:indexPath.row];
  PaoPaoCollectionViewCell *cell =
      [collectionView dequeueReusableCellWithReuseIdentifier:HeziChouseOneVCID
                                                forIndexPath:indexPath];
  if (cell == nil) {
    cell = [[PaoPaoCollectionViewCell alloc] init];
  }
  cell.tagModel = dy;
  return cell;
}

- (void)goNext {
  if (tagnum == 0) {
    [self showToastFast:@"最少要选择一项哦"];
  } else {
    if (!_tagIdstr) {
      _tagIdstr = [NSMutableArray new];
    } else {
      [_tagIdstr removeAllObjects];
    }

    for (TagModelResult *tt in self.paoTagArray) {
      if ([tt.isChouseTag isEqualToString:@"1"]) {
        [_tagIdstr addObject:tt];
      }
      if ([tt.isChouseTag isEqualToString:@"2"]) {
        [_tagIdstr addObject:tt];
        [_tagIdstr addObject:tt];
      }
    }
    for (TagModelResult *dda in self.tagA) {
      [_tagIdstr addObject:dda];
    }
    __weak typeof(self) wSelf = self;
    [RateCalculateModel postRateCalculateModel:@"1"
        tagArrStr:[NemoUtil arrayChangeString:self.tagIdstr]
        likeTagArrStr:[NemoUtil arrayChangeString:self.tagB]
        attribute:dataattribute
        rate:datarate
        success:^(NSDictionary *resultObject) {
          RateCalculateModel *member =
              [RateCalculateModel mj_objectWithKeyValues:resultObject];
          if ([member.success boolValue]) {
            [wSelf getUserHeziInfo];
          } else {
            [wSelf showToastFast:member.msg];
          }
        }
        failure:^(NSError *requestErr) {
          [wSelf showToastFast:@"数据有误,请检查网络后重试"];
        }];
  }
}

- (void)getUserHeziInfo {
  @weakify(self);
  [[FTHZAccountManager shared]
      syncUserInfoWithCompletion:^(UserPersonResult *_Nullable user,
                                   NSError *_Nullable error) {
        @strongify(self);
        [HUD dissmiss];
        if (error) {
          [self showToastFast:error.localizedDescription];
          return;
        }
        [self dismissView];
      }];
}

- (void)dismissView {
  [NOTIFICENTER postNotificationName:ChangeMyUserInfoMation object:@(7)];
  [self.navigationController popViewControllerAnimated:YES];
}

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];
  [self setShowBackBtn:YES];
}

@end
