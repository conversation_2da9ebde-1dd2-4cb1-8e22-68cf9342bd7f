#import "SettingTableViewCell.h"

@implementation SettingTableViewCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style
              reuseIdentifier:(NSString *)reuseIdentifier {
  self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
  if (self) {
    [self loadCellView];
    [self setSelectionStyle:(UITableViewCellSelectionStyleNone)];
  }
  return self;
}

- (void)loadCellView {
  if (!_nameLabel) {
    _nameLabel = [[UILabel alloc] init];
  }
  [_nameLabel setTextColor:KColor_HighBlack];
  [_nameLabel setLineBreakMode:NSLineBreakByWordWrapping];
  _nameLabel.font = SourceHanSerifMediumFont(14 * kMainTemp);
  [self.contentView addSubview:self.nameLabel];

  if (!_mainSwitch) {
    _mainSwitch = [[UISwitch alloc] initWithFrame:CGRectMake(200, 100, 0, 0)];
    _mainSwitch.transform = CGAffineTransformMakeScale(0.6, 0.6);
    _mainSwitch.onTintColor = KColor_HighBlack;
    [self updateSwitchColor];
  }
  [self.contentView addSubview:_mainSwitch];

  if (!_lineLabel) {
    _lineLabel = [[UILabel alloc] init];
  }
  _lineLabel.textColor = KColor_textTinyGray;
  _lineLabel.text = @"展示地理位置，需要去iPhone设置系统-隐私-定位服务中设置";
  _lineLabel.font = [UIFont systemFontOfSize:12 * kMainTemp];

  if (!_detailLabel) {
    _detailLabel = [[UILabel alloc] init];
    _detailLabel.textColor = KColor_detailLightGray;
    _detailLabel.font = SourceHanSerifRegularFont(12 * kMainTemp);
    _detailLabel.textAlignment = NSTextAlignmentRight;
    [self.contentView addSubview:_detailLabel];
  }
}

- (void)loadSetting:(NSInteger)indexrow {
  if (indexrow == 0) {
    [self.nameLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.contentView).offset(24 * kMainTemp);
      make.top.equalTo(self.contentView).offset(11.5 * kMainTemp);
      make.size.mas_equalTo(CGSizeMake(kMainWidth * 0.55, 14.5 * kMainTemp));
    }];
    [_mainSwitch mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.right.equalTo(self.contentView.mas_right).offset(-24 * kMainTemp);
      make.centerY.equalTo(self.nameLabel);
    }];
    [self.contentView addSubview:_lineLabel];
    [_lineLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.contentView).offset(24 * kMainTemp);
      make.top.equalTo(self.nameLabel.mas_bottom).offset(16 * kMainTemp);
      make.size.mas_equalTo(
          CGSizeMake(kMainWidth - 48 * kMainTemp, 12 * kMainTemp));
    }];

  } else {
    [_lineLabel removeFromSuperview];
    [self.nameLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.contentView).offset(24 * kMainTemp);
      make.centerY.equalTo(self.contentView);
      make.size.mas_equalTo(CGSizeMake(kMainWidth * 0.55, 14.5 * kMainTemp));
    }];
    if (indexrow == 4) {
      _mainSwitch.hidden = YES;
      [_detailLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.contentView.mas_right).offset(-24 * kMainTemp);
        make.centerY.equalTo(self.nameLabel);
        make.size.mas_equalTo(CGSizeMake(100 * kMainTemp, 14.5 * kMainTemp));
      }];
      _detailLabel.hidden = NO;
    } else {
      _detailLabel.hidden = YES;
      [_mainSwitch mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.contentView.mas_right).offset(-24 * kMainTemp);
        make.centerY.equalTo(self.nameLabel).offset(6 * kMainTemp);
      }];
    }
  }
}

- (void)awakeFromNib {
  [super awakeFromNib];
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
  [super setSelected:selected animated:animated];
}

- (void)prepareForReuse {
  [super prepareForReuse];

  self.nameLabel.text = nil;
  self.detailLabel.text = nil;

  self.mainSwitch.on = NO;

  self.mainSwitch.hidden = NO;
  self.detailLabel.hidden = YES;

  [self.lineLabel removeFromSuperview];
}

- (void)updateSwitchColor {
  if ([CurrentUser.gender isEqualToString:@"1"]) {
    _mainSwitch.thumbTintColor = KColor_switchLightBlue;
  } else if ([CurrentUser.gender isEqualToString:@"2"]) {
    _mainSwitch.thumbTintColor = KColor_switchLightPink;
  }
}

@end
