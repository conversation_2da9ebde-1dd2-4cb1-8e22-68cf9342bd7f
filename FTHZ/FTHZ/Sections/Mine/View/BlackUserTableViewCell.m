#import "BlackUserTableViewCell.h"

@implementation BlackUserTableViewCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style
              reuseIdentifier:(NSString *)reuseIdentifier {
  self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
  if (self) {
    [self loadCellView];
    [self setSelectionStyle:(UITableViewCellSelectionStyleNone)];
  }
  return self;
}

- (void)loadCellView {
  if (!_iconImageLabel) {
    _iconImageLabel = [[UIImageView alloc]
        initWithFrame:CGRectMake(16 * kMainTemp, 6 * kMainTemp, 38 * kMainTemp,
                                 38 * kMainTemp)];
  }
  UIBezierPath *maskPath =
      [UIBezierPath bezierPathWithRoundedRect:_iconImageLabel.bounds
                            byRoundingCorners:UIRectCornerAllCorners
                                  cornerRadii:_iconImageLabel.bounds.size];
  CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
  maskLayer.frame = _iconImageLabel.bounds;
  maskLayer.path = maskPath.CGPath;
  _iconImageLabel.layer.mask = maskLayer;
  [self.contentView addSubview:_iconImageLabel];
  [_iconImageLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.contentView).offset(16 * kMainTemp);
    make.centerY.equalTo(self.contentView);
    make.size.mas_equalTo(CGSizeMake(38 * kMainTemp, 38 * kMainTemp));
  }];

  if (!_nameLabel) {
    _nameLabel = [[UILabel alloc] init];
  }
  [_nameLabel setTextColor:KColor_Black];
  [_nameLabel setLineBreakMode:NSLineBreakByWordWrapping];
  _nameLabel.font = SourceHanSerifSemiBoldFont(14 * kMainTemp);
  _nameLabel.textColor = KColor_HighBlue;
  [self.contentView addSubview:self.nameLabel];
  [self.nameLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(_iconImageLabel.mas_right).offset(10 * kMainTemp);
    make.centerY.equalTo(self.contentView);
    make.size.mas_equalTo(CGSizeMake(kMainWidth * 0.55, 38 * kMainTemp));
  }];

  _changeBtn = [FlatButton buttonWithType:UIButtonTypeCustom];
  _changeBtn.layer.masksToBounds = YES;
  [_changeBtn setTitleColor:KColor_White forState:UIControlStateNormal];
  _changeBtn.layer.cornerRadius = 6 * kMainTemp;
  _changeBtn.backgroundColor = KColor_HighBlack;
  [_changeBtn setTitle:@"解除拉黑" forState:UIControlStateNormal];
  _changeBtn.titleLabel.font = [UIFont systemFontOfSize:10 * kMainTemp];
  [self.contentView addSubview:_changeBtn];
  [_changeBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.right.mas_equalTo(self.contentView.mas_right).offset(-12 * kMainTemp);
    make.centerY.equalTo(self.contentView);
    make.size.mas_equalTo(CGSizeMake(60 * kMainTemp, 25 * kMainTemp));
  }];
}

- (void)awakeFromNib {
  [super awakeFromNib];
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
  [super setSelected:selected animated:animated];
}

@end
