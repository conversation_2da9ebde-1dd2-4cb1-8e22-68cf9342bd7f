#import "UserHeaderView.h"
@interface UserHeaderView ()
@property(nonatomic, strong) UILabel *nickNameLabel;
@property(nonatomic, strong) UILabel *heziLabel;
@property(nonatomic, strong) UIImageView *smHezi;
@property(nonatomic, strong) UILabel *adressDetailLabel;
@property(nonatomic, strong) UIImageView *avatarImageView;
@property(nonatomic, strong) UILabel *likeDetailLabel;
@property(nonatomic, strong) UILabel *detailLabel;
@property(nonatomic, strong) UIImageView *genderImage;

@end

@implementation UserHeaderView

- (instancetype)initWithFrame:(CGRect)frame {
  self = [super initWithFrame:frame];
  if (self) {
    @weakify(self);
    self.userInteractionEnabled = YES;
    UIImageView *bbbIMG = [[UIImageView alloc] init];
    bbbIMG.image = KImage_name(@"upBg");
    [self addSubview:bbbIMG];
    [bbbIMG mas_remakeConstraints:^(MASConstraintMaker *make) {
      @strongify(self);
      make.left.right.bottom.mas_equalTo(self);
      make.height.mas_equalTo(350 * kMainTemp);
    }];

    CGSize nameTemp = [NemoUtil
        calculateLabelHeightByText:[UIFont systemFontOfSize:20 * kMainTemp]
                             width:kMainWidth
                         heightMax:kMainHeight
                           content:CurrentUser.nickname];
    _nickNameLabel = [[UILabel alloc] init];
    _nickNameLabel.textColor = KColor_White;
    _nickNameLabel.text = CurrentUser.nickname;
    _nickNameLabel.font = [UIFont systemFontOfSize:20 * kMainTemp];
    [self addSubview:_nickNameLabel];
    [_nickNameLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
      @strongify(self);
      make.left.mas_equalTo(self).offset(24 * kMainTemp);
      make.top.mas_equalTo(self).offset(59 * kMainTemp);
      make.size.mas_equalTo(CGSizeMake(nameTemp.width + 2, 30 * kMainTemp));
    }];
    _heziLabel = [[UILabel alloc] init];
    _heziLabel.textColor = KColor_White;
    _heziLabel.text = CurrentUser.hertz;
    _heziLabel.textAlignment = NSTextAlignmentRight;
    _heziLabel.font = [UIFont fontWithName:@"DINCondensed-Bold"
                                      size:21 * kMainTemp];
    [self addSubview:_heziLabel];
    [_heziLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
      @strongify(self);
      make.left.mas_equalTo(self.nickNameLabel.mas_right)
          .offset(10 * kMainTemp);
      make.bottom.mas_equalTo(self.nickNameLabel.mas_bottom)
          .offset(0 * kMainTemp);
      make.size.mas_equalTo(CGSizeMake(40 * kMainTemp, 22 * kMainTemp));
    }];
    _smHezi = [[UIImageView alloc] init];
    _smHezi.image = KImage_name(@"whitehz");
    [self addSubview:_smHezi];
    [_smHezi mas_remakeConstraints:^(MASConstraintMaker *make) {
      @strongify(self);
      make.left.mas_equalTo(self.heziLabel.mas_right).offset(1 * kMainTemp);
      make.centerY.mas_equalTo(self.heziLabel.mas_top);
      make.size.mas_equalTo(CGSizeMake(8 * kMainTemp, 8 * kMainTemp));
    }];

    CGSize adressTemp = [NemoUtil
        calculateLabelHeightByText:[UIFont systemFontOfSize:12 * kMainTemp]
                             width:kMainWidth
                         heightMax:kMainHeight
                           content:[NSString
                                       stringWithFormat:
                                           @"%@  %@岁", CurrentUser.city,
                                           [NemoUtil getAgeOfData:CurrentUser
                                                                      .birth]]];
    _adressDetailLabel = [[UILabel alloc] init];
    _adressDetailLabel.textColor = KColor_White;
    _adressDetailLabel.text =
        [NSString stringWithFormat:@"%@  %@岁", CurrentUser.city,
                                   [NemoUtil getAgeOfData:CurrentUser.birth]];
    _adressDetailLabel.font = [UIFont systemFontOfSize:12 * kMainTemp];
    [self addSubview:_adressDetailLabel];
    [_adressDetailLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
      @strongify(self);
      make.left.mas_equalTo(self).offset(24 * kMainTemp);
      make.top.mas_equalTo(self.nickNameLabel.mas_bottom).offset(7 * kMainTemp);
      make.size.mas_equalTo(CGSizeMake(adressTemp.width + 6, 12 * kMainTemp));
    }];
    _genderImage = [[UIImageView alloc] init];
    if ([CurrentUser.gender isEqualToString:@"1"]) {
      _genderImage.image = KImage_name(@"wboy");
    } else if ([CurrentUser.gender isEqualToString:@"2"]) {
      _genderImage.image = KImage_name(@"wgirl");
    }
    [self addSubview:_genderImage];
    [_genderImage mas_remakeConstraints:^(MASConstraintMaker *make) {
      @strongify(self);
      make.left.mas_equalTo(self.adressDetailLabel.mas_right)
          .offset(6 * kMainTemp);
      make.centerY.mas_equalTo(self.adressDetailLabel).offset(1 * kMainTemp);
      make.size.mas_equalTo(CGSizeMake(12 * kMainTemp, 12 * kMainTemp));
    }];

    UIView *tempView = nil;
    for (int i = 0; i < CurrentUser.tag.count; i++) {

      TagModelResult *tagInfo =
          [TagModelResult mj_objectWithKeyValues:CurrentUser.tag[i]];
      UIView *tagBgView = [UIView new];
      tagBgView.backgroundColor =
          [KColor_HighBlack colorWithAlphaComponent:0.4];
      tagBgView.layer.cornerRadius = 2.0;
      tagBgView.layer.masksToBounds = YES;

      UILabel *tagL = [[UILabel alloc] init];
      tagL.textColor = KColor_White;
      tagL.text = [NSString stringWithFormat:@"  %@ ", tagInfo.name];
      tagL.font = [UIFont systemFontOfSize:10 * kMainTemp];
      [tagBgView addSubview:tagL];
      [tagL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(tagBgView);
      }];

      [self addSubview:tagBgView];
      if (tempView == nil) {
        [tagBgView mas_makeConstraints:^(MASConstraintMaker *make) {
          make.left.mas_equalTo(self).offset(24 * kMainTemp);
          make.top.mas_equalTo(self.adressDetailLabel.mas_bottom)
              .offset(12 * kMainTemp);
        }];
      } else {
        if (i % 2 > 0) {
          [tagBgView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(tempView.mas_right).offset(12 * kMainTemp);
            make.centerY.equalTo(tempView);
          }];
        } else {
          [tagBgView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(self).offset(24 * kMainTemp);
            make.top.mas_equalTo(tempView.mas_bottom).offset(10 * kMainTemp);
          }];
        }
      }
      tempView = tagBgView;
    }

    _avatarImageView = [[UIImageView alloc] init];
    [_avatarImageView
        sd_setImageWithURL:[NemoUtil
                               getUrlWithUserDefartIcon:CurrentUser.avatar]
          placeholderImage:KImage_name(@"empty")];
    _avatarImageView.layer.masksToBounds = YES;
    _avatarImageView.layer.cornerRadius = 50 * kMainTemp;
    [self addSubview:_avatarImageView];
    [_avatarImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.right.mas_equalTo(self.mas_right).offset(-24 * kMainTemp);
      make.top.mas_equalTo(self).offset(88 * kMainTemp);
      make.size.mas_equalTo(CGSizeMake(100 * kMainTemp, 100 * kMainTemp));
    }];

    NSArray *badgePosArr;
    switch (CurrentUser.badge.count) {
    case 1:
      badgePosArr = @[ @[ @8, @4 ] ];
      break;
    case 2:
      badgePosArr = @[ @[ @17, @-2 ], @[ @-2, @13 ] ];
      break;
    default:
      badgePosArr = @[ @[ @40, @-8.5 ], @[ @17, @-2 ], @[ @-2, @13 ] ];
      break;
    }
    for (int i = 0; i < CurrentUser.badge.count; i++) {
      TagModelResult *badgeInfo = [TagModelResult
          mj_objectWithKeyValues:[CurrentUser.badge objectAtIndex:i]];
      NSString *badgeName =
          [NSString stringWithFormat:@"badge_type%@", badgeInfo.type];
      UIImageView *badgeIV =
          [[UIImageView alloc] initWithImage:[UIImage imageNamed:badgeName]];
      [self addSubview:badgeIV];
      NSArray *pos = badgePosArr[i];
      [badgeIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(20 * kMainTemp, 20.5 * kMainTemp));
        make.right.equalTo(self.avatarImageView.mas_right)
            .offset(-[pos[0] intValue] * kMainTemp);
        make.bottom.equalTo(self.avatarImageView.mas_bottom)
            .offset(-[pos[1] intValue] * kMainTemp);
      }];
    }
    if (CurrentUser.badge.count > 0) {
      UIButton *tapButton = [UIButton new];
      [tapButton addTarget:self
                    action:@selector(showBadgeInfoAlert)
          forControlEvents:UIControlEventTouchUpInside];
      [self addSubview:tapButton];
      [tapButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(100 * kMainTemp, 100 * kMainTemp));
        make.right.equalTo(self.avatarImageView.mas_right)
            .offset(50 * kMainTemp);
        make.bottom.equalTo(self.avatarImageView.mas_bottom)
            .offset(50 * kMainTemp);
      }];
    }

    _changeBtn = [FlatButton buttonWithType:UIButtonTypeCustom];
    _changeBtn.layer.masksToBounds = YES;
    _changeBtn.layer.cornerRadius = 2 * kMainTemp;
    _changeBtn.layer.borderWidth = 0.5 * kMainTemp;
    _changeBtn.layer.borderColor = KColor_Black.CGColor;
    [_changeBtn setTitle:@"编辑资料" forState:UIControlStateNormal];
    [_changeBtn setTitleColor:KColor_Black forState:UIControlStateNormal];
    _changeBtn.titleLabel.font = [UIFont systemFontOfSize:10 * kMainTemp];
    [self addSubview:_changeBtn];
    [_changeBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.mas_equalTo(self).offset(24 * kMainTemp);
      make.top.mas_equalTo(self).offset(196 * kMainTemp);
      make.size.mas_equalTo(CGSizeMake(60 * kMainTemp, 21 * kMainTemp));
    }];

    _userWhale = [FlatButton buttonWithType:UIButtonTypeCustom];
    _userWhale.layer.masksToBounds = YES;
    _userWhale.layer.cornerRadius = 2 * kMainTemp;
    _userWhale.layer.borderWidth = 0.5 * kMainTemp;
    _userWhale.layer.borderColor = KColor_Black.CGColor;
    [_userWhale setTitle:@"鲸鱼卡片" forState:UIControlStateNormal];
    [_userWhale setTitleColor:KColor_Black forState:UIControlStateNormal];
    _userWhale.titleLabel.font = [UIFont systemFontOfSize:10 * kMainTemp];
    [self addSubview:_userWhale];
    [_userWhale mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.mas_equalTo(self.changeBtn.mas_right).offset(12 * kMainTemp);
      make.top.mas_equalTo(self).offset(196 * kMainTemp);
      make.size.mas_equalTo(CGSizeMake(60 * kMainTemp, 21 * kMainTemp));
    }];

    _settingBtn = [FlatButton buttonWithType:UIButtonTypeCustom];
    _settingBtn.layer.masksToBounds = YES;
    _settingBtn.layer.cornerRadius = 2 * kMainTemp;
    [_settingBtn setBackgroundImage:KImage_name(@"Setting")
                           forState:UIControlStateNormal];
    [self addSubview:_settingBtn];
    [_settingBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.mas_equalTo(self.userWhale.mas_right).offset(12 * kMainTemp);
      make.top.mas_equalTo(self).offset(196 * kMainTemp);
      make.size.mas_equalTo(CGSizeMake(36 * kMainTemp, 21 * kMainTemp));
    }];
  }
  return self;
}

- (void)showBadgeInfoAlert {
  int perHeight = (18 + (16 + 37.5) * CurrentUser.badge.count) * kMainTemp;
  UIView *listView = [[UIView alloc]
      initWithFrame:CGRectMake(0, 0, 270 * kMainTemp, perHeight)];
  int top = 18 * kMainTemp;
  for (NSDictionary *tag in CurrentUser.badge) {
    TagModelResult *badgeInfo = [TagModelResult mj_objectWithKeyValues:tag];
    NSString *badgeName =
        [NSString stringWithFormat:@"badge_type%@", badgeInfo.type];
    UIImageView *iv =
        [[UIImageView alloc] initWithImage:[UIImage imageNamed:badgeName]];
    iv.frame =
        CGRectMake(28 * kMainTemp, top, 37 * kMainTemp, 37.5 * kMainTemp);
    [listView addSubview:iv];

    UILabel *titleL = [[UILabel alloc]
        initWithFrame:CGRectMake(73 * kMainTemp, top,
                                 kMainTemp * (270 - 73 - 24), 20)];
    titleL.font = SourceHanSerifMediumFont(12 * kMainTemp);
    titleL.text = badgeInfo.type_name;
    titleL.textColor = KColor_HighBlack;
    [listView addSubview:titleL];

    UILabel *detailL = [[UILabel alloc]
        initWithFrame:CGRectMake(73 * kMainTemp, top + 23 * kMainTemp,
                                 kMainTemp * (270 - 73 - 24), 20)];
    detailL.font = SourceHanSerifRegularFont(10 * kMainTemp);
    detailL.text = badgeInfo.introduction;
    detailL.textColor = KColor_textGray;
    [listView addSubview:detailL];
    top = detailL.bottom + 18 * kMainTemp;
  }

  FTHZAlertDialogController *dialog =
      [[FTHZAlertDialogController alloc] initWithCustomView:listView
                                                      title:@"勋章说明"];

  [dialog addAction:[FTHZAlertDialogAction
                        actionWithTitle:@"好的，我知道了"
                                 action:nil
                                  style:FTHZAlertDialogActionStyleHighlighted]];
  [[UIViewController topViewController] presentViewController:dialog
                                                     animated:YES
                                                   completion:nil];
}

- (void)loadNewIcon {
  CGSize nameTemp = [NemoUtil
      calculateLabelHeightByText:[UIFont systemFontOfSize:20 * kMainTemp]
                           width:kMainWidth
                       heightMax:kMainHeight
                         content:CurrentUser.nickname];
  _nickNameLabel.text = CurrentUser.nickname;
  [_nickNameLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.mas_equalTo(self).offset(24 * kMainTemp);
    make.top.mas_equalTo(self).offset(64 * kMainTemp);
    make.size.mas_equalTo(CGSizeMake(nameTemp.width + 2, 20 * kMainTemp));
  }];

  _heziLabel.text = CurrentUser.hertz;
  [_heziLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.mas_equalTo(self.nickNameLabel.mas_right).offset(10 * kMainTemp);
    make.bottom.mas_equalTo(self.nickNameLabel.mas_bottom)
        .offset(0 * kMainTemp);
    make.size.mas_equalTo(CGSizeMake(40 * kMainTemp, 22 * kMainTemp));
  }];

  [_smHezi mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.mas_equalTo(self.heziLabel.mas_right).offset(1 * kMainTemp);
    make.centerY.mas_equalTo(self.heziLabel.mas_top);
    make.size.mas_equalTo(CGSizeMake(8 * kMainTemp, 8 * kMainTemp));
  }];

  if ([CurrentUser.gender isEqualToString:@"1"]) {
    _genderImage.image = KImage_name(@"wboy");
  } else if ([CurrentUser.gender isEqualToString:@"2"]) {
    _genderImage.image = KImage_name(@"wgirl");
  }
  [_genderImage mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.mas_equalTo(self.adressDetailLabel.mas_right)
        .offset(6 * kMainTemp);
    make.centerY.mas_equalTo(self.adressDetailLabel).offset(1 * kMainTemp);
    make.size.mas_equalTo(CGSizeMake(12 * kMainTemp, 12 * kMainTemp));
  }];

  [_avatarImageView
      sd_setImageWithURL:[NemoUtil getUrlWithUserDefartIcon:CurrentUser.avatar]
        placeholderImage:KImage_name(@"empty")];
  [self setNeedsLayout];
  [self layoutIfNeeded];
}
@end
