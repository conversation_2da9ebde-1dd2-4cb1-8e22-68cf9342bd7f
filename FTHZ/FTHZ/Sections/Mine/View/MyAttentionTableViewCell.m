#import "MyAttentionTableViewCell.h"

@interface MyAttentionTableViewCell ()
@end

@implementation MyAttentionTableViewCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style
              reuseIdentifier:(NSString *)reuseIdentifier {
  self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
  if (self) {
    [self loadCellView];
    [self setSelectionStyle:(UITableViewCellSelectionStyleNone)];
    self.frame =
        CGRectMake(0, 0, kMainWidth, 48 * kWidthFactor + 16 * kWidthFactor);
  }
  return self;
}

- (void)loadCellView {
  _iconImage = [[UIImageView alloc] init];
  _nameLabel = [[UILabel alloc] init];
  _signLabel = [[UILabel alloc] init];
  _attentionBtn = [FlatButton buttonWithType:UIButtonTypeCustom];
  _timeLabel = [[UILabel alloc] init];

  [self.contentView addSubview:_iconImage];
  [self.contentView addSubview:_nameLabel];
  [self.contentView addSubview:_signLabel];
  [self.contentView addSubview:_attentionBtn];
  [self.contentView addSubview:_timeLabel];

  UIBezierPath *maskPath = [UIBezierPath
      bezierPathWithRoundedRect:CGRectMake(0, 0, 48 * kWidthFactor,
                                           48 * kWidthFactor)
              byRoundingCorners:UIRectCornerAllCorners
                    cornerRadii:CGSizeMake(48 * kWidthFactor,
                                           48 * kWidthFactor)];
  CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
  maskLayer.frame = CGRectMake(0, 0, 48 * kWidthFactor, 48 * kWidthFactor);
  maskLayer.path = maskPath.CGPath;
  _iconImage.layer.mask = maskLayer;

  [_nameLabel setTextColor:KColor_HighBlue];
  [_nameLabel setLineBreakMode:NSLineBreakByWordWrapping];
  _nameLabel.font = SourceHanSerifBoldFont(14 * kWidthFactor);

  [_signLabel setTextColor:KColor_detailDarkGray];
  _signLabel.font = SourceHanSerifRegularFont(13 * kWidthFactor);
  _signLabel.numberOfLines = 1;
  _signLabel.lineBreakMode = NSLineBreakByTruncatingTail;

  [_timeLabel setTextColor:KColor_textTinyGray];
  _timeLabel.font = SourceHanSerifRegularFont(12 * kWidthFactor);
  _timeLabel.textAlignment = NSTextAlignmentLeft;

  [_iconImage mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.contentView).offset(24 * kWidthFactor);
    make.centerY.equalTo(self.contentView);
    make.size.mas_equalTo(CGSizeMake(48 * kWidthFactor, 48 * kWidthFactor));
  }];

  [_nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.iconImage.mas_right).offset(16 * kWidthFactor);
    make.top.equalTo(self.iconImage).offset(4 * kWidthFactor);
  }];

  [_attentionBtn mas_makeConstraints:^(MASConstraintMaker *make) {
    make.right.equalTo(self.contentView).offset(-26 * kWidthFactor);
    make.centerY.equalTo(self.signLabel);
    make.size.mas_equalTo(CGSizeMake(16 * kWidthFactor, 16 * kWidthFactor));
  }];

  [_timeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.nameLabel.mas_right).offset(10 * kWidthFactor);
    make.centerY.equalTo(self.nameLabel);
  }];

  [_signLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.nameLabel);
    make.right.equalTo(self.attentionBtn.mas_left).offset(-10 * kWidthFactor);
    make.top.equalTo(self.nameLabel.mas_bottom).offset(8 * kWidthFactor);
  }];
}

- (void)awakeFromNib {
  [super awakeFromNib];
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
  [super setSelected:selected animated:animated];
}

- (void)prepareForReuse {
  [super prepareForReuse];

  self.iconImage.image = nil;
  self.nameLabel.text = nil;
  self.signLabel.text = nil;
  self.timeLabel.text = nil;
  self.attentionBtn.hidden = YES;
  [self.attentionBtn setBackgroundImage:nil forState:UIControlStateNormal];
  self.attentionBtn.tag = 0;
}

@end