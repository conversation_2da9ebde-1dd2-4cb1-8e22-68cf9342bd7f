#import "WaveView.h"

@interface WaveView ()

@property(nonatomic, strong) CADisplayLink *displayLink;
@property(nonatomic, assign) CGFloat offsetX1;
@property(nonatomic, assign) CGFloat offsetX2;
@property(nonatomic, assign) CGFloat offsetX3;
@property(nonatomic, strong) NSArray<CAShapeLayer *> *waveLayers;
@property(nonatomic, strong) NSArray<NSNumber *> *amplitudes;
@property(nonatomic, strong) NSArray<NSNumber *> *frequencies;
@property(nonatomic, strong) NSArray<NSNumber *> *baseHeights;
@property(nonatomic, assign) CGFloat phase1;
@property(nonatomic, assign) CGFloat phase2;
@property(nonatomic, assign) CGFloat phase3;

@property(nonatomic, assign) CGFloat initialWaveSpeed;
@property(nonatomic, assign) NSTimeInterval slowDownStartTime;
@property(nonatomic, assign) NSTimeInterval slowDownDuration;

@end

@implementation WaveView

- (instancetype)initWithFrame:(CGRect)frame {
  if (self = [super initWithFrame:frame]) {
    [self setup];
  }
  return self;
}

- (void)setup {
  self.initialWaveSpeed = 0.05;
  self.waveSpeed = self.initialWaveSpeed;
  self.slowDownDuration = 3.0;

  self.waveColor = [UIColor colorWithRed:65 / 255.0
                                   green:85 / 255.0
                                    blue:135 / 255.0
                                   alpha:1.0];

  NSMutableArray *layers = [NSMutableArray array];
  NSArray *alphas = @[ @(0.2), @(0.3), @(0.6) ];
  for (int i = 0; i < 3; i++) {
    CAShapeLayer *waveLayer = [CAShapeLayer layer];
    waveLayer.fillColor =
        [self.waveColor colorWithAlphaComponent:[alphas[i] floatValue]].CGColor;
    [self.layer addSublayer:waveLayer];
    [layers addObject:waveLayer];
  }
  self.waveLayers = layers;

  self.amplitudes = @[ @(20), @(18), @(16) ];
  self.frequencies = @[ @(0.8), @(0.6), @(0.4) ];
  self.baseHeights = @[ @(0.85), @(0.90), @(0.95) ];

  [self resetPhases];
}

- (void)resetPhases {
  self.phase1 = (arc4random_uniform(100) / 100.0) * M_PI * 2;
  self.phase2 = (arc4random_uniform(100) / 100.0) * M_PI * 2;
  self.phase3 = (arc4random_uniform(100) / 100.0) * M_PI * 2;
}

- (void)startWave {
  if (!self.displayLink) {
    [self resetPhases];
    self.waveSpeed = self.initialWaveSpeed;
    self.slowDownStartTime = CACurrentMediaTime();
    self.displayLink =
        [CADisplayLink displayLinkWithTarget:self
                                    selector:@selector(waveUpdate)];
    self.displayLink.preferredFramesPerSecond = 30;
    [self.displayLink addToRunLoop:[NSRunLoop mainRunLoop]
                           forMode:NSRunLoopCommonModes];
  }
}

- (void)stopWave {
  [self.displayLink invalidate];
  self.displayLink = nil;
}

- (void)waveUpdate {
  NSTimeInterval currentTime = CACurrentMediaTime();
  NSTimeInterval elapsedTime = currentTime - self.slowDownStartTime;

  if (elapsedTime >= self.slowDownDuration) {
    [self stopWave];
    return;
  }

  CGFloat progress = 1.0 - (elapsedTime / self.slowDownDuration);
  progress = [self easeOutQuad:progress];
  self.waveSpeed = self.initialWaveSpeed * progress;

  if (self.waveSpeed < 0.001) {
    [self stopWave];
    return;
  }

  self.phase1 += self.waveSpeed;
  self.phase2 += self.waveSpeed * 0.8;
  self.phase3 += self.waveSpeed * 0.6;

  CGFloat width = CGRectGetWidth(self.frame);
  CGFloat height = CGRectGetHeight(self.frame);

  [self updateWaveLayer:self.waveLayers[0]
                  phase:self.phase1
              amplitude:[self.amplitudes[0] floatValue]
              frequency:[self.frequencies[0] floatValue]
             baseHeight:[self.baseHeights[0] floatValue]
                  width:width
                 height:height];

  [self updateWaveLayer:self.waveLayers[1]
                  phase:self.phase2
              amplitude:[self.amplitudes[1] floatValue]
              frequency:[self.frequencies[1] floatValue]
             baseHeight:[self.baseHeights[1] floatValue]
                  width:width
                 height:height];

  [self updateWaveLayer:self.waveLayers[2]
                  phase:self.phase3
              amplitude:[self.amplitudes[2] floatValue]
              frequency:[self.frequencies[2] floatValue]
             baseHeight:[self.baseHeights[2] floatValue]
                  width:width
                 height:height];
}

- (void)updateWaveLayer:(CAShapeLayer *)layer
                  phase:(CGFloat)phase
              amplitude:(CGFloat)amplitude
              frequency:(CGFloat)frequency
             baseHeight:(CGFloat)baseHeight
                  width:(CGFloat)width
                 height:(CGFloat)height {
  UIBezierPath *path = [UIBezierPath bezierPath];
  [path moveToPoint:CGPointMake(0, height)];

  CGFloat baseY = height * baseHeight;

  CGFloat step = 2.0;
  for (CGFloat x = 0; x <= width; x += step) {
    CGFloat y =
        amplitude * sin(2 * M_PI * frequency * x / width + phase) +
        amplitude * 0.5 * sin(4 * M_PI * frequency * x / width + phase * 1.5);
    y = y + baseY;
    [path addLineToPoint:CGPointMake(x, y)];
  }

  [path addLineToPoint:CGPointMake(width, height)];
  [path addLineToPoint:CGPointMake(0, height)];

  layer.path = path.CGPath;
}

- (CGFloat)easeOutQuad:(CGFloat)t {
  return t * (2 - t);
}

- (void)layoutSubviews {
  [super layoutSubviews];
  for (CAShapeLayer *layer in self.waveLayers) {
    layer.frame = self.bounds;
  }
}

- (void)willMoveToWindow:(UIWindow *)newWindow {
  [super willMoveToWindow:newWindow];
  if (newWindow) {
    [self startWave];
  } else {
    [self stopWave];
  }
}

- (void)didReceiveMemoryWarning {
  [self stopWave];
}

- (void)dealloc {
  [self stopWave];
}

@end