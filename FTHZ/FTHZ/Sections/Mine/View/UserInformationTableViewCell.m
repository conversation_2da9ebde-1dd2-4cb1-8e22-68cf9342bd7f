#import "UserInformationTableViewCell.h"

@interface UserInformationTableViewCell ()
@property(nonatomic, assign) NSInteger cellType;
@property(nonatomic, strong) UILabel *titleLabel;
@property(nonatomic, strong) UILabel *lineLabel;
@property(nonatomic, strong) UIImageView *avterImage;
@property(nonatomic, strong) UIImageView *setBtn;
@property(nonatomic, strong) UILabel *type1;
@property(nonatomic, strong) UILabel *type2;
@property(nonatomic, strong) UILabel *type3;
@property(nonatomic, strong) UILabel *type4;
@property(nonatomic, strong) UILabel *likeTitle;

@end

@implementation UserInformationTableViewCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style
              reuseIdentifier:(NSString *)reuseIdentifier {
  self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
  if (self) {
    if (!_nameLabel) {
      _nameLabel = [[UILabel alloc] init];
    }
    [_nameLabel setTextColor:KColor_titleDarkGray];
    _nameLabel.font = SourceHanSerifMediumFont(14 * kWidthFactor);
    [self.contentView addSubview:self.nameLabel];
    [self.nameLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.contentView).offset(32 * kWidthFactor);
      make.top.equalTo(self.contentView).offset(12 * kWidthFactor);
      make.size.mas_equalTo(CGSizeMake(120 * kWidthFactor, 20 * kWidthFactor));
    }];
    [self setSelectionStyle:(UITableViewCellSelectionStyleNone)];
  }
  return self;
}

- (void)prepareForReuse {
  [super prepareForReuse];

  self.nameLabel.text = nil;
  self.titleLabel.text = nil;
  self.lineLabel.backgroundColor = nil;
  self.avterImage.image = nil;
  self.setBtn.image = nil;

  [self.type1 removeFromSuperview];
  self.type1 = nil;
  [self.type2 removeFromSuperview];
  self.type2 = nil;
  [self.type3 removeFromSuperview];
  self.type3 = nil;
  [self.type4 removeFromSuperview];
  self.type4 = nil;

  self.likeTitle.text = nil;
}

- (void)loadType1:(NSString *)tempstr {
  if (!_titleLabel) {
    _titleLabel = [[UILabel alloc] init];
  }
  [_titleLabel setTextColor:KColor_titleDarkGray];
  _titleLabel.font = [UIFont systemFontOfSize:14 * kWidthFactor];
  _titleLabel.text = tempstr;
  _titleLabel.textAlignment = NSTextAlignmentRight;
  [self.contentView addSubview:self.titleLabel];
  [self.titleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.right.equalTo(self.contentView).offset(-26 * kWidthFactor);
    make.top.equalTo(self.contentView).offset(12 * kWidthFactor);
    make.size.mas_equalTo(CGSizeMake(120 * kWidthFactor, 20 * kWidthFactor));
  }];

  CGSize tempSize = [NemoUtil
      calculateLabelHeightByText:[UIFont systemFontOfSize:14 * kWidthFactor]
                           width:kMainWidth
                       heightMax:kMainHeight
                         content:tempstr];
  if (!_lineLabel) {
    _lineLabel = [[UILabel alloc] init];
  }
  _lineLabel.backgroundColor = KColor_HighBlack;
  [self.contentView addSubview:self.lineLabel];
  [_lineLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.right.equalTo(self.contentView).offset(-26 * kWidthFactor);
    make.top.equalTo(self.titleLabel.mas_bottom).offset(5 * kWidthFactor);
    make.size.mas_equalTo(CGSizeMake(tempSize.width, 0.5 * kWidthFactor));
  }];
}
- (void)loadType2 {
  if (!_avterImage) {
    _avterImage = [[UIImageView alloc]
        initWithFrame:CGRectMake(0, 0, 30 * kWidthFactor, 30 * kWidthFactor)];
  }
  UIBezierPath *maskPath =
      [UIBezierPath bezierPathWithRoundedRect:_avterImage.bounds
                            byRoundingCorners:UIRectCornerAllCorners
                                  cornerRadii:_avterImage.bounds.size];
  CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
  maskLayer.frame = _avterImage.bounds;
  maskLayer.path = maskPath.CGPath;
  _avterImage.layer.mask = maskLayer;
  [self.contentView addSubview:self.avterImage];
  [self.avterImage mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.right.equalTo(self.contentView).offset(-26 * kWidthFactor);
    make.top.equalTo(self.contentView).offset(4 * kWidthFactor);
    make.size.mas_equalTo(CGSizeMake(30 * kWidthFactor, 30 * kWidthFactor));
  }];
  [_avterImage
      sd_setImageWithURL:[NemoUtil getUrlWithUserSmallIcon:CurrentUser.avatar]
        placeholderImage:KImage_name(@"empty")];
}
- (void)loadType3 {
  if (!_setBtn) {
    _setBtn = [[UIImageView alloc]
        initWithFrame:CGRectMake(0, 0, 15 * kWidthFactor, 15 * kWidthFactor)];
  }
  [self.contentView addSubview:self.setBtn];
  [_setBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.right.equalTo(self.contentView).offset(-26 * kWidthFactor);
    make.top.equalTo(self.contentView).offset(10 * kWidthFactor);
    make.size.mas_equalTo(CGSizeMake(15 * kWidthFactor, 15 * kWidthFactor));
  }];
  _setBtn.image = KImage_name(@"编辑");

  if (CurrentUser.tag.count >= 4) {
    if (_type1) {
      [_type1 removeFromSuperview];
      _type1 = nil;
      [_type2 removeFromSuperview];
      _type2 = nil;
      [_type3 removeFromSuperview];
      _type3 = nil;
      [_type4 removeFromSuperview];
      _type4 = nil;
    }
    TagModelResult *tInfo = [TagModelResult
        mj_objectWithKeyValues:[CurrentUser.tag objectAtIndex:0]];
    TagModelResult *tInfo2 = [TagModelResult
        mj_objectWithKeyValues:[CurrentUser.tag objectAtIndex:1]];
    TagModelResult *tInfo3 = [TagModelResult
        mj_objectWithKeyValues:[CurrentUser.tag objectAtIndex:2]];
    TagModelResult *tInfo4 = [TagModelResult
        mj_objectWithKeyValues:[CurrentUser.tag objectAtIndex:3]];
    CGSize Temp1 = [NemoUtil
        calculateLabelHeightByText:[UIFont systemFontOfSize:12 * kWidthFactor]
                             width:kMainWidth
                         heightMax:kMainHeight
                           content:[NSString
                                       stringWithFormat:@"  %@ ", tInfo.name]];
    _type1 = [[UILabel alloc] init];
    _type1.textColor = KColor_White;
    _type1.backgroundColor = KColor_HighBlack3;
    _type1.layer.masksToBounds = YES;
    _type1.layer.cornerRadius = 3;
    _type1.text = [NSString stringWithFormat:@"  %@ ", tInfo.name];
    _type1.font = [UIFont systemFontOfSize:12 * kWidthFactor];
    [self addSubview:_type1];
    [_type1 mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.mas_equalTo(self).offset(32 * kWidthFactor);
      make.top.mas_equalTo(self.nameLabel.mas_bottom)
          .offset(12.5 * kWidthFactor);
      make.size.mas_equalTo(CGSizeMake(Temp1.width + 2, 22.5 * kWidthFactor));
    }];
    CGSize Temp2 = [NemoUtil
        calculateLabelHeightByText:[UIFont systemFontOfSize:12 * kWidthFactor]
                             width:kMainWidth
                         heightMax:kMainHeight
                           content:[NSString
                                       stringWithFormat:@"  %@ ", tInfo2.name]];
    _type2 = [[UILabel alloc] init];
    _type2.textColor = KColor_White;
    _type2.backgroundColor = KColor_HighBlack3;
    _type2.layer.masksToBounds = YES;
    _type2.layer.cornerRadius = 3;
    _type2.text = [NSString stringWithFormat:@"  %@ ", tInfo2.name];
    _type2.font = [UIFont systemFontOfSize:12 * kWidthFactor];
    [self addSubview:_type2];
    [_type2 mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.mas_equalTo(self.type1.mas_right).offset(12 * kWidthFactor);
      make.centerY.mas_equalTo(self.type1);
      make.size.mas_equalTo(CGSizeMake(Temp2.width + 2, 22.5 * kWidthFactor));
    }];
    CGSize Temp3 = [NemoUtil
        calculateLabelHeightByText:[UIFont systemFontOfSize:12 * kWidthFactor]
                             width:kMainWidth
                         heightMax:kMainHeight
                           content:[NSString
                                       stringWithFormat:@"  %@ ", tInfo3.name]];
    _type3 = [[UILabel alloc] init];
    _type3.textColor = KColor_White;
    _type3.backgroundColor = KColor_HighBlack3;
    _type3.layer.masksToBounds = YES;
    _type3.layer.cornerRadius = 3;
    _type3.text = [NSString stringWithFormat:@"  %@ ", tInfo3.name];
    _type3.font = [UIFont systemFontOfSize:12 * kWidthFactor];
    [self addSubview:_type3];
    [_type3 mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.mas_equalTo(self).offset(32 * kWidthFactor);
      make.top.mas_equalTo(self.type1.mas_bottom).offset(12 * kWidthFactor);
      make.size.mas_equalTo(CGSizeMake(Temp3.width + 2, 22.5 * kWidthFactor));
    }];
    CGSize Temp4 = [NemoUtil
        calculateLabelHeightByText:[UIFont systemFontOfSize:12 * kWidthFactor]
                             width:kMainWidth
                         heightMax:kMainHeight
                           content:[NSString
                                       stringWithFormat:@"  %@ ", tInfo4.name]];
    _type4 = [[UILabel alloc] init];
    _type4.textColor = KColor_White;
    _type4.backgroundColor = KColor_HighBlack3;
    _type4.layer.masksToBounds = YES;
    _type4.layer.cornerRadius = 3;
    _type4.text = [NSString stringWithFormat:@"  %@ ", tInfo4.name];
    _type4.font = [UIFont systemFontOfSize:12 * kWidthFactor];
    [self addSubview:_type4];
    [_type4 mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.mas_equalTo(self.type3.mas_right).offset(12 * kWidthFactor);
      make.centerY.mas_equalTo(self.type3);
      make.size.mas_equalTo(CGSizeMake(Temp4.width + 2, 22.5 * kWidthFactor));
    }];
  }
}

- (void)loadType5 {
  if (!_setBtn) {
    _setBtn = [[UIImageView alloc]
        initWithFrame:CGRectMake(0, 0, 15 * kWidthFactor, 15 * kWidthFactor)];
  }
  [self.contentView addSubview:self.setBtn];
  [_setBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.right.equalTo(self.contentView).offset(-26 * kWidthFactor);
    make.top.equalTo(self.contentView).offset(10 * kWidthFactor);
    make.size.mas_equalTo(CGSizeMake(15 * kWidthFactor, 15 * kWidthFactor));
  }];
  _setBtn.image = KImage_name(@"编辑");

  if (CurrentUser.tag.count >= 4) {
    if (_type1) {
      [_type1 removeFromSuperview];
      _type1 = nil;
      [_type2 removeFromSuperview];
      _type2 = nil;
      [_type3 removeFromSuperview];
      _type3 = nil;
      [_type4 removeFromSuperview];
      _type4 = nil;
    }
    TagModelResult *tInfo = [TagModelResult
        mj_objectWithKeyValues:[CurrentUser.likeTag objectAtIndex:0]];
    TagModelResult *tInfo2 = [TagModelResult
        mj_objectWithKeyValues:[CurrentUser.likeTag objectAtIndex:1]];
    TagModelResult *tInfo3 = [TagModelResult
        mj_objectWithKeyValues:[CurrentUser.likeTag objectAtIndex:2]];
    TagModelResult *tInfo4 = [TagModelResult
        mj_objectWithKeyValues:[CurrentUser.likeTag objectAtIndex:3]];
    CGSize Temp1 = [NemoUtil
        calculateLabelHeightByText:[UIFont systemFontOfSize:12 * kWidthFactor]
                             width:kMainWidth
                         heightMax:kMainHeight
                           content:[NSString
                                       stringWithFormat:@"  %@ ", tInfo.name]];
    _type1 = [[UILabel alloc] init];
    _type1.textColor = KColor_White;
    _type1.backgroundColor = KColor_HighBlack3;
    _type1.layer.masksToBounds = YES;
    _type1.layer.cornerRadius = 3;
    _type1.text = [NSString stringWithFormat:@"  %@ ", tInfo.name];
    _type1.font = [UIFont systemFontOfSize:12 * kWidthFactor];
    [self addSubview:_type1];
    [_type1 mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.mas_equalTo(self).offset(32 * kWidthFactor);
      make.top.mas_equalTo(self.nameLabel.mas_bottom)
          .offset(12.5 * kWidthFactor);
      make.size.mas_equalTo(CGSizeMake(Temp1.width + 2, 22.5 * kWidthFactor));
    }];
    CGSize Temp2 = [NemoUtil
        calculateLabelHeightByText:[UIFont systemFontOfSize:12 * kWidthFactor]
                             width:kMainWidth
                         heightMax:kMainHeight
                           content:[NSString
                                       stringWithFormat:@"  %@ ", tInfo2.name]];
    _type2 = [[UILabel alloc] init];
    _type2.textColor = KColor_White;
    _type2.backgroundColor = KColor_HighBlack3;
    _type2.layer.masksToBounds = YES;
    _type2.layer.cornerRadius = 3;
    _type2.text = [NSString stringWithFormat:@"  %@ ", tInfo2.name];
    _type2.font = [UIFont systemFontOfSize:12 * kWidthFactor];
    [self addSubview:_type2];
    [_type2 mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.mas_equalTo(self.type1.mas_right).offset(12 * kWidthFactor);
      make.centerY.mas_equalTo(self.type1);
      make.size.mas_equalTo(CGSizeMake(Temp2.width + 2, 22.5 * kWidthFactor));
    }];
    CGSize Temp3 = [NemoUtil
        calculateLabelHeightByText:[UIFont systemFontOfSize:12 * kWidthFactor]
                             width:kMainWidth
                         heightMax:kMainHeight
                           content:[NSString
                                       stringWithFormat:@"  %@ ", tInfo3.name]];
    _type3 = [[UILabel alloc] init];
    _type3.textColor = KColor_White;
    _type3.backgroundColor = KColor_HighBlack3;
    _type3.layer.masksToBounds = YES;
    _type3.layer.cornerRadius = 3;
    _type3.text = [NSString stringWithFormat:@"  %@ ", tInfo3.name];
    _type3.font = [UIFont systemFontOfSize:12 * kWidthFactor];
    [self addSubview:_type3];
    [_type3 mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.mas_equalTo(self).offset(32 * kWidthFactor);
      make.top.mas_equalTo(self.type1.mas_bottom).offset(12 * kWidthFactor);
      make.size.mas_equalTo(CGSizeMake(Temp3.width + 2, 22.5 * kWidthFactor));
    }];
    CGSize Temp4 = [NemoUtil
        calculateLabelHeightByText:[UIFont systemFontOfSize:12 * kWidthFactor]
                             width:kMainWidth
                         heightMax:kMainHeight
                           content:[NSString
                                       stringWithFormat:@"  %@ ", tInfo4.name]];
    _type4 = [[UILabel alloc] init];
    _type4.textColor = KColor_White;
    _type4.backgroundColor = KColor_HighBlack3;
    _type4.layer.masksToBounds = YES;
    _type4.layer.cornerRadius = 3;
    _type4.text = [NSString stringWithFormat:@"  %@ ", tInfo4.name];
    _type4.font = [UIFont systemFontOfSize:12 * kWidthFactor];
    [self addSubview:_type4];
    [_type4 mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.mas_equalTo(self.type3.mas_right).offset(12 * kWidthFactor);
      make.centerY.mas_equalTo(self.type3);
      make.size.mas_equalTo(CGSizeMake(Temp4.width + 2, 22.5 * kWidthFactor));
    }];
  }
}

- (void)loadType4 {
  if (!_setBtn) {
    _setBtn = [[UIImageView alloc]
        initWithFrame:CGRectMake(0, 0, 15 * kWidthFactor, 15 * kWidthFactor)];
    [self.contentView addSubview:self.setBtn];
  }
  [_setBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.right.equalTo(self.contentView).offset(-26 * kWidthFactor);
    make.top.equalTo(self.contentView).offset(10 * kWidthFactor);
    make.size.mas_equalTo(CGSizeMake(15 * kWidthFactor, 15 * kWidthFactor));
  }];
  _setBtn.image = KImage_name(@"编辑");

  if (!_likeTitle) {
    _likeTitle = [[UILabel alloc] init];
    _likeTitle.textColor = KColor_detailDarkGray;
    _likeTitle.numberOfLines = 0;
    _likeTitle.font = [UIFont systemFontOfSize:12 * kWidthFactor];
    [self.contentView addSubview:_likeTitle];
  }
  CGSize likeTemp = [NemoUtil
      calculateLabelHeightByText:[UIFont systemFontOfSize:14 * kWidthFactor]
                           width:kMainWidth - 64 * kWidthFactor
                       heightMax:kMainHeight
                         content:CurrentUser.signature];
  _likeTitle.text = CurrentUser.signature;
  [_likeTitle mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.mas_equalTo(self.contentView).offset(32 * kWidthFactor);
    make.top.mas_equalTo(self.nameLabel.mas_bottom).offset(12 * kWidthFactor);
    make.size.mas_equalTo(
        CGSizeMake(kMainWidth - 64 * kWidthFactor, likeTemp.height + 5));
  }];
}

- (void)loadType6 {
  if (!_setBtn) {
    _setBtn = [[UIImageView alloc]
        initWithFrame:CGRectMake(0, 0, 15 * kWidthFactor, 15 * kWidthFactor)];
    [self.contentView addSubview:self.setBtn];
  }
  [_setBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.right.equalTo(self.contentView).offset(-26 * kWidthFactor);
    make.top.equalTo(self.contentView).offset(10 * kWidthFactor);
    make.size.mas_equalTo(CGSizeMake(15 * kWidthFactor, 15 * kWidthFactor));
  }];
  _setBtn.image = KImage_name(@"编辑");

  if (!_likeTitle) {
    _likeTitle = [[UILabel alloc] init];
    _likeTitle.textColor = KColor_detailDarkGray;
    _likeTitle.numberOfLines = 0;
    _likeTitle.font = [UIFont systemFontOfSize:12 * kWidthFactor];
    [self.contentView addSubview:_likeTitle];
  }
  CGSize likeTemp = [NemoUtil
      calculateLabelHeightByText:[UIFont systemFontOfSize:14 * kWidthFactor]
                           width:kMainWidth - 64 * kWidthFactor
                       heightMax:kMainHeight
                         content:[NemoUtil
                                     getYiDuiPaoXieGang:CurrentUser.paoLikeTag
                                                fenType:@" / "]];
  _likeTitle.text = [NemoUtil getYiDuiPaoXieGang:CurrentUser.paoLikeTag
                                         fenType:@" / "];
  [_likeTitle mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.mas_equalTo(self.contentView).offset(32 * kWidthFactor);
    make.top.mas_equalTo(self.nameLabel.mas_bottom).offset(12 * kWidthFactor);
    make.size.mas_equalTo(
        CGSizeMake(kMainWidth - 64 * kWidthFactor, likeTemp.height + 5));
  }];
}

@end
