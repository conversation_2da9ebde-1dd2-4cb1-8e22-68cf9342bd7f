#import "VerifyCodeVC.h"
#import "VerifyCodeModel.h"

@interface VerifyCodeVC ()

@property(nonatomic, strong) UILabel *phoneLabel;
@property(nonatomic, strong) UITextField *verifyCodeTxd;
@property(nonatomic, strong) UIButton *sendCodeBtn;
@property(nonatomic, strong) UIButton *submitBtn;
@property(nonatomic, strong) NSTimer *countdownTimer;
@property(nonatomic, assign) NSInteger countdown;

@end

@implementation VerifyCodeVC

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];

  // 确保导航栏始终显示
  [self.navigationController setNavigationBarHidden:NO animated:NO];
  [self.navigationController.navigationBar setShadowImage:[UIImage new]];
  [self.navigationController.navigationBar
      setBackgroundImage:[UIImage new]
           forBarMetrics:UIBarMetricsDefault];
  self.showBackBtn = YES;
  [AppConfig statusbarStyle:YES];
}

- (void)viewDidLoad {
  [super viewDidLoad];
  self.title = @"验证身份";
  [self setupUI];
  [self sendVerifyCode];
}

- (void)setupUI {
  self.phoneLabel = [[UILabel alloc] init];
  self.phoneLabel.font = [UIFont systemFontOfSize:16];
  self.phoneLabel.textColor = [UIColor grayColor];
  [self.view addSubview:self.phoneLabel];

  // 创建手机号码标签
  UILabel *mobileLabel = [[UILabel alloc] init];
  mobileLabel.text = CurrentUser.mobile;
  mobileLabel.font = [UIFont systemFontOfSize:16];
  mobileLabel.textColor = KColor_White;
  mobileLabel.backgroundColor = KColor_HighBlack;
  mobileLabel.textAlignment = NSTextAlignmentCenter;
  mobileLabel.layer.cornerRadius = 4 * kMainTemp; // 根据需要调整圆角大小
  mobileLabel.layer.masksToBounds = YES;
  [self.view addSubview:mobileLabel];

  // 设置约束
  self.phoneLabel.text = @"验证码已发送至：";
  [self.phoneLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(self.view).offset(20 * kMainTemp);
    make.left.equalTo(self.view).offset(20 * kMainTemp);
  }];

  [mobileLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.phoneLabel.mas_right).offset(5 * kMainTemp);
    make.centerY.equalTo(self.phoneLabel);
    make.height.equalTo(@(20 * kMainTemp)); // 根据需要调整高度
    make.width.equalTo(@(120 * kMainTemp)); // 根据需要调整宽度
  }];

  // 验证码输入框样式
  self.verifyCodeTxd = [[UITextField alloc] init];
  self.verifyCodeTxd.placeholder = @"验证码";
  self.verifyCodeTxd.keyboardType = UIKeyboardTypeNumberPad;
  // 添加圆角背景
  self.verifyCodeTxd.backgroundColor = [UIColor colorWithRed:0.95
                                                       green:0.95
                                                        blue:0.95
                                                       alpha:1.0];
  self.verifyCodeTxd.layer.cornerRadius = 8 * kMainTemp;
  self.verifyCodeTxd.layer.masksToBounds = YES;
  // 添加左边距
  UIView *leftView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 15, 44)];
  self.verifyCodeTxd.leftView = leftView;
  self.verifyCodeTxd.leftViewMode = UITextFieldViewModeAlways;
  [self.view addSubview:self.verifyCodeTxd];

  self.sendCodeBtn = [UIButton buttonWithType:UIButtonTypeSystem];
  [self.sendCodeBtn setTitle:@"获取验证码" forState:UIControlStateNormal];
  [self.sendCodeBtn setTitleColor:KColor_White forState:UIControlStateNormal];
  [self.sendCodeBtn setTitleColor:KColor_White forState:UIControlStateDisabled];
  self.sendCodeBtn.backgroundColor = KColor_HighBlack;
  self.sendCodeBtn.layer.cornerRadius = 22 * kMainTemp;
  self.sendCodeBtn.layer.masksToBounds = YES;
  [self.sendCodeBtn addTarget:self
                       action:@selector(sendVerifyCode)
             forControlEvents:UIControlEventTouchUpInside];
  [self.view addSubview:self.sendCodeBtn];

  self.submitBtn = [FlatButton buttonWithType:UIButtonTypeSystem];
  [self.submitBtn setTitle:@"确认" forState:UIControlStateNormal];
  [self.submitBtn setTitleColor:KColor_White forState:UIControlStateNormal];
  self.submitBtn.backgroundColor = KColor_HighBlack;
  self.submitBtn.layer.cornerRadius = 30 * kMainTemp;
  self.submitBtn.layer.masksToBounds = YES;
  [self.submitBtn addTarget:self
                     action:@selector(submitButtonTapped)
           forControlEvents:UIControlEventTouchUpInside];
  [self.view addSubview:self.submitBtn];

  [self setupConstraints];
}

- (void)setupConstraints {
  [self.phoneLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(self.view).offset(20 * kMainTemp);
    make.left.equalTo(self.view).offset(20 * kMainTemp);
  }];

  [self.verifyCodeTxd mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(self.phoneLabel.mas_bottom).offset(20 * kMainTemp);
    make.left.equalTo(self.phoneLabel);
    make.width.equalTo(@(100 * kMainTemp));
    make.height.equalTo(@(44 * kMainTemp));
  }];

  [self.sendCodeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
    make.right.equalTo(self.view).offset(-20 * kMainTemp); // 添加右边距
    make.centerY.equalTo(self.verifyCodeTxd);
    make.width.equalTo(@(100 * kMainTemp));
    make.height.equalTo(@(44 * kMainTemp));
  }];

  [self.submitBtn mas_makeConstraints:^(MASConstraintMaker *make) {
    make.bottom.equalTo(self.view).offset(-100 * kMainTemp);
    make.centerX.equalTo(self.view);
    make.size.mas_equalTo(CGSizeMake(160 * kMainTemp, 60 * kMainTemp));
  }];
}

- (void)sendVerifyCode {
  self.sendCodeBtn.enabled = NO;
  self.countdown = 60;

  [VerifyCodeModel
      sendVerifyCode:^(NSDictionary *resultObject) {
        VerifyCodeModel *model =
            [VerifyCodeModel mj_objectWithKeyValues:resultObject];
        if ([model.success boolValue]) {
          [self showToastFast:@"验证码已发送"];
          [self startCountdown];
        } else {
          [self showToastFast:model.msg];
          self.sendCodeBtn.enabled = YES;
        }
      }
      failure:^(NSError *requestErr) {
        [self showToastFast:@"发送失败，请重试"];
        self.sendCodeBtn.enabled = YES;
      }];
}

- (void)startCountdown {
  if (self.countdownTimer) {
    [self.countdownTimer invalidate];
  }

  self.countdownTimer =
      [NSTimer scheduledTimerWithTimeInterval:1.0
                                       target:self
                                     selector:@selector(updateCountdown)
                                     userInfo:nil
                                      repeats:YES];
}

- (void)updateCountdown {
  self.countdown--;
  NSString *countdownText =
      [NSString stringWithFormat:@"%lds", (long)self.countdown];
  NSMutableAttributedString *attributedString =
      [[NSMutableAttributedString alloc] initWithString:countdownText];
  [attributedString addAttributes:@{
    NSFontAttributeName : [UIFont boldSystemFontOfSize:16],
    NSForegroundColorAttributeName : KColor_White
  }
                            range:NSMakeRange(0, countdownText.length)];

  [self.sendCodeBtn setAttributedTitle:attributedString
                              forState:UIControlStateNormal];

  if (self.countdown <= 0) {
    [self.countdownTimer invalidate];
    self.countdownTimer = nil;
    self.sendCodeBtn.enabled = YES;
    // 重新获取时也使用粗体
    NSAttributedString *resetText = [[NSAttributedString alloc]
        initWithString:@"重新获取"
            attributes:@{
              NSFontAttributeName : [UIFont boldSystemFontOfSize:16],
              NSForegroundColorAttributeName : KColor_White
            }];
    [self.sendCodeBtn setAttributedTitle:resetText
                                forState:UIControlStateNormal];
  }
}

- (void)submitButtonTapped {
  if (self.verifyCodeTxd.text.length == 0) {
    [self showToastFast:@"请输入验证码"];
    return;
  }

  [VerifyCodeModel verifyCode:self.verifyCodeTxd.text
      success:^(NSDictionary *resultObject) {
        VerifyCodeModel *model =
            [VerifyCodeModel mj_objectWithKeyValues:resultObject];
        if ([model.success boolValue]) {
          [self showToastFast:@"验证成功"];
          if (self.didVerifySuccess) {
            self.didVerifySuccess();
          }
          dispatch_after(
              dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)),
              dispatch_get_main_queue(), ^{
                [self.navigationController popViewControllerAnimated:YES];
              });
        } else {
          [self showToastFast:model.msg];
        }
      }
      failure:^(NSError *requestErr) {
        [self showToastFast:@"验证失败，请重试"];
      }];
}

- (void)dealloc {
  if (self.countdownTimer) {
    [self.countdownTimer invalidate];
    self.countdownTimer = nil;
  }
}

@end