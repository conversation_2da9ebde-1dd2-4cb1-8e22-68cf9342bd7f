#import "FriendSelectionVC.h"
#import "IMUserInfoModel.h"
#import "MyAttentionModel.h"
#import "SearchFriendsModel.h"

@interface CheckBoxView : UIView
@property(nonatomic, assign) BOOL selected;
@property(nonatomic, strong) UIColor *checkedColor;
@property(nonatomic, strong) UIColor *uncheckedColor;
@property(nonatomic, strong) CAShapeLayer *borderLayer;
@property(nonatomic, strong) CAShapeLayer *checkmarkLayer;
@end

@implementation CheckBoxView

- (instancetype)initWithFrame:(CGRect)frame {
  if (self = [super initWithFrame:frame]) {
    self.backgroundColor = [UIColor clearColor];
    _checkedColor = [UIColor blackColor];
    _uncheckedColor = [UIColor blackColor];

    _borderLayer = [CAShapeLayer layer];
    _borderLayer.strokeColor = _uncheckedColor.CGColor;
    _borderLayer.fillColor = [UIColor clearColor].CGColor;
    _borderLayer.lineWidth = 1.0;
    [self.layer addSublayer:_borderLayer];

    _checkmarkLayer = [CAShapeLayer layer];
    _checkmarkLayer.fillColor = _checkedColor.CGColor;
    _checkmarkLayer.hidden = YES;
    [self.layer addSublayer:_checkmarkLayer];
  }
  return self;
}

- (void)layoutSubviews {
  [super layoutSubviews];

  CGFloat margin = 3.0;
  CGRect boxRect = CGRectInset(self.bounds, margin, margin);

  UIBezierPath *borderPath = [UIBezierPath bezierPathWithOvalInRect:boxRect];
  _borderLayer.path = borderPath.CGPath;

  _checkmarkLayer.path = borderPath.CGPath;
}

- (void)setSelected:(BOOL)selected {
  _selected = selected;

  if (selected) {
    _checkmarkLayer.hidden = NO;
  } else {
    _checkmarkLayer.hidden = YES;
  }
}

@end

@interface FriendSelectionCell : UITableViewCell
@property(nonatomic, strong) UIImageView *avatarImageView;
@property(nonatomic, strong) UILabel *nameLabel;
@property(nonatomic, strong) UILabel *signatureLabel;
@property(nonatomic, strong) UIButton *selectButton;
@property(nonatomic, assign) BOOL isSelected;
@property(nonatomic, copy) void (^selectAction)(BOOL selected);
@end

@implementation FriendSelectionCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style
              reuseIdentifier:(NSString *)reuseIdentifier {
  if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
    self.selectionStyle = UITableViewCellSelectionStyleNone;

    _avatarImageView = [[UIImageView alloc] init];
    _avatarImageView.layer.cornerRadius = 20;
    _avatarImageView.layer.masksToBounds = YES;
    [self.contentView addSubview:_avatarImageView];

    _nameLabel = [[UILabel alloc] init];
    _nameLabel.font = SourceHanSerifBoldFont(15 * kWidthFactor);
    _nameLabel.textColor = KColor_HighBlue;
    [self.contentView addSubview:_nameLabel];

    _signatureLabel = [[UILabel alloc] init];
    _signatureLabel.font = [UIFont systemFontOfSize:12];
    _signatureLabel.textColor = [UIColor grayColor];
    _signatureLabel.numberOfLines = 1;
    [self.contentView addSubview:_signatureLabel];

    _selectButton = [UIButton buttonWithType:UIButtonTypeCustom];

    UIImage *uncheckedImage = [self createCheckboxImageWithSelected:NO];
    UIImage *checkedImage = [self createCheckboxImageWithSelected:YES];

    [_selectButton setImage:uncheckedImage forState:UIControlStateNormal];
    [_selectButton setImage:checkedImage forState:UIControlStateSelected];

    [_selectButton addTarget:self
                      action:@selector(selectButtonAction:)
            forControlEvents:UIControlEventTouchUpInside];
    [self.contentView addSubview:_selectButton];

    [_avatarImageView mas_makeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.contentView).offset(15);
      make.centerY.equalTo(self.contentView);
      make.size.mas_equalTo(CGSizeMake(40, 40));
    }];

    [_nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(_avatarImageView.mas_right).offset(10);
      make.bottom.equalTo(self.contentView.mas_centerY).offset(-2);
      make.right.equalTo(_selectButton.mas_left).offset(-10);
    }];

    [_signatureLabel mas_makeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(_nameLabel);
      make.top.equalTo(self.contentView.mas_centerY).offset(2);
      make.right.equalTo(_nameLabel);
    }];

    [_selectButton mas_makeConstraints:^(MASConstraintMaker *make) {
      make.right.equalTo(self.contentView).offset(-15);
      make.centerY.equalTo(self.contentView);
      make.size.mas_equalTo(CGSizeMake(28, 28));
    }];
  }
  return self;
}

- (UIImage *)createCheckboxImageWithSelected:(BOOL)selected {
  CGSize size = CGSizeMake(20, 20);
  UIGraphicsBeginImageContextWithOptions(size, NO, 0);

  CGContextRef context = UIGraphicsGetCurrentContext();

  CGRect rect = CGRectMake(2, 2, size.width - 4, size.height - 4);
  UIBezierPath *borderPath = [UIBezierPath bezierPathWithOvalInRect:rect];

  [[UIColor blackColor] setStroke];
  [borderPath setLineWidth:1.0];
  [borderPath stroke];

  if (selected) {
    [[UIColor blackColor] setFill];
    [borderPath fill];
  }

  UIImage *image = UIGraphicsGetImageFromCurrentImageContext();
  UIGraphicsEndImageContext();

  return image;
}

- (void)selectButtonAction:(UIButton *)sender {
  sender.selected = !sender.selected;
  _isSelected = sender.selected;

  if (self.selectAction) {
    self.selectAction(_isSelected);
  }
}

- (void)setIsSelected:(BOOL)isSelected {
  _isSelected = isSelected;
  _selectButton.selected = isSelected;
  self.contentView.backgroundColor = [UIColor whiteColor];
}

@end

@interface FriendSelectionVC () <UITableViewDelegate, UITableViewDataSource,
                                 UISearchBarDelegate>

@property(nonatomic, strong) UITableView *tableView;
@property(nonatomic, strong) UISearchBar *searchBar;
@property(nonatomic, strong) UIButton *doneButton;
@property(nonatomic, strong) NSMutableArray *friendsList;
@property(nonatomic, strong) NSMutableArray *filteredFriends;
@property(nonatomic, strong) NSMutableDictionary *selectedFriendsDict;
@property(nonatomic, assign) NSInteger page;
@property(nonatomic, assign) BOOL isLoading;
@property(nonatomic, assign) BOOL hasMoreData;
@property(nonatomic, copy) NSString *currentSearchText;
@end

@implementation FriendSelectionVC

- (void)viewDidLoad {
  [super viewDidLoad];

  self.navigationController.navigationBarHidden = YES;

  [self setupSelectedFriendsDict];
  [self setupViews];

  self.page = 1;
  self.isLoading = NO;
  self.hasMoreData = YES;

  [self loadFriendsList];
}

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];
  self.navigationController.navigationBarHidden = YES;
}

- (void)setupSelectedFriendsDict {
  self.selectedFriendsDict = [NSMutableDictionary dictionary];

  if (self.selectedFriends.count > 0) {
    for (id friendObj in self.selectedFriends) {
      if ([friendObj isKindOfClass:[AttentionUserResult class]]) {
        AttentionUserResult *friend = friendObj;
        if (friend.userid) {
          [self.selectedFriendsDict setObject:friend forKey:friend.userid];
        }
      } else if ([friendObj isKindOfClass:[NSDictionary class]]) {
        NSDictionary *friend = friendObj;
        NSString *userId = friend[@"userid"];
        if (userId) {
          [self.selectedFriendsDict setObject:friend forKey:userId];
        }
      }
    }
  }
}

- (void)setupViews {
  self.view.backgroundColor = [UIColor whiteColor];

  UIView *topbar = [UIView new];
  [self.view addSubview:topbar];
  [topbar mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.right.equalTo(self.view);
    make.top.equalTo(self.view.mas_safeAreaLayoutGuideTop).offset(0);
    make.height.mas_equalTo(70 * kWidthFactor);
  }];

  FlatButton *cancelBt = [FlatButton new];
  [cancelBt setImage:KImage_name(@"Close") forState:UIControlStateNormal];
  [cancelBt addTarget:self
                action:@selector(cancelAction)
      forControlEvents:UIControlEventTouchUpInside];
  [topbar addSubview:cancelBt];
  [cancelBt mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(topbar).offset(24 * kWidthFactor);
    make.centerY.equalTo(topbar);
  }];

  UILabel *navTitle = [UILabel new];
  navTitle.text = @"选择好友";
  navTitle.font = SourceHanSerifSemiBoldFont(16 * kWidthFactor);
  navTitle.textColor = KColor_HighBlack;
  [topbar addSubview:navTitle];
  [navTitle mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(topbar).offset(23 * kWidthFactor);
    make.centerX.equalTo(topbar);
  }];

  self.doneButton = [FlatButton new];
  [self.doneButton setImage:KImage_name(@"Send") forState:UIControlStateNormal];
  [self.doneButton addTarget:self
                      action:@selector(doneButtonAction)
            forControlEvents:UIControlEventTouchUpInside];
  [topbar addSubview:self.doneButton];
  [self.doneButton mas_makeConstraints:^(MASConstraintMaker *make) {
    make.right.equalTo(topbar).offset(-24 * kWidthFactor);
    make.centerY.equalTo(topbar);
  }];

  self.searchBar = [[UISearchBar alloc] init];
  self.searchBar.placeholder = @"搜索好友";
  self.searchBar.delegate = self;

  if (@available(iOS 13.0, *)) {
    self.searchBar.searchBarStyle = UISearchBarStyleMinimal;
    self.searchBar.searchTextField.backgroundColor = [UIColor clearColor];
  } else {
    self.searchBar.backgroundImage = [UIImage new];
    self.searchBar.backgroundColor = [UIColor clearColor];

    for (UIView *subview in self.searchBar.subviews) {
      if ([subview isKindOfClass:NSClassFromString(@"UISearchBarBackground")]) {
        subview.alpha = 0;
      }
    }
  }

  [self.view addSubview:self.searchBar];

  self.tableView = [[UITableView alloc] initWithFrame:CGRectZero
                                                style:UITableViewStylePlain];
  self.tableView.delegate = self;
  self.tableView.dataSource = self;
  self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
  [self.tableView registerClass:[FriendSelectionCell class]
         forCellReuseIdentifier:@"FriendCell"];
  [self.view addSubview:self.tableView];

  [self.searchBar mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(topbar.mas_bottom);
    make.left.right.equalTo(self.view);
    make.height.mas_equalTo(44);
  }];

  [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(self.searchBar.mas_bottom);
    make.left.right.bottom.equalTo(self.view);
  }];
}

- (void)cancelAction {
  [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)loadFriendsList {
  if (self.isLoading || !self.hasMoreData) {
    return;
  }

  self.isLoading = YES;

  @weakify(self);
  [SearchFriendsModel searchFriends:nil
      page:self.page
      success:^(NSDictionary *resultObject) {
        @strongify(self);
        SearchFriendsModel *model =
            [SearchFriendsModel mj_objectWithKeyValues:resultObject];
        if ([model.success boolValue]) {
          NSMutableArray *newFriends = [NSMutableArray array];
          for (NSDictionary *friendDict in model.data) {
            AttentionUserResult *friend =
                [AttentionUserResult mj_objectWithKeyValues:friendDict];
            [newFriends addObject:friend];
          }

          if (self.page == 1) {
            self.friendsList = newFriends;
            self.filteredFriends =
                [NSMutableArray arrayWithArray:self.friendsList];
          } else {
            [self.friendsList addObjectsFromArray:newFriends];
            [self.filteredFriends addObjectsFromArray:newFriends];
          }

          if (newFriends.count > 0) {
            self.page += 1;
            self.hasMoreData = YES;
          } else {
            self.hasMoreData = NO;
          }

          [self.tableView reloadData];
        } else {
          [self showToastFast:model.msg];
        }
        self.isLoading = NO;
      }
      failure:^(NSError *requestErr) {
        @strongify(self);
        [self showToastFast:@"获取好友列表失败，请检查网络后重试"];
        self.isLoading = NO;
      }];
}

- (void)searchFriends:(NSString *)keyword {
  if (keyword.length == 0) {
    self.filteredFriends = [NSMutableArray arrayWithArray:self.friendsList];
    [self.tableView reloadData];
    return;
  }

  self.isLoading = YES;
  self.page = 1;

  @weakify(self);
  [SearchFriendsModel searchFriends:keyword
      page:self.page
      success:^(NSDictionary *resultObject) {
        @strongify(self);
        SearchFriendsModel *model =
            [SearchFriendsModel mj_objectWithKeyValues:resultObject];
        if ([model.success boolValue]) {
          NSMutableArray *searchResults = [NSMutableArray array];
          for (NSDictionary *friendDict in model.data) {
            AttentionUserResult *friend =
                [AttentionUserResult mj_objectWithKeyValues:friendDict];
            [searchResults addObject:friend];
          }

          self.filteredFriends = searchResults;

          if (searchResults.count > 0) {
            self.page += 1;
            self.hasMoreData = YES;
          } else {
            self.hasMoreData = NO;
          }

          [self.tableView reloadData];
        } else {
          [self showToastFast:model.msg];
        }
        self.isLoading = NO;
      }
      failure:^(NSError *requestErr) {
        @strongify(self);
        [self showToastFast:@"搜索失败，请检查网络后重试"];
        self.isLoading = NO;
      }];
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
  if (scrollView.contentOffset.y > 0 &&
      (scrollView.contentOffset.y + scrollView.frame.size.height) >
          scrollView.contentSize.height - 50 &&
      !self.isLoading && self.hasMoreData) {
    if (self.currentSearchText.length > 0) {
      [self searchMoreFriends:self.currentSearchText];
    } else {
      [self loadFriendsList];
    }
  }
}

- (void)searchMoreFriends:(NSString *)keyword {
  if (self.isLoading || !self.hasMoreData) {
    return;
  }

  self.isLoading = YES;

  @weakify(self);
  [SearchFriendsModel searchFriends:keyword
      page:self.page
      success:^(NSDictionary *resultObject) {
        @strongify(self);
        SearchFriendsModel *model =
            [SearchFriendsModel mj_objectWithKeyValues:resultObject];
        if ([model.success boolValue]) {
          NSMutableArray *newFriends = [NSMutableArray array];
          for (NSDictionary *friendDict in model.data) {
            AttentionUserResult *friend =
                [AttentionUserResult mj_objectWithKeyValues:friendDict];
            [newFriends addObject:friend];
          }

          [self.filteredFriends addObjectsFromArray:newFriends];

          if (newFriends.count > 0) {
            self.page += 1;
            self.hasMoreData = YES;
          } else {
            self.hasMoreData = NO;
          }

          [self.tableView reloadData];
        } else {
          [self showToastFast:model.msg];
        }
        self.isLoading = NO;
      }
      failure:^(NSError *requestErr) {
        @strongify(self);
        [self showToastFast:@"搜索失败，请检查网络后重试"];
        self.isLoading = NO;
      }];
}

- (void)doneButtonAction {
  if (self.delegate &&
      [self.delegate respondsToSelector:@selector(didSelectFriends:)]) {
    NSArray *selectedFriends = [self.selectedFriendsDict allValues];
    [self.delegate didSelectFriends:selectedFriends];
  }
  [self dismissViewControllerAnimated:YES completion:nil];
}

#pragma mark - UITableViewDataSource & UITableViewDelegate

- (NSInteger)tableView:(UITableView *)tableView
    numberOfRowsInSection:(NSInteger)section {
  return self.filteredFriends.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView
         cellForRowAtIndexPath:(NSIndexPath *)indexPath {
  FriendSelectionCell *cell =
      [tableView dequeueReusableCellWithIdentifier:@"FriendCell"
                                      forIndexPath:indexPath];

  id friendObj = self.filteredFriends[indexPath.row];
  AttentionUserResult *friend;

  if ([friendObj isKindOfClass:[AttentionUserResult class]]) {
    friend = friendObj;
  } else if ([friendObj isKindOfClass:[NSDictionary class]]) {
    friend = [AttentionUserResult mj_objectWithKeyValues:friendObj];
  } else {
    NSLog(@"意外的对象类型: %@", [friendObj class]);
    friend = [[AttentionUserResult alloc] init];
  }

  [cell.avatarImageView
      sd_setImageWithURL:[NemoUtil getUrlWithUserSmallIcon:friend.avatar]
        placeholderImage:KImage_name(@"empty")];
  cell.nameLabel.text = friend.nickname;
  cell.signatureLabel.text =
      friend.signature.length > 0 ? friend.signature : @"";

  BOOL isSelected =
      [self.selectedFriendsDict objectForKey:friend.userid] != nil;
  cell.isSelected = isSelected;

  @weakify(self);
  cell.selectAction = ^(BOOL selected) {
    @strongify(self);
    if (selected) {
      [self.selectedFriendsDict setObject:friend forKey:friend.userid];
    } else {
      [self.selectedFriendsDict removeObjectForKey:friend.userid];
    }
  };

  return cell;
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForRowAtIndexPath:(NSIndexPath *)indexPath {
  return 70;
}

#pragma mark - UISearchBarDelegate

- (void)searchBarCancelButtonClicked:(UISearchBar *)searchBar {
  searchBar.text = @"";
  self.currentSearchText = @"";

  self.page = 1;
  self.hasMoreData = YES;

  self.filteredFriends = [NSMutableArray arrayWithArray:self.friendsList];

  [self.tableView reloadData];

  [searchBar resignFirstResponder];
}

- (void)searchBar:(UISearchBar *)searchBar
    textDidChange:(NSString *)searchText {
  if (searchText.length == 0) {
    self.currentSearchText = @"";

    self.page = 1;
    self.hasMoreData = YES;

    self.filteredFriends = [NSMutableArray arrayWithArray:self.friendsList];

    [self.tableView reloadData];

    [searchBar resignFirstResponder];
  }
}

- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView {
  if ([self.searchBar isFirstResponder]) {
    [self.searchBar resignFirstResponder];
  }
}

- (void)searchBarSearchButtonClicked:(UISearchBar *)searchBar {
  NSString *searchText = searchBar.text;
  self.currentSearchText = searchText;
  self.page = 1;
  self.hasMoreData = YES;
  [self searchFriends:searchText];
  [searchBar resignFirstResponder];
}

@end