#import "BaseJsonModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface POSTIPStatusModel : BaseJsonModel
@property(nonatomic, strong) NSArray *data;

+ (void)postPOSTIPStatusModel:(NSString *)locationStatus
                   pushStatus:(NSString *)pushStatus
                      success:(success)_success
                      failure:(failure)_failure;

+ (void)postPOSTIPStatusModel:(NSString *)locationStatus
                   pushStatus:(NSString *)pushStatus
                     location:(NSString *_Nullable)location
                      success:(success)_success
                      failure:(failure)_failure;

@end

NS_ASSUME_NONNULL_END
