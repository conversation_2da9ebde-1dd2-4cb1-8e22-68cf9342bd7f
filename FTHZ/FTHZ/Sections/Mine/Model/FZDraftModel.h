#import "MusicInfoModel.h"
#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface FZDraftModel : NSObject
@property(nonatomic, copy) NSString *articleContent;
@property(nonatomic, strong) NSDate *time;
@property(nonatomic, strong) NSArray<NSString *> *pictureArrays;
@property(nonatomic, strong) MusicInfoData *musicInfo;

@property(nonatomic, strong) NSString *musicOriginText;

@property(nonatomic, assign) NSInteger selectTag;

@property (nonatomic, strong) NSDate *scheduleTime;

+ (NSArray *)getUnArchiveData;
@end

NS_ASSUME_NONNULL_END
