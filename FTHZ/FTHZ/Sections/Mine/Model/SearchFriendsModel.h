#import "BaseJsonModel.h"
#import "MyAttentionModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface SearchFriendsModel : BaseJsonModel

@property(nonatomic, strong) NSArray<AttentionUserResult *> *data;
@property(nonatomic, assign) NSInteger page;

+ (void)searchFriends:(nullable NSString *)keyword
                page:(NSInteger)page
             success:(success)_success
             failure:(failure)_failure;

@end

NS_ASSUME_NONNULL_END