#import "POSTIPStatusModel.h"

@implementation POSTIPStatusModel
+ (void)postPOSTIPStatusModel:(NSString *)locationStatus
                   pushStatus:(NSString *)pushStatus
                      success:(success)_success
                      failure:(failure)_failure {
  NSMutableDictionary *param = [NSMutableDictionary new];
  [param setObject:locationStatus forKey:@"locationStatus"];
  [param setObject:pushStatus forKey:@"pushStatus"];
  [Http postAsynRequestWithUrl:KURLPOSTIP
                        params:param
                       success:_success
                       failure:_failure];
}

+ (void)postPOSTIPStatusModel:(NSString *)locationStatus
                   pushStatus:(NSString *)pushStatus
                     location:(NSString *_Nullable)location
                      success:(success)_success
                      failure:(failure)_failure {
  NSMutableDictionary *param = [NSMutableDictionary new];
  [param setObject:locationStatus forKey:@"locationStatus"];
  [param setObject:pushStatus forKey:@"pushStatus"];
  if (location) {
    [param setObject:location forKey:@"location"];
  }

  [Http postAsynRequestWithUrl:KURLPOSTIP
                        params:param
                       success:_success
                       failure:_failure];
}
@end
