#import "SearchFriendsModel.h"

@implementation SearchFriendsModel

+ (void)searchFriends:(nullable NSString *)keyword
                 page:(NSInteger)page
              success:(success)_success
              failure:(failure)_failure {
  NSMutableDictionary *params = [NSMutableDictionary dictionary];

  if (keyword && keyword.length > 0) {
    [params setObject:keyword forKey:@"keyword"];
  }

  [params setObject:@(page) forKey:@"page"];

  [Http getAsynRequestWithUrl:KURLGetFriends
                       params:params
                      success:_success
                      failure:_failure];
}

@end