#import "ChangeGenderVC.h"
#import "UserUserinfoModel.h"

@interface ChangeGenderVC () {
  NSInteger genderType;
}
@property(nonatomic, strong) FlatButton *loginBtn;
@property(nonatomic, strong) FlatButton *genderBtn1;
@property(nonatomic, strong) FlatButton *genderBtn2;
@property(nonatomic, strong) UIImageView *manImageView;
@property(nonatomic, strong) UIImageView *womanImageView;
@end

@implementation ChangeGenderVC
- (void)saveData {
  @weakify(self);
  [HUD show];
  [AccountManager
      updateUserInfoWithName:CurrentUser.nickname
                      gender:[NSString stringWithFormat:@"%ld", genderType]
                      avatar:CurrentUser.avatar
                   signature:CurrentUser.signature
                       brith:CurrentUser.birth
                  completion:^(UserPersonResult *_Nullable user,
                               NSError *_Nullable error) {
                    [HUD dissmiss];
                    @strongify(self);
                    if (error) {
                      [self showToastFast:error.localizedDescription];
                      return;
                    }
                    [self dismissView];
                  }];
}

- (void)viewDidLoad {
  [super viewDidLoad];
  [self setTitleForV199:@"性别"];

  UILabel *genderTitleLabel = [[UILabel alloc] init];
  genderTitleLabel.textColor = KColor_Black;
  genderTitleLabel.text = @"";
  genderTitleLabel.font = SourceHanSerifSemiBoldFont(20 * kWidthFactor);
  [self.view addSubview:genderTitleLabel];
  [genderTitleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.centerX.equalTo(self.view);
    make.top.equalTo(self.safeContentView).offset(60 * kWidthFactor);
    make.height.mas_equalTo(24 * kWidthFactor);
  }];

  genderType = [CurrentUser.gender integerValue];

  UIView *buttonsContainer = [[UIView alloc] init];
  [self.view addSubview:buttonsContainer];
  [buttonsContainer mas_makeConstraints:^(MASConstraintMaker *make) {
    make.centerX.equalTo(self.view);
    make.top.equalTo(genderTitleLabel.mas_bottom).offset(50 * kWidthFactor);
    make.width.mas_equalTo(160 * kWidthFactor);
    make.height.mas_equalTo(340 * kWidthFactor);
  }];

  // 创建男性按钮
  _genderBtn1 = [FlatButton buttonWithType:UIButtonTypeCustom];
  _genderBtn1.clipsToBounds = NO;
  _genderBtn1.tag = 001;
  _genderBtn1.layer.cornerRadius = 70 * kWidthFactor;
  _genderBtn1.layer.masksToBounds = NO;
  // Remove title
  [_genderBtn1 setTitle:@"" forState:UIControlStateNormal];
  [_genderBtn1 setTitleColor:KColor_White forState:UIControlStateNormal];

  _genderBtn1.layer.shadowColor = [UIColor blackColor].CGColor;
  _genderBtn1.layer.shadowOffset = CGSizeMake(0, 5);
  _genderBtn1.layer.shadowOpacity = 0.3;
  _genderBtn1.layer.shadowRadius = 5.0;
  _genderBtn1.backgroundColor = [UIColor clearColor];

  UIView *innerView1 = [[UIView alloc] init];
  innerView1.layer.cornerRadius = 70 * kWidthFactor;
  innerView1.layer.masksToBounds = YES;
  innerView1.backgroundColor = [UIColor colorWithRed:0.85
                                               green:0.85
                                                blue:0.85
                                               alpha:1.0];
  innerView1.userInteractionEnabled = NO;
  [_genderBtn1 addSubview:innerView1];
  [innerView1 mas_makeConstraints:^(MASConstraintMaker *make) {
    make.edges.equalTo(_genderBtn1);
  }];

  // 创建男性图标 - 确保正确加载图片
  UIImage *manImage = [UIImage imageNamed:@"man"];

  _manImageView = [[UIImageView alloc] initWithImage:manImage];
  _manImageView.contentMode = UIViewContentModeScaleAspectFit;
  _manImageView.layer.zPosition = 999; // 确保图标在最上层
  _manImageView.backgroundColor = [UIColor clearColor];
  _manImageView.userInteractionEnabled = NO;

  // 添加到按钮上
  [_genderBtn1 addSubview:_manImageView];
  [_manImageView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.center.equalTo(_genderBtn1);
    make.size.mas_equalTo(CGSizeMake(50 * kWidthFactor, 50 * kWidthFactor));
  }];

  _genderBtn1.layer.borderWidth = 0.5;
  _genderBtn1.layer.borderColor =
      [UIColor colorWithWhite:1.0 alpha:0.2].CGColor;

  [_genderBtn1 addTarget:self
                  action:@selector(chouse:)
        forControlEvents:UIControlEventTouchUpInside];

  [buttonsContainer addSubview:_genderBtn1];
  [_genderBtn1 mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(buttonsContainer);
    make.centerX.equalTo(buttonsContainer);
    make.size.mas_equalTo(CGSizeMake(140 * kWidthFactor, 140 * kWidthFactor));
  }];

  // 创建女性按钮
  _genderBtn2 = [FlatButton buttonWithType:UIButtonTypeCustom];
  _genderBtn2.clipsToBounds = NO;
  _genderBtn2.tag = 002;
  _genderBtn2.layer.cornerRadius = 70 * kWidthFactor;
  _genderBtn2.layer.masksToBounds = NO;
  // Remove title
  [_genderBtn2 setTitle:@"" forState:UIControlStateNormal];
  [_genderBtn2 setTitleColor:KColor_White forState:UIControlStateNormal];

  _genderBtn2.layer.shadowColor = [UIColor blackColor].CGColor;
  _genderBtn2.layer.shadowOffset = CGSizeMake(0, 5);
  _genderBtn2.layer.shadowOpacity = 0.3;
  _genderBtn2.layer.shadowRadius = 5.0;
  _genderBtn2.backgroundColor = [UIColor clearColor];

  UIView *innerView2 = [[UIView alloc] init];
  innerView2.layer.cornerRadius = 70 * kWidthFactor;
  innerView2.layer.masksToBounds = YES;
  innerView2.backgroundColor = [UIColor colorWithRed:0.85
                                               green:0.85
                                                blue:0.85
                                               alpha:1.0];
  innerView2.userInteractionEnabled = NO;
  [_genderBtn2 addSubview:innerView2];
  [innerView2 mas_makeConstraints:^(MASConstraintMaker *make) {
    make.edges.equalTo(_genderBtn2);
  }];

  // 创建女性图标 - 确保正确加载图片
  UIImage *womanImage = [UIImage imageNamed:@"woman"];
  _womanImageView = [[UIImageView alloc] initWithImage:womanImage];
  _womanImageView.contentMode = UIViewContentModeScaleAspectFit;
  _womanImageView.layer.zPosition = 999; // 确保图标在最上层
  _womanImageView.backgroundColor = [UIColor clearColor];
  _womanImageView.userInteractionEnabled = NO;

  // 添加到按钮上
  [_genderBtn2 addSubview:_womanImageView];
  [_womanImageView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.center.equalTo(_genderBtn2);
    make.size.mas_equalTo(CGSizeMake(50 * kWidthFactor, 50 * kWidthFactor));
  }];

  _genderBtn2.layer.borderWidth = 0.5;
  _genderBtn2.layer.borderColor =
      [UIColor colorWithWhite:1.0 alpha:0.2].CGColor;

  [_genderBtn2 addTarget:self
                  action:@selector(chouse:)
        forControlEvents:UIControlEventTouchUpInside];
  [buttonsContainer addSubview:_genderBtn2];
  [_genderBtn2 mas_makeConstraints:^(MASConstraintMaker *make) {
    make.bottom.equalTo(buttonsContainer);
    make.centerX.equalTo(buttonsContainer);
    make.size.mas_equalTo(CGSizeMake(140 * kWidthFactor, 140 * kWidthFactor));
  }];

  [self updateButtonAppearance];

  FlatButton *loginBtn = [FlatButton buttonWithType:UIButtonTypeSystem];
  loginBtn.backgroundColor = KColor_HighBlack;
  loginBtn.layer.masksToBounds = YES;
  loginBtn.layer.cornerRadius = 30 * kWidthFactor;
  [loginBtn setTitle:@"确定" forState:UIControlStateNormal];
  [loginBtn setTitleColor:KColor_White forState:UIControlStateNormal];
  [loginBtn addTarget:self
                action:@selector(goNext)
      forControlEvents:UIControlEventTouchUpInside];
  [self.view addSubview:loginBtn];
  [loginBtn mas_makeConstraints:^(MASConstraintMaker *make) {
    make.bottom.equalTo(self.view.mas_bottom).with.offset(-100 * kWidthFactor);
    make.centerX.equalTo(self.view);
    make.size.mas_equalTo(CGSizeMake(160 * kWidthFactor, 60 * kWidthFactor));
  }];
}

- (void)updateButtonAppearance {
  UIColor *defaultColor = [UIColor colorWithRed:0.85
                                          green:0.85
                                           blue:0.85
                                          alpha:1.0];
  UIColor *maleColor = KColor_switchLightBlue;
  UIColor *femaleColor = KColor_switchLightPink;

  for (UIView *view in _genderBtn1.subviews) {
    if (view.layer.cornerRadius == 70 * kWidthFactor &&
        view != _genderBtn1.titleLabel) {
      if (genderType == 1) {
        view.backgroundColor = maleColor;
      } else {
        view.backgroundColor = defaultColor;
      }
      break;
    }
  }

  for (UIView *view in _genderBtn2.subviews) {
    if (view.layer.cornerRadius == 70 * kWidthFactor &&
        view != _genderBtn2.titleLabel) {
      if (genderType == 2) {
        view.backgroundColor = femaleColor;
      } else {
        view.backgroundColor = defaultColor;
      }
      break;
    }
  }

  // 确保图标总是可见
  _manImageView.hidden = NO;
  _womanImageView.hidden = NO;

  [_genderBtn1 setNeedsDisplay];
  [_genderBtn2 setNeedsDisplay];
}

- (void)chouse:(FlatButton *)btn {

  if (btn.tag == 001) {
    genderType = 1;

  } else if (btn.tag == 002) {
    genderType = 2;
  }

  [self updateButtonAppearance];
}

- (void)goNext {
  [self saveData];
}

- (void)dismissView {
  [NOTIFICENTER postNotificationName:ChangeMyUserInfoMation object:@(3)];
  [self.navigationController popViewControllerAnimated:YES];
}

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];
  [AppConfig statusbarStyle:YES];
}
@end
