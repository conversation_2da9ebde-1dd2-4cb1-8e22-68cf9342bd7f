#import "AttentionMeVC.h"
#import "AddAttentionModel.h"
#import "AttentionMeModel.h"
#import "MyAttentionTableViewCell.h"
#import "ReleaseAttentionModel.h"
#import "WhaleDetailVC.h"
#import "_2hz-Swift.h"

#define AttentionMeTableViewCellID @"AttentionMeTableViewCellID"

@interface AttentionMeVC () <UITableViewDelegate, UITableViewDataSource> {
  NSInteger currentPage;
}
@property(nonatomic, strong) UITableView *tableView;
@property(nonatomic, assign) NSInteger page;
@property(nonatomic, assign) BOOL isHeader;
@property(nonatomic, strong) NSMutableArray *cellData;
@property(nonatomic, assign) NSInteger lastId;

@end

@implementation AttentionMeVC
- (void)loadData {
  __weak typeof(self) wSelf = self;
  if (!_cellData) {
    _cellData = [NSMutableArray new];
  }

  NSString *lastIdStr =
      self.isHeader ? nil
                    : [NSString stringWithFormat:@"%ld", (long)self.lastId];
  [AttentionMeModel getAttentionMeModel:lastIdStr
      success:^(NSDictionary *resultObject) {
        AttentionMeModel *member =
            [AttentionMeModel mj_objectWithKeyValues:resultObject];
        if ([member.success boolValue]) {
          if (wSelf.isHeader) {
            [wSelf.cellData removeAllObjects];
          }

          if (member.data.count > 0) {
            for (int i = 0; i < member.data.count; i++) {
              AttentionUserResult *userMember = [AttentionUserResult
                  mj_objectWithKeyValues:[member.data objectAtIndex:i]];
              [wSelf.cellData addObject:userMember];
            }
          }

          wSelf.lastId = member.lastId;

          if (member.lastId == 0) {
            [wSelf.tableView.mj_footer endRefreshingWithNoMoreData];
          } else {
            [wSelf.tableView.mj_footer resetNoMoreData];
          }

          [wSelf.tableView reloadData];
          [wSelf.tableView.mj_header endRefreshing];
        }
      }
      failure:^(NSError *requestErr) {
        [wSelf.tableView.mj_header endRefreshing];
        [wSelf.tableView.mj_footer endRefreshing];
      }];
}

- (void)viewDidLoad {
  [super viewDidLoad];

  [self.safeContentView addSubview:self.tableView];
  @weakify(self);
  [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.top.right.left.equalTo(self.safeContentView);
    make.bottom.equalTo(self.view);
  }];
  self.isHeader = YES;
  [self loadData];
}
- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];
  [AppConfig statusbarStyle:YES];
}
- (void)loadMoreData {
  self.isHeader = NO;
  [self loadData];
}

- (void)loadBData {
  [self tableViewloadData];
}

- (void)refreshData {
  self.isHeader = YES;
  [self loadData];
}

- (void)tableViewloadData {
  if (self.tableView) {
    self.isHeader = YES;
    [self loadData];
  }
}

- (UITableView *)tableView {
  if (!_tableView) {
    _tableView = [[UITableView alloc] init];
    _tableView.delegate = self;
    _tableView.dataSource = self;
    _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    [_tableView registerClass:[MyAttentionTableViewCell class]
        forCellReuseIdentifier:AttentionMeTableViewCellID];

    MJRefreshAutoNormalFooter *footer = [MJRefreshAutoNormalFooter
        footerWithRefreshingTarget:self
                  refreshingAction:@selector(loadMoreData)];
    [footer setTitle:@"" forState:MJRefreshStateIdle];
    [footer setTitle:@"" forState:MJRefreshStateNoMoreData];
    self.tableView.mj_footer = footer;
    MJChiBaoZiHeader *header = [MJChiBaoZiHeader
        headerWithRefreshingTarget:self
                  refreshingAction:@selector(tableViewloadData)];
    header.lastUpdatedTimeLabel.hidden = YES;
    header.stateLabel.hidden = YES;
    self.tableView.mj_header = header;
  }
  return _tableView;
}

#pragma mark - Table view data source
- (CGFloat)tableView:(UITableView *)tableView
    heightForFooterInSection:(NSInteger)section {
  return 50.0;
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForHeaderInSection:(NSInteger)section {
  return 0.01f;
}

- (UIView *)tableView:(UITableView *)tableView
    viewForFooterInSection:(NSInteger)section {
  return [[UIView alloc] initWithFrame:CGRectZero];
}

- (UIView *)tableView:(UITableView *)tableView
    viewForHeaderInSection:(NSInteger)section {
  return [[UIView alloc] initWithFrame:CGRectZero];
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForRowAtIndexPath:(NSIndexPath *)indexPath {
  return 72 * kWidthFactor;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
  return 1;
}

- (NSInteger)tableView:(UITableView *)tableView
    numberOfRowsInSection:(NSInteger)section {
  return _cellData.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView
         cellForRowAtIndexPath:(NSIndexPath *)indexPath {
  AttentionUserResult *userMember = [_cellData objectAtIndex:indexPath.row];
  MyAttentionTableViewCell *cell = (MyAttentionTableViewCell *)[tableView
      dequeueReusableCellWithIdentifier:AttentionMeTableViewCellID
                           forIndexPath:indexPath];
  if (!cell) {
    cell = [[MyAttentionTableViewCell alloc]
          initWithStyle:(UITableViewCellStyleDefault)
        reuseIdentifier:AttentionMeTableViewCellID];
  }

  [cell.iconImage
      sd_setImageWithURL:[NemoUtil getUrlWithUserSmallIcon:userMember.avatar]
        placeholderImage:KImage_name(@"empty")];
  cell.nameLabel.text = userMember.nickname;
  cell.signLabel.text = userMember.signature;

  if (userMember.create_time && userMember.create_time.length > 0) {
    NSTimeInterval timeInterval = [userMember.create_time doubleValue];
    cell.timeLabel.text = [NemoUtil distanceTimeWithBeforeTime:timeInterval];
  }

  if ([userMember.type isEqualToString:@"0"]) {
    cell.attentionBtn.hidden = NO;
    [cell.attentionBtn setBackgroundImage:KImage_name(@"添加好友")
                                 forState:UIControlStateNormal];
  } else if ([userMember.type isEqualToString:@"2"]) {
    cell.attentionBtn.hidden = NO;
    [cell.attentionBtn setBackgroundImage:KImage_name(@"互相关注")
                                 forState:UIControlStateNormal];
  } else {
    cell.attentionBtn.hidden = YES;
  }

  cell.attentionBtn.tag = [userMember.userid integerValue];
  [cell.attentionBtn addTarget:self
                        action:@selector(releaseAtt:)
              forControlEvents:UIControlEventTouchUpInside];

  return cell;
}

- (void)releaseAtt:(UIButton *)btn {
  AttentionUserResult *userMember = nil;
  for (AttentionUserResult *user in _cellData) {
    if ([user.userid integerValue] == btn.tag) {
      userMember = user;
      break;
    }
  }

  if (!userMember)
    return;

  @weakify(self);
  if ([userMember.type isEqualToString:@"0"]) {
    [AddAttentionModel
        postAddAttentionModel:userMember.userid
                      success:^(NSDictionary *resultObject) {
                        @strongify(self);
                        AddAttentionModel *member = [AddAttentionModel
                            mj_objectWithKeyValues:resultObject];
                        if ([member.success boolValue]) {
                          [self loadData];
                          [NOTIFICENTER postNotificationName:AttMyToMyAtt
                                                      object:nil];
                        }
                      }
                      failure:^(NSError *requestErr){
                      }];
  } else if ([userMember.type isEqualToString:@"2"]) {
    UIAlertController *alertController = [UIAlertController
        alertControllerWithTitle:@"提示"
                         message:@"确认取消关注？"
                  preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction *cancelAction =
        [UIAlertAction actionWithTitle:@"取消"
                                 style:UIAlertActionStyleCancel
                               handler:nil];
    UIAlertAction *okAction = [UIAlertAction
        actionWithTitle:@"确定"
                  style:UIAlertActionStyleDefault
                handler:^(UIAlertAction *_Nonnull action) {
                  @strongify(self);
                  [ReleaseAttentionModel
                      postReleaseAttentionModel:userMember.userid
                                        success:^(NSDictionary *resultObject) {
                                          ReleaseAttentionModel *member =
                                              [ReleaseAttentionModel
                                                  mj_objectWithKeyValues:
                                                      resultObject];
                                          if ([member.success boolValue]) {
                                            [self loadData];
                                            [NOTIFICENTER
                                                postNotificationName:
                                                    AttMyToMyAtt
                                                              object:nil];
                                          } else {
                                          }
                                        }
                                        failure:^(NSError *requestErr){

                                        }];
                }];
    [alertController addAction:cancelAction];
    [alertController addAction:okAction];
    [self presentViewController:alertController animated:YES completion:nil];
  }
}

- (void)tableView:(UITableView *)tableView
    didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
  [tableView deselectRowAtIndexPath:indexPath animated:YES];
  AttentionUserResult *userMember = [_cellData objectAtIndex:indexPath.row];
  WhaleDetailVC *ddVC = [[WhaleDetailVC alloc] init];
  ddVC.uid = userMember.userid;
  [self.navigationController pushViewController:ddVC animated:YES];
}

- (void)dealloc {
  [NOTIFICENTER removeObserver:self];
}

@end
