#import "BlackUserVC.h"
#import "BlackUserTableViewCell.h"
#import "DoOutBlackModel.h"
#import "GetBlackUserModel.h"

#define BlackUserTableViewCellID @"BlackUserTableViewCellID"
@interface BlackUserVC () <UITableViewDelegate, UITableViewDataSource>
@property(nonatomic, strong) UITableView *tableView;
@property(nonatomic, strong) NSMutableArray *cellData;
@end

@implementation BlackUserVC

- (void)loadData {
  __weak typeof(self) wSelf = self;
  if (!_cellData) {
    _cellData = [NSMutableArray new];
  } else {
    [_cellData removeAllObjects];
  }
  [GetBlackUserModel
      getGetBlackUserModel:^(NSDictionary *resultObject) {
        GetBlackUserModel *member =
            [GetBlackUserModel mj_objectWithKeyValues:resultObject];
        if ([member.success boolValue]) {
          for (int i = 0; i < member.data.count; i++) {
            GetBlackUserModelResult *dy = [GetBlackUserModelResult
                mj_objectWithKeyValues:[member.data objectAtIndex:i]];
            [_cellData addObject:dy];
          }
          [wSelf.tableView reloadData];
        } else {
          [wSelf showToastFast:member.msg];
        }
        [wSelf.tableView.mj_header endRefreshing];
      }
      failure:^(NSError *requestErr) {
        [wSelf.tableView.mj_header endRefreshing];
        [wSelf showToastFast:@"数据有误,请检查网络后重试"];
      }];
}

- (void)outBlackData:(NSString *)uid {
  __weak typeof(self) wSelf = self;
  [DoOutBlackModel postDoOutBlackModel:uid
      success:^(NSDictionary *resultObject) {
        DoOutBlackModel *member =
            [DoOutBlackModel mj_objectWithKeyValues:resultObject];
        if ([member.success boolValue]) {
          [wSelf showToastFast:@"解除成功!"];
          [wSelf loadData];
        } else {
          [wSelf showToastFast:member.msg];
        }
      }
      failure:^(NSError *requestErr) {
        [wSelf showToastFast:@"数据有误,请检查网络后重试"];
      }];
}

- (void)viewDidLoad {
  [super viewDidLoad];
  self.title = @"黑名单";

  _tableView = [[UITableView alloc] initWithFrame:self.view.bounds
                                            style:UITableViewStylePlain];
  _tableView.delegate = self;
  _tableView.dataSource = self;

  if (@available(iOS 11.0, *)) {
    _tableView.contentInsetAdjustmentBehavior =
        UIScrollViewContentInsetAdjustmentNever;
  } else {
    self.automaticallyAdjustsScrollViewInsets = NO;
  }

  [_tableView registerClass:[BlackUserTableViewCell class]
      forCellReuseIdentifier:BlackUserTableViewCellID];
  _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
  [self.view addSubview:_tableView];

  MJChiBaoZiHeader *header =
      [MJChiBaoZiHeader headerWithRefreshingTarget:self
                                  refreshingAction:@selector(loadData)];
  header.lastUpdatedTimeLabel.hidden = YES;
  header.stateLabel.hidden = YES;
  [header beginRefreshing];
  self.tableView.mj_header = header;
}

- (void)viewDidLayoutSubviews {
  [super viewDidLayoutSubviews];

  CGRect frame = self.view.bounds;
  self.tableView.frame = frame;
}

#pragma mark - Table view data source
- (CGFloat)tableView:(UITableView *)tableView
    heightForFooterInSection:(NSInteger)section {
  return 1.0;
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForHeaderInSection:(NSInteger)section {
  return 1.01f;
}

- (UIView *)tableView:(UITableView *)tableView
    viewForFooterInSection:(NSInteger)section {
  return [[UIView alloc] initWithFrame:CGRectZero];
}

- (UIView *)tableView:(UITableView *)tableView
    viewForHeaderInSection:(NSInteger)section {
  return [[UIView alloc] initWithFrame:CGRectZero];
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForRowAtIndexPath:(NSIndexPath *)indexPath {
  return 50 * kMainTemp;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
  return 1;
}

- (NSInteger)tableView:(UITableView *)tableView
    numberOfRowsInSection:(NSInteger)section {
  return _cellData.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView
         cellForRowAtIndexPath:(NSIndexPath *)indexPath {
  GetBlackUserModelResult *dy = [_cellData objectAtIndex:indexPath.row];
  BlackUserTableViewCell *cell = (BlackUserTableViewCell *)[tableView
      dequeueReusableCellWithIdentifier:BlackUserTableViewCellID
                           forIndexPath:indexPath];
  if (!cell) {
    cell = [[BlackUserTableViewCell alloc]
          initWithStyle:(UITableViewCellStyleDefault)
        reuseIdentifier:BlackUserTableViewCellID];
  }
  [cell.iconImageLabel
      sd_setImageWithURL:[NemoUtil getUrlWithUserSmallIcon:dy.avatar]
        placeholderImage:KImage_name(@"empty")];
  cell.nameLabel.text = dy.nickName;
  cell.changeBtn.tag = indexPath.row;
  [cell.changeBtn addTarget:self
                     action:@selector(changeBlack:)
           forControlEvents:UIControlEventTouchUpInside];
  return cell;
}

- (void)changeBlack:(UIButton *)sender {
  GetBlackUserModelResult *dy = [_cellData objectAtIndex:sender.tag];
  UIAlertController *alertController =
      [UIAlertController alertControllerWithTitle:@"提示"
                                          message:@"确定解除拉黑?"
                                   preferredStyle:UIAlertControllerStyleAlert];
  UIAlertAction *cancelAction =
      [UIAlertAction actionWithTitle:@"取消"
                               style:UIAlertActionStyleCancel
                             handler:nil];
  __weak typeof(self) wSelf = self;
  UIAlertAction *okAction =
      [UIAlertAction actionWithTitle:@"确定"
                               style:UIAlertActionStyleDefault
                             handler:^(UIAlertAction *_Nonnull action) {
                               [wSelf outBlackData:dy.userid];
                             }];

  [alertController addAction:cancelAction];
  [alertController addAction:okAction];
  [self presentViewController:alertController animated:YES completion:nil];
}

- (void)tableView:(UITableView *)tableView
    didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
  [tableView deselectRowAtIndexPath:indexPath animated:YES];
}

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];

  [self.navigationController setNavigationBarHidden:NO animated:NO];

  self.showBackBtn = YES;

  [AppConfig statusbarStyle:YES];
}

@end
