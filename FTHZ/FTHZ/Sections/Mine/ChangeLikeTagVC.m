#import "ChangeLikeTagVC.h"
#import "HeziChouseCollectionViewCell.h"
#import "HeziChouseThreeVC.h"
#import "RateCalculateModel.h"
#import "UICollectionViewLeftAlignedLayout.h"
#import <ImageIO/ImageIO.h>

#define HeziChouseTwoVCID @"HeziChouseTwoVCID"

@interface ChangeLikeTagVC () <UICollectionViewDataSource,
                               UICollectionViewDelegate,
                               UICollectionViewDelegateFlowLayout> {
  UICollectionView *mainCollectionView;
  NSMutableArray *tagArray;
  NSMutableArray *disArray;
  NSMutableArray *chouseArray;
  NSMutableArray *ischouseArray;
  NSString *dataattribute;
  NSString *datarate;
}

@property(nonatomic, strong) FlatButton *loginBtn;
@property(nonatomic, strong) UICountingLabel *heziLabel;
@property(nonatomic, strong) UILabel *numLabel;

@end

@implementation ChangeLikeTagVC
- (void)loadArray {
  NSInteger tempLengh = 0;
  for (TagModelResult *cda in chouseArray) {
    if (cda.name.length > 5) {
      tempLengh = tempLengh + 2;
    } else {
      tempLengh = tempLengh + 1;
    }
  }

  if (tempLengh < 18) {
    [ischouseArray removeAllObjects];
    for (TagModelResult *dda in disArray) {
      [ischouseArray addObject:dda];
    }
  }
  if (chouseArray) {
    [chouseArray removeAllObjects];
  }
  chouseArray = [NemoUtil getShowTagArray:tagArray
                                 isChouse:ischouseArray
                                 disArray:disArray];
}

- (void)viewDidLoad {
  [super viewDidLoad];

  MASViewAttribute *bottom = [self setTitleForV199:@""];

  if (!tagArray) {
    tagArray = [NSMutableArray new];
    disArray = [NSMutableArray new];
    chouseArray = [NSMutableArray new];
    ischouseArray = [NSMutableArray new];
  }
  for (TagModelResult *dda in self.needlikedisArray) {
    [disArray addObject:dda];
  }
  dataattribute = @"";
  datarate = @"";
  tagArray = self.tagNextArray;
  [self loadArray];

  UILabel *titleLabel = [[UILabel alloc] init];
  titleLabel.textColor = KColor_Black;
  titleLabel.text = @"想认识的人";
  titleLabel.font = SourceHanSerifSemiBoldFont(20 * kWidthFactor);
  [self.safeContentView addSubview:titleLabel];
  [titleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.view).offset(24 * kWidthFactor);
    make.top.equalTo(self.safeContentView).offset(60 * kWidthFactor);
    make.size.mas_equalTo(CGSizeMake(300 * kWidthFactor, 24 * kWidthFactor));
  }];

  UIView *circleBackgroundView = [[UIView alloc] init];
  circleBackgroundView.backgroundColor = KColor_HighBlack;
  circleBackgroundView.layer.cornerRadius = 40 * kWidthFactor;
  circleBackgroundView.layer.masksToBounds = YES;
  [self.safeContentView addSubview:circleBackgroundView];
  [circleBackgroundView mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.view).offset(24 * kWidthFactor);
    make.top.equalTo(titleLabel.mas_bottom).offset(12 * kWidthFactor);
    make.size.mas_equalTo(CGSizeMake(80 * kWidthFactor, 80 * kWidthFactor));
  }];

  UILabel *phoneLabel = [[UILabel alloc] init];
  phoneLabel.textColor = KColor_detailDarkGray;
  phoneLabel.text = @"选择四项最贴近你的形容";
  phoneLabel.font = SourceHanSerifMediumFont(14 * kWidthFactor);
  [self.safeContentView addSubview:phoneLabel];
  [phoneLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(circleBackgroundView);
    make.top.equalTo(circleBackgroundView.mas_bottom).offset(22 * kWidthFactor);
  }];

  _numLabel = [[UILabel alloc] init];
  _numLabel.textColor = KColor_detailDarkGray;
  _numLabel.text = [NSString stringWithFormat:@"%d/4", (int)disArray.count];
  _numLabel.font = [UIFont fontWithName:@"DINCondensed-Bold"
                                   size:16 * kWidthFactor];
  [self.safeContentView addSubview:_numLabel];
  [_numLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(phoneLabel.mas_right).offset(8 * kWidthFactor);
    make.top.equalTo(phoneLabel.mas_top).offset(4 * kWidthFactor);
    make.size.mas_equalTo(CGSizeMake(24 * kWidthFactor, 16 * kWidthFactor));
  }];

  _heziLabel = [[UICountingLabel alloc] init];
  _heziLabel.method = UILabelCountingMethodEaseOut;
  _heziLabel.format = @"%.2f";
  _heziLabel.textColor = KColor_White;
  _heziLabel.font = [UIFont fontWithName:@"DINCondensed-Bold"
                                    size:24 * kWidthFactor];
  [circleBackgroundView addSubview:_heziLabel];
  [_heziLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.centerX.equalTo(circleBackgroundView).offset(-8 * kWidthFactor);
    make.centerY.equalTo(circleBackgroundView);
    make.size.mas_equalTo(CGSizeMake(42 * kWidthFactor, 24 * kWidthFactor));
  }];
  [_heziLabel countFrom:self.heziNum to:self.heziNum withDuration:1.5];

  UIImageView *hzimage = [[UIImageView alloc] init];
  hzimage.image = [UIImage imageNamed:@"heziicon"];

  NSString *gender = [CurrentUser.gender isEqualToString:@"1"] ? @"1" : @"2";
  UIColor *hzColor = [gender isEqualToString:@"1"] ? KColor_switchLightBlue
                                                   : KColor_switchLightPink;
  hzimage.tintColor = hzColor;

  hzimage.image =
      [hzimage.image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];

  [circleBackgroundView addSubview:hzimage];
  [hzimage mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.heziLabel.mas_right).offset(4 * kWidthFactor);
    make.centerY.equalTo(self.heziLabel);
    make.size.mas_equalTo(CGSizeMake(10 * kWidthFactor, 10 * kWidthFactor));
  }];

  UILabel *reloadIcomLabel = [[UILabel alloc] init];
  reloadIcomLabel.text = @"换一批";
  reloadIcomLabel.textColor = KColor_titleDarkGray;
  reloadIcomLabel.font = SourceHanSerifMediumFont(14 * kWidthFactor);
  [self.safeContentView addSubview:reloadIcomLabel];
  [reloadIcomLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.right.equalTo(self.view).offset(-24 * kWidthFactor);
    make.centerY.equalTo(phoneLabel);
  }];

  UIImageView *reloadIcom = [[UIImageView alloc] init];
  reloadIcom.image = KImage_name(@"ReloadShape");
  [self.safeContentView addSubview:reloadIcom];
  [reloadIcom mas_makeConstraints:^(MASConstraintMaker *make) {
    make.right.equalTo(reloadIcomLabel.mas_left).offset(-6 * kWidthFactor);
    make.centerY.equalTo(reloadIcomLabel);
    make.size.mas_equalTo(CGSizeMake(12 * kWidthFactor, 12 * kWidthFactor));
  }];

  UIButton *reloadButton = [UIButton buttonWithType:UIButtonTypeCustom];
  reloadButton.backgroundColor = [UIColor clearColor];
  [reloadButton addTarget:self
                   action:@selector(changeMost)
         forControlEvents:UIControlEventTouchUpInside];
  [self.safeContentView addSubview:reloadButton];
  [reloadButton mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(reloadIcom);
    make.right.equalTo(reloadIcomLabel);
    make.height.equalTo(@(30 * kWidthFactor));
    make.centerY.equalTo(reloadIcomLabel);
  }];

  FlatButton *changeBtn = [FlatButton buttonWithType:UIButtonTypeSystem];
  [changeBtn addTarget:self
                action:@selector(changeMost)
      forControlEvents:UIControlEventTouchUpInside];
  [self.safeContentView addSubview:changeBtn];
  [changeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
    make.right.equalTo(self.view).offset(-24 * kWidthFactor);
    make.centerY.equalTo(phoneLabel);
    make.size.mas_equalTo(CGSizeMake(60 * kWidthFactor, 16 * kWidthFactor));
  }];

  _loginBtn = [FlatButton buttonWithType:UIButtonTypeSystem];
  _loginBtn.backgroundColor = KColor_HighBlack;
  _loginBtn.layer.masksToBounds = YES;
  _loginBtn.layer.cornerRadius = 30 * kWidthFactor;
  [_loginBtn setTitle:@"确定" forState:UIControlStateNormal];
  [_loginBtn setTitleColor:KColor_White forState:UIControlStateNormal];
  [_loginBtn addTarget:self
                action:@selector(goNext)
      forControlEvents:UIControlEventTouchUpInside];
  [self.safeContentView addSubview:_loginBtn];
  [_loginBtn mas_makeConstraints:^(MASConstraintMaker *make) {
    make.bottom.equalTo(self.safeContentView.mas_bottom)
        .with.offset(-100 * kWidthFactor);
    make.centerX.equalTo(self.safeContentView);
    make.size.mas_equalTo(CGSizeMake(160 * kWidthFactor, 60 * kWidthFactor));
  }];

  [self loadCollectionView];
}
- (void)loadCollectionView {

  UICollectionViewLeftAlignedLayout *leftAlignedLayout =
      [[UICollectionViewLeftAlignedLayout alloc] init];

  if (!mainCollectionView) {
    mainCollectionView = [[UICollectionView alloc]
               initWithFrame:CGRectMake(0, 220 * kWidthFactor, kMainWidth,
                                        326 * kWidthFactor)
        collectionViewLayout:leftAlignedLayout];
  }
  [self.safeContentView addSubview:mainCollectionView];
  mainCollectionView.backgroundColor = [UIColor clearColor];

  [mainCollectionView registerClass:[HeziChouseCollectionViewCell class]
         forCellWithReuseIdentifier:HeziChouseTwoVCID];

  mainCollectionView.delegate = self;
  mainCollectionView.dataSource = self;
}

#pragma mark collectionView代理方法
- (NSInteger)numberOfSectionsInCollectionView:
    (UICollectionView *)collectionView {
  return 1;
}

- (NSInteger)collectionView:(UICollectionView *)collectionView
     numberOfItemsInSection:(NSInteger)section {
  return chouseArray.count;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView
                  cellForItemAtIndexPath:(NSIndexPath *)indexPath {
  HeziChouseCollectionViewCell *cell =
      (HeziChouseCollectionViewCell *)[collectionView
          dequeueReusableCellWithReuseIdentifier:HeziChouseTwoVCID
                                    forIndexPath:indexPath];
  if (indexPath.row < disArray.count) {
    cell.botlabel.backgroundColor = KColor_Black;
    cell.botlabel.textColor = KColor_White;
    cell.botlabel.layer.borderColor = KColor_White.CGColor;
  } else {
    cell.botlabel.backgroundColor = KColor_White;
    cell.botlabel.textColor = KColor_Black;
    cell.botlabel.layer.borderColor = KColor_Black.CGColor;
  }
  TagModelResult *cda = [chouseArray objectAtIndex:indexPath.row];
  cell.botlabel.text = cda.name;
  return cell;
}

- (CGSize)collectionView:(UICollectionView *)collectionView
                    layout:(UICollectionViewLayout *)collectionViewLayout
    sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
  TagModelResult *cda = [chouseArray objectAtIndex:indexPath.row];
  if (cda.name.length < 5) {
    return CGSizeMake(65 * kWidthFactor, 35 * kWidthFactor);
  }
  return CGSizeMake(147 * kWidthFactor, 35 * kWidthFactor);
}

- (UIEdgeInsets)collectionView:(UICollectionView *)collectionView
                        layout:(UICollectionViewLayout *)collectionViewLayout
        insetForSectionAtIndex:(NSInteger)section {
  return UIEdgeInsetsMake(12 * kWidthFactor, 30 * kWidthFactor,
                          12 * kWidthFactor, 30 * kWidthFactor);
}

- (CGFloat)collectionView:(UICollectionView *)collectionView
                                      layout:(UICollectionViewLayout *)
                                                 collectionViewLayout
    minimumInteritemSpacingForSectionAtIndex:(NSInteger)section {
  return 18 * kWidthFactor;
}

- (CGFloat)collectionView:(UICollectionView *)collectionView
                                 layout:(UICollectionViewLayout *)
                                            collectionViewLayout
    minimumLineSpacingForSectionAtIndex:(NSInteger)section {
  return 18 * kWidthFactor;
}

- (void)collectionView:(UICollectionView *)collectionView
    didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
  __weak typeof(self) wSelf = self;
  TagModelResult *cda = [chouseArray objectAtIndex:indexPath.row];
  HeziChouseCollectionViewCell *cell =
      (HeziChouseCollectionViewCell *)[collectionView
          cellForItemAtIndexPath:indexPath];
  if ([NemoUtil compareRGBAColor1:cell.botlabel.backgroundColor
                       withColor2:KColor_Black]) {

    [RateCalculateModel postRateCalculateModel:@"0"
        tagArrStr:[NemoUtil arrayChangeString:self.tagidStr]
        likeTagArrStr:@""
        attribute:dataattribute
        rate:datarate
        success:^(NSDictionary *resultObject) {
          RateCalculateModel *member =
              [RateCalculateModel mj_objectWithKeyValues:resultObject];
          if ([member.success boolValue]) {
            RateCalculateModelResult *useMember = [RateCalculateModelResult
                mj_objectWithKeyValues:member.data.firstObject];
            self->dataattribute = useMember.attribute;
            self->datarate = useMember.rate;

            [wSelf.heziLabel countFrom:self.heziNum
                                    to:[useMember.rate doubleValue]
                          withDuration:1.5];
            wSelf.heziNum = [useMember.rate doubleValue];

            [self->disArray removeObject:cda];
            [self->ischouseArray removeObject:cda];
            [self->chouseArray addObject:cda];
            cell.botlabel.backgroundColor = KColor_White;
            cell.botlabel.textColor = KColor_Black;
            cell.botlabel.layer.borderColor = KColor_Black.CGColor;
            [self changeBiaoqian];
            self->_numLabel.text =
                [NSString stringWithFormat:@"%ld/4", disArray.count];
            [self loadGoNextBtn];
          } else {
            [self->disArray addObject:cda];
          }
        }
        failure:^(NSError *requestErr) {
          [self->disArray addObject:cda];
        }];

  } else {
    if (disArray.count >= 4) {

    } else {

      [RateCalculateModel postRateCalculateModel:@"0"
          tagArrStr:[NemoUtil arrayChangeString:self.tagidStr]
          likeTagArrStr:@""
          attribute:dataattribute
          rate:datarate
          success:^(NSDictionary *resultObject) {
            RateCalculateModel *member =
                [RateCalculateModel mj_objectWithKeyValues:resultObject];
            if ([member.success boolValue]) {
              RateCalculateModelResult *useMember = [RateCalculateModelResult
                  mj_objectWithKeyValues:member.data.firstObject];
              self->dataattribute = useMember.attribute;
              self->datarate = useMember.rate;
              [wSelf.heziLabel countFrom:self.heziNum
                                      to:[useMember.rate doubleValue]
                            withDuration:1.5];
              wSelf.heziNum = [useMember.rate doubleValue];

              [self->disArray addObject:cda];
              [self->ischouseArray addObject:cda];
              [self->chouseArray removeObject:cda];
              cell.botlabel.backgroundColor = KColor_Black;
              cell.botlabel.textColor = KColor_White;
              cell.botlabel.layer.borderColor = KColor_White.CGColor;
              [self changeBiaoqian];
              _numLabel.text =
                  [NSString stringWithFormat:@"%ld/4", self->disArray.count];
              [self loadGoNextBtn];
            } else {
              [self->disArray removeObject:cda];
            }
          }
          failure:^(NSError *requestErr) {
            [self->disArray removeObject:cda];
          }];
    }
  }
}

- (void)loadGoNextBtn {
  if (disArray.count == 4) {
    _loginBtn.backgroundColor = KColor_Black;
  } else {
    _loginBtn.backgroundColor = KColor_codeGray;
  }
}

- (void)didReceiveMemoryWarning {
  [super didReceiveMemoryWarning];
}

- (void)goNext {
  __weak typeof(self) wSelf = self;
  if (disArray.count == 4) {
    [RateCalculateModel postRateCalculateModel:@"1"
        tagArrStr:[NemoUtil arrayChangeString:self.tagidStr]
        likeTagArrStr:[NemoUtil arrayChangeString:disArray]
        attribute:dataattribute
        rate:datarate
        success:^(NSDictionary *resultObject) {
          RateCalculateModel *member =
              [RateCalculateModel mj_objectWithKeyValues:resultObject];
          if ([member.success boolValue]) {
            [wSelf getUserHeziInfo];
          } else {
            [wSelf showToastFast:member.msg];
          }
        }
        failure:^(NSError *requestErr) {
          [wSelf showToastFast:@"数据有误,请检查网络后重试"];
        }];
  } else {
    [self showToast:@"请选择4个标签" time:0.5];
  }
}

- (void)getUserHeziInfo {
  @weakify(self);
  [AccountManager syncUserInfoWithCompletion:^(UserPersonResult *_Nullable user,
                                               NSError *_Nullable error) {
    @strongify(self);
    [HUD dissmiss];
    if (error) {
      [self showToastFast:error.localizedDescription];
      return;
    }
    [self dismissView];
  }];
}

- (void)dismissView {
  [NOTIFICENTER postNotificationName:ChangeMyUserInfoMation object:@(6)];
  [self.navigationController popViewControllerAnimated:YES];
}

- (void)changeMost {
  for (TagModelResult *tag in chouseArray) {
    [ischouseArray addObject:tag];
  }
  [self changeBiaoqian];
}

- (void)changeBiaoqian {
  [mainCollectionView removeFromSuperview];
  mainCollectionView = nil;
  [self loadCollectionView];
  [self loadArray];
}

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];

  [self setShowBackBtn:YES];
}

@end
