#import "ReportVC.h"
#import "AppConfig.h"
#import "ReportModel.h"

@interface ReportVC () <UITableViewDelegate, UITableViewDataSource,
                        UITextViewDelegate> {
  NSMutableArray *reportTypes;
  NSInteger selectedType;
}

@property(nonatomic, strong) UITableView *tableView;
@property(nonatomic, strong) UITextView *reportTextView;
@property(nonatomic, strong) UIButton *submitButton;
@property(nonatomic, strong) UILabel *characterCountLabel;
- (UIImage *)circleImageWithDiameter:(CGFloat)diameter
                              filled:(BOOL)filled
                               color:(UIColor *)color;

@end

@implementation ReportVC

- (UIImage *)circleImageWithDiameter:(CGFloat)diameter
                              filled:(BOOL)filled
                               color:(UIColor *)color {
  CGRect rect = CGRectMake(0, 0, diameter, diameter);
  UIGraphicsBeginImageContextWithOptions(rect.size, NO, 0);
  CGContextRef ctx = UIGraphicsGetCurrentContext();
  if (filled) {
    [color setFill];
    CGContextFillEllipseInRect(ctx, rect);
  } else {
    [color setStroke];
    CGContextSetLineWidth(ctx, 1.0);
    CGContextStrokeEllipseInRect(ctx, CGRectInset(rect, 1, 1));
  }
  UIImage *img = UIGraphicsGetImageFromCurrentImageContext();
  UIGraphicsEndImageContext();
  return img;
}

- (void)viewDidLoad {
  [super viewDidLoad];
  self.title = @"举报";

  reportTypes = [NSMutableArray arrayWithArray:@[
    @"举报类型:", @"对话令我反感", @"色情骚扰", @"垃圾广告", @"不友善/恶意辱骂",
    @"违法有害", @"提供色情服务"
  ]];

  selectedType = -1;

  @weakify(self);
  UIButton *button = [[UIButton alloc] initWithFrame:CGRectMake(0, 0, 10, 15)];
  [button setImage:KImage_name(@"back") forState:UIControlStateNormal];
  [button setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
  [button addTarget:self
                action:@selector(back)
      forControlEvents:UIControlEventTouchUpInside];
  [self.safeContentView addSubview:button];
  [button mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.equalTo(self.safeContentView).offset(24 * kWidthFactor);
    make.top.equalTo(self.safeContentView).offset(24 * kWidthFactor);
  }];

  UILabel *titleL = UILabel.new;
  titleL.text = @"举报";
  titleL.textColor = KColor_HighBlack;
  titleL.font = SourceHanSerifSemiBoldFont(16 * kWidthFactor);
  [self.safeContentView addSubview:titleL];
  [titleL mas_makeConstraints:^(MASConstraintMaker *make) {
    make.centerY.equalTo(button);
    make.centerX.equalTo(self.safeContentView);
  }];

  _tableView = [[UITableView alloc] init];
  _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
  _tableView.dataSource = self;
  _tableView.delegate = self;
  if (@available(iOS 15.0, *)) {
    _tableView.sectionHeaderTopPadding = 0;
  }
  [self.safeContentView addSubview:_tableView];
  [_tableView mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.right.equalTo(self.safeContentView);
    make.top.equalTo(button.mas_bottom);
    make.bottom.equalTo(self.safeContentView).offset(-100 * kMainTemp);
  }];

  _submitButton = [UIButton buttonWithType:UIButtonTypeCustom];
  [_submitButton setTitle:@"提交举报" forState:UIControlStateNormal];
  [_submitButton setTitleColor:[UIColor whiteColor]
                      forState:UIControlStateNormal];
  _submitButton.backgroundColor = KColor_HighBlack;
  _submitButton.titleLabel.font = SourceHanSerifMediumFont(16 * kMainTemp);
  _submitButton.layer.cornerRadius = 30 * kMainTemp;
  _submitButton.layer.masksToBounds = YES;
  [_submitButton addTarget:self
                    action:@selector(submitReport)
          forControlEvents:UIControlEventTouchUpInside];
  [self.view addSubview:_submitButton];
  [_submitButton mas_makeConstraints:^(MASConstraintMaker *make) {
    make.centerX.equalTo(self.view);
    make.size.mas_equalTo(CGSizeMake(160 * kMainTemp, 60 * kMainTemp));
    make.bottom.equalTo(self.view.mas_safeAreaLayoutGuideBottom)
        .offset(-50 * kMainTemp);
  }];

  [[NSNotificationCenter defaultCenter]
      addObserver:self
         selector:@selector(keyboardWillShow:)
             name:UIKeyboardWillShowNotification
           object:nil];
  [[NSNotificationCenter defaultCenter]
      addObserver:self
         selector:@selector(keyboardWillHide:)
             name:UIKeyboardWillHideNotification
           object:nil];
}

- (void)dealloc {
  [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark - Keyboard Handling

- (void)keyboardWillShow:(NSNotification *)notification {
  CGRect keyboardFrame =
      [notification.userInfo[UIKeyboardFrameEndUserInfoKey] CGRectValue];
  CGFloat keyboardHeight = keyboardFrame.size.height;

  [UIView animateWithDuration:0.3
                   animations:^{
                     self.tableView.contentInset =
                         UIEdgeInsetsMake(0, 0, keyboardHeight, 0);
                     self.tableView.scrollIndicatorInsets =
                         UIEdgeInsetsMake(0, 0, keyboardHeight, 0);
                   }];

  NSIndexPath *indexPath = [NSIndexPath indexPathForRow:0 inSection:1];
  [self.tableView scrollToRowAtIndexPath:indexPath
                        atScrollPosition:UITableViewScrollPositionBottom
                                animated:YES];
}

- (void)keyboardWillHide:(NSNotification *)notification {
  [UIView animateWithDuration:0.3
                   animations:^{
                     self.tableView.contentInset = UIEdgeInsetsZero;
                     self.tableView.scrollIndicatorInsets = UIEdgeInsetsZero;
                   }];
}

#pragma mark - UITableViewDataSource

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
  return 2;
}

- (NSInteger)tableView:(UITableView *)tableView
    numberOfRowsInSection:(NSInteger)section {
  if (section == 0) {
    return reportTypes.count;
  }
  return 1;
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForFooterInSection:(NSInteger)section {
  return 0.0f;
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForHeaderInSection:(NSInteger)section {
  if (section == 1) {
    return 40.0f;
  }
  return 0.0f;
}

- (UIView *)tableView:(UITableView *)tableView
    viewForFooterInSection:(NSInteger)section {
  return [[UIView alloc] initWithFrame:CGRectZero];
}

- (UIView *)tableView:(UITableView *)tableView
    viewForHeaderInSection:(NSInteger)section {
  if (section == 1) {
    UIView *headerView = [[UIView alloc]
        initWithFrame:CGRectMake(0, 0, tableView.frame.size.width, 40)];
    UILabel *label = [[UILabel alloc]
        initWithFrame:CGRectMake(16, 0, tableView.frame.size.width - 32, 40)];
    label.font = SourceHanSerifSemiBoldFont(14 * kWidthFactor);
    label.textColor = KColor_HighBlack;
    label.text = @"举报详情(选填):";
    [headerView addSubview:label];
    return headerView;
  }
  return [[UIView alloc] initWithFrame:CGRectZero];
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForRowAtIndexPath:(NSIndexPath *)indexPath {
  if (indexPath.section == 0 && indexPath.row == 0) {
    return 40.0;
  } else if (indexPath.section == 0) {
    return 48.0;
  } else {
    return 200.0;
  }
}

- (UITableViewCell *)tableView:(UITableView *)tableView
         cellForRowAtIndexPath:(NSIndexPath *)indexPath {
  if (indexPath.section == 0 && indexPath.row == 0) {
    UITableViewCell *cell =
        [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault
                               reuseIdentifier:@"TitleCell"];
    cell.textLabel.text = @"举报类型:";
    cell.textLabel.font = SourceHanSerifSemiBoldFont(14 * kWidthFactor);
    cell.textLabel.textColor = KColor_HighBlack;
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    cell.accessoryView = nil;
    return cell;
  } else if (indexPath.section == 0) {
    UITableViewCell *cell =
        [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault
                               reuseIdentifier:nil];
    cell.textLabel.text = reportTypes[indexPath.row];
    cell.textLabel.textColor = KColor_Black;
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    cell.accessoryType = UITableViewCellAccessoryNone;
    cell.accessoryView = nil;

    if (indexPath.row > 0) {
      CGFloat circleSize = 14;
      BOOL isSelected = (selectedType == indexPath.row);
      cell.imageView.image = [self circleImageWithDiameter:circleSize
                                                    filled:isSelected
                                                     color:KColor_HighBlack];
    } else {
      cell.imageView.image = nil;
    }

    return cell;
  } else {
    UITableViewCell *cell =
        [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault
                               reuseIdentifier:@"InputCell"];

    if (!self.reportTextView) {
      self.reportTextView = [[UITextView alloc] init];
      self.reportTextView.layer.cornerRadius = 10 * kWidthFactor;
      self.reportTextView.layer.masksToBounds = YES;
      self.reportTextView.font = SourceHanSerifRegularFont(14 * kWidthFactor);
      self.reportTextView.textColor = KColor_HighBlack;
      self.reportTextView.backgroundColor = [UIColor colorWithWhite:0.95
                                                              alpha:1.0];
      self.reportTextView.delegate = self;
      self.reportTextView.textContainerInset =
          UIEdgeInsetsMake(12 * kWidthFactor, 12 * kWidthFactor,
                           12 * kWidthFactor, 12 * kWidthFactor);
    }

    [cell.contentView addSubview:self.reportTextView];
    [self.reportTextView mas_makeConstraints:^(MASConstraintMaker *make) {
      make.edges.equalTo(cell.contentView)
          .insets(UIEdgeInsetsMake(8, 16, 8, 16));
    }];

    UIView *countContainer = [[UIView alloc] init];
    countContainer.backgroundColor = [UIColor clearColor];
    countContainer.userInteractionEnabled = NO;
    [cell.contentView addSubview:countContainer];

    [countContainer mas_makeConstraints:^(MASConstraintMaker *make) {
      make.right.equalTo(self.reportTextView).offset(-12 * kWidthFactor);
      make.bottom.equalTo(self.reportTextView).offset(-12 * kWidthFactor);
      make.width.mas_equalTo(70 * kWidthFactor);
      make.height.mas_equalTo(20 * kWidthFactor);
    }];

    _characterCountLabel = [UILabel new];
    _characterCountLabel.text = @"0/200";
    _characterCountLabel.font = SourceHanSerifRegularFont(12 * kWidthFactor);
    _characterCountLabel.textColor =
        [KColor_HighBlack colorWithAlphaComponent:0.5];
    _characterCountLabel.textAlignment = NSTextAlignmentRight;
    [countContainer addSubview:_characterCountLabel];

    [_characterCountLabel mas_makeConstraints:^(MASConstraintMaker *make) {
      make.edges.equalTo(countContainer);
    }];

    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    return cell;
  }
}

- (void)tableView:(UITableView *)tableView
    didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
  if (indexPath.section == 0 && indexPath.row > 0) {
    selectedType = indexPath.row;
    [tableView reloadSections:[NSIndexSet indexSetWithIndex:0]
             withRowAnimation:UITableViewRowAnimationNone];
  }
}

- (void)textViewDidChange:(UITextView *)textView {
  NSInteger characterCount = textView.text.length;
  _characterCountLabel.text =
      [NSString stringWithFormat:@"%ld/200", (long)characterCount];

  if (characterCount > 200) {
    _characterCountLabel.textColor = [UIColor redColor];
  } else {
    _characterCountLabel.textColor =
        [KColor_HighBlack colorWithAlphaComponent:0.5];
  }
}

- (BOOL)textView:(UITextView *)textView
    shouldChangeTextInRange:(NSRange)range
            replacementText:(NSString *)text {
  if ([text isEqualToString:@""]) {
    return YES;
  }

  NSString *newText = [textView.text stringByReplacingCharactersInRange:range
                                                             withString:text];

  if (newText.length > 200) {
    [self.view makeToast:@"已达字数上限"
                duration:1.0
                position:CSToastPositionCenter];
    return NO;
  }

  return YES;
}

- (void)submitReport {
  if (selectedType == -1) {
    [self.view makeToast:@"请选择举报类型"
                duration:1.0
                position:CSToastPositionCenter];
    return;
  }

  if (self.reportTextView.text.length > 200) {
    [self.view makeToast:@"举报内容超出字数限制"
                duration:1.0
                position:CSToastPositionCenter];
    return;
  }

  UIAlertController *alertController =
      [UIAlertController alertControllerWithTitle:@"提示"
                                          message:@"确定举报该用户?"
                                   preferredStyle:UIAlertControllerStyleAlert];

  UIAlertAction *cancelAction =
      [UIAlertAction actionWithTitle:@"取消"
                               style:UIAlertActionStyleCancel
                             handler:nil];

  UIAlertAction *okAction = [UIAlertAction
      actionWithTitle:@"确定"
                style:UIAlertActionStyleDefault
              handler:^(UIAlertAction *_Nonnull action) {
                if ([self.delegate respondsToSelector:@selector
                                   (delegateGetReport:detail:)]) {
                  [self.delegate delegateGetReport:self->selectedType
                                            detail:self.reportTextView.text];
                  [self.navigationController popViewControllerAnimated:YES];
                }
              }];

  [alertController addAction:cancelAction];
  [alertController addAction:okAction];
  [self presentViewController:alertController animated:YES completion:nil];
}

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];
  [AppConfig statusbarStyle:YES];
}

- (void)back {
  [self.navigationController popViewControllerAnimated:YES];
}

@end