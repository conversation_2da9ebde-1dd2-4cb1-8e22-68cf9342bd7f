#import "ShudongDetailVC.h"
#import "_2hz-Swift.h"
#define ShudongDetailVCID @"ShudongDetailVCID"
#import "CreateShudongVC.h"
#import "ShudongCardCell.h"
#import "ShudongDetailView.h"
#import "ShudongModel.h"

@interface ShudongCustomLayout : UICollectionViewLayout

@property(nonatomic, assign) CGFloat sideInset;
@property(nonatomic, assign) CGFloat interItemSpacing;
@property(nonatomic, assign) CGFloat verticalSpacing;
@property(nonatomic, strong) NSMutableArray *cachedAttributes;
@property(nonatomic, assign) CGFloat contentHeight;

@end

@implementation ShudongCustomLayout

- (void)prepareLayout {
  [super prepareLayout];

  self.cachedAttributes = [NSMutableArray array];
  self.contentHeight = 0;

  NSInteger itemCount = [self.collectionView numberOfItemsInSection:0];
  if (itemCount == 0)
    return;

  CGFloat screenWidth = self.collectionView.bounds.size.width;
  CGFloat cardWidth = floor(
      (screenWidth - (2 * self.sideInset) - (2 * self.interItemSpacing)) / 3.0);

  NSMutableArray *columnHeights = [@[ @(0), @(0), @(0) ] mutableCopy];

  for (NSInteger idx = 0; idx < itemCount; idx++) {
    NSIndexPath *indexPath = [NSIndexPath indexPathForItem:idx inSection:0];
    CGSize itemSize =
        [(id<UICollectionViewDelegateFlowLayout>)self.collectionView.delegate
                    collectionView:self.collectionView
                            layout:self
            sizeForItemAtIndexPath:indexPath];

    CGFloat xOffset;
    CGFloat yOffset;

    if (idx == 0) {
      xOffset = 0;
      yOffset = 0;
      for (NSInteger i = 0; i < 3; i++) {
        columnHeights[i] = @(itemSize.height + self.verticalSpacing);
      }
    } else if (idx == itemCount - 1) {
      xOffset = 0;
      yOffset = MAX(
          [columnHeights[0] floatValue],
          MAX([columnHeights[1] floatValue], [columnHeights[2] floatValue]));
      for (NSInteger i = 0; i < 3; i++) {
        columnHeights[i] = @(yOffset + itemSize.height);
      }
    } else if (idx < 4) {
      NSInteger columnIndex = (idx - 1) % 3;
      xOffset =
          self.sideInset + columnIndex * (cardWidth + self.interItemSpacing);
      yOffset = [columnHeights[columnIndex] floatValue];
      columnHeights[columnIndex] =
          @(yOffset + itemSize.height + self.verticalSpacing);
    } else if (idx == itemCount - 2 && (itemCount - 2) % 3 == 1) {
      NSInteger shortestColumn = 0;
      CGFloat minHeight = [columnHeights[0] floatValue];
      BOOL allEqual = YES;

      for (NSInteger i = 1; i < 3; i++) {
        CGFloat height = [columnHeights[i] floatValue];
        if (fabs(height - minHeight) > 0.01) {
          allEqual = NO;
        }
        if (height < minHeight) {
          minHeight = height;
          shortestColumn = i;
        }
      }

      shortestColumn = allEqual ? 0 : shortestColumn;

      xOffset =
          self.sideInset + shortestColumn * (cardWidth + self.interItemSpacing);
      yOffset = [columnHeights[shortestColumn] floatValue];
      columnHeights[shortestColumn] =
          @(yOffset + itemSize.height + self.verticalSpacing);
    } else {
      NSInteger shortestColumn = 0;
      CGFloat minHeight = [columnHeights[0] floatValue];
      for (NSInteger i = 1; i < 3; i++) {
        CGFloat height = [columnHeights[i] floatValue];
        if (height < minHeight) {
          minHeight = height;
          shortestColumn = i;
        }
      }

      xOffset =
          self.sideInset + shortestColumn * (cardWidth + self.interItemSpacing);
      yOffset = [columnHeights[shortestColumn] floatValue];
      columnHeights[shortestColumn] =
          @(yOffset + itemSize.height + self.verticalSpacing);
    }

    UICollectionViewLayoutAttributes *attributes =
        [UICollectionViewLayoutAttributes
            layoutAttributesForCellWithIndexPath:indexPath];
    if (idx == 0 || idx == itemCount - 1) {
      attributes.frame = CGRectMake(0, yOffset, screenWidth, itemSize.height);
    } else {
      attributes.frame =
          CGRectMake(xOffset, yOffset, cardWidth, itemSize.height);
    }
    [self.cachedAttributes addObject:attributes];

    self.contentHeight = MAX(self.contentHeight, yOffset + itemSize.height);
  }
}

- (CGSize)collectionViewContentSize {
  return CGSizeMake(self.collectionView.bounds.size.width,
                    self.contentHeight + self.sideInset);
}

- (NSArray<UICollectionViewLayoutAttributes *> *)
    layoutAttributesForElementsInRect:(CGRect)rect {
  NSMutableArray *attributes = [NSMutableArray array];

  for (UICollectionViewLayoutAttributes *attr in self.cachedAttributes) {
    if (CGRectIntersectsRect(rect, attr.frame)) {
      [attributes addObject:attr];
    }
  }

  return attributes;
}

- (UICollectionViewLayoutAttributes *)layoutAttributesForItemAtIndexPath:
    (NSIndexPath *)indexPath {
  if (indexPath.item < self.cachedAttributes.count) {
    return self.cachedAttributes[indexPath.item];
  }
  return nil;
}

@end

CGPoint ShudongPostPosition = (CGPoint){0, 0};

@interface ShudongDetailVC () {
  NSInteger currentPage;
}

@property(nonatomic, strong) UICollectionView *collectionView;
@property(nonatomic, strong) UIView *navHeaderView;
@property(nonatomic, strong) NSMutableArray *cellData;
@property(nonatomic, strong) UIView *detailView;
@property(nonatomic, strong) UIView *emptyView;
@property(nonatomic, assign) BOOL isFirstLoad;

#define FZSCREEN_W [UIScreen mainScreen].bounds.size.width
#define FZSCREEN_H                                                             \
  ([UIScreen mainScreen].bounds.size.height == 812                             \
       ? [UIScreen mainScreen].bounds.size.height - 64.0                       \
       : [UIScreen mainScreen].bounds.size.height)

@end

@implementation ShudongDetailVC

- (void)loadData {
  if (!_cellData) {
    _cellData = [NSMutableArray new];
  }

  @weakify(self);
  [ShudongNotificationModel
      getShudongNotificationModel:[NSString
                                      stringWithFormat:@"%ld", currentPage]
      size:@"50"
      success:^(NSDictionary *resultObject) {
        @strongify(self);

        if (self->currentPage == 1) {
          [self.cellData removeAllObjects];
        }

        ShudongNotificationModel *memeber =
            [ShudongNotificationModel mj_objectWithKeyValues:resultObject];

        if ([memeber.success boolValue]) {
          ShudongPageResult *tempMember = [ShudongPageResult
              mj_objectWithKeyValues:memeber.data.firstObject];

          for (int i = 0; i < tempMember.data.count; i++) {
            ShudongModel *dy = [ShudongModel
                mj_objectWithKeyValues:[tempMember.data objectAtIndex:i]];
            [self.cellData addObject:dy];
          }

          self.isFirstLoad = NO;

          NSInteger originCount = CurrentUserConfig.unreadMessageCount;
          CurrentUserConfig.unreadMessageCount =
              [tempMember.count integerValue];

          if (self.delegate) {
            [self.delegate
                unreadMessage:1
                       number:[tempMember.count integerValue] - originCount];
          }

          if ([tempMember.count integerValue] > self.cellData.count) {
            if (!self.collectionView.mj_footer) {
              MJChiBaoZiFooter *footer = [MJChiBaoZiFooter
                  footerWithRefreshingTarget:self
                            refreshingAction:@selector(loadMoreData)];
              footer.refreshingTitleHidden = YES;
              self.collectionView.mj_footer = footer;
            }
          } else {
            [self.collectionView.mj_footer removeFromSuperview];
            self.collectionView.mj_footer = nil;
          }
        }

        [self.collectionView.mj_header endRefreshing];
        [self.collectionView.mj_footer endRefreshing];
        [self.collectionView reloadData];
      }
      failure:^(NSError *requestErr) {
        [self.collectionView.mj_header endRefreshing];
        [self.collectionView.mj_footer endRefreshing];
        [self showToastFast:@"数据有误,请检查网络后重试"];
      }];
}

- (void)viewDidLoad {
  [super viewDidLoad];
  currentPage = 1;
  self.isFirstLoad = YES;
  self.collectionView.backgroundColor = [UIColor whiteColor];

  self.view.backgroundColor = KColor_White;
  [self.collectionView registerClass:[UICollectionViewCell class]
          forCellWithReuseIdentifier:@"PublishCardCellID"];
  [self.collectionView registerClass:[UICollectionViewCell class]
          forCellWithReuseIdentifier:@"HeaderCellID"];

  [self.safeContentView addSubview:self.collectionView];
  [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.left.right.bottom.equalTo(self.view);
  }];

  if (self.mainTempScale == 0) {
    self.mainTempScale = kMainTemp;
  }

  CGFloat headerHeight = [self isNotchScreen] ? (108 * self.mainTempScale)
                                              : (84 * self.mainTempScale);

  MJChiBaoZiHeader *header =
      [MJChiBaoZiHeader headerWithRefreshingTarget:self
                                  refreshingAction:@selector(loadMessageData)];
  header.lastUpdatedTimeLabel.hidden = YES;
  header.stateLabel.hidden = YES;
  header.ignoredScrollViewContentInsetTop = -headerHeight;
  [header beginRefreshing];
  self.collectionView.mj_header = header;

  [NOTIFICENTER addObserver:self
                   selector:@selector(loadnewMessage)
                       name:NewMessageOfisLoading
                     object:nil];

  [[NSNotificationCenter defaultCenter] addObserver:self
                                           selector:@selector(loadMessageData)
                                               name:@"NeedRefreshShudongList"
                                             object:nil];
}

- (CGFloat)calculateCardHeightForContent:(NSString *)content
                                   width:(CGFloat)cardWidth {
  UIFont *contentFont = SourceHanSerifRegularFont(12 * kWidthFactor);
  NSMutableParagraphStyle *paragraphStyle =
      [[NSMutableParagraphStyle alloc] init];
  paragraphStyle.lineSpacing = 3 * kWidthFactor;

  CGFloat contentWidth = cardWidth - (20 * kWidthFactor);
  CGRect contentRect =
      [content boundingRectWithSize:CGSizeMake(contentWidth, CGFLOAT_MAX)
                            options:NSStringDrawingUsesLineFragmentOrigin |
                                    NSStringDrawingUsesFontLeading
                         attributes:@{
                           NSFontAttributeName : contentFont,
                           NSParagraphStyleAttributeName : paragraphStyle
                         }
                            context:nil];

  CGFloat contentHeight = ceil(contentRect.size.height);

  if (contentHeight <= 36 * kWidthFactor) {
    return cardWidth * 1;
  } else if (contentHeight <= 72 * kWidthFactor) {
    return cardWidth * 1.3;
  } else {
    return cardWidth * 1.5;
  }
}

- (void)scrollViewWillEndDragging:(UIScrollView *)scrollView
                     withVelocity:(CGPoint)velocity
              targetContentOffset:(inout CGPoint *)targetContentOffset {
}

- (void)loadnewMessage {
  if (_collectionView) {
    [self loadData];
  }
}

- (void)loadMessageData {
  currentPage = 1;
  [self loadData];
}

- (void)loadMoreData {
  currentPage += 1;
  [self loadData];
}

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];
  self.showBackBtn = YES;
  [AppConfig statusbarStyle:YES];
}

#pragma mark - UICollectionView

- (UICollectionView *)collectionView {
  if (!_collectionView) {
    ShudongCustomLayout *layout = [[ShudongCustomLayout alloc] init];
    layout.sideInset = 10.0 * kWidthFactor;
    layout.interItemSpacing = 8.0 * kWidthFactor;
    layout.verticalSpacing = 10.0 * kWidthFactor;

    _collectionView = [[UICollectionView alloc] initWithFrame:CGRectZero
                                         collectionViewLayout:layout];
    _collectionView.delegate = self;
    _collectionView.dataSource = self;
    [_collectionView registerClass:[ShudongCardCell class]
        forCellWithReuseIdentifier:ShudongDetailVCID];
    _collectionView.backgroundColor = KColor_White;

    if (@available(iOS 11.0, *)) {
      _collectionView.contentInsetAdjustmentBehavior =
          UIScrollViewContentInsetAdjustmentNever;
    }
  }
  return _collectionView;
}

#pragma mark - UICollectionViewDataSource & Delegate

- (NSInteger)numberOfSectionsInCollectionView:
    (UICollectionView *)collectionView {
  return 1;
}

- (NSInteger)collectionView:(UICollectionView *)collectionView
     numberOfItemsInSection:(NSInteger)section {
  return _cellData.count + 2;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView
                  cellForItemAtIndexPath:(NSIndexPath *)indexPath {
  if (indexPath.item == 0) {
    UICollectionViewCell *cell =
        [collectionView dequeueReusableCellWithReuseIdentifier:@"HeaderCellID"
                                                  forIndexPath:indexPath];
    cell.backgroundColor = [UIColor clearColor];
    return cell;
  } else if (indexPath.item == _cellData.count + 1) {
    UICollectionViewCell *cell = [collectionView
        dequeueReusableCellWithReuseIdentifier:@"PublishCardCellID"
                                  forIndexPath:indexPath];
    for (UIView *subview in cell.contentView.subviews) {
      [subview removeFromSuperview];
    }
    cell.backgroundColor = [UIColor clearColor];
    return cell;
  } else {
    ShudongCardCell *cell =
        [collectionView dequeueReusableCellWithReuseIdentifier:ShudongDetailVCID
                                                  forIndexPath:indexPath];

    ShudongModel *dy = [_cellData objectAtIndex:indexPath.item - 1];
    cell.messageData = dy;

    return cell;
  }
}

- (CGSize)collectionView:(UICollectionView *)collectionView
                    layout:(UICollectionViewLayout *)collectionViewLayout
    sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
  CGFloat sideInset = 10.0 * kWidthFactor;
  CGFloat interItemSpacing = 8.0 * kWidthFactor;
  CGFloat screenWidth = FZSCREEN_W;
  CGFloat cardWidth =
      floor((screenWidth - (2 * sideInset) - (2 * interItemSpacing)) / 3.0);

  if (indexPath.item == 0) {
    CGFloat headerHeight = [self isNotchScreen] ? (108 * self.mainTempScale)
                                                : (84 * self.mainTempScale);
    return CGSizeMake(screenWidth, headerHeight);
  } else if (indexPath.item == _cellData.count + 1) {
    return CGSizeMake(screenWidth, 64 * kWidthFactor);
  } else {
    ShudongModel *dy = [self.cellData objectAtIndex:indexPath.item - 1];
    CGFloat cardHeight = [self calculateCardHeightForContent:dy.message
                                                       width:cardWidth];
    return CGSizeMake(cardWidth, cardHeight);
  }
}

- (void)collectionView:(UICollectionView *)collectionView
    didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
  if (indexPath.item > 0 && indexPath.item < _cellData.count + 1) {
    ShudongModel *dy = [_cellData objectAtIndex:indexPath.item - 1];

    ShudongDetailView *detailVC = [[ShudongDetailView alloc] init];
    detailVC.message = dy.message;
    detailVC.userid = dy.userid;
    detailVC.sid = dy.sid;
    detailVC.modalPresentationStyle = UIModalPresentationOverCurrentContext;
    detailVC.modalTransitionStyle = UIModalTransitionStyleCrossDissolve;

    [self presentViewController:detailVC animated:NO completion:nil];
  }
}

- (BOOL)isNotchScreen {
  if (@available(iOS 11.0, *)) {
    UIWindow *window = [UIApplication sharedApplication].windows.firstObject;
    return window.safeAreaInsets.top > 20;
  }
  return NO;
}
@end