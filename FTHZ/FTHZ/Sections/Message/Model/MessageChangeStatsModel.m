#import "MessageChangeStatsModel.h"

@implementation MessageChangeStatsModel
+ (void)postMessageChangeStatsModel:(NSString *)noticationId
                            success:(success)_success
                            failure:(failure)_failure {
  NSMutableDictionary *param = [NSMutableDictionary new];
  [param setObject:noticationId forKey:@"noticationId"];
  [Http postAsynRequestWithUrl:KURLPostChangeMessageStats
                        params:param
                       success:_success
                       failure:_failure];
}

+ (void)postDelNotificationModel:(NSString *)noticationIds
                         success:(success)_success
                         failure:(failure)_failure {
  NSMutableDictionary *param = [NSMutableDictionary new];
  [param setObject:noticationIds forKey:@"noticationIds"];
  [Http postAsynRequestWithUrl:KURLPostDelNotification
                        params:param
                       success:_success
                       failure:_failure];
}

@end
