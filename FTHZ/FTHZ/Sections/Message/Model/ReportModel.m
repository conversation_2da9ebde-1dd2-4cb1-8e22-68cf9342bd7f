#import "ReportModel.h"

@implementation ReportModel
+ (void)postReportModel:(NSString *)toUserid
            report_type:(NSString *)report_type
                   type:(NSString *)type
                    rid:(NSString *)rid
                 detail:(NSString *)detail
                success:(success)_success
                failure:(failure)_failure {
  NSMutableDictionary *param = [NSMutableDictionary new];
  [param setObject:toUserid forKey:@"toUserid"];
  [param setObject:report_type forKey:@"report_type"];
  [param setObject:type forKey:@"type"];
  [param setObject:rid forKey:@"rid"];
  if (detail.length > 0) {
    [param setObject:detail forKey:@"detail"];
  }
  [Http postAsynRequestWithUrl:KURLIMUserReport
                        params:param
                       success:_success
                       failure:_failure];
}
@end
