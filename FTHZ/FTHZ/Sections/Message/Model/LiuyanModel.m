#import "LiuyanModel.h"

@implementation LiuyanModel

@end
@implementation <PERSON>yanPageResult
@end

@implementation LiuyanNotificationModel
+ (void)getLiuyanNotificationModel:(NSString *)page
                              size:(NSString *)size
                           success:(success)_success
                           failure:(failure)_failure {
  NSMutableDictionary *param = [NSMutableDictionary new];

  [param setObject:page forKey:@"page"];
  [param setObject:size forKey:@"size"];
  [Http getAsynRequestWithUrl:KURLGETLiuyanList
                       params:param
                      success:_success
                      failure:_failure];
}

+ (void)markMessageAsRead:(NSString *)lid
                  success:(success)_success
                  failure:(failure)_failure {

  NSMutableDictionary *params = [NSMutableDictionary dictionary];
  [params setObject:lid forKey:@"lid"];

  [Http postAsynRequestWithUrl:KURLPOSTLiuyanRead
                        params:params
                       success:_success
                       failure:_failure];
}

+ (void)getLiuyanUnreadCount:(success)_success failure:(failure)_failure {
  [Http getAsynRequestWithUrl:KURLGETLiuyanUnreadCount
                       params:nil
                      success:_success
                      failure:_failure];
}

+ (void)deleteLiuyanMessage:(NSString *)lids
                    success:(success)_success
                    failure:(failure)_failure {

  NSMutableDictionary *params = [NSMutableDictionary dictionary];
  [params setObject:lids forKey:@"lids"];

  [Http postAsynRequestWithUrl:KURLPOSTLiuyanDelete
                        params:params
                       success:_success
                       failure:_failure];
}

+ (void)clearAllLiuyanMessages:(success)_success failure:(failure)_failure {
  [Http postAsynRequestWithUrl:KURLPOSTLiuyanClearAll
                        params:nil
                       success:_success
                       failure:_failure];
}

@end