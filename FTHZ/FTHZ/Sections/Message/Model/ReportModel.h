#import "BaseJsonModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface ReportModel : BaseJsonModel
@property(nonatomic, strong) NSArray *data;

+ (void)postReportModel:(NSString *)toUserid
            report_type:(NSString *)report_type
                   type:(NSString *)type
                    rid:(NSString *)rid
                 detail:(NSString *)detail
                success:(success)_success
                failure:(failure)_failure;

@end

NS_ASSUME_NONNULL_END
