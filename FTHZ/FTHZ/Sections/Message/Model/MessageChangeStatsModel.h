#import "BaseJsonModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface MessageChangeStatsModel : BaseJsonModel
@property(nonatomic, strong) NSArray *data;

+ (void)postMessageChangeStatsModel:(NSString *)noticationId
                            success:(success)_success
                            failure:(failure)_failure;

+ (void)postDelNotificationModel:(NSString *)noticationIds
                            success:(success)_success
                            failure:(failure)_failure;
@end

NS_ASSUME_NONNULL_END
