#import "BaseJsonModel.h"

NS_ASSUME_NONNULL_BEGIN
@protocol LiuyanModel <NSObject>
@end

@interface LiuyanModel : BaseJsonModel
@property(nonatomic, strong) NSString *lid;
@property(nonatomic, strong) NSString *userid;
@property(nonatomic, strong) NSString *gender;
@property(nonatomic, strong) NSString *message;
@property(nonatomic, strong) NSString *created;
@property(nonatomic, strong) NSString *nickname;
@property(nonatomic, strong) NSString *avatar;
@property(nonatomic, strong) NSString *tonickname;
@property(nonatomic, strong) NSString *toavatar;
@property(nonatomic, strong) NSString *status;

@end

@protocol LiuyanPageResult <NSObject>
@end
@interface LiuyanPageResult : BaseJsonModel
@property(nonatomic, strong) NSString *page;
@property(nonatomic, strong) NSString *size;
@property(nonatomic, strong) NSString *count;
@property(nonatomic, strong) NSString *unreadCount;
@property(nonatomic, strong) NSArray<LiuyanModel *> *data;
@end

@interface LiuyanNotificationModel : BaseJsonModel
@property(nonatomic, strong) NSArray<LiuyanPageResult *> *data;

+ (void)getLiuyanNotificationModel:(NSString *)page
                              size:(NSString *)size
                           success:(success)_success
                           failure:(failure)_failure;

+ (void)markMessageAsRead:(NSString *)lid
                  success:(success)_success
                  failure:(failure)_failure;
+ (void)getLiuyanUnreadCount:(success)_success failure:(failure)_failure;

+ (void)deleteLiuyanMessage:(NSString *)lids
                    success:(success)_success
                    failure:(failure)_failure;

+ (void)clearAllLiuyanMessages:(success)_success failure:(failure)_failure;

@end

NS_ASSUME_NONNULL_END