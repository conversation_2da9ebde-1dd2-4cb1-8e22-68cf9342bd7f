#import "CurrencyRootVC.h"

NS_ASSUME_NONNULL_BEGIN

@protocol LiuyanDetailDelegate <NSObject>
- (void)unreadMessage:(NSInteger)index number:(NSInteger)count;
@end

@interface LiuyanDetailVC : CurrencyRootVC
@property(nonatomic, assign) id<LiuyanDetailDelegate> delegate;
@property(nonatomic, assign) CGFloat mainTempScale;

- (NSString *)getSelectedLiuyanIds;
- (void)markAllMessagesAsRead;

@end

NS_ASSUME_NONNULL_END
