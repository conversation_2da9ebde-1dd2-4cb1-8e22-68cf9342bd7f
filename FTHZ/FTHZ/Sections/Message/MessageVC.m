#import "MessageVC.h"
#import "AffairListModel.h"
#import "AttentionDetailVC.h"
#import "AttentionTableViewCell.h"
#import "GetMessageNumberModel.h"
#import "IMUserInfoModel.h"
#import "MessageDetailVC.h"
#import "MessageNotificationModel.h"
#import "MessageTableViewCell.h"
#import "ReportModel.h"
#import "ReportVC.h"
#import "ShudongDetailVC.h"

#define MessageTableViewCellID @"MessageTableViewCellID"
@interface MessageVC () <UITableViewDelegate, UITableViewDataSource,
                         NicknameDelegate, MessageDetailDelegate> {
}

//@property (nonatomic, strong) AffairListModelResult      *tempMember;
@property(nonatomic, strong) UITableView *tableView;
@property(nonatomic, strong) UIView *navHeaderView;
@property(nonatomic, strong) NSMutableArray *lastMessage;
@property(nonatomic, strong) NSMutableArray *userIdArray;
@property(nonatomic, strong) NSMutableArray *userInfoArray;
@property(nonatomic, strong) NSString *replaceId;
@property(nonatomic, strong) NSMutableArray *systemArray;
// 数量判断
@property(nonatomic, assign) NSInteger messageCount;

@end

@implementation MessageVC
// 举报
- (void)loadReportData:(NSString *)report_type detail:(NSString *)detail {
  __weak typeof(self) wSelf = self;
  [ReportModel postReportModel:self.replaceId
      report_type:report_type
      type:@"0"
      rid:self.replaceId
      detail:detail
      success:^(NSDictionary *resultObject) {
        ReportModel *member = [ReportModel mj_objectWithKeyValues:resultObject];
        if ([member.success boolValue]) {
          [wSelf showToastFast:@"举报成功"];
        } else {
          [wSelf showToastFast:member.msg];
        }
      }
      failure:^(NSError *requestErr) {
        [wSelf showToastFast:@"举报失败,请重试"];
      }];
}

- (void)loadImData {
  __weak typeof(self) wSelf = self;
  if (!_systemArray) {
    _systemArray = [NSMutableArray new];
  }
  [GetMessageNumberModel
      getGetMessageNumberModel:^(NSDictionary *resultObject) {
        NSArray *datas = resultObject[@"data"];
        NSNumber *unreadCount = [datas.firstObject objectForKey:@"count"];
        wSelf.messageCount = [unreadCount integerValue];
        [MessageNotificationModel getMessageNotificationModel:@"1"
            size:@"1"
            success:^(NSDictionary *resultObject) {
              [self->_systemArray removeAllObjects];
              MessageNotificationModel *memeber = [MessageNotificationModel
                  mj_objectWithKeyValues:resultObject];
              if ([memeber.success boolValue]) {
                MessageNotificationPageResult *tempMember =
                    [MessageNotificationPageResult
                        mj_objectWithKeyValues:memeber.data.firstObject];
                for (int i = 0; i < tempMember.data.count; i++) {
                  MessageNotificationModelResult *dy =
                      [MessageNotificationModelResult
                          mj_objectWithKeyValues:[tempMember.data
                                                     objectAtIndex:i]];
                  [wSelf.systemArray addObject:dy];
                }
              }
              [wSelf.tableView.mj_header endRefreshing];
              [wSelf loadImContentData];
            }
            failure:^(NSError *requestErr) {
              [wSelf.tableView.mj_header endRefreshing];
              [wSelf showToastFast:@"数据有误,请检查网络后重试"];
            }];
      }
      failure:^(NSError *requestErr) {
        [wSelf.tableView.mj_header endRefreshing];
        [wSelf showToastFast:requestErr.localizedDescription];
      }];
}

- (void)loadImContentData {
  if (!_lastMessage) {
    _lastMessage = [NSMutableArray new];
    _userIdArray = [NSMutableArray new];
    _userInfoArray = [NSMutableArray new];
  } else {
    [_lastMessage removeAllObjects];
    [_userIdArray removeAllObjects];
    [_userInfoArray removeAllObjects];
  }

  @weakify(self);
  NSArray *arraya = [[TIMManager sharedInstance] getConversationList];
  for (TIMConversation *conversation in arraya) {
    [conversation
        getMessage:1
              last:nil
              succ:^(NSArray *msgs) {
                @strongify(self);
                TIMMessage *message = [msgs objectAtIndex:0];
                if ([[conversation getReceiver] isEqualToString:@"52HZ"] ||
                    ![[conversation getReceiver] isValid]) {
                } else {
                  [self.lastMessage addObject:message];
                  [self.userIdArray addObject:[conversation getReceiver]];
                }
                [self getIMUserInfo];
              }
              fail:^(int code, NSString *msg){

              }];
    //        NSArray *msgs = [conversation getLastMsgs:1];
  }
}

- (void)getIMUserInfo {
  @weakify(self);
  [IMUserInfoModel
      getIMUserInfoModel:[NemoUtil arrayOfStrChangeString:_userIdArray]
      success:^(NSDictionary *resultObject) {
        @strongify(self);
        IMUserInfoModel *member =
            [IMUserInfoModel mj_objectWithKeyValues:resultObject];
        if ([member.success boolValue]) {
          for (int i = 0; i < member.data.count; i++) {
            IMUserInfoModelResult *da = [IMUserInfoModelResult
                mj_objectWithKeyValues:[member.data objectAtIndex:i]];
            [self.userInfoArray addObject:da];
          }
        } else {
          [AccountManager loginIM];
        }
        [self.tableView.mj_header endRefreshing];
        [self.tableView reloadData];
      }
      failure:^(NSError *requestErr) {
        [AccountManager loginIM];
        [self.tableView.mj_header endRefreshing];
      }];
}

- (void)viewDidLoad {
  [super viewDidLoad];
  self.tableView.estimatedRowHeight = 200;
  self.tableView.rowHeight = UITableViewAutomaticDimension;
  [self loadHead];
  self.messageCount = 0;
  self.view.backgroundColor = KColor_White;
  [self.view addSubview:self.tableView];
  MJChiBaoZiHeader *header =
      [MJChiBaoZiHeader headerWithRefreshingTarget:self
                                  refreshingAction:@selector(loadImData)];
  header.lastUpdatedTimeLabel.hidden = YES;
  header.stateLabel.hidden = YES;
  [header beginRefreshing];
  self.tableView.mj_header = header;

  [NOTIFICENTER addObserver:self
                   selector:@selector(newMessage)
                       name:NewMessage
                     object:nil];
  [NOTIFICENTER addObserver:self
                   selector:@selector(reloadAllUI)
                       name:AllReloadUI
                     object:nil];
  [NOTIFICENTER addObserver:self
                   selector:@selector(LoadindexData)
                       name:ReloadIndexThree
                     object:nil];
}

- (void)reloadAllUI {
  if (_tableView) {
    [self newMessage];
  }
}

- (void)LoadindexData {
  [self.tableView.mj_header beginRefreshing];
}

- (void)newMessage {
  [self loadImData];
}

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];
  [self.navigationController setNavigationBarHidden:YES animated:animated];
  //    [UIApplication sharedApplication].statusBarStyle =
  //    UIStatusBarStyleDefault;
  [AppConfig statusbarStyle:YES];
}

- (UITableView *)tableView {
  if (FT_IS_IPhoneX_All) {
    if (!_tableView) {
      _tableView = [[UITableView alloc]
          initWithFrame:(CGRectMake(0, 107, kMainWidth,
                                    kMainHeight - 107 - kTabarHeight))
                  style:(UITableViewStylePlain)];
      //        _tableView.backgroundColor = KColor_Gray;
      _tableView.delegate = self;
      _tableView.dataSource = self;
      _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
      [_tableView registerClass:[MessageTableViewCell class]
          forCellReuseIdentifier:MessageTableViewCellID];
      _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    }
    return _tableView;

  } else {
    if (!_tableView) {
      _tableView = [[UITableView alloc]
          initWithFrame:(CGRectMake(0, KHomeNavHeight, kMainWidth,
                                    kMainHeight - KHomeNavHeight -
                                        kTabarHeight))
                  style:(UITableViewStylePlain)];
      //        _tableView.backgroundColor = KColor_Gray;
      _tableView.delegate = self;
      _tableView.dataSource = self;
      _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
      [_tableView registerClass:[MessageTableViewCell class]
          forCellReuseIdentifier:MessageTableViewCellID];
      _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    }
    return _tableView;
  }
}

- (void)loadHead {
  if (FT_IS_IPhoneX_All) {
    if (!_navHeaderView) {
      _navHeaderView = [[UIView alloc] init];
      _navHeaderView.backgroundColor = KColor_White;
      [self.view addSubview:_navHeaderView];
      [self.navHeaderView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view);
        make.top.equalTo(self.view);
        make.size.mas_equalTo(CGSizeMake(kMainWidth, 107));
      }];

      UILabel *titleLabel = [[UILabel alloc] init];
      titleLabel.text = @"消息";
      titleLabel.textColor = KColor_Black;

      titleLabel.font = [UIFont systemFontOfSize:32];
      //        titleLabel.font = [UIFont fontWithName:@"" size:32];
      [self.navHeaderView addSubview:titleLabel];
      [titleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.navHeaderView).offset(20);
        make.top.equalTo(self.navHeaderView).offset(50);
        make.size.mas_equalTo(CGSizeMake(kMainWidth * 0.5, 44));
      }];

      UILabel *lineLabel = [[UILabel alloc] init];
      lineLabel = [[UILabel alloc] init];
      lineLabel.backgroundColor = KColor_LineGray;
      [self.view addSubview:lineLabel];
      [lineLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(self.navHeaderView.mas_bottom).offset(-0.8);
        make.size.mas_equalTo(CGSizeMake(kMainWidth, 0.8));
      }];
    }

  } else {
    if (!_navHeaderView) {
      _navHeaderView = [[UIView alloc] init];
      _navHeaderView.backgroundColor = KColor_White;
      [self.view addSubview:_navHeaderView];
      [self.navHeaderView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view);
        make.top.equalTo(self.view);
        make.size.mas_equalTo(CGSizeMake(kMainWidth, KHomeNavHeight));
      }];

      UILabel *titleLabel = [[UILabel alloc] init];
      titleLabel.text = @"消息";
      titleLabel.textColor = KColor_Black;
      titleLabel.font = [UIFont systemFontOfSize:32];
      //        titleLabel.font = [UIFont fontWithName:@"" size:32];
      [self.navHeaderView addSubview:titleLabel];
      [titleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.navHeaderView).offset(20);
        make.centerY.equalTo(self.navHeaderView).offset(10);
        make.size.mas_equalTo(CGSizeMake(kMainWidth * 0.5, 64));
      }];

      UILabel *lineLabel = [[UILabel alloc] init];
      lineLabel = [[UILabel alloc] init];
      lineLabel.backgroundColor = KColor_LineGray;
      [self.view addSubview:lineLabel];
      [lineLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(self.navHeaderView.mas_bottom).offset(-1);
        make.size.mas_equalTo(CGSizeMake(kMainWidth, 1));
      }];
    }
  }
}
- (void)gotoMessageTest {
  [self loadImData];
}

#pragma mark - Table view data source
- (CGFloat)tableView:(UITableView *)tableView
    heightForFooterInSection:(NSInteger)section {
  return 10.0;
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForHeaderInSection:(NSInteger)section {
  return 0.01f;
}

- (UIView *)tableView:(UITableView *)tableView
    viewForFooterInSection:(NSInteger)section {
  return [[UIView alloc] initWithFrame:CGRectZero];
}

- (UIView *)tableView:(UITableView *)tableView
    viewForHeaderInSection:(NSInteger)section {
  return [[UIView alloc] initWithFrame:CGRectZero];
}

// - (CGFloat)tableView:(UITableView *)tableView
//     heightForRowAtIndexPath:(NSIndexPath *)indexPath {
//   return 62 * kMainTemp;
// }

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
  return 1;
}

- (NSInteger)tableView:(UITableView *)tableView
    numberOfRowsInSection:(NSInteger)section {
  return _lastMessage.count + 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView
         cellForRowAtIndexPath:(NSIndexPath *)indexPath {
  if (indexPath.row == 0) {
    MessageTableViewCell *cell = (MessageTableViewCell *)[tableView
        dequeueReusableCellWithIdentifier:MessageTableViewCellID
                             forIndexPath:indexPath];
    if (!cell) {
      cell = [[MessageTableViewCell alloc]
            initWithStyle:(UITableViewCellStyleDefault)
          reuseIdentifier:MessageTableViewCellID];
    }
    if (_systemArray.count == 0) {
      [cell loadMessagCountAction:0];
      cell.iconImage.image = KImage_name(@"admin");
      cell.titleDetailsLabel.text = @"暂无新消息";
      cell.nameLabel.text = @"系统消息";
    } else {
      if (CurrentUserConfig.unreadMessageCount > 0 &&
          _messageCount == CurrentUserConfig.unreadMessageCount) {
        [cell loadMessagCountAction:0];
        cell.iconImage.image = KImage_name(@"admin");
        cell.titleDetailsLabel.text = @"暂无新消息";
        cell.nameLabel.text = @"系统消息";
      } else {
        [cell loadMessagCountAction:self.messageCount];
        MessageNotificationModelResult *dy = [_systemArray objectAtIndex:0];
        [cell.iconImage
            sd_setImageWithURL:[NemoUtil getUrlWithUserSmallIcon:dy.avatar]
              placeholderImage:KImage_name(@"empty")];
        cell.titleDetailsLabel.text =
            [NSString stringWithFormat:@"%@%@", dy.nickName, dy.text];
        cell.nameLabel.text = @"系统消息";
      }
    }
    return cell;
  } else {
    TIMMessage *message = [_lastMessage objectAtIndex:indexPath.row - 1];
    TIMConversation *conversation = [message getConversation];
    [conversation getLocalMessage:99
                             last:nil
                             succ:^(NSArray *msgList) {
                               for (TIMMessage *msg in msgList) {
                                 if ([msg isKindOfClass:[TIMMessage class]]) {
                                 }
                               }
                             }
                             fail:^(int code, NSString *err){
                             }];

    MessageTableViewCell *cell = (MessageTableViewCell *)[tableView
        dequeueReusableCellWithIdentifier:MessageTableViewCellID
                             forIndexPath:indexPath];
    if (!cell) {
      cell = [[MessageTableViewCell alloc]
            initWithStyle:(UITableViewCellStyleDefault)
          reuseIdentifier:MessageTableViewCellID];
    }
    TIMElem *elem = [message getElem:0];
    if ([elem isKindOfClass:[TIMTextElem class]]) {
      TIMTextElem *text_elem = (TIMTextElem *)elem;
      if ([text_elem.text containsString:@"http://im.hz-52.com/"]) {
        if ([text_elem.text containsString:@"4_"]) {
          cell.titleDetailsLabel.text = @"动态消息";
        } else {
          cell.titleDetailsLabel.text = @"图片消息";
        }
      } else {
        cell.titleDetailsLabel.text = text_elem.text;
      }

    } else if ([elem isKindOfClass:[TIMSoundElem class]]) {
      cell.titleDetailsLabel.text = @"语音消息";
    }
    if (_userInfoArray.count == _lastMessage.count) {
      IMUserInfoModelResult *da =
          [_userInfoArray objectAtIndex:indexPath.row - 1];
      [cell.iconImage
          sd_setImageWithURL:[NemoUtil getUrlWithUserSmallIcon:da.avatar]
            placeholderImage:KImage_name(@"empty")];
      cell.nameLabel.text = da.nickName;
      cell.timeLabel.text = [NemoUtil
          distanceTimeWithBeforeTime:[message.timestamp timeIntervalSince1970]];
      ;
    }
    [cell loadMessagCountAction:0];
    return cell;
  }
}

- (BOOL)tableView:(UITableView *)tableView
    canEditRowAtIndexPath:(NSIndexPath *)indexPath {
  if (indexPath.row == 0) {
    return NO;
  }
  return YES;
}
// 定义编辑样式
- (UITableViewCellEditingStyle)tableView:(UITableView *)tableView
           editingStyleForRowAtIndexPath:(NSIndexPath *)indexPath {
  return UITableViewCellEditingStyleDelete;
}

// 修改编辑按钮文字
//- (NSString *)tableView:(UITableView *)tableView
// titleForDeleteConfirmationButtonForRowAtIndexPath:(NSIndexPath *)indexPath
//{
//     return NSLocalizedString(@"举报", @"删除");
// }

- (NSArray<UITableViewRowAction *> *)tableView:(UITableView *)tableView
                  editActionsForRowAtIndexPath:(NSIndexPath *)indexPath {
  TIMMessage *message = [_lastMessage objectAtIndex:indexPath.row - 1];
  TIMConversation *conversation = [message getConversation];
  UITableViewRowAction *editAction = [UITableViewRowAction
      rowActionWithStyle:UITableViewRowActionStyleNormal
                   title:@"举报"
                 handler:^(UITableViewRowAction *_Nonnull action,
                           NSIndexPath *_Nonnull indexPath) {
                   // 收回侧滑
                   self.replaceId = [conversation getReceiver];

                   ReportVC *ddVC = [[ReportVC alloc] init];
                   ddVC.delegate = self;
                   [self.navigationController pushViewController:ddVC
                                                        animated:YES];
                 }];

  UITableViewRowAction *deleteAction = [UITableViewRowAction
      rowActionWithStyle:UITableViewRowActionStyleDestructive
                   title:@"删除"
                 handler:^(UITableViewRowAction *_Nonnull action,
                           NSIndexPath *_Nonnull indexPath) {
                   // 删除cell: 必须要先删除数据源，才能删除cell
                   [[TIMManager sharedInstance]
                       deleteConversation:TIM_C2C
                                 receiver:[conversation getReceiver]];
                   [self.lastMessage removeObjectAtIndex:indexPath.row - 1];
                   [self.userInfoArray removeObjectAtIndex:indexPath.row - 1];
                   [tableView
                       deleteRowsAtIndexPaths:@[ indexPath ]
                             withRowAnimation:UITableViewRowAnimationFade];
                 }];

  return @[ deleteAction, editAction ];
}
// 设置进入编辑状态时，Cell不会缩进
- (BOOL)tableView:(UITableView *)tableView
    shouldIndentWhileEditingRowAtIndexPath:(NSIndexPath *)indexPath {
  if (indexPath.row == 0) {
    return NO;
  }
  return YES;
}

- (void)tableView:(UITableView *)tableView
    didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
  if (indexPath.row == 0) {
    MessageDetailVC *ddVC = [[MessageDetailVC alloc] init];
    ddVC.delegate = self;
    [self.navigationController pushViewController:ddVC animated:YES];
  } else {
  }
  [tableView deselectRowAtIndexPath:indexPath animated:YES];
}
// 举报delegate
- (void)delegateGetReport:(NSInteger)RelationshipType
                   detail:(NSString *)detail {
  [self loadReportData:[NSString stringWithFormat:@"%ld", RelationshipType]
                detail:detail];
}

- (void)reloadMessageList {
  [self loadImData];
}

@end
