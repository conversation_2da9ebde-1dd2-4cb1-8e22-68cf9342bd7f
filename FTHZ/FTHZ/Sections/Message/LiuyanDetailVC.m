#import "LiuyanDetailVC.h"
#import "AttentionDetailVC.h"
#import "FTHZChannelDetailVC.h"
#import "LiuyanListCell.h"
#import "LiuyanVC.h"
#import "WhaleDetailVC.h"
#import "_2hz-Swift.h"
#define liuyanDetailVCID @"LiuyanDetailVCID"
// #import "LiuyanDetailTableCell.h"
// #import "LiuyanDetailView.h"
#import "LiuyanModel.h"
#import "LiuyanTimeSeparatorCell.h"
#import "LiuyanTimeSeparatorModel.h"
@interface LiuyanDetailVC () <UITableViewDelegate, UITableViewDataSource,
                              UIGestureRecognizerDelegate, NicknameDelegate> {
  NSInteger currentPage;
  BOOL isSelectionMode;
  NSIndexPath *expandedIndexPath;
}
@property(nonatomic, strong) UITableView *tableView;
@property(nonatomic, strong) UIView *navHeaderView;
@property(nonatomic, strong) NSMutableArray *cellData;
@property(nonatomic, strong) UIView *detailView;
@property(nonatomic, assign) BOOL didInitialAppear;
@property(nonatomic, strong) NSMutableSet *selectedItems;
@property(nonatomic, assign) NSInteger totalUnreadCount;
@property(nonatomic, copy) NSString *currentReportUserid;
@property(nonatomic, copy) NSString *currentReportLid;

#define FZSCREEN_W [UIScreen mainScreen].bounds.size.width
#define FZSCREEN_H                                                             \
  [UIScreen mainScreen].bounds.size.height == 812                              \
      ? [UIScreen mainScreen].bounds.size.height - 64.0                        \
      : [UIScreen mainScreen].bounds.size.height

@end

@implementation LiuyanDetailVC

- (void)loadData {
  if (!_cellData) {
    _cellData = [NSMutableArray new];
  }

  @weakify(self);
  [LiuyanNotificationModel
      getLiuyanNotificationModel:[NSString stringWithFormat:@"%ld", currentPage]
      size:@"20"
      success:^(NSDictionary *resultObject) {
        @strongify(self);
        if (self->currentPage == 1) {
          [self.cellData removeAllObjects];
        }

        LiuyanNotificationModel *memeber =
            [LiuyanNotificationModel mj_objectWithKeyValues:resultObject];

        if ([memeber.success boolValue]) {
          LiuyanPageResult *tempMember = [LiuyanPageResult
              mj_objectWithKeyValues:memeber.data.firstObject];
          NSInteger unreadCount = [tempMember.unreadCount integerValue];
          if (unreadCount >= 0) {
            self.totalUnreadCount = unreadCount;
          }
          NSMutableArray *mixedData = [NSMutableArray array];
          NSString *lastGroupTitle = nil;
          for (int i = 0; i < tempMember.data.count; i++) {
            LiuyanModel *dy = [LiuyanModel
                mj_objectWithKeyValues:[tempMember.data objectAtIndex:i]];
            NSString *groupTitle = [self groupTitleForDate:dy.created];
            NSTimeInterval time = [dy.created doubleValue];
            NSDate *date = [NSDate dateWithTimeIntervalSince1970:time];
            if (![groupTitle isEqualToString:lastGroupTitle]) {
              [mixedData addObject:[LiuyanTimeSeparatorModel
                                       modelWithTitle:groupTitle]];
              lastGroupTitle = groupTitle;
            }
            [mixedData addObject:dy];
          }
          if (self.cellData.count > 0 && mixedData.count > 0 &&
              currentPage > 1) {
            NSString *lastTitle = nil;
            for (NSInteger i = self.cellData.count - 1; i >= 0; i--) {
              id obj = self.cellData[i];
              if ([obj isKindOfClass:[LiuyanTimeSeparatorModel class]]) {
                lastTitle = ((LiuyanTimeSeparatorModel *)obj).title;
                break;
              }
            }
            NSString *firstTitle = nil;
            for (id obj in mixedData) {
              if ([obj isKindOfClass:[LiuyanTimeSeparatorModel class]]) {
                firstTitle = ((LiuyanTimeSeparatorModel *)obj).title;
                break;
              }
            }
            if (lastTitle && firstTitle &&
                [lastTitle isEqualToString:firstTitle]) {
              if ([mixedData.firstObject
                      isKindOfClass:[LiuyanTimeSeparatorModel class]]) {
                [mixedData removeObjectAtIndex:0];
              }
            }
          }
          if (self->currentPage == 1) {
            [self.cellData removeAllObjects];
          }
          [self.cellData addObjectsFromArray:mixedData];

          if (self.delegate && [self.delegate respondsToSelector:@selector
                                              (unreadMessage:number:)]) {
            NSInteger unreadCount = [tempMember.unreadCount integerValue];
            if (unreadCount < 0) {
              unreadCount = 0;
            }
            [self.delegate unreadMessage:0 number:unreadCount];
          }

          if ([tempMember.count integerValue] > self.cellData.count) {
            if (!self.tableView.mj_footer) {
              MJChiBaoZiFooter *footer = [MJChiBaoZiFooter
                  footerWithRefreshingTarget:self
                            refreshingAction:@selector(loadMoreData)];
              footer.refreshingTitleHidden = YES;
              self.tableView.mj_footer = footer;
            }
          } else {
            [self.tableView.mj_footer removeFromSuperview];
            self.tableView.mj_footer = nil;
          }
        }

        [self.tableView.mj_header endRefreshing];
        [self.tableView.mj_footer endRefreshing];
        [self.tableView reloadData];
      }
      failure:^(NSError *requestErr) {
        [self.tableView.mj_header endRefreshing];
        [self.tableView.mj_footer endRefreshing];
        [self showToastFast:@"数据有误,请检查网络后重试"];
      }];
}

- (NSString *)groupTitleForDate:(NSString *)created {
  NSTimeInterval time = [created doubleValue];
  NSDate *date = [NSDate dateWithTimeIntervalSince1970:time];
  NSDate *now = [NSDate date];
  NSCalendar *calendar = [NSCalendar currentCalendar];

  if ([calendar isDateInToday:date]) {
    return @"今天";
  }
  if ([calendar isDateInYesterday:date]) {
    return @"一天前";
  }
  // 计算天数差
  NSDateComponents *diff = [calendar components:NSCalendarUnitDay
                                       fromDate:date
                                         toDate:now
                                        options:0];
  NSInteger days = diff.day;
  if (days < 7) {
    return @"一周内";
  }
  if (days < 30) {
    return @"一个月内";
  }
  if (days < 365) {
    return @"一年内";
  }
  return @"一年前";
}

- (void)viewDidLoad {
  [super viewDidLoad];
  self.didInitialAppear = NO;
  self.tableView.backgroundColor = [UIColor whiteColor];

  currentPage = 1;
  self.view.backgroundColor = KColor_White;

  [self.view addSubview:self.tableView];
  [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.edges.equalTo(self.view);
  }];

  UILongPressGestureRecognizer *longPress =
      [[UILongPressGestureRecognizer alloc]
          initWithTarget:self
                  action:@selector(handleLongPress:)];
  longPress.minimumPressDuration = 0.5;
  longPress.delegate = self;
  [self.tableView addGestureRecognizer:longPress];

  if (self.mainTempScale == 0) {
    self.mainTempScale = kMainTemp;
  }

  CGFloat headerHeight = [self isNotchScreen] ? (108 * self.mainTempScale)
                                              : (84 * self.mainTempScale);

  MJChiBaoZiHeader *header =
      [MJChiBaoZiHeader headerWithRefreshingTarget:self
                                  refreshingAction:@selector(loadMessageData)];
  header.lastUpdatedTimeLabel.hidden = YES;
  header.stateLabel.hidden = YES;
  header.ignoredScrollViewContentInsetTop = -headerHeight;
  [header setAutomaticallyChangeAlpha:YES];
  self.tableView.mj_header = header;
  [header beginRefreshing];
  self.tableView.bounces = YES;
  if (@available(iOS 11.0, *)) {
    self.tableView.contentInsetAdjustmentBehavior =
        UIScrollViewContentInsetAdjustmentNever;
  }
  [NOTIFICENTER addObserver:self
                   selector:@selector(loadnewMessage)
                       name:ReloadLiuyanList
                     object:nil];
  [NOTIFICENTER addObserver:self
                   selector:@selector(handleScrollToTop)
                       name:SegementViewChildVCBackToTop
                     object:nil];
  [[NSNotificationCenter defaultCenter]
      addObserver:self
         selector:@selector(enterSelectionMode)
             name:@"EnterLiuyanSelectionMode"
           object:nil];
  [[NSNotificationCenter defaultCenter] addObserver:self
                                           selector:@selector(exitSelectionMode)
                                               name:@"ExitLiuyanSelectionMode"
                                             object:nil];
  [[NSNotificationCenter defaultCenter]
      addObserver:self
         selector:@selector(handleClearAllLiuyanMessages)
             name:@"ClearAllLiuyanMessagesSuccess"
           object:nil];
  [[NSNotificationCenter defaultCenter]
      addObserver:self
         selector:@selector(handleCellReportClicked:)
             name:@"LiuyanDetailCellReportClicked"
           object:nil];
  [[NSNotificationCenter defaultCenter]
      addObserver:self
         selector:@selector(handleCellReplySuccess:)
             name:@"LiuyanDetailCellReplySuccess"
           object:nil];
  [[NSNotificationCenter defaultCenter]
      addObserver:self
         selector:@selector(handleCellReplyFailed:)
             name:@"LiuyanDetailCellReplyFailed"
           object:nil];
  UIView *headerView = [[UIView alloc]
      initWithFrame:CGRectMake(0, 0, self.view.frame.size.width, headerHeight)];
  headerView.backgroundColor = [UIColor clearColor];
  self.tableView.tableHeaderView = headerView;

  self.tableView.allowsMultipleSelectionDuringEditing = NO;
  self.tableView.estimatedRowHeight = 120;
  self.tableView.rowHeight = UITableViewAutomaticDimension;
  self.tableView.estimatedSectionHeaderHeight = 0;
  self.tableView.estimatedSectionFooterHeight = 0;
  self.tableView.prefetchingEnabled = YES;
  self.selectedItems = [NSMutableSet new];
}

- (void)closeDetailCellIfNeeded {
  if (expandedIndexPath) {
    NSIndexPath *toRemove = expandedIndexPath;
    expandedIndexPath = nil;
    [self.tableView beginUpdates];
    [self.tableView reloadRowsAtIndexPaths:@[ toRemove ]
                          withRowAnimation:UITableViewRowAnimationFade];
    [self.tableView endUpdates];
  }
}

- (void)handleLongPress:(UILongPressGestureRecognizer *)gestureRecognizer {
  if (gestureRecognizer.state == UIGestureRecognizerStateBegan) {
    [self closeDetailCellIfNeeded];
    CGPoint p = [gestureRecognizer locationInView:self.tableView];
    NSIndexPath *indexPath = [self.tableView indexPathForRowAtPoint:p];
    if (indexPath && !isSelectionMode) {
      [[NSNotificationCenter defaultCenter]
          postNotificationName:@"NotificationCellLongPressed"
                        object:nil];
    }
  }
}

- (void)markAllMessagesAsRead {
  for (id msg in self.cellData) {
    if ([msg isKindOfClass:[LiuyanModel class]]) {
      ((LiuyanModel *)msg).status = @"1";
    }
  }
  [self.tableView reloadData];
}

- (void)handleScrollToTop {
  if (self.tableView) {
    [self.tableView setContentOffset:CGPointZero animated:YES];
  }
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
}

- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer
    shouldRecognizeSimultaneouslyWithGestureRecognizer:
        (UIGestureRecognizer *)otherGestureRecognizer {
  if ([gestureRecognizer isKindOfClass:[UIPanGestureRecognizer class]]) {
    UIPanGestureRecognizer *pan = (UIPanGestureRecognizer *)gestureRecognizer;
    CGPoint velocity = [pan velocityInView:self.view];
    if (fabs(velocity.x) > fabs(velocity.y)) {
      return YES;
    } else {
      return NO;
    }
  }
  return YES;
}

- (void)viewDidLayoutSubviews {
  [super viewDidLayoutSubviews];
  self.tableView.frame = self.view.bounds;
  self.tableView.scrollIndicatorInsets = self.tableView.contentInset;
}

#pragma mark - 批量选择相关
- (void)enterSelectionMode {
  if (isSelectionMode)
    return;
  isSelectionMode = YES;
  if (!self.selectedItems)
    self.selectedItems = [NSMutableSet new];
  else
    [self.selectedItems removeAllObjects];

  dispatch_async(dispatch_get_main_queue(), ^{
    for (UITableViewCell *cell in self.tableView.visibleCells) {
      if ([cell isKindOfClass:[LiuyanListCell class]]) {
        [(LiuyanListCell *)cell setSelectionMode:YES selected:NO animated:YES];
      }
    }
  });
}
- (void)exitSelectionMode {
  if (!isSelectionMode)
    return;
  isSelectionMode = NO;
  [self.selectedItems removeAllObjects];
  [self.tableView reloadData];
}
- (NSString *)getSelectedLiuyanIds {
  if (self.selectedItems.count == 0)
    return @"";
  return [[self.selectedItems allObjects] componentsJoinedByString:@","];
}

#pragma mark - UITableView Delegate

- (void)replyMessageAtIndexPath:(NSIndexPath *)indexPath {
  LiuyanModel *message = [self.cellData objectAtIndex:indexPath.row];
  LiuyanVC *ddVC = [[LiuyanVC alloc] init];
  ddVC.to = message.userid;
  [self.navigationController pushViewController:ddVC animated:YES];
}

- (void)deleteMessageAtIndexPath:(NSIndexPath *)indexPath {
  LiuyanModel *message = [self.cellData objectAtIndex:indexPath.row];

  @weakify(self);
  [LiuyanNotificationModel deleteLiuyanMessage:message.lid
      success:^(NSDictionary *resultObject) {
        @strongify(self);
        LiuyanNotificationModel *member =
            [LiuyanNotificationModel mj_objectWithKeyValues:resultObject];

        if ([member.success boolValue]) {
          [self.cellData removeObjectAtIndex:indexPath.row];
          [self.tableView deleteRowsAtIndexPaths:@[ indexPath ]
                                withRowAnimation:UITableViewRowAnimationFade];
          if (self.cellData.count == 0) {
            [self.tableView reloadData];
          }
          if ([message.status isEqualToString:@"0"]) {
            CurrentUserConfig.unreadMessageCount =
                MAX(0, CurrentUserConfig.unreadMessageCount - 1);
            if (self.delegate) {
              [self.delegate unreadMessage:0 number:-1];
            }
          }

          [self showToastFast:@"删除成功"];
        } else {
          [self showToastFast:@"删除失败"];
        }
      }
      failure:^(NSError *requestErr) {
        @strongify(self);
        [self showToastFast:@"删除失败，请检查网络后重试"];
      }];
}

- (void)loadnewMessage {
  if (_tableView) {
    [self loadData];
  }
}

- (void)loadMessageData {
  currentPage = 1;
  [self loadData];
}

- (void)loadMoreData {
  currentPage += 1;
  [self loadData];
}

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];
}

- (void)viewDidAppear:(BOOL)animated {
  [super viewDidAppear:animated];
  if (!self.didInitialAppear) {
    self.didInitialAppear = YES;
    [self.tableView reloadData];
  }
}

- (UITableView *)tableView {
  if (!_tableView) {
    _tableView = [[UITableView alloc] initWithFrame:CGRectZero
                                              style:(UITableViewStyleGrouped)];
    _tableView.delegate = self;
    _tableView.dataSource = self;
    [_tableView registerClass:[LiuyanListCell class]
        forCellReuseIdentifier:liuyanDetailVCID];
    [_tableView registerClass:[LiuyanTimeSeparatorCell class]
        forCellReuseIdentifier:@"LiuyanTimeSeparatorCellID"];
    _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    _tableView.bounces = YES;
    _tableView.alwaysBounceVertical = YES;
    if (@available(iOS 11.0, *)) {
      _tableView.contentInsetAdjustmentBehavior =
          UIScrollViewContentInsetAdjustmentNever;
    }
  }

  return _tableView;
}

#pragma mark - Table view data source
- (CGFloat)tableView:(UITableView *)tableView
    heightForFooterInSection:(NSInteger)section {
  return 0.0;
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForHeaderInSection:(NSInteger)section {
  return 0.0;
}

- (UIView *)tableView:(UITableView *)tableView
    viewForFooterInSection:(NSInteger)section {
  return nil;
}

- (UIView *)tableView:(UITableView *)tableView
    viewForHeaderInSection:(NSInteger)section {
  return nil;
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForRowAtIndexPath:(NSIndexPath *)indexPath {
  if (indexPath.row >= self.cellData.count) {
    return 49 * kWidthFactor;
  }
  id obj = self.cellData[indexPath.row];
  if ([obj isKindOfClass:[LiuyanTimeSeparatorModel class]]) {
    return 40 * kWidthFactor;
  }
  NSInteger dataCount = _cellData.count;
  if (expandedIndexPath && [expandedIndexPath isEqual:indexPath]) {
    return UITableViewAutomaticDimension;
  }
  return 72 * kWidthFactor;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
  return 1;
}

- (NSInteger)tableView:(UITableView *)tableView
    numberOfRowsInSection:(NSInteger)section {
  return _cellData.count + 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView
         cellForRowAtIndexPath:(NSIndexPath *)indexPath {
  if (indexPath.row >= self.cellData.count) {
    UITableViewCell *cell =
        [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault
                               reuseIdentifier:@"EmptyCell"];
    cell.backgroundColor = [UIColor clearColor];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    return cell;
  }
  id obj = self.cellData[indexPath.row];
  if ([obj isKindOfClass:[LiuyanTimeSeparatorModel class]]) {
    LiuyanTimeSeparatorCell *cell = [tableView
        dequeueReusableCellWithIdentifier:@"LiuyanTimeSeparatorCellID"
                             forIndexPath:indexPath];
    [cell setTitle:((LiuyanTimeSeparatorModel *)obj).title];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    return cell;
  }
  LiuyanModel *dy = (LiuyanModel *)obj;
  LiuyanListCell *cell = (LiuyanListCell *)[tableView
      dequeueReusableCellWithIdentifier:liuyanDetailVCID
                           forIndexPath:indexPath];
  if (!cell) {
    cell = [[LiuyanListCell alloc] initWithStyle:(UITableViewCellStyleDefault)
                                 reuseIdentifier:liuyanDetailVCID];
  }
  cell.delegate = self;
  cell.messageData = dy;
  if (isSelectionMode) {
    BOOL selected = [self.selectedItems containsObject:dy.lid];
    if ([cell respondsToSelector:@selector(setSelectionMode:
                                                   selected:animated:)]) {
      [cell setSelectionMode:YES selected:selected animated:NO];
    } else if ([cell respondsToSelector:@selector(setSelectionMode:
                                                          selected:)]) {
      [cell setSelectionMode:YES selected:selected];
    }
    [cell hideBottomView];
  } else {
    if ([cell respondsToSelector:@selector(setSelectionMode:
                                                   selected:animated:)]) {
      [cell setSelectionMode:NO selected:NO animated:NO];
    } else if ([cell respondsToSelector:@selector(setSelectionMode:
                                                          selected:)]) {
      [cell setSelectionMode:NO selected:NO];
    }
    if (expandedIndexPath && [expandedIndexPath isEqual:indexPath]) {
      [cell showBottomViewWithText:dy.message];
    } else {
      [cell hideBottomView];
    }
  }
  return cell;
}

#pragma mark - UIScrollViewDelegate
- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView {
  CGPoint touchPoint =
      [scrollView.panGestureRecognizer locationInView:self.tableView];
  if (expandedIndexPath) {
    CGRect cellRect = [self.tableView rectForRowAtIndexPath:expandedIndexPath];
    if (CGRectContainsPoint(cellRect, touchPoint)) {
      return;
    }
  }
  [self closeDetailCellIfNeeded];
}

- (BOOL)isNotchScreen {
  if (@available(iOS 11.0, *)) {
    UIWindow *window = [UIApplication sharedApplication].windows.firstObject;
    return window.safeAreaInsets.top > 20;
  }
  return NO;
}

#pragma mark - Cell 通知处理
- (void)handleCellReportClicked:(NSNotification *)notification {
  NSDictionary *userInfo = notification.userInfo;
  self.currentReportUserid = userInfo[@"userid"];
  self.currentReportLid = userInfo[@"lid"];
  ReportVC *reportVC = [[ReportVC alloc] init];
  reportVC.delegate = self;
  [self.navigationController pushViewController:reportVC animated:YES];
}

- (void)handleCellReplySuccess:(NSNotification *)notification {
  NSDictionary *userInfo = notification.userInfo;
  NSString *message = userInfo[@"message"];
  [self showToastFast:message];
}

- (void)handleCellReplyFailed:(NSNotification *)notification {
  NSDictionary *userInfo = notification.userInfo;
  NSString *message = userInfo[@"message"];
  [self showToastFast:message];
}

#pragma mark - NicknameDelegate
- (void)delegateGetReport:(NSInteger)RelationshipType
                   detail:(NSString *)detail {
  [self loadReportData:[NSString stringWithFormat:@"%ld", RelationshipType]
                detail:detail];
}

- (void)loadReportData:(NSString *)report_type detail:(NSString *)detail {
  if (!self.currentReportUserid || self.currentReportUserid.length == 0) {
    [self showToastFast:@"无法举报，用户信息无效"];
    return;
  }
  [ReportModel postReportModel:self.currentReportUserid
      report_type:report_type
      type:@"4"
      rid:self.currentReportLid
      detail:detail
      success:^(NSDictionary *resultObject) {
        ReportModel *member = [ReportModel mj_objectWithKeyValues:resultObject];
        NSString *msg = [member.success boolValue]
                            ? @"举报成功"
                            : (member.msg ?: @"举报失败");
        [self showToastFast:msg];
        [self.navigationController popViewControllerAnimated:YES];
      }
      failure:^(NSError *requestErr) {
        [self showToastFast:@"举报失败，请重试"];
        [self.navigationController popViewControllerAnimated:YES];
      }];
}

- (void)tableView:(UITableView *)tableView
    didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
  if (indexPath.row >= self.cellData.count)
    return;

  if (isSelectionMode) {
    LiuyanModel *notification = [self.cellData objectAtIndex:indexPath.row];
    NSString *lid = notification.lid;
    BOOL wasSelected = [self.selectedItems containsObject:lid];
    if (wasSelected) {
      [self.selectedItems removeObject:lid];
    } else {
      [self.selectedItems addObject:lid];
    }
    LiuyanListCell *cell =
        (LiuyanListCell *)[tableView cellForRowAtIndexPath:indexPath];
    if (cell) {
      [cell setSelectionMode:YES selected:!wasSelected animated:NO];
    }
    // 可选：发送选中数量变化通知
    [[NSNotificationCenter defaultCenter]
        postNotificationName:@"SelectedLiuyanChanged"
                      object:nil
                    userInfo:@{@"count" : @(self.selectedItems.count)}];
    return;
  }

  if (expandedIndexPath && ![expandedIndexPath isEqual:indexPath]) {
    LiuyanListCell *lastCell =
        [tableView cellForRowAtIndexPath:expandedIndexPath];
    if (lastCell) {
      [lastCell hideBottomView];
    }
  }

  LiuyanModel *dy = [self.cellData objectAtIndex:indexPath.row];
  @weakify(self);
  if ([dy.status isEqualToString:@"0"]) {
    [LiuyanNotificationModel markMessageAsRead:dy.lid
        success:^(NSDictionary *resultObject) {
          @strongify(self);
          LiuyanNotificationModel *member =
              [LiuyanNotificationModel mj_objectWithKeyValues:resultObject];
          if ([member.success boolValue]) {
            dy.status = @"1";
            [tableView reloadRowsAtIndexPaths:@[ indexPath ]
                             withRowAnimation:UITableViewRowAnimationNone];
            if (self.totalUnreadCount > 0) {
              self.totalUnreadCount--;
            }
            if (self.delegate && [self.delegate respondsToSelector:@selector
                                                (unreadMessage:number:)]) {
              [self.delegate unreadMessage:0 number:self.totalUnreadCount];
            }
          }
        }
        failure:^(NSError *requestErr) {
          @strongify(self);
          [self showToastFast:@"数据有误,请检查网络后重试"];
        }];
  }

  LiuyanListCell *cell = [tableView cellForRowAtIndexPath:indexPath];
  if ([expandedIndexPath isEqual:indexPath]) {
    [cell hideBottomView];
    expandedIndexPath = nil;
  } else {
    [cell showBottomViewWithText:cell.messageData.message];
    expandedIndexPath = indexPath;
  }
  [tableView beginUpdates];
  [tableView endUpdates];
  [tableView deselectRowAtIndexPath:indexPath animated:YES];
}

#pragma mark - LiuyanListCellDelegate
- (void)didTapAvatarWithUserId:(NSString *)userId {
  WhaleDetailVC *detailVC = [[WhaleDetailVC alloc] init];
  detailVC.uid = userId;
  [self.navigationController pushViewController:detailVC animated:YES];
}

@end