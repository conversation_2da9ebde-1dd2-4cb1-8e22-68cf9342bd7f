#import "MessageDetailVC.h"
#import "AttentionDetailVC.h"
#import "FTHZChannelDetailVC.h"
#import "LiuyanTimeSeparatorCell.h"
#import "LiuyanTimeSeparatorModel.h"
#import "MessageChangeStatsModel.h"
#import "MessageNotificationModel.h"
#import "MessageNotificationTableViewCell.h"
#import "WhaleDetailVC.h"
#import "_2hz-Swift.h"
#define MessageDetailVCID @"MessageDetailVCID"
@interface MessageDetailVC () <UITableViewDelegate, UITableViewDataSource,
                               UIGestureRecognizerDelegate> {
  NSInteger currentPage;
  BOOL isReturningFromDetail;
  BOOL isSelectionMode;
}

@property(nonatomic, strong) UITableView *tableView;
@property(nonatomic, strong) UIView *navHeaderView;
@property(nonatomic, strong) NSMutableArray *cellData;
@property(nonatomic, strong) NSMutableSet *selectedItems;
@property(nonatomic, strong) UILongPressGestureRecognizer *longPressGesture;
@property(nonatomic, assign) BOOL didInitialAppear;
@property(nonatomic, assign) NSInteger totalUnreadCount;

@end

@implementation MessageDetailVC

- (void)loadData {
  if (!_cellData) {
    _cellData = [NSMutableArray new];
  }

  @weakify(self);
  [MessageNotificationModel
      getMessageNotificationModel:[NSString
                                      stringWithFormat:@"%ld", currentPage]
      size:@"20"
      success:^(NSDictionary *resultObject) {
        @strongify(self);

        if (self->currentPage == 1) {
          [self.cellData removeAllObjects];
        }

        MessageNotificationModel *memeber =
            [MessageNotificationModel mj_objectWithKeyValues:resultObject];

        if ([memeber.success boolValue]) {
          MessageNotificationPageResult *tempMember =
              [MessageNotificationPageResult
                  mj_objectWithKeyValues:memeber.data.firstObject];
          NSInteger unreadCount = [tempMember.unreadCount integerValue];
          if (unreadCount >= 0) {
            self.totalUnreadCount = unreadCount;
          }
          NSMutableArray *mixedData = [NSMutableArray array];
          NSString *lastGroupTitle = nil;
          for (int i = 0; i < tempMember.data.count; i++) {
            MessageNotificationModelResult *dy = [MessageNotificationModelResult
                mj_objectWithKeyValues:[tempMember.data objectAtIndex:i]];
            NSString *groupTitle = [self groupTitleForDate:dy.time];
            if (![groupTitle isEqualToString:lastGroupTitle]) {
              [mixedData addObject:[LiuyanTimeSeparatorModel
                                       modelWithTitle:groupTitle]];
              lastGroupTitle = groupTitle;
            }
            [mixedData addObject:dy];
          }
          if (self.cellData.count > 0 && mixedData.count > 0 &&
              currentPage > 1) {
            NSString *lastTitle = nil;
            for (NSInteger i = self.cellData.count - 1; i >= 0; i--) {
              id obj = self.cellData[i];
              if ([obj isKindOfClass:[LiuyanTimeSeparatorModel class]]) {
                lastTitle = ((LiuyanTimeSeparatorModel *)obj).title;
                break;
              }
            }
            NSString *firstTitle = nil;
            for (id obj in mixedData) {
              if ([obj isKindOfClass:[LiuyanTimeSeparatorModel class]]) {
                firstTitle = ((LiuyanTimeSeparatorModel *)obj).title;
                break;
              }
            }
            if (lastTitle && firstTitle &&
                [lastTitle isEqualToString:firstTitle]) {
              if ([mixedData.firstObject
                      isKindOfClass:[LiuyanTimeSeparatorModel class]]) {
                [mixedData removeObjectAtIndex:0];
              }
            }
          }
          if (self->currentPage == 1) {
            [self.cellData removeAllObjects];
          }
          [self.cellData addObjectsFromArray:mixedData];

          if (self.delegate) {
            [self.delegate unreadMessage:1 number:self.totalUnreadCount];
          }

          if ([tempMember.count integerValue] > self.cellData.count) {
            if (!self.tableView.mj_footer) {
              MJChiBaoZiFooter *footer = [MJChiBaoZiFooter
                  footerWithRefreshingTarget:self
                            refreshingAction:@selector(loadMoreData)];
              footer.refreshingTitleHidden = YES;
              self.tableView.mj_footer = footer;
            }
          } else {
            [self.tableView.mj_footer removeFromSuperview];
            self.tableView.mj_footer = nil;
          }
        }

        [self.tableView.mj_header endRefreshing];
        [self.tableView.mj_footer endRefreshing];
        [self.tableView reloadData];
      }
      failure:^(NSError *requestErr) {
        @strongify(self);
        [self.tableView.mj_header endRefreshing];
        [self.tableView.mj_footer endRefreshing];
        [self showToastFast:@"数据有误,请检查网络后重试"];
      }];
}

- (void)markAllNotificationsAsRead {
  for (id msg in self.cellData) {
    if ([msg isKindOfClass:[MessageNotificationModelResult class]]) {
      ((MessageNotificationModelResult *)msg).status = @"1";
    }
  }
  [self.tableView reloadData];
}

- (void)loadMessageStats:(NSString *)nid {
  __weak typeof(self) wSelf = self;
  [MessageChangeStatsModel postMessageChangeStatsModel:nid
      success:^(NSDictionary *resultObject) {
        MessageChangeStatsModel *member =
            [MessageChangeStatsModel mj_objectWithKeyValues:resultObject];

        if ([member.success boolValue]) {
          if (wSelf.totalUnreadCount > 0) {
            wSelf.totalUnreadCount--;
          }

          if (wSelf.delegate) {
            [wSelf.delegate unreadMessage:1 number:wSelf.totalUnreadCount];
          }
        }
      }
      failure:^(NSError *requestErr) {
        [wSelf showToastFast:requestErr.localizedDescription];
      }];
}

- (void)viewDidLoad {
  [super viewDidLoad];
  self.didInitialAppear = NO;
  self.tableView.backgroundColor = [UIColor whiteColor];

  currentPage = 1;
  isReturningFromDetail = NO;
  isSelectionMode = NO;
  if (!self.selectedItems) {
    self.selectedItems = [NSMutableSet new];
  }

  self.view.backgroundColor = KColor_White;
  [self.view addSubview:self.tableView];
  [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.edges.equalTo(self.view);
  }];

  if (self.mainTempScale == 0) {
    self.mainTempScale = kMainTemp;
  }

  CGFloat headerHeight = [self isNotchScreen] ? (108 * self.mainTempScale)
                                              : (84 * self.mainTempScale);

  MJChiBaoZiHeader *header =
      [MJChiBaoZiHeader headerWithRefreshingTarget:self
                                  refreshingAction:@selector(loadMessageData)];
  header.lastUpdatedTimeLabel.hidden = YES;
  header.stateLabel.hidden = YES;
  header.ignoredScrollViewContentInsetTop = -headerHeight;
  [header setAutomaticallyChangeAlpha:YES];
  self.tableView.mj_header = header;
  [header beginRefreshing];
  self.tableView.bounces = YES;
  if (@available(iOS 11.0, *)) {
    self.tableView.contentInsetAdjustmentBehavior =
        UIScrollViewContentInsetAdjustmentNever;
  }
  isSelectionMode = NO;
  self.selectedItems = [NSMutableSet new];

  self.longPressGesture = [[UILongPressGestureRecognizer alloc]
      initWithTarget:self
              action:@selector(handleLongPress:)];
  self.longPressGesture.minimumPressDuration = 0.5;
  self.longPressGesture.delegate = self;
  [self.tableView addGestureRecognizer:self.longPressGesture];
  self.tableView.delaysContentTouches = NO;

  [NOTIFICENTER addObserver:self
                   selector:@selector(enterSelectionMode)
                       name:@"EnterNotificationSelectionMode"
                     object:nil];
  [NOTIFICENTER addObserver:self
                   selector:@selector(exitSelectionMode)
                       name:@"ExitNotificationSelectionMode"
                     object:nil];
  [NOTIFICENTER addObserver:self
                   selector:@selector(loadnewMessage)
                       name:@"ReloadNotiList"
                     object:nil];
  [NOTIFICENTER addObserver:self
                   selector:@selector(handleScrollToTop)
                       name:SegementViewChildVCBackToTop
                     object:nil];

  UIView *headerView = [[UIView alloc]
      initWithFrame:CGRectMake(0, 0, self.view.frame.size.width, headerHeight)];
  headerView.backgroundColor = [UIColor clearColor];
  self.tableView.tableHeaderView = headerView;

  self.tableView.estimatedRowHeight = 0;
  self.tableView.estimatedSectionHeaderHeight = 0;
  self.tableView.estimatedSectionFooterHeight = 0;
  self.tableView.prefetchingEnabled = YES;
}

- (void)handleScrollToTop {
  if (self.tableView) {
    [self.tableView setContentOffset:CGPointZero animated:YES];
  }
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
}

- (void)viewDidLayoutSubviews {
  [super viewDidLayoutSubviews];
  self.tableView.frame = self.view.bounds;
  self.tableView.scrollIndicatorInsets = self.tableView.contentInset;
}

- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer
    shouldRecognizeSimultaneouslyWithGestureRecognizer:
        (UIGestureRecognizer *)otherGestureRecognizer {
  if ([gestureRecognizer isKindOfClass:[UIPanGestureRecognizer class]]) {
    UIPanGestureRecognizer *pan = (UIPanGestureRecognizer *)gestureRecognizer;
    CGPoint velocity = [pan velocityInView:self.view];
    if (fabs(velocity.x) > fabs(velocity.y)) {
      return YES;
    } else {
      return NO;
    }
  }
  return YES;
}

- (void)handleLongPress:(UILongPressGestureRecognizer *)gestureRecognizer {
  if (gestureRecognizer.state == UIGestureRecognizerStateBegan) {
    CGPoint p = [gestureRecognizer locationInView:self.tableView];
    NSIndexPath *indexPath = [self.tableView indexPathForRowAtPoint:p];

    if (indexPath && !isSelectionMode) {
      [NOTIFICENTER postNotificationName:@"NotificationCellLongPressed"
                                  object:nil];
    }
  }
}

- (void)enterSelectionMode {
  if (isSelectionMode)
    return;
  isSelectionMode = YES;

  if (!self.selectedItems) {
    self.selectedItems = [NSMutableSet new];
  } else {
    [self.selectedItems removeAllObjects];
  }

  [NOTIFICENTER postNotificationName:@"HideClearButton" object:nil];
  [NOTIFICENTER postNotificationName:@"HideAllUnreadIndicators" object:nil];

  [self.tableView reloadData];
}

- (void)exitSelectionMode {
  if (!isSelectionMode)
    return;

  isSelectionMode = NO;
  [self.selectedItems removeAllObjects];
  [NOTIFICENTER postNotificationName:@"RestoreUnreadIndicators" object:nil];
  [self.tableView reloadData];

  [UIView
      animateWithDuration:0.3
               animations:^{
                 for (UITableViewCell *cell in self.tableView.visibleCells) {
                   if ([cell isKindOfClass:[MessageNotificationTableViewCell
                                               class]]) {
                     MessageNotificationTableViewCell *notificationCell =
                         (MessageNotificationTableViewCell *)cell;
                     [notificationCell layoutIfNeeded];
                   }
                 }
               }];
}

- (void)loadnewMessage {
  if (_tableView) {
    [self markAllNotificationsAsRead];

    currentPage = 1;

    [self loadData];

    if (self.selectedItems) {
      [self.selectedItems removeAllObjects];
    }

    if (isSelectionMode) {
      [self exitSelectionMode];
    }
  }
}

- (void)loadMessageData {
  currentPage = 1;
  [self loadData];
}

- (void)loadMoreData {
  currentPage += 1;
  [self loadData];
}

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];
  if (!isReturningFromDetail) {
    currentPage = 1;
    [self loadData];
  } else {
    isReturningFromDetail = NO;
  }

  if (![self.tableView.gestureRecognizers
          containsObject:self.longPressGesture]) {
    [self.tableView addGestureRecognizer:self.longPressGesture];
  }
}

- (void)viewDidAppear:(BOOL)animated {
  [super viewDidAppear:animated];
  if (!self.didInitialAppear) {
    self.didInitialAppear = YES;
    [self.tableView reloadData];
  }
}

- (UITableView *)tableView {
  if (!_tableView) {
    _tableView = [[UITableView alloc] initWithFrame:CGRectZero
                                              style:(UITableViewStyleGrouped)];
    _tableView.delegate = self;
    _tableView.dataSource = self;
    [_tableView registerClass:[MessageNotificationTableViewCell class]
        forCellReuseIdentifier:MessageDetailVCID];
    [_tableView registerClass:[LiuyanTimeSeparatorCell class]
        forCellReuseIdentifier:@"LiuyanTimeSeparatorCellID"];
    _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;

    _tableView.bounces = YES;
    _tableView.alwaysBounceVertical = YES;
    if (@available(iOS 11.0, *)) {
      _tableView.contentInsetAdjustmentBehavior =
          UIScrollViewContentInsetAdjustmentNever;
    }
  }

  return _tableView;
}

#pragma mark - Table view data source
- (CGFloat)tableView:(UITableView *)tableView
    heightForFooterInSection:(NSInteger)section {
  return 0.0;
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForHeaderInSection:(NSInteger)section {
  return 0.0;
}

- (UIView *)tableView:(UITableView *)tableView
    viewForFooterInSection:(NSInteger)section {
  return nil;
}

- (UIView *)tableView:(UITableView *)tableView
    viewForHeaderInSection:(NSInteger)section {
  return nil;
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForRowAtIndexPath:(NSIndexPath *)indexPath {
  if (indexPath.row == _cellData.count) {
    return 49 * kWidthFactor;
  }
  id obj = self.cellData[indexPath.row];
  if ([obj isKindOfClass:[LiuyanTimeSeparatorModel class]]) {
    return 40 * kWidthFactor;
  }
  return 72 * kWidthFactor;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
  return 1;
}

- (NSInteger)tableView:(UITableView *)tableView
    numberOfRowsInSection:(NSInteger)section {
  return _cellData.count + 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView
         cellForRowAtIndexPath:(NSIndexPath *)indexPath {
  if (indexPath.row == _cellData.count) {
    UITableViewCell *cell =
        [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault
                               reuseIdentifier:@"EmptyCell"];
    cell.backgroundColor = [UIColor clearColor];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    return cell;
  }

  id obj = self.cellData[indexPath.row];
  if ([obj isKindOfClass:[LiuyanTimeSeparatorModel class]]) {
    LiuyanTimeSeparatorCell *cell = [tableView
        dequeueReusableCellWithIdentifier:@"LiuyanTimeSeparatorCellID"
                             forIndexPath:indexPath];
    [cell setTitle:((LiuyanTimeSeparatorModel *)obj).title];
    [cell setAlignmentToRight:YES]; // 设置为右对齐
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    return cell;
  }

  MessageNotificationModelResult *dy = [_cellData objectAtIndex:indexPath.row];
  MessageNotificationTableViewCell *cell =
      (MessageNotificationTableViewCell *)[tableView
          dequeueReusableCellWithIdentifier:MessageDetailVCID
                               forIndexPath:indexPath];

  cell.messageData = dy;
  cell.delegate = self;

  if (isSelectionMode) {
    BOOL isSelected = [self.selectedItems containsObject:dy.noticationId];
    [cell setSelectionMode:YES selected:isSelected];
  } else {
    [cell setSelectionMode:NO selected:NO];
  }

  return cell;
}

- (void)messageCell:(MessageNotificationTableViewCell *)cell
    didMessageStatusChanged:(MessageNotificationModelResult *)message {
  NSInteger unreadCount = 0;
  for (MessageNotificationModelResult *msg in self.cellData) {
    if ([msg.status isEqualToString:@"0"]) {
      unreadCount++;
    }
  }

  if (self.delegate &&
      [self.delegate respondsToSelector:@selector(unreadMessage:number:)]) {
    [self.delegate unreadMessage:1 number:unreadCount];
  }
}

- (NSString *)getSelectedNotificationIds {
  if (self.selectedItems.count == 0) {
    return @"";
  }
  return [[self.selectedItems allObjects] componentsJoinedByString:@","];
}

- (void)tableView:(UITableView *)tableView
    didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
  if (isSelectionMode) {
    MessageNotificationModelResult *notification =
        [_cellData objectAtIndex:indexPath.row];
    NSString *notificationId = notification.noticationId;

    BOOL wasSelected = [self.selectedItems containsObject:notificationId];

    if (wasSelected) {
      [self.selectedItems removeObject:notificationId];
    } else {
      [self.selectedItems addObject:notificationId];
    }

    MessageNotificationTableViewCell *cell =
        (MessageNotificationTableViewCell *)[tableView
            cellForRowAtIndexPath:indexPath];
    if (cell) {
      [cell setSelectionMode:YES selected:!wasSelected];
    }

    [NOTIFICENTER
        postNotificationName:@"SelectedNotificationsChanged"
                      object:nil
                    userInfo:@{@"count" : @(self.selectedItems.count)}];

    return;
  }

  MessageNotificationModelResult *dy = [_cellData objectAtIndex:indexPath.row];
  dy.status = @"1";
  MessageNotificationTableViewCell *cell =
      (MessageNotificationTableViewCell *)[tableView
          cellForRowAtIndexPath:indexPath];
  [cell handleCellClick];
  [self loadMessageStats:dy.noticationId];

  NSString *userId = dy.authorid;

  if (userId.length == 0) {
    userId = CurrentUser.userid;
  }

  isReturningFromDetail = YES;

  if ([dy.type isEqualToString:@"1"] || [dy.type isEqualToString:@"2"] ||
      [dy.type isEqualToString:@"7"]) {
    FZMomentVC *ddVC = [[FZMomentVC alloc] init];
    ddVC.momentId = dy.contentid;
    ddVC.userId = userId;
    [self.navigationController pushViewController:ddVC animated:YES];
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
  } else if ([dy.type isEqualToString:@"3"]) {
    WhaleDetailVC *ddVC = [[WhaleDetailVC alloc] init];
    ddVC.uid = dy.attention_userid;
    [self.navigationController pushViewController:ddVC animated:YES];
  } else if ([dy.type isEqualToString:@"4"]) {
    FTHZChannelDetailVC *cdVC = [[FTHZChannelDetailVC alloc] init];
    cdVC.discussId = dy.discussid;
    [self.navigationController pushViewController:cdVC animated:YES];
  }

  [tableView deselectRowAtIndexPath:indexPath animated:YES];
}

- (BOOL)isNotchScreen {
  if (@available(iOS 11.0, *)) {
    UIWindow *window = [UIApplication sharedApplication].windows.firstObject;
    return window.safeAreaInsets.top > 20;
  }
  return NO;
}

- (NSString *)groupTitleForDate:(NSString *)created {
  NSTimeInterval time = [created doubleValue];
  NSDate *date = [NSDate dateWithTimeIntervalSince1970:time];
  NSDate *now = [NSDate date];
  NSCalendar *calendar = [NSCalendar currentCalendar];
  if ([calendar isDateInToday:date]) {
    return @"今天";
  }
  if ([calendar isDateInYesterday:date]) {
    return @"一天前";
  }
  NSDateComponents *diff = [calendar components:NSCalendarUnitDay
                                       fromDate:date
                                         toDate:now
                                        options:0];
  NSInteger days = diff.day;
  if (days < 7) {
    return @"一周内";
  }
  if (days < 30) {
    return @"一个月内";
  }
  if (days < 365) {
    return @"一年内";
  }
  return @"一年前";
}

@end