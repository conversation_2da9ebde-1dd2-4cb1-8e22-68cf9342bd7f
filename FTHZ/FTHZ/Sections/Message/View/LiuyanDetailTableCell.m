// #import "LiuyanDetailTableCell.h"
// #import "FTHZChannelReplyModel.h"
// #import "PopoverView.h"
// #import "ReportModel.h"
// #import "ReportVC.h"
// #import "XHInputView.h"

// #define kWidthFactor ([UIScreen mainScreen].bounds.size.width / 375.0)
// #define SCREENWIDTH [UIScreen mainScreen].bounds.size.width
// #define SCREENHEIGHT [UIScreen mainScreen].bounds.size.height

// @interface LiuyanDetailTableCell ()
// @property(nonatomic, strong) UIView *paperView;
// @property(nonatomic, strong) UILabel *messageLabel;
// @property(nonatomic, strong) UIButton *replyButton;
// @end

// @implementation LiuyanDetailTableCell

// - (instancetype)initWithStyle:(UITableViewCellStyle)style
//               reuseIdentifier:(NSString *)reuseIdentifier {
//   self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
//   if (self) {
//     self.selectionStyle = UITableViewCellSelectionStyleNone;
//     self.backgroundColor = [UIColor clearColor];
//     [self setupUI];
//   }
//   return self;
// }

// - (void)setupUI {
//   self.clipsToBounds = NO;
//   self.contentView.clipsToBounds = NO;
//   self.paperView = [[UIView alloc] init];
//   self.paperView.backgroundColor = [UIColor whiteColor];
//   self.paperView.clipsToBounds = NO;

//   UIView *topGradientView = [[UIView alloc] init];
//   topGradientView.userInteractionEnabled = NO;
//   [self.paperView addSubview:topGradientView];
//   [topGradientView mas_makeConstraints:^(MASConstraintMaker *make) {
//     make.left.right.top.equalTo(self.paperView);
//     make.height.mas_equalTo(6 * kWidthFactor);
//   }];
//   CAGradientLayer *topGradient = [CAGradientLayer layer];
//   topGradient.frame = CGRectMake(0, 0, SCREENWIDTH, 6 * kWidthFactor);
//   topGradient.colors = @[
//     (__bridge id)[UIColor colorWithWhite:0 alpha:0.12].CGColor,
//     (__bridge id)[UIColor clearColor].CGColor
//   ];
//   topGradient.startPoint = CGPointMake(0.5, 0.0);
//   topGradient.endPoint = CGPointMake(0.5, 1.0);
//   [topGradientView.layer addSublayer:topGradient];

//   UIView *bottomGradientView = [[UIView alloc] init];
//   bottomGradientView.userInteractionEnabled = NO;
//   [self.paperView addSubview:bottomGradientView];
//   [bottomGradientView mas_makeConstraints:^(MASConstraintMaker *make) {
//     make.left.right.bottom.equalTo(self.paperView);
//     make.height.mas_equalTo(6 * kWidthFactor);
//   }];
//   CAGradientLayer *bottomGradient = [CAGradientLayer layer];
//   bottomGradient.frame = CGRectMake(0, 0, SCREENWIDTH, 6 * kWidthFactor);
//   bottomGradient.colors = @[
//     (__bridge id)[UIColor clearColor].CGColor,
//     (__bridge id)[UIColor colorWithWhite:0 alpha:0.12].CGColor
//   ];
//   bottomGradient.startPoint = CGPointMake(0.5, 0.0);
//   bottomGradient.endPoint = CGPointMake(0.5, 1.0);
//   [bottomGradientView.layer addSublayer:bottomGradient];

//   [self.contentView addSubview:self.paperView];
//   [self.paperView mas_makeConstraints:^(MASConstraintMaker *make) {
//     make.left.equalTo(self.contentView);
//     make.right.equalTo(self.contentView);
//     make.top.equalTo(self.contentView);
//     make.bottom.equalTo(self.contentView);
//   }];

//   UIButton *settingBt = [[UIButton alloc] init];
//   [settingBt setBackgroundImage:[UIImage imageNamed:@"more-black"]
//                        forState:UIControlStateNormal];
//   [self.paperView addSubview:settingBt];
//   [settingBt mas_makeConstraints:^(MASConstraintMaker *make) {
//     make.right.equalTo(self.paperView).offset(-16 * kWidthFactor);
//     make.top.equalTo(self.paperView).offset(16 * kWidthFactor);
//   }];
//   [settingBt addTarget:self
//                 action:@selector(gotoMessagePage:)
//       forControlEvents:UIControlEventTouchUpInside];

//   self.replyButton = [UIButton buttonWithType:UIButtonTypeCustom];
//   [self.replyButton setTitle:@" 回复" forState:UIControlStateNormal];
//   [self.replyButton setTitleColor:KColor_HighBlack
//                          forState:UIControlStateNormal];
//   self.replyButton.titleLabel.font =
//       SourceHanSerifMediumFont(12 * kWidthFactor);
//   self.replyButton.backgroundColor = [UIColor colorWithRed:237 / 255.0
//                                                      green:237 / 255.0
//                                                       blue:237 / 255.0
//                                                      alpha:1.0];
//   self.replyButton.layer.cornerRadius = 10 * kWidthFactor;
//   self.replyButton.layer.masksToBounds = YES;
//   [self.paperView addSubview:self.replyButton];
//   [self.replyButton mas_makeConstraints:^(MASConstraintMaker *make) {
//     make.right.equalTo(self.paperView).offset(-16 * kWidthFactor);
//     make.bottom.equalTo(self.paperView).offset(-16 * kWidthFactor);
//     make.height.equalTo(@(24 * kWidthFactor));
//     make.width.equalTo(@(56 * kWidthFactor));
//   }];
//   [self.replyButton addTarget:self
//                        action:@selector(replyButtonClicked:)
//              forControlEvents:UIControlEventTouchUpInside];

//   UIImageView *replyIcon =
//       [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"回复w"]];
//   replyIcon.contentMode = UIViewContentModeScaleAspectFit;
//   [self.replyButton addSubview:replyIcon];
//   [replyIcon mas_makeConstraints:^(MASConstraintMaker *make) {
//     make.left.equalTo(self.replyButton).offset(8 * kWidthFactor);
//     make.centerY.equalTo(self.replyButton);
//     make.width.height.equalTo(@(12 * kWidthFactor));
//   }];
//   self.replyButton.titleEdgeInsets =
//       UIEdgeInsetsMake(0, 8 * kWidthFactor, 0, 0);

//   self.messageLabel = [[UILabel alloc] init];
//   self.messageLabel.textAlignment = NSTextAlignmentLeft;
//   self.messageLabel.textColor = [UIColor blackColor];
//   self.messageLabel.font = [UIFont systemFontOfSize:14];
//   self.messageLabel.numberOfLines = 0;
//   [self.paperView addSubview:self.messageLabel];
//   [self.messageLabel mas_makeConstraints:^(MASConstraintMaker *make) {
//     make.left.equalTo(self.paperView).offset(20 * kWidthFactor);
//     make.right.equalTo(self.paperView).offset(-20 * kWidthFactor);
//     make.top.equalTo(settingBt.mas_bottom).offset(18 * kWidthFactor);
//     make.bottom.equalTo(self.replyButton.mas_top).offset(-12 * kWidthFactor);
//     make.bottom.lessThanOrEqualTo(self.paperView)
//         .offset(-20 * kWidthFactor)
//         .priorityLow();
//   }];
// }

// - (void)setMessage:(NSString *)message {
//   _message = [message copy];
//   if (message) {
//     CGFloat lineSpacing = 5 * kWidthFactor;
//     NSMutableParagraphStyle *paragraphStyle =
//         [[NSMutableParagraphStyle alloc] init];
//     paragraphStyle.lineSpacing = lineSpacing;
//     paragraphStyle.lineBreakMode = self.messageLabel.lineBreakMode;
//     paragraphStyle.alignment = self.messageLabel.textAlignment;
//     NSMutableAttributedString *attributedString =
//         [[NSMutableAttributedString alloc] initWithString:message];
//     [attributedString addAttribute:NSParagraphStyleAttributeName
//                              value:paragraphStyle
//                              range:NSMakeRange(0, [message length])];
//     [attributedString addAttribute:NSFontAttributeName
//                              value:self.messageLabel.font
//                              range:NSMakeRange(0, [message length])];
//     self.messageLabel.attributedText = attributedString;
//   } else {
//     self.messageLabel.text = @"";
//   }
// }

// - (void)replyButtonClicked:(UIButton *)sender {
//   [USERDEFAULT removeObjectForKey:ContenText];
//   [USERDEFAULT synchronize];

//   __weak typeof(self) weakSelf = self;
//   [XHInputView showWithStyle:InputViewStyleDefault
//       configurationBlock:^(XHInputView *inputView) {
//         inputView.delegate = nil;
//         inputView.placeholder = @"请输入你的留言";
//         inputView.maxCount = 200;
//         inputView.backgroundColor = [UIColor colorWithWhite:0.95 alpha:1.0];
//       }
//       sendBlock:^BOOL(NSString *_Nullable text, BOOL sendIM) {
//         if (text.length == 0) {
//           return NO;
//         }
//         [HUD show];
//         [LiuyanCreateModel message:text
//             to:weakSelf.userid
//             success:^(NSDictionary *resultObject) {
//               [HUD dissmiss];
//               LiuyanCreateModel *member =
//                   [LiuyanCreateModel mj_objectWithKeyValues:resultObject];
//               if ([member.success boolValue]) {
//                 [[NSNotificationCenter defaultCenter]
//                     postNotificationName:@"LiuyanDetailCellReplySuccess"
//                                   object:weakSelf
//                                 userInfo:@{@"message" : @"留言成功"}];
//               } else {
//                 [[NSNotificationCenter defaultCenter]
//                     postNotificationName:@"LiuyanDetailCellReplyFailed"
//                                   object:weakSelf
//                                 userInfo:@{
//                                   @"message" : member.msg ?: @"留言失败"
//                                 }];
//               }
//             }
//             failure:^(NSError *requestErr) {
//               [HUD dissmiss];
//               [[NSNotificationCenter defaultCenter]
//                   postNotificationName:@"LiuyanDetailCellReplyFailed"
//                                 object:weakSelf
//                               userInfo:@{
//                                 @"message" : @"数据有误,请检查网络后重试"
//                               }];
//             }];
//         return YES;
//       }];
// }

// - (void)gotoMessagePage:(UIButton *)sender {
//   PopoverView *popoverView = [PopoverView popoverView];

//   popoverView.showShade = YES;
//   [popoverView showToView:sender withActions:[self QQActions]];
// }

// - (NSArray<PopoverAction *> *)QQActions {
//   PopoverAction *multichatAction = [PopoverAction
//       actionWithImage:[UIImage imageNamed:@"Report"]
//                 title:@"举报"
//               handler:^(PopoverAction *action) {
//                 [[NSNotificationCenter defaultCenter]
//                     postNotificationName:@"LiuyanDetailCellReportClicked"
//                                   object:self
//                                 userInfo:@{
//                                   @"userid" : self.userid ?: @"",
//                                   @"lid" : self.lid ?: @""
//                                 }];
//               }];

//   return @[ multichatAction ];
// }

// - (void)layoutSubviews {
//   [super layoutSubviews];
//   for (UIView *subview in self.paperView.subviews) {
//     if (subview.layer.sublayers.count > 0 &&
//         [subview.layer.sublayers[0] isKindOfClass:[CAGradientLayer class]]) {
//       CAGradientLayer *layer = (CAGradientLayer *)subview.layer.sublayers[0];
//       layer.frame = subview.bounds;
//     }
//   }
// }

// @end