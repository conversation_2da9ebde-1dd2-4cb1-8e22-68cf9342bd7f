#import "LiuyanListCell.h"
#import "FTHZChannelReplyModel.h"
#import "HUD.h"
#import "PopoverView.h"
#import "XHInputView.h"
#import "_2hz-Swift.h"

@interface LiuyanListCell ()
@property(nonatomic, strong) UIView *mainContentView;
@property(nonatomic, strong) UILabel *nameLabel;
@property(nonatomic, strong) UILabel *timeLabel;
@property(nonatomic, strong) UILabel *detailLabel;
@property(nonatomic, strong) UIImageView *iconImage;
@property(nonatomic, strong) UIView *redDotView;
@property(nonatomic, strong) UIView *selectionCircle;
@property(nonatomic, assign) BOOL isInSelectionMode;
@property(nonatomic, assign) BOOL wasUnreadBeforeSelection;
@property(nonatomic, strong) UIView *bottomContainerView;
@property(nonatomic, strong) UILabel *bottomLabel;
@property(nonatomic, strong) MASConstraint *bottomViewHeightConstraint;
@property(nonatomic, strong) UIButton *replyButton;
@end

@implementation LiuyanListCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style
              reuseIdentifier:(NSString *)reuseIdentifier {
  self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
  if (self) {
    self.isInSelectionMode = NO;

    self.mainContentView = [[UIView alloc] init];
    [self.contentView addSubview:self.mainContentView];
    [self.mainContentView mas_makeConstraints:^(MASConstraintMaker *make) {
      make.top.equalTo(self.contentView);
      make.left.equalTo(self.contentView).offset(24.0 * kWidthFactor);
      make.right.equalTo(self.contentView);
      make.height.mas_equalTo(72 * kWidthFactor);
    }];

    [self loadCellView];

    self.bottomContainerView = [[UIView alloc] init];
    self.bottomContainerView.hidden = YES;
    self.bottomContainerView.alpha = 0;
    self.bottomContainerView.clipsToBounds = YES; // 确保内容不会溢出
    [self.contentView addSubview:self.bottomContainerView];

    // 初始时，设置一个最小高度而不是0，避免约束冲突
    [self.bottomContainerView mas_makeConstraints:^(MASConstraintMaker *make) {
      make.top.equalTo(self.mainContentView.mas_bottom);
      make.left.right.equalTo(self.contentView);
      self.bottomViewHeightConstraint =
          make.height.mas_equalTo(1); // 使用1而不是0
      make.bottom.equalTo(self.contentView).priorityLow();
    }];

    [self setupBottomCardUI];

    self.selectionCircle = [[UIView alloc] init];
    self.selectionCircle.backgroundColor = [UIColor clearColor];
    self.selectionCircle.layer.borderColor = [UIColor blackColor].CGColor;
    self.selectionCircle.layer.borderWidth = 1.0;
    self.selectionCircle.layer.cornerRadius = 9 * kWidthFactor;
    self.selectionCircle.hidden = YES;
    [self.contentView addSubview:self.selectionCircle];
    [self.selectionCircle mas_makeConstraints:^(MASConstraintMaker *make) {
      make.centerY.equalTo(self.contentView);
      make.left.equalTo(self.contentView).offset(24 * kWidthFactor);
      make.width.height.equalTo(@(18 * kWidthFactor));
    }];

    [self setSelectionStyle:(UITableViewCellSelectionStyleNone)];
  }
  return self;
}

- (void)loadCellView {
  if (!_iconImage) {
    _iconImage = [[UIImageView alloc] init];
    _iconImage.userInteractionEnabled = YES;
    UITapGestureRecognizer *tapGesture =
        [[UITapGestureRecognizer alloc] initWithTarget:self
                                                action:@selector(avatarTapped)];
    [_iconImage addGestureRecognizer:tapGesture];
  }
  UIBezierPath *maskPath = [UIBezierPath
      bezierPathWithRoundedRect:CGRectMake(0, 0, 48 * kWidthFactor,
                                           48 * kWidthFactor)
              byRoundingCorners:UIRectCornerAllCorners
                    cornerRadii:CGSizeMake(24 * kWidthFactor,
                                           24 * kWidthFactor)];
  CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
  maskLayer.frame = CGRectMake(0, 0, 48 * kWidthFactor, 48 * kWidthFactor);
  maskLayer.path = maskPath.CGPath;
  _iconImage.layer.mask = maskLayer;
  [self.mainContentView addSubview:_iconImage];
  [_iconImage mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.mainContentView);
    make.top.equalTo(self.mainContentView).offset(12 * kWidthFactor);
    make.size.mas_equalTo(CGSizeMake(48 * kWidthFactor, 48 * kWidthFactor));
  }];

  if (!_redDotView) {
    _redDotView = [[UIView alloc] init];
    if ([CurrentUser.gender isEqualToString:@"1"]) {
      _redDotView.backgroundColor = KColor_switchLightBlue;
    } else if ([CurrentUser.gender isEqualToString:@"2"]) {
      _redDotView.backgroundColor = KColor_switchLightPink;
    } else {
      _redDotView.backgroundColor = [UIColor systemBlueColor];
    }
    _redDotView.layer.cornerRadius = 4 * kWidthFactor;
    _redDotView.hidden = YES;
  }
  [self.mainContentView addSubview:_redDotView];
  [_redDotView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(self.iconImage).offset(-2 * kWidthFactor);
    make.left.equalTo(self.iconImage).offset(-2 * kWidthFactor);
    make.size.mas_equalTo(CGSizeMake(8 * kWidthFactor, 8 * kWidthFactor));
  }];

  if (!_nameLabel) {
    _nameLabel = [[UILabel alloc] init];
    _nameLabel.textColor = KColor_HighBlue;
    _nameLabel.font = SourceHanSerifBoldFont(14 * kWidthFactor);
  }
  [self.mainContentView addSubview:_nameLabel];

  if (!_timeLabel) {
    _timeLabel = [[UILabel alloc] init];
    _timeLabel.textColor = KColor_textTinyGray;
    _timeLabel.font = [UIFont systemFontOfSize:12 * kWidthFactor];
    _timeLabel.textAlignment = NSTextAlignmentRight;
  }
  [self.mainContentView addSubview:_timeLabel];

  if (!_detailLabel) {
    _detailLabel = [[UILabel alloc] init];
    _detailLabel.textColor = KColor_detailDarkGray;
    _detailLabel.font = SourceHanSerifRegularFont(13 * kWidthFactor);
    _detailLabel.numberOfLines = 1;
    _detailLabel.textAlignment = NSTextAlignmentLeft;
  }
  [self.mainContentView addSubview:_detailLabel];
}

- (void)layoutSubviews {
  [super layoutSubviews];
  for (UIView *subview in self.bottomContainerView.subviews) {
    if (subview.layer.sublayers.count > 0 &&
        [subview.layer.sublayers[0] isKindOfClass:[CAGradientLayer class]]) {
      CAGradientLayer *layer = (CAGradientLayer *)subview.layer.sublayers[0];
      layer.frame = subview.bounds;
    }
  }
}

- (void)updateSelectionCircleWithSelected:(BOOL)selected {
  CGFloat diameter = 18 * kWidthFactor;
  CGRect circleFrame = CGRectMake(0, 0, diameter, diameter);

  NSArray<CALayer *> *oldLayers = [self.selectionCircle.layer.sublayers copy];
  for (CALayer *layer in oldLayers) {
    [layer removeFromSuperlayer];
  }

  if (selected) {
    CAShapeLayer *fillLayer = [CAShapeLayer layer];
    UIBezierPath *fillPath =
        [UIBezierPath bezierPathWithOvalInRect:circleFrame];
    fillLayer.path = fillPath.CGPath;
    fillLayer.fillColor = [UIColor blackColor].CGColor;
    fillLayer.frame = circleFrame;
    [self.selectionCircle.layer addSublayer:fillLayer];
  } else {
    CGFloat lineWidth = 1.5 * kWidthFactor;
    CAShapeLayer *borderLayer = [CAShapeLayer layer];
    UIBezierPath *circlePath = [UIBezierPath
        bezierPathWithOvalInRect:CGRectInset(circleFrame, lineWidth / 2,
                                             lineWidth / 2)];
    borderLayer.path = circlePath.CGPath;
    borderLayer.strokeColor = [UIColor blackColor].CGColor;
    borderLayer.fillColor = [UIColor clearColor].CGColor;
    borderLayer.lineWidth = lineWidth;
    borderLayer.frame = circleFrame;
    [self.selectionCircle.layer addSublayer:borderLayer];
  }
}

- (void)setSelectionMode:(BOOL)selectionMode
                selected:(BOOL)selected
                animated:(BOOL)animated {
  BOOL wasInSelectionMode = self.isInSelectionMode;
  self.isInSelectionMode = selectionMode;

  if (selectionMode && !self.redDotView.hidden) {
    self.wasUnreadBeforeSelection = YES;
    self.redDotView.hidden = YES;
  } else if (!selectionMode && self.wasUnreadBeforeSelection &&
             [self.messageData.status isEqualToString:@"0"]) {
    self.redDotView.hidden = NO;
  }

  CGFloat targetOffset = selectionMode ? 40.0 * kWidthFactor : 0.0;
  void (^updateBlock)(void) = ^{
    [self.mainContentView mas_updateConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.contentView)
          .offset(24.0 * kWidthFactor + targetOffset);
    }];
    [self layoutIfNeeded];
    if (selectionMode) {
      [self updateSelectionCircleWithSelected:selected];
    }
  };

  if (animated && wasInSelectionMode != selectionMode) {
    self.selectionCircle.hidden = YES;
    [UIView animateWithDuration:0.3
                     animations:updateBlock
                     completion:^(BOOL finished) {
                       self.selectionCircle.hidden = !selectionMode;
                       if (selectionMode) {
                         [self updateSelectionCircleWithSelected:selected];
                       }
                     }];
  } else {
    updateBlock();
    self.selectionCircle.hidden = !selectionMode;
    if (selectionMode) {
      [self updateSelectionCircleWithSelected:selected];
    }
  }
}

- (void)setSelectionMode:(BOOL)selectionMode selected:(BOOL)selected {
  [self setSelectionMode:selectionMode selected:selected animated:NO];
}

- (void)hideUnreadIndicator {
  self.wasUnreadBeforeSelection = !self.redDotView.hidden;
  self.redDotView.hidden = YES;
}

- (void)restoreUnreadIndicator {
  if (self.wasUnreadBeforeSelection &&
      [self.messageData.status isEqualToString:@"0"] &&
      !self.isInSelectionMode) {
    self.redDotView.hidden = NO;
  }
}

- (void)setMessageData:(LiuyanModel *)messageData {
  _messageData = messageData;
  self.userid = messageData.userid;
  self.lid = messageData.lid;
  BOOL shouldShowRedDot =
      [messageData.status isEqualToString:@"0"] && !self.isInSelectionMode;
  self.redDotView.hidden = !shouldShowRedDot;

  [_iconImage
      sd_setImageWithURL:[NemoUtil getUrlWithUserSmallIcon:_messageData.avatar]
        placeholderImage:KImage_name(@"empty")];

  _nameLabel.text = _messageData.nickname;
  _timeLabel.text =
      [NemoUtil distanceTimeWithBeforeTime:[_messageData.created doubleValue]];
  _detailLabel.text = _messageData.message;

  [_nameLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.iconImage.mas_right).offset(14 * kWidthFactor);
    make.top.equalTo(self.iconImage.mas_top).offset(4 * kWidthFactor);
    make.right.lessThanOrEqualTo(self.timeLabel.mas_left)
        .offset(-8 * kWidthFactor);
    make.height.mas_greaterThanOrEqualTo(18 * kWidthFactor);
  }];
  [_timeLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.right.equalTo(self.mainContentView).offset(-24 * kWidthFactor);
    make.centerY.equalTo(self.nameLabel);
    make.width.lessThanOrEqualTo(@(100 * kWidthFactor));
    make.height.mas_greaterThanOrEqualTo(13 * kWidthFactor);
  }];
  [_detailLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.iconImage.mas_right).offset(14 * kWidthFactor);
    make.right.equalTo(self.mainContentView).offset(-24 * kWidthFactor);
    make.bottom.equalTo(self.iconImage.mas_bottom).offset(-4 * kWidthFactor);
    make.height.mas_equalTo(18 * kWidthFactor);
  }];

  CGFloat targetOffset = self.isInSelectionMode ? 40.0 * kWidthFactor : 0.0;
  [self.mainContentView mas_updateConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.contentView)
        .offset(24.0 * kWidthFactor + targetOffset);
  }];
  [self layoutIfNeeded];
}

- (void)avatarTapped {
  if ([self.delegate respondsToSelector:@selector(didTapAvatarWithUserId:)]) {
    [self.delegate didTapAvatarWithUserId:self.messageData.userid];
  }
}

- (void)prepareForReuse {
  [super prepareForReuse];
  if (self.isInSelectionMode) {
    self.selectionCircle.hidden = NO;
    self.selectionCircle.backgroundColor = [UIColor clearColor];
  } else {
    self.selectionCircle.hidden = YES;
  }
  CGFloat targetOffset = self.isInSelectionMode ? 40.0 * kWidthFactor : 0.0;
  [self.mainContentView mas_updateConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.contentView)
        .offset(24.0 * kWidthFactor + targetOffset);
  }];

  // 重置底部视图的状态 - 立即隐藏避免闪烁
  self.bottomContainerView.hidden = YES;
  self.bottomContainerView.alpha = 0;
  [self.bottomViewHeightConstraint uninstall];
  [self.bottomContainerView mas_updateConstraints:^(MASConstraintMaker *make) {
    self.bottomViewHeightConstraint = make.height.mas_equalTo(1);
  }];

  [self layoutIfNeeded];
  [self.mainContentView.layer removeAllAnimations];
  [self.bottomContainerView.layer removeAllAnimations];
  // 清除所有子视图上的动画
  for (UIView *subview in self.bottomContainerView.subviews) {
    [subview.layer removeAllAnimations];
  }
}

#pragma mark - Bottom View Methods

- (void)showBottomViewWithText:(NSString *)text {
  NSString *msg = text.length > 0 ? text : self.messageData.message;
  if (msg) {
    CGFloat lineSpacing = 5 * kWidthFactor;
    NSMutableParagraphStyle *paragraphStyle =
        [[NSMutableParagraphStyle alloc] init];
    paragraphStyle.lineSpacing = lineSpacing;
    paragraphStyle.lineBreakMode = self.bottomLabel.lineBreakMode;
    paragraphStyle.alignment = self.bottomLabel.textAlignment;
    NSMutableAttributedString *attributedString =
        [[NSMutableAttributedString alloc] initWithString:msg];
    [attributedString addAttribute:NSParagraphStyleAttributeName
                             value:paragraphStyle
                             range:NSMakeRange(0, [msg length])];
    [attributedString addAttribute:NSFontAttributeName
                             value:self.bottomLabel.font
                             range:NSMakeRange(0, [msg length])];
    self.bottomLabel.attributedText = attributedString;
  } else {
    self.bottomLabel.text = @"";
  }

  // 准备展开前，先隐藏子视图
  for (UIView *subview in self.bottomContainerView.subviews) {
    subview.alpha = 0;
  }

  // 计算所需的高度
  CGFloat estimatedHeight = [self estimatedHeightForText:msg];

  // 先将容器设置为可见
  self.bottomContainerView.hidden = NO;
  self.bottomContainerView.alpha = 1;

  // 确保所有子视图被创建并正确约束
  [self layoutIfNeeded];

  // 动画展开视图高度
  [self.bottomViewHeightConstraint uninstall];
  [self.bottomContainerView mas_updateConstraints:^(MASConstraintMaker *make) {
    self.bottomViewHeightConstraint = make.height.mas_equalTo(estimatedHeight);
  }];

  // 执行展开动画
  [UIView animateWithDuration:0.25
      animations:^{
        [self layoutIfNeeded];
      }
      completion:^(BOOL finished) {
        // 展开后淡入内容
        [UIView animateWithDuration:0.15
                         animations:^{
                           for (UIView *subview in self.bottomContainerView
                                    .subviews) {
                             subview.alpha = 1.0;
                           }
                         }];
      }];
}

- (CGFloat)estimatedHeightForText:(NSString *)text {
  if (!text || text.length == 0) {
    return 100 * kWidthFactor; // 最小高度
  }

  UIFont *font = self.bottomLabel.font ?: [UIFont systemFontOfSize:14];
  CGFloat labelWidth =
      [UIScreen mainScreen].bounds.size.width - 44 * kWidthFactor;

  CGFloat lineSpacing = 5 * kWidthFactor;
  NSMutableParagraphStyle *paragraphStyle =
      [[NSMutableParagraphStyle alloc] init];
  paragraphStyle.lineSpacing = lineSpacing;

  NSMutableAttributedString *attributedText =
      [[NSMutableAttributedString alloc] initWithString:text];
  [attributedText addAttribute:NSParagraphStyleAttributeName
                         value:paragraphStyle
                         range:NSMakeRange(0, text.length)];
  [attributedText addAttribute:NSFontAttributeName
                         value:font
                         range:NSMakeRange(0, text.length)];

  CGRect rect = [attributedText
      boundingRectWithSize:CGSizeMake(labelWidth, CGFLOAT_MAX)
                   options:NSStringDrawingUsesLineFragmentOrigin |
                           NSStringDrawingUsesFontLeading
                   context:nil];

  return MAX(rect.size.height + 100 * kWidthFactor, 100 * kWidthFactor);
}

- (void)hideBottomView {
  // 先将alpha降低避免闪烁
  [UIView animateWithDuration:0.15
      animations:^{
        for (UIView *subview in self.bottomContainerView.subviews) {
          subview.alpha = 0;
        }
        self.bottomContainerView.alpha = 0.8;
      }
      completion:^(BOOL finished) {
        // 再收起容器
        [self.bottomViewHeightConstraint uninstall];
        [self.bottomContainerView
            mas_updateConstraints:^(MASConstraintMaker *make) {
              self.bottomViewHeightConstraint = make.height.mas_equalTo(1);
            }];

        [UIView animateWithDuration:0.25
            animations:^{
              // 动画过程中继续降低alpha，避免闪烁
              self.bottomContainerView.alpha = 0.1;
              [self layoutIfNeeded];
            }
            completion:^(BOOL finished) {
              // 动画结束后完全隐藏
              self.bottomContainerView.alpha = 0;
              self.bottomContainerView.hidden = YES;
              self.bottomLabel.attributedText = nil;
            }];
      }];
}

- (void)reportButtonClicked:(UIButton *)sender {
  PopoverView *popoverView = [PopoverView popoverView];
  popoverView.showShade = YES;
  __weak typeof(self) weakSelf = self;
  PopoverAction *reportAction = [PopoverAction
      actionWithImage:[UIImage imageNamed:@"Report"]
                title:@"举报"
              handler:^(PopoverAction *action) {
                [[NSNotificationCenter defaultCenter]
                    postNotificationName:@"LiuyanDetailCellReportClicked"
                                  object:weakSelf
                                userInfo:@{
                                  @"userid" : weakSelf.userid ?: @"",
                                  @"lid" : weakSelf.lid ?: @""
                                }];
              }];
  [popoverView showToView:sender withActions:@[ reportAction ]];
}

- (void)replyButtonClicked:(UIButton *)sender {
  [USERDEFAULT removeObjectForKey:ContenText];
  [USERDEFAULT synchronize];
  __weak typeof(self) weakSelf = self;
  [XHInputView showWithStyle:InputViewStyleDefault
      configurationBlock:^(XHInputView *inputView) {
        inputView.delegate = nil;
        inputView.placeholder = @"请输入你的留言";
        inputView.maxCount = 200;
        inputView.backgroundColor = [UIColor colorWithWhite:0.95 alpha:1.0];
      }
      sendBlock:^BOOL(NSString *_Nullable text, BOOL sendIM) {
        if (text.length == 0) {
          return NO;
        }
        [HUD show];
        [LiuyanCreateModel message:text
            to:weakSelf.userid
            success:^(NSDictionary *resultObject) {
              [HUD dissmiss];
              LiuyanCreateModel *member =
                  [LiuyanCreateModel mj_objectWithKeyValues:resultObject];
              if ([member.success boolValue]) {
                [[NSNotificationCenter defaultCenter]
                    postNotificationName:@"LiuyanDetailCellReplySuccess"
                                  object:weakSelf
                                userInfo:@{@"message" : @"留言成功"}];
              } else {
                [[NSNotificationCenter defaultCenter]
                    postNotificationName:@"LiuyanDetailCellReplyFailed"
                                  object:weakSelf
                                userInfo:@{
                                  @"message" : member.msg ?: @"留言失败"
                                }];
              }
            }
            failure:^(NSError *requestErr) {
              [HUD dissmiss];
              [[NSNotificationCenter defaultCenter]
                  postNotificationName:@"LiuyanDetailCellReplyFailed"
                                object:weakSelf
                              userInfo:@{
                                @"message" : @"数据有误,请检查网络后重试"
                              }];
            }];
        return YES;
      }];
}

- (void)setupBottomCardUI {
  self.bottomContainerView.backgroundColor = [UIColor colorWithRed:0.96
                                                             green:0.96
                                                              blue:0.96
                                                             alpha:1.0];
  self.bottomContainerView.clipsToBounds = YES;

  // 顶部内部阴影效果
  UIView *topInnerShadowView = [[UIView alloc] init];
  topInnerShadowView.userInteractionEnabled = NO;
  [self.bottomContainerView addSubview:topInnerShadowView];
  [topInnerShadowView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.right.top.equalTo(self.bottomContainerView);
    make.height.mas_equalTo(8 * kWidthFactor);
  }];
  CAGradientLayer *topInnerShadow = [CAGradientLayer layer];
  topInnerShadow.colors = @[
    (__bridge id)[UIColor colorWithWhite:0.0 alpha:0.15].CGColor,
    (__bridge id)[UIColor clearColor].CGColor
  ];
  topInnerShadow.locations = @[ @0.0, @0.7 ]; // 让阴影渐变更加集中在顶部
  topInnerShadow.startPoint = CGPointMake(0.5, 0.0);
  topInnerShadow.endPoint = CGPointMake(0.5, 1.0);
  topInnerShadow.frame =
      CGRectMake(0, 0, self.frame.size.width, 8 * kWidthFactor);
  [topInnerShadowView.layer addSublayer:topInnerShadow];

  // 底部内部阴影效果
  UIView *bottomInnerShadowView = [[UIView alloc] init];
  bottomInnerShadowView.userInteractionEnabled = NO;
  [self.bottomContainerView addSubview:bottomInnerShadowView];
  [bottomInnerShadowView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.right.bottom.equalTo(self.bottomContainerView);
    make.height.mas_equalTo(8 * kWidthFactor);
  }];
  CAGradientLayer *bottomInnerShadow = [CAGradientLayer layer];
  bottomInnerShadow.colors = @[
    (__bridge id)[UIColor clearColor].CGColor,
    (__bridge id)[UIColor colorWithWhite:0.0 alpha:0.10].CGColor
  ];
  bottomInnerShadow.locations = @[ @0.3, @1.0 ]; // 让阴影渐变更加集中在底部
  bottomInnerShadow.startPoint = CGPointMake(0.5, 0.0);
  bottomInnerShadow.endPoint = CGPointMake(0.5, 1.0);
  bottomInnerShadow.frame =
      CGRectMake(0, 0, self.frame.size.width, 8 * kWidthFactor);
  [bottomInnerShadowView.layer addSublayer:bottomInnerShadow];

  // 完全重写更多按钮的创建方式
  UIButton *settingBt = [UIButton buttonWithType:UIButtonTypeCustom];
  UIImage *moreImage = [UIImage imageNamed:@"more-black"];
  [settingBt setImage:moreImage forState:UIControlStateNormal];
  settingBt.contentMode = UIViewContentModeScaleAspectFit;
  settingBt.imageView.contentMode = UIViewContentModeScaleAspectFit;
  settingBt.contentHorizontalAlignment =
      UIControlContentHorizontalAlignmentCenter;
  settingBt.contentVerticalAlignment = UIControlContentVerticalAlignmentCenter;
  [self.bottomContainerView addSubview:settingBt];
  [settingBt mas_makeConstraints:^(MASConstraintMaker *make) {
    make.right.equalTo(self.bottomContainerView).offset(-20 * kWidthFactor);
    make.top.equalTo(self.bottomContainerView)
        .offset(12 * kWidthFactor); // 从20调整为12，让按钮靠上一些
    make.width.mas_equalTo(24 * kWidthFactor);
    make.height.mas_equalTo(24 * kWidthFactor);
  }];
  [settingBt addTarget:self
                action:@selector(reportButtonClicked:)
      forControlEvents:UIControlEventTouchUpInside];

  self.replyButton = [UIButton buttonWithType:UIButtonTypeCustom];
  [self.replyButton setTitle:@"  回复" forState:UIControlStateNormal];
  [self.replyButton setTitleColor:KColor_White forState:UIControlStateNormal];
  self.replyButton.titleLabel.font =
      SourceHanSerifMediumFont(12 * kWidthFactor);
  self.replyButton.backgroundColor = KColor_HighBlack;
  self.replyButton.layer.cornerRadius = 12 * kWidthFactor;
  self.replyButton.layer.masksToBounds = YES;
  [self.bottomContainerView addSubview:self.replyButton];
  [self.replyButton mas_makeConstraints:^(MASConstraintMaker *make) {
    make.right.equalTo(self.bottomContainerView).offset(-16 * kWidthFactor);
    make.bottom.equalTo(self.bottomContainerView).offset(-16 * kWidthFactor);
    make.height.mas_equalTo(24 * kWidthFactor);
    make.width.mas_equalTo(56 * kWidthFactor);
  }];
  [self.replyButton addTarget:self
                       action:@selector(replyButtonClicked:)
             forControlEvents:UIControlEventTouchUpInside];
  UIImageView *replyIcon =
      [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"回复w2"]];
  replyIcon.contentMode = UIViewContentModeScaleAspectFit;
  [self.replyButton addSubview:replyIcon];
  [replyIcon mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.replyButton).offset(8 * kWidthFactor);
    make.centerY.equalTo(self.replyButton);
    make.width.height.equalTo(@(12 * kWidthFactor));
  }];
  self.replyButton.titleEdgeInsets =
      UIEdgeInsetsMake(0, 8 * kWidthFactor, 0, 0);

  self.bottomLabel = [[UILabel alloc] init];
  self.bottomLabel.textAlignment = NSTextAlignmentLeft;
  self.bottomLabel.textColor = [UIColor blackColor];
  self.bottomLabel.font = [UIFont systemFontOfSize:14];
  self.bottomLabel.numberOfLines = 0;
  [self.bottomContainerView addSubview:self.bottomLabel];
  [self.bottomLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.bottomContainerView).offset(24 * kWidthFactor);
    make.right.equalTo(self.bottomContainerView).offset(-20 * kWidthFactor);
    make.top.equalTo(settingBt.mas_bottom)
        .offset(24 * kWidthFactor)
        .priorityMedium();
    make.bottom.equalTo(self.replyButton.mas_top)
        .offset(-20 * kWidthFactor)
        .priorityMedium();
  }];
}

@end