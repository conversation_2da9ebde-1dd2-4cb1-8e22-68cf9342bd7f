#import "MessageTableViewCell.h"

@interface MessageTableViewCell ()

@end

@implementation MessageTableViewCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style
              reuseIdentifier:(NSString *)reuseIdentifier {
  self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
  if (self) {
    [self loadCellView];
    [self setSelectionStyle:(UITableViewCellSelectionStyleNone)];
  }
  return self;
}

- (void)loadCellView {
  if (!_iconImage) {
    _iconImage = [[UIImageView alloc]
        initWithFrame:CGRectMake(16 * kMainTemp, 10 * kMainTemp, 38 * kMainTemp,
                                 38 * kMainTemp)];
  }
  UIBezierPath *maskPath =
      [UIBezierPath bezierPathWithRoundedRect:_iconImage.bounds
                            byRoundingCorners:UIRectCornerAllCorners
                                  cornerRadii:_iconImage.bounds.size];
  CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
  maskLayer.frame = _iconImage.bounds;
  maskLayer.path = maskPath.CGPath;
  _iconImage.layer.mask = maskLayer;
  [self.contentView addSubview:_iconImage];
  [_iconImage mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.contentView).offset(16 * kMainTemp);
    make.centerY.equalTo(self.contentView);
    make.size.mas_equalTo(CGSizeMake(38 * kMainTemp, 38 * kMainTemp));
  }];
  if (!_mesNumLabel) {
    _mesNumLabel = [[UILabel alloc] init];
  }
  _mesNumLabel.layer.masksToBounds = YES;
  _mesNumLabel.layer.cornerRadius = 7 * kMainTemp;
  _mesNumLabel.backgroundColor = KColor_Red;
  _mesNumLabel.textColor = KColor_White;
  _mesNumLabel.font = [UIFont systemFontOfSize:10 * kMainTemp];
  _mesNumLabel.textAlignment = NSTextAlignmentCenter;

  if (!_nameLabel) {
    _nameLabel = [[UILabel alloc] init];
  }
  [_nameLabel setTextColor:KColor_Black];
  _nameLabel.font = [UIFont systemFontOfSize:16 * kMainTemp];
  [self.contentView addSubview:self.nameLabel];
  [self.nameLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.contentView).offset(66 * kMainTemp);
    make.top.equalTo(self.contentView).offset(12 * kMainTemp);
    make.size.mas_equalTo(CGSizeMake(200 * kMainTemp, 18 * kMainTemp));
  }];

  if (!_timeLabel) {
    _timeLabel = [[UILabel alloc] init];
  }
  [_timeLabel setTextColor:KColor_Gray];
  _timeLabel.textAlignment = NSTextAlignmentRight;
  _timeLabel.font = [UIFont systemFontOfSize:12 * kMainTemp];
  [self.contentView addSubview:self.timeLabel];
  [self.timeLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.right.equalTo(self.contentView.mas_right).offset(-16 * kMainTemp);
    make.centerY.equalTo(self.nameLabel);
    make.size.mas_equalTo(CGSizeMake(200 * kMainTemp, 18 * kMainTemp));
  }];

  if (!_titleDetailsLabel) {
    _titleDetailsLabel = [[UILabel alloc] init];
  }
  _titleDetailsLabel.font = [UIFont systemFontOfSize:14 * kMainTemp];
  [_titleDetailsLabel setTextColor:KColor_Gray];
  [_titleDetailsLabel setNumberOfLines:0];
  [self.contentView addSubview:_titleDetailsLabel];
  [_titleDetailsLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.contentView).offset(66 * kMainTemp);
    make.top.equalTo(self.contentView).offset(36 * kMainTemp);
    make.size.mas_equalTo(CGSizeMake(240 * kMainTemp, +16 * kMainTemp));
  }];

  UILabel *linedownLabel = [[UILabel alloc] init];
  linedownLabel.backgroundColor = KColor_LineGray;
  [self.contentView addSubview:linedownLabel];
  [linedownLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.contentView);
    make.bottom.equalTo(self.contentView.mas_bottom);
    make.size.mas_equalTo(CGSizeMake(kMainWidth, 0.8));
  }];
}

- (void)loadMessagCountAction:(NSInteger)messageCount {
  if (messageCount == 0) {
    [self.mesNumLabel removeFromSuperview];
  } else {
    NSInteger tempCC = 0;
    if (CurrentUserConfig.unreadMessageCount > 0) {
      tempCC = messageCount - CurrentUserConfig.unreadMessageCount;
    } else {
      tempCC = messageCount;
    }
    if (tempCC > 0 && tempCC < 10) {
      self.mesNumLabel.text = [NSString stringWithFormat:@"%ld", tempCC];
      [self.contentView addSubview:self.mesNumLabel];
      [self.mesNumLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.iconImage.mas_top).offset(3 * kMainTemp);
        make.centerX.equalTo(self.iconImage.mas_right).offset(-3 * kMainTemp);
        make.size.mas_equalTo(CGSizeMake(14 * kMainTemp, 14 * kMainTemp));
      }];
    }
    if (tempCC >= 10 && tempCC < 100) {
      self.mesNumLabel.text = [NSString stringWithFormat:@"%ld", tempCC];
      [self.contentView addSubview:self.mesNumLabel];
      [self.mesNumLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.iconImage.mas_top).offset(3 * kMainTemp);
        make.centerX.equalTo(self.iconImage.mas_right).offset(-3 * kMainTemp);
        make.size.mas_equalTo(CGSizeMake(18 * kMainTemp, 14 * kMainTemp));
      }];
    }
    if (tempCC >= 100) {
      self.mesNumLabel.text = @"99";
      [self.contentView addSubview:self.mesNumLabel];
      [self.mesNumLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.iconImage.mas_top).offset(3 * kMainTemp);
        make.centerX.equalTo(self.iconImage.mas_right).offset(-3 * kMainTemp);
        make.size.mas_equalTo(CGSizeMake(18 * kMainTemp, 14 * kMainTemp));
      }];
    }
  }
}

@end
