#import "ShudongTableViewCell.h"
#import "_2hz-Swift.h"

@interface ShudongTableViewCell ()
@property(nonatomic, strong) UILabel *nameLabel;
@property(nonatomic, strong) UILabel *timeLabel;
@property(nonatomic, strong) UILabel *detailLabel;
@property(nonatomic, strong) UIView *detailView;
@property(nonatomic, strong) UIButton *createBt;
@property(nonatomic, strong) UIImageView *avatarImageView; // 添加头像视图

@end

@implementation ShudongTableViewCell
- (instancetype)initWithStyle:(UITableViewCellStyle)style
              reuseIdentifier:(NSString *)reuseIdentifier {
  self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];

  if (self) {
    self.backgroundColor = [UIColor whiteColor]; // 改为白色背景而非透明
    self.contentView.backgroundColor = [UIColor whiteColor];

    [self loadCellView];
    [self setSelectionStyle:(UITableViewCellSelectionStyleNone)];
  }

  return self;
}

- (void)loadCellView {
  if (!_avatarImageView) {
    _avatarImageView = [[UIImageView alloc] init];
    _avatarImageView.layer.cornerRadius = 25 * kWidthFactor;
    _avatarImageView.layer.masksToBounds = YES;
    _avatarImageView.layer.borderWidth = 1.0;
    _avatarImageView.layer.borderColor = [UIColor colorWithRed:240 / 255.0
                                                         green:240 / 255.0
                                                          blue:240 / 255.0
                                                         alpha:1.0]
                                             .CGColor;

    _avatarImageView.contentMode = UIViewContentModeScaleAspectFill;
    [self.contentView addSubview:_avatarImageView];
  }
  if (!_nameLabel) {
    _nameLabel = [[UILabel alloc] init];
  }

  [_nameLabel setTextColor:KColor_HighBlack];
  _nameLabel.font = SourceHanSerifRegularFont(16 * kWidthFactor);
  [self.contentView addSubview:self.nameLabel];

  if (!_timeLabel) {
    _timeLabel = [[UILabel alloc] init];
  }

  [_timeLabel setTextColor:KColor_textTinyGray];
  _timeLabel.textAlignment = NSTextAlignmentRight;
  _timeLabel.font = [UIFont systemFontOfSize:12 * kWidthFactor];
  [self.contentView addSubview:self.timeLabel];

  if (!_detailLabel) {
    _detailLabel = [[UILabel alloc] init];
  }

  _detailLabel.textAlignment = NSTextAlignmentLeft;
  [_detailLabel setTextColor:KColor_detailDarkGray];
  _detailLabel.font = SourceHanSerifRegularFont(12 * kWidthFactor);
  _detailLabel.numberOfLines = 2;
}

- (void)setMessageData:(ShudongModel *)messageData {
  _messageData = messageData;
  [self.contentView mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self);
    make.right.equalTo(self);
    make.top.equalTo(self);
    make.bottom.equalTo(self);
  }];
  _nameLabel.text = _messageData.whale;

  // 设置头像
  NSString *avatarName =
      [NSString stringWithFormat:@"whale_%@.jpg", _messageData.avatar];
  UIImage *avatarImage = [UIImage imageNamed:avatarName];
  _avatarImageView.image = avatarImage;

  // 头像位置：左侧，垂直居中
  [self.avatarImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.contentView).offset(15 * kWidthFactor);
    make.centerY.equalTo(self.contentView);
    make.size.mas_equalTo(CGSizeMake(50 * kWidthFactor, 50 * kWidthFactor));
  }];

  // 用户名位置：头像右侧
  [self.nameLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.avatarImageView.mas_right).offset(10 * kWidthFactor);
    make.top.equalTo(self.contentView).offset(14 * kWidthFactor);
    make.size.mas_equalTo(CGSizeMake(120 * kWidthFactor, 20 * kWidthFactor));
  }];

  _nameLabel.textColor = KColor_HighBlack;

  _timeLabel.text =
      [NemoUtil distanceTimeWithBeforeTime:[_messageData.created doubleValue]];
  [self.timeLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.right.equalTo(self.contentView).offset(-20 * kWidthFactor);
    make.centerY.equalTo(self.nameLabel);
    make.size.mas_equalTo(CGSizeMake(80 * kWidthFactor, 13 * kWidthFactor));
  }];

  // 改为带行间距的设置方式
  _detailLabel.text = _messageData.message;
  CGFloat lineSpacing = 5 * kWidthFactor;
  NSMutableParagraphStyle *paragraphStyle =
      [[NSMutableParagraphStyle alloc] init];
  paragraphStyle.lineSpacing = lineSpacing;
  paragraphStyle.lineBreakMode = _detailLabel.lineBreakMode;
  paragraphStyle.alignment = _detailLabel.textAlignment;

  NSMutableAttributedString *attributedString =
      [[NSMutableAttributedString alloc] initWithString:_detailLabel.text];
  [attributedString addAttribute:NSParagraphStyleAttributeName
                           value:paragraphStyle
                           range:NSMakeRange(0, [_detailLabel.text length])];
  // 保留原来的字体
  [attributedString addAttribute:NSFontAttributeName
                           value:_detailLabel.font
                           range:NSMakeRange(0, [_detailLabel.text length])];
  _detailLabel.attributedText = attributedString;
  [self.contentView addSubview:self.detailLabel];
  [self.detailLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.nameLabel);
    make.top.equalTo(self.nameLabel.mas_bottom).offset(8 * kWidthFactor);
    make.right.equalTo(self.contentView).offset(-20 * kWidthFactor);
    make.height.mas_equalTo(36 * kWidthFactor);
  }];
}

- (void)awakeFromNib {
  [super awakeFromNib];
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
  [super setSelected:selected animated:animated];
}

@end
