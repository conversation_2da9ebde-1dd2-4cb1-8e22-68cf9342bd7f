#import "MessageNotificationTableViewCell.h"
#import "_2hz-Swift.h"
#import <QuartzCore/QuartzCore.h>
@interface MessageNotificationTableViewCell ()
@property(nonatomic, strong) UIImageView *iconImage;
@property(nonatomic, strong) UILabel *nameLabel;
@property(nonatomic, strong) UILabel *timeLabel;
@property(nonatomic, strong) UILabel *typeLabel;
@property(nonatomic, strong) UILabel *detailLabel;
@property(nonatomic, strong) UIImageView *dyImage;
@property(nonatomic, strong) UIView *redDotView;
@property(nonatomic, strong) UIView *redCircleView;
@property(nonatomic, strong) UIView *selectionCircle;
@property(nonatomic, assign) BOOL isInSelectionMode;
@property(nonatomic, strong) UIView *contentContainer;
@property(nonatomic, assign) BOOL wasUnreadBeforeSelection;

@end

@implementation MessageNotificationTableViewCell
- (instancetype)initWithStyle:(UITableViewCellStyle)style
              reuseIdentifier:(NSString *)reuseIdentifier {
  self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
  if (self) {
    self.isInSelectionMode = NO;

    self.contentContainer = [[UIView alloc] init];
    [self.contentView addSubview:self.contentContainer];

    [self.contentContainer mas_makeConstraints:^(MASConstraintMaker *make) {
      make.top.bottom.equalTo(self.contentView);
      make.right.equalTo(self.contentView);
      make.left.equalTo(self.contentView).offset(24 * kWidthFactor);
    }];

    [self loadCellView];

    self.selectionCircle = [[UIView alloc] init];
    self.selectionCircle.backgroundColor = [UIColor clearColor];
    self.selectionCircle.layer.borderColor = [UIColor blackColor].CGColor;
    self.selectionCircle.layer.borderWidth = 1.0;
    self.selectionCircle.layer.cornerRadius = 9 * kWidthFactor;
    self.selectionCircle.hidden = YES;
    [self.contentView addSubview:self.selectionCircle];

    [self.selectionCircle mas_makeConstraints:^(MASConstraintMaker *make) {
      make.centerY.equalTo(self.contentView);
      make.left.equalTo(self.contentView).offset(24 * kWidthFactor);
      make.width.height.equalTo(@(18 * kWidthFactor));
    }];

    UILongPressGestureRecognizer *longPress =
        [[UILongPressGestureRecognizer alloc]
            initWithTarget:self
                    action:@selector(handleCellLongPress:)];
    longPress.minimumPressDuration = 0.5;
    [self addGestureRecognizer:longPress];
    [self setSelectionStyle:(UITableViewCellSelectionStyleNone)];
  }
  [NOTIFICENTER addObserver:self
                   selector:@selector(hideUnreadIndicator)
                       name:@"HideAllUnreadIndicators"
                     object:nil];
  [NOTIFICENTER addObserver:self
                   selector:@selector(restoreUnreadIndicator)
                       name:@"RestoreUnreadIndicators"
                     object:nil];
  return self;
}

- (void)handleCellLongPress:(UILongPressGestureRecognizer *)gesture {
  if (gesture.state == UIGestureRecognizerStateBegan) {
    NSLog(@"Cell long press detected");
    [NOTIFICENTER postNotificationName:@"NotificationCellLongPressed"
                                object:nil];
  }
}

- (void)layoutSubviews {
  [super layoutSubviews];
  if (!self.redCircleView.hidden) {
    [self updateRedCircleGradient];
  }
}

- (void)loadCellView {
  if (!_iconImage) {
    _iconImage = [[UIImageView alloc] init];
  }
  UIBezierPath *maskPath = [UIBezierPath
      bezierPathWithRoundedRect:CGRectMake(0, 0, 48 * kWidthFactor,
                                           48 * kWidthFactor)
              byRoundingCorners:UIRectCornerAllCorners
                    cornerRadii:CGSizeMake(24 * kWidthFactor,
                                           24 * kWidthFactor)];
  CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
  maskLayer.frame = CGRectMake(0, 0, 48 * kWidthFactor, 48 * kWidthFactor);
  maskLayer.path = maskPath.CGPath;
  _iconImage.layer.mask = maskLayer;
  [self.contentContainer addSubview:_iconImage];
  [_iconImage mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.contentContainer);
    make.centerY.equalTo(self.contentContainer);
    make.size.mas_equalTo(CGSizeMake(48 * kWidthFactor, 48 * kWidthFactor));
  }];

  if (!_redDotView) {
    _redDotView = [[UIView alloc] init];
    if ([CurrentUser.gender isEqualToString:@"1"]) {
      _redDotView.backgroundColor = KColor_switchLightBlue;
    } else if ([CurrentUser.gender isEqualToString:@"2"]) {
      _redDotView.backgroundColor = KColor_switchLightPink;
    } else {
      _redDotView.backgroundColor = [UIColor systemBlueColor];
    }
    _redDotView.layer.cornerRadius = 4 * kWidthFactor;
    _redDotView.hidden = YES;
  }
  [self.contentContainer addSubview:_redDotView];
  [_redDotView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(self.iconImage).offset(-2 * kWidthFactor);
    make.left.equalTo(self.iconImage).offset(-2 * kWidthFactor);
    make.size.mas_equalTo(CGSizeMake(8 * kWidthFactor, 8 * kWidthFactor));
  }];

  if (!_nameLabel) {
    _nameLabel = [[UILabel alloc] init];
    _nameLabel.textColor = KColor_HighBlue;
    _nameLabel.font = SourceHanSerifBoldFont(14 * kWidthFactor);
  }
  [self.contentContainer addSubview:_nameLabel];

  if (!_typeLabel) {
    _typeLabel = [[UILabel alloc] init];
    _typeLabel.textColor = KColor_detailDarkGray;
    _typeLabel.font = SourceHanSerifRegularFont(13 * kWidthFactor);
  }
  [self.contentContainer addSubview:_typeLabel];

  if (!_timeLabel) {
    _timeLabel = [[UILabel alloc] init];
    _timeLabel.textColor = KColor_textTinyGray;
    _timeLabel.font = [UIFont systemFontOfSize:12 * kWidthFactor];
    _timeLabel.textAlignment = NSTextAlignmentLeft;
  }
  [self.contentContainer addSubview:_timeLabel];

  if (!_detailLabel) {
    _detailLabel = [[UILabel alloc] init];
    _detailLabel.textColor = KColor_textTinyGray;
    _detailLabel.font = SourceHanSerifRegularFont(12 * kWidthFactor);
    _detailLabel.numberOfLines = 1;
    _detailLabel.textAlignment = NSTextAlignmentRight;
  }
  [self.contentContainer addSubview:_detailLabel];
  _detailLabel.hidden = YES;

  if (!_dyImage) {
    _dyImage = [[UIImageView alloc] init];
    _dyImage.layer.cornerRadius = 4 * kWidthFactor;
    _dyImage.layer.masksToBounds = YES;
    _dyImage.contentMode = UIViewContentModeScaleAspectFill;
  }
  [self.contentContainer addSubview:_dyImage];
  _dyImage.hidden = YES;

  if (!_redCircleView) {
    _redCircleView = [[UIView alloc] init];
    _redCircleView.backgroundColor = [UIColor clearColor];
    _redCircleView.hidden = YES;
  }
  [self.contentContainer addSubview:_redCircleView];

  [_redCircleView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.center.equalTo(self.iconImage);
    make.width.height.equalTo(self.iconImage).offset(12 * kWidthFactor);
  }];

  _redCircleView.layer.cornerRadius =
      (48 * kWidthFactor + 12 * kWidthFactor) / 2;
}

- (void)updateSelectionCircleWithSelected:(BOOL)selected {
  CGFloat diameter = 18 * kWidthFactor;
  CGRect circleFrame = CGRectMake(0, 0, diameter, diameter);

  NSArray<CALayer *> *oldLayers = [self.selectionCircle.layer.sublayers copy];
  for (CALayer *layer in oldLayers) {
    [layer removeFromSuperlayer];
  }

  if (selected) {
    CAShapeLayer *fillLayer = [CAShapeLayer layer];
    UIBezierPath *fillPath =
        [UIBezierPath bezierPathWithOvalInRect:circleFrame];
    fillLayer.path = fillPath.CGPath;
    fillLayer.fillColor = [UIColor blackColor].CGColor;
    fillLayer.frame = circleFrame;
    [self.selectionCircle.layer addSublayer:fillLayer];
  } else {
    CGFloat lineWidth = 1.5 * kWidthFactor;
    CAShapeLayer *borderLayer = [CAShapeLayer layer];
    UIBezierPath *circlePath = [UIBezierPath
        bezierPathWithOvalInRect:CGRectInset(circleFrame, lineWidth / 2,
                                             lineWidth / 2)];
    borderLayer.path = circlePath.CGPath;
    borderLayer.strokeColor = [UIColor blackColor].CGColor;
    borderLayer.fillColor = [UIColor clearColor].CGColor;
    borderLayer.lineWidth = lineWidth;
    borderLayer.frame = circleFrame;
    [self.selectionCircle.layer addSublayer:borderLayer];
  }
}

- (void)setSelectionMode:(BOOL)selectionMode selected:(BOOL)selected {
  BOOL wasInSelectionMode = self.isInSelectionMode;
  self.isInSelectionMode = selectionMode;

  if (selectionMode && !self.redDotView.hidden) {
    self.wasUnreadBeforeSelection = YES;
    self.redDotView.hidden = YES;
  } else if (!selectionMode && self.wasUnreadBeforeSelection &&
             [self.messageData.status isEqualToString:@"0"]) {
    self.redDotView.hidden = NO;
  }

  CGFloat targetOffset = selectionMode ? 40.0 * kWidthFactor : 0.0;

  if (wasInSelectionMode != selectionMode) {
    [self.contentContainer.layer removeAllAnimations];
    [self layoutIfNeeded];

    if (selectionMode) {
      self.selectionCircle.hidden = YES;
      [UIView animateWithDuration:0.3
          animations:^{
            [self.contentContainer
                mas_updateConstraints:^(MASConstraintMaker *make) {
                  make.left.equalTo(self.contentView)
                      .offset(24.0 * kWidthFactor + targetOffset);
                }];
            [self layoutIfNeeded];
          }
          completion:^(BOOL finished) {
            self.selectionCircle.hidden = NO;
            [self updateSelectionCircleWithSelected:selected];
          }];
    } else {
      self.selectionCircle.hidden = YES;
      [UIView animateWithDuration:0.3
                       animations:^{
                         [self.contentContainer
                             mas_updateConstraints:^(MASConstraintMaker *make) {
                               make.left.equalTo(self.contentView)
                                   .offset(24.0 * kWidthFactor + targetOffset);
                             }];
                         [self layoutIfNeeded];
                       }];
    }
  } else {
    [self.contentContainer mas_updateConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.contentView)
          .offset(24.0 * kWidthFactor + targetOffset);
    }];
    self.selectionCircle.hidden = !selectionMode;
    if (selectionMode) {
      [self updateSelectionCircleWithSelected:selected];
    }
  }
}

- (void)updateRedCircleGradient {
  for (CALayer *layer in self.redCircleView.layer.sublayers) {
    [layer removeFromSuperlayer];
  }

  CAGradientLayer *gradientLayer = [CAGradientLayer layer];
  gradientLayer.frame = self.redCircleView.bounds;

  UIColor *deepGreen = KColor_HighBlack;

  gradientLayer.colors = @[
    (id)deepGreen.CGColor, (id)[deepGreen colorWithAlphaComponent:1.0].CGColor,
    (id)[deepGreen colorWithAlphaComponent:0.0].CGColor
  ];

  gradientLayer.locations = @[ @0.0, @0.7, @1.0 ];

  gradientLayer.startPoint = CGPointMake(0.5, 0.5);
  gradientLayer.endPoint = CGPointMake(1.0, 1.0);
  gradientLayer.type = kCAGradientLayerRadial;

  CAShapeLayer *maskLayer = [CAShapeLayer layer];
  CGRect bounds = self.redCircleView.bounds;
  UIBezierPath *path = [UIBezierPath bezierPathWithOvalInRect:bounds];

  CGRect innerRect = CGRectInset(bounds, 6 * kWidthFactor, 6 * kWidthFactor);
  UIBezierPath *innerPath = [UIBezierPath bezierPathWithOvalInRect:innerRect];
  [path appendPath:innerPath];

  maskLayer.fillRule = kCAFillRuleEvenOdd;
  maskLayer.path = path.CGPath;

  gradientLayer.mask = maskLayer;

  [self.redCircleView.layer addSublayer:gradientLayer];
}

- (void)handleCellClick {
  BOOL wasUnread = [self.messageData.status isEqualToString:@"0"];

  self.redDotView.hidden = YES;

  if (self.messageData) {
    self.messageData.status = @"1";
  }

  if (wasUnread && self.delegate &&
      [self.delegate respondsToSelector:@selector(messageCell:
                                            didMessageStatusChanged:)]) {
    [self.delegate messageCell:self didMessageStatusChanged:self.messageData];
  }
}

- (void)setMessageData:(MessageNotificationModelResult *)messageData {
  _messageData = messageData;
  BOOL shouldShowRedDot =
      [messageData.status isEqualToString:@"0"] && !self.isInSelectionMode;
  self.redDotView.hidden = !shouldShowRedDot;

  [_iconImage
      sd_setImageWithURL:[NemoUtil getUrlWithUserSmallIcon:_messageData.avatar]
        placeholderImage:KImage_name(@"empty")];

  _nameLabel.text = _messageData.nickName;
  _nameLabel.textColor = KColor_HighBlue;
  if (messageData.colorFont != nil) {
    [_nameLabel applyGradientWithType:messageData.colorFont.type
                           expireTime:messageData.colorFont.expire_time];
  }

  _timeLabel.text =
      [NemoUtil distanceTimeWithBeforeTime:[_messageData.time doubleValue]];

  if ([_messageData.type isEqualToString:@"1"]) {
    _typeLabel.text = @"赞了你的动态";
  } else if ([_messageData.type isEqualToString:@"2"]) {
    _typeLabel.text = @"评论了你的动态";
  } else if ([_messageData.type isEqualToString:@"3"]) {
    _typeLabel.text = @"关注了你";
  } else if ([_messageData.type isEqualToString:@"4"]) {
    _typeLabel.text = @"评论了你的声波";
  } else if ([_messageData.type isEqualToString:@"7"]) {
    _typeLabel.text = @"提及了你";
  }

  [UIView performWithoutAnimation:^{
    [_nameLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.iconImage.mas_right).offset(14 * kWidthFactor);
      make.top.equalTo(self.iconImage.mas_top).offset(4 * kWidthFactor);
      make.height.mas_equalTo(18 * kWidthFactor);
    }];

    [self.typeLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.nameLabel);
      make.bottom.equalTo(self.iconImage.mas_bottom).offset(-4 * kWidthFactor);
      make.size.mas_equalTo(CGSizeMake(120 * kWidthFactor, 13 * kWidthFactor));
    }];

    [self.timeLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.nameLabel.mas_right).offset(12 * kWidthFactor);
      make.centerY.equalTo(self.nameLabel);
      make.width.mas_equalTo(100 * kWidthFactor);
      make.height.equalTo(self.nameLabel);
    }];

    self.detailLabel.hidden = YES;
    self.dyImage.hidden = YES;

    if ([_messageData.images isEqualToString:@""]) {
      self.detailLabel.text = _messageData.content;
      self.detailLabel.hidden = NO;

      [self.detailLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.contentContainer).offset(-24 * kWidthFactor);
        make.centerY.equalTo(self.typeLabel);
        make.size.mas_equalTo(
            CGSizeMake(110 * kWidthFactor, 30 * kWidthFactor));
      }];
    } else {
      [self.dyImage
          sd_setImageWithURL:[NemoUtil
                                 getUrlWithUserSmallIcon:
                                     [NemoUtil
                                         stringChangeArray:_messageData.images]
                                         .firstObject]
            placeholderImage:KImage_name(@"empty")];
      self.dyImage.hidden = NO;

      [self.dyImage mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.contentContainer).offset(-24 * kWidthFactor);
        make.top.equalTo(self.iconImage.mas_top);
        make.bottom.equalTo(self.iconImage.mas_bottom);
        make.width.equalTo(self.dyImage.mas_height);
      }];
    }

    [self layoutIfNeeded];
  }];
}

- (void)hideUnreadIndicator {
  self.wasUnreadBeforeSelection = !self.redDotView.hidden;
  self.redDotView.hidden = YES;
}

- (void)restoreUnreadIndicator {
  if (self.wasUnreadBeforeSelection &&
      [self.messageData.status isEqualToString:@"0"] &&
      !self.isInSelectionMode) {
    self.redDotView.hidden = NO;
  }
}

- (void)dealloc {
  [NOTIFICENTER removeObserver:self];
}

- (void)prepareForReuse {
  [super prepareForReuse];
  if (self.isInSelectionMode) {
    self.selectionCircle.hidden = NO;
    self.selectionCircle.backgroundColor = [UIColor clearColor];
  } else {
    self.selectionCircle.hidden = YES;
  }
  [self.contentContainer.layer removeAllAnimations];
}

- (void)awakeFromNib {
}

@end