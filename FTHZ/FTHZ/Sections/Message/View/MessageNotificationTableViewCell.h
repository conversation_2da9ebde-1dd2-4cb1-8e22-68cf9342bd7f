#import "MessageNotificationModel.h"
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@class MessageNotificationTableViewCell;

@protocol MessageNotificationCellDelegate <NSObject>
@optional
- (void)messageCell:(MessageNotificationTableViewCell *)cell
    didMessageStatusChanged:(MessageNotificationModelResult *)message;
@end

@interface MessageNotificationTableViewCell : UITableViewCell
@property(nonatomic, strong) MessageNotificationModelResult *messageData;
@property(nonatomic, weak) id<MessageNotificationCellDelegate> delegate;
- (void)handleCellClick;

- (void)setSelectionMode:(BOOL)selectionMode selected:(BOOL)selected;

@end

NS_ASSUME_NONNULL_END