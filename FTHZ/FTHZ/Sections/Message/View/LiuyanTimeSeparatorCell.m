#import "LiuyanTimeSeparatorCell.h"

@implementation LiuyanTimeSeparatorCell {
  UIView *_bgView;
  UILabel *_titleLabel;
  BOOL _isRightAligned; // 新增属性
}

- (instancetype)initWithStyle:(UITableViewCellStyle)style
              reuseIdentifier:(NSString *)reuseIdentifier {
  self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
  if (self) {
    self.backgroundColor = [UIColor clearColor];
    _isRightAligned = NO; // 默认左对齐
    
    _bgView = [[UIView alloc] init];
    _bgView.backgroundColor = KColor_HighBlack;
    _bgView.layer.cornerRadius = 0;
    _bgView.layer.masksToBounds = YES;
    _bgView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.contentView addSubview:_bgView];

    _titleLabel = [[UILabel alloc] init];
    _titleLabel.font = SourceHanSerifBoldFont(14 * kWidthFactor);
    _titleLabel.textColor = KColor_White;
    _titleLabel.textAlignment = NSTextAlignmentLeft;
    _titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [_bgView addSubview:_titleLabel];

    // 默认左对齐约束
    [self updateConstraints];
  }
  return self;
}

- (void)layoutSubviews {
  [super layoutSubviews];
  
  UIRectCorner corners;
  if (_isRightAligned) {
    // 右对齐时，左侧圆角，右侧直角
    corners = UIRectCornerTopLeft | UIRectCornerBottomLeft;
  } else {
    // 左对齐时，右侧圆角，左侧直角
    corners = UIRectCornerTopRight | UIRectCornerBottomRight;
  }
  
  UIBezierPath *maskPath = [UIBezierPath
      bezierPathWithRoundedRect:_bgView.bounds
              byRoundingCorners:corners
                    cornerRadii:CGSizeMake(12 * kWidthFactor, 12 * kWidthFactor)];
  CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
  maskLayer.frame = _bgView.bounds;
  maskLayer.path = maskPath.CGPath;
  _bgView.layer.mask = maskLayer;
}

- (void)setTitle:(NSString *)title {
  _titleLabel.text = title;
}

- (void)updateConstraints {
  [super updateConstraints];
  
  if (_isRightAligned) {
    // 右对齐约束
    [_bgView mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.trailing.equalTo(self.contentView).offset(0);
      make.centerY.equalTo(self.contentView);
      make.height.mas_equalTo(24 * kWidthFactor);
      make.leading.greaterThanOrEqualTo(self.contentView).offset(24 * kWidthFactor);
    }];
    [_titleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.leading.equalTo(_bgView).offset(12 * kWidthFactor);
      make.trailing.equalTo(_bgView).offset(-24 * kWidthFactor);
      make.centerY.equalTo(_bgView);
    }];
  } else {
    // 左对齐约束（原来的约束）
    [_bgView mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.leading.equalTo(self.contentView).offset(0);
      make.centerY.equalTo(self.contentView);
      make.height.mas_equalTo(24 * kWidthFactor);
      make.trailing.lessThanOrEqualTo(self.contentView).offset(-24 * kWidthFactor);
    }];
    [_titleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.leading.equalTo(_bgView).offset(24 * kWidthFactor);
      make.trailing.equalTo(_bgView).offset(-12 * kWidthFactor);
      make.centerY.equalTo(_bgView);
    }];
  }
}

- (void)setAlignmentToRight:(BOOL)toRight {
  if (_isRightAligned != toRight) {
    _isRightAligned = toRight;
    [self setNeedsUpdateConstraints];
    [self updateConstraintsIfNeeded];
  }
}

@end