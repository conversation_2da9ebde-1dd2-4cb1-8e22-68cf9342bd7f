// #import "LiuyanDetailView.h"
// #import "FTHZChannelReplyModel.h"
// #import "LiuyanVC.h"
// #import "PopoverView.h"
// #import "ReportModel.h"
// #import "ReportVC.h"
// #import "XHInputView.h"

// #define SCREENWIDTH [UIScreen mainScreen].bounds.size.width
// #define SCREENHEIGHT [UIScreen mainScreen].bounds.size.height

// @interface LiuyanDetailView () <NicknameDelegate>
// @property(nonatomic, weak) UINavigationController *mainNavigationController;
// @property(nonatomic, strong) id selfRetainer;
// @end

// @implementation LiuyanDetailView

// - (void)viewDidLoad {
//   [super viewDidLoad];
//   self.view.backgroundColor = [UIColor clearColor];

//   self.paperView = [[UIView alloc] init];
//   self.paperView.backgroundColor = [UIColor colorWithRed:255 / 255.0
//                                                    green:253 / 255.0
//                                                     blue:245 / 255.0
//                                                    alpha:1.0];

//   self.paperView.layer.cornerRadius = 8 * kWidthFactor;
//   self.paperView.layer.shadowColor = [UIColor blackColor].CGColor;
//   self.paperView.layer.shadowOffset = CGSizeMake(0, 2);
//   self.paperView.layer.shadowOpacity = 0.1;
//   self.paperView.layer.shadowRadius = 4;
//   [self.view addSubview:self.paperView];

//   [self.paperView mas_makeConstraints:^(MASConstraintMaker *make) {
//     make.left.equalTo(self.view);
//     make.right.equalTo(self.view);
//     make.centerY.equalTo(self.view);
//   }];

//   UIButton *settingBt = [[UIButton alloc] init];
//   [settingBt setBackgroundImage:[UIImage imageNamed:@"more-black"]
//                        forState:UIControlStateNormal];
//   [self.paperView addSubview:settingBt];
//   [settingBt mas_makeConstraints:^(MASConstraintMaker *make) {
//     make.right.equalTo(self.paperView).offset(-16 * kWidthFactor);
//     make.top.equalTo(self.paperView).offset(16 * kWidthFactor);
//   }];
//   [settingBt addTarget:self
//                 action:@selector(gotoMessagePage:)
//       forControlEvents:UIControlEventTouchUpInside];

//   UILabel *messageLabel = [[UILabel alloc] init];
//   messageLabel.textAlignment = NSTextAlignmentLeft;
//   messageLabel.textColor = [UIColor blackColor];
//   messageLabel.text = self.message;
//   messageLabel.font = [UIFont systemFontOfSize:16];
//   messageLabel.numberOfLines = 0;
//   CGFloat lineSpacing = 5 * kWidthFactor;
//   NSMutableParagraphStyle *paragraphStyle =
//       [[NSMutableParagraphStyle alloc] init];
//   paragraphStyle.lineSpacing = lineSpacing;
//   paragraphStyle.lineBreakMode = messageLabel.lineBreakMode;
//   paragraphStyle.alignment = messageLabel.textAlignment;

//   NSMutableAttributedString *attributedString =
//       [[NSMutableAttributedString alloc] initWithString:messageLabel.text];
//   [attributedString addAttribute:NSParagraphStyleAttributeName
//                            value:paragraphStyle
//                            range:NSMakeRange(0, [messageLabel.text length])];
//   // 保留原来的字体
//   [attributedString addAttribute:NSFontAttributeName
//                            value:messageLabel.font
//                            range:NSMakeRange(0, [messageLabel.text length])];
//   messageLabel.attributedText = attributedString;
//   [self.paperView addSubview:messageLabel];

//   [messageLabel mas_makeConstraints:^(MASConstraintMaker *make) {
//     make.left.equalTo(self.paperView).offset(20 * kWidthFactor);
//     make.right.equalTo(self.paperView).offset(-20 * kWidthFactor);
//     make.top.equalTo(settingBt.mas_bottom).offset(18 * kWidthFactor);
//   }];

//   self.view.backgroundColor = [UIColor clearColor];

//   UISwipeGestureRecognizer *swipeDown = [[UISwipeGestureRecognizer alloc]
//       initWithTarget:self
//               action:@selector(handleSwipeGesture:)];
//   swipeDown.direction = UISwipeGestureRecognizerDirectionDown;
//   [self.view addGestureRecognizer:swipeDown];

//   UIView *signatureView = [[UIView alloc] init];
//   [self.paperView addSubview:signatureView];
//   [signatureView mas_makeConstraints:^(MASConstraintMaker *make) {
//     make.right.equalTo(self.paperView).offset(-20 * kWidthFactor);
//     make.top.greaterThanOrEqualTo(messageLabel.mas_bottom)
//         .offset(30 * kWidthFactor);
//     make.bottom.equalTo(self.paperView).offset(-20 * kWidthFactor);
//     make.width.mas_equalTo(150 * kWidthFactor);
//   }];

//   UILabel *nameLabel = [[UILabel alloc] init];
//   nameLabel.text = self.nickname ?: @"";
//   nameLabel.font = SourceHanSerifBoldFont(12 * kWidthFactor);
//   nameLabel.textColor = KColor_HighBlue;
//   nameLabel.textAlignment = NSTextAlignmentRight;
//   [signatureView addSubview:nameLabel];
//   [nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
//     make.right.equalTo(signatureView);
//     make.top.equalTo(signatureView);
//   }];

//   UILabel *dateLabel = [[UILabel alloc] init];
//   NSDate *date =
//       [NSDate dateWithTimeIntervalSince1970:[self.created doubleValue]];
//   NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
//   [formatter setDateFormat:@"yy年M月d日"];
//   dateLabel.text = [formatter stringFromDate:date];
//   dateLabel.font = SourceHanSerifRegularFont(10 * kWidthFactor);
//   dateLabel.textColor = KColor_textTinyGray;
//   dateLabel.textAlignment = NSTextAlignmentRight;
//   [signatureView addSubview:dateLabel];
//   [dateLabel mas_makeConstraints:^(MASConstraintMaker *make) {
//     make.right.equalTo(signatureView);
//     make.top.equalTo(nameLabel.mas_bottom).offset(4 * kWidthFactor);
//     make.bottom.equalTo(signatureView);
//   }];

//   UIButton *replyButton = [UIButton buttonWithType:UIButtonTypeCustom];
//   [replyButton setTitle:@" 回复" forState:UIControlStateNormal];
//   [replyButton setTitleColor:KColor_HighBlack forState:UIControlStateNormal];
//   replyButton.titleLabel.font = SourceHanSerifMediumFont(14 * kWidthFactor);
//   replyButton.backgroundColor = [UIColor colorWithRed:237 / 255.0
//                                                 green:237 / 255.0
//                                                  blue:237 / 255.0
//                                                 alpha:1.0];
//   replyButton.layer.cornerRadius = 15 * kWidthFactor;
//   replyButton.layer.masksToBounds = YES;

//   UIImageView *replyIcon =
//       [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"回复w"]];
//   replyIcon.contentMode = UIViewContentModeScaleAspectFit;
//   [replyButton addSubview:replyIcon];

//   [replyIcon mas_makeConstraints:^(MASConstraintMaker *make) {
//     make.left.equalTo(replyButton).offset(10 * kWidthFactor);
//     make.centerY.equalTo(replyButton);
//     make.width.height.equalTo(@(14 * kWidthFactor));
//   }];

//   replyButton.titleEdgeInsets = UIEdgeInsetsMake(0, 10 * kWidthFactor, 0, 0);

//   [self.paperView addSubview:replyButton];

//   [replyButton mas_makeConstraints:^(MASConstraintMaker *make) {
//     make.left.equalTo(self.paperView).offset(20 * kWidthFactor);
//     make.centerY.equalTo(signatureView);
//     make.height.equalTo(@(30 * kWidthFactor));
//     make.width.equalTo(@(70 * kWidthFactor));
//   }];

//   [replyButton addTarget:self
//                   action:@selector(replyButtonClicked:)
//         forControlEvents:UIControlEventTouchUpInside];
// }

// - (void)replyButtonClicked:(UIButton *)sender {
//   // 弹出评论输入框前，清空上次内容
//   [USERDEFAULT removeObjectForKey:ContenText];
//   [USERDEFAULT synchronize];

//   __weak typeof(self) weakSelf = self;
//   [XHInputView showWithStyle:InputViewStyleDefault
//       configurationBlock:^(XHInputView *inputView) {
//         inputView.delegate = nil;
//         inputView.placeholder = @"请输入你的留言";
//         inputView.maxCount = 200;
//         inputView.backgroundColor = [UIColor colorWithWhite:0.95 alpha:1.0];
//       }
//       sendBlock:^BOOL(NSString *_Nullable text, BOOL sendIM) {
//         if (text.length == 0) {
//           return NO;
//         }
//         // 调用创建留言接口
//         [HUD show];
//         [LiuyanCreateModel message:text
//             to:weakSelf.userid
//             success:^(NSDictionary *resultObject) {
//               [HUD dissmiss];
//               LiuyanCreateModel *member =
//                   [LiuyanCreateModel mj_objectWithKeyValues:resultObject];
//               if ([member.success boolValue]) {
//                 [weakSelf.view makeToast:@"留言成功"
//                                 duration:1.0
//                                 position:CSToastPositionCenter];
//                 [weakSelf dismissViewControllerAnimated:YES completion:nil];
//               } else {
//                 [weakSelf.view makeToast:member.msg
//                                 duration:1.0
//                                 position:CSToastPositionCenter];
//               }
//             }
//             failure:^(NSError *requestErr) {
//               [HUD dissmiss];
//               [weakSelf.view makeToast:@"数据有误,请检查网络后重试"
//                               duration:1.0
//                               position:CSToastPositionCenter];
//             }];
//         return YES;
//       }];
// }

// - (void)handleSwipeGesture:(UISwipeGestureRecognizer *)gesture {
//   [self dismissViewControllerAnimated:YES completion:nil];
// }

// - (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
//   UITouch *touch = [touches anyObject];

//   CGPoint locationInPaperView = [touch locationInView:self.paperView];

//   if (locationInPaperView.y < 0 ||
//       locationInPaperView.y > self.paperView.bounds.size.height ||
//       locationInPaperView.x < 0 ||
//       locationInPaperView.x > self.paperView.bounds.size.width) {

//     [UIView animateWithDuration:0.3
//         animations:^{
//           self.view.alpha = 0;
//           self.paperView.transform = CGAffineTransformMakeScale(0.8, 0.8);
//         }
//         completion:^(BOOL finished) {
//           [self dismissViewControllerAnimated:NO completion:nil];
//         }];
//   }
// }

// - (void)viewWillAppear:(BOOL)animated {
//   [super viewWillAppear:animated];

//   self.view.backgroundColor = [UIColor clearColor];
//   self.paperView.transform = CGAffineTransformMakeScale(0.8, 0.8);

//   [UIView animateWithDuration:0.3
//                    animations:^{
//                      self.view.backgroundColor = [UIColor clearColor];
//                      self.paperView.transform = CGAffineTransformIdentity;
//                    }];
// }

// - (void)viewWillDisappear:(BOOL)animated {
//   [super viewWillDisappear:animated];

//   [UIView animateWithDuration:0.3
//                    animations:^{
//                      self.view.backgroundColor = [UIColor clearColor];
//                      self.paperView.transform =
//                          CGAffineTransformMakeScale(0.8, 0.8);
//                    }];
// }

// - (void)gotoMessagePage:(UIButton *)sender {
//   PopoverView *popoverView = [PopoverView popoverView];

//   popoverView.showShade = YES;
//   [popoverView showToView:sender withActions:[self QQActions]];
// }

// - (NSArray<PopoverAction *> *)QQActions {
//   PopoverAction *multichatAction = [PopoverAction
//       actionWithImage:[UIImage imageNamed:@"Report"]
//                 title:@"举报"
//               handler:^(PopoverAction *action) {
//                 typeof(self) strongSelf = self;
//                 strongSelf.selfRetainer = strongSelf;

//                 [strongSelf
//                     dismissViewControllerAnimated:NO
//                                        completion:^{
//                                          UINavigationController *mainNav =
//                                              [strongSelf
//                                                  getMainNavigationController];
//                                          strongSelf.mainNavigationController
//                                          =
//                                              mainNav;

//                                          ReportVC *reportVC =
//                                              [[ReportVC alloc] init];
//                                          reportVC.delegate = strongSelf;
//                                          [mainNav pushViewController:reportVC
//                                                             animated:YES];
//                                        }];
//               }];

//   return @[ multichatAction ];
// }

// - (UINavigationController *)getMainNavigationController {
//   UIWindow *window = [UIApplication sharedApplication].keyWindow;
//   UIViewController *rootVC = window.rootViewController;

//   if ([rootVC isKindOfClass:[UITabBarController class]]) {
//     UITabBarController *tabController = (UITabBarController *)rootVC;
//     UIViewController *selectedVC = tabController.selectedViewController;
//     if ([selectedVC isKindOfClass:[UINavigationController class]]) {
//       return (UINavigationController *)selectedVC;
//     }
//   }

//   if ([rootVC isKindOfClass:[UINavigationController class]]) {
//     return (UINavigationController *)rootVC;
//   }

//   return nil;
// }

// - (UIViewController *)getTopMostViewController {
//   UIViewController *topViewController =
//       [UIApplication sharedApplication].keyWindow.rootViewController;

//   while (topViewController.presentedViewController) {
//     topViewController = topViewController.presentedViewController;
//   }

//   return topViewController;
// }

// - (void)didReceiveMemoryWarning {
//   [super didReceiveMemoryWarning];
// }

// - (void)delegateGetReport:(NSInteger)RelationshipType
//                    detail:(NSString *)detail {
//   typeof(self) strongSelf = self;
//   [strongSelf
//       loadReportData:[NSString stringWithFormat:@"%ld", RelationshipType]
//               detail:detail];
//   strongSelf.selfRetainer = nil;
// }

// - (UIViewController *)getTopViewController {
//   UIViewController *rootVC =
//       [UIApplication sharedApplication].keyWindow.rootViewController;
//   while (rootVC.presentedViewController) {
//     rootVC = rootVC.presentedViewController;
//   }

//   if ([rootVC isKindOfClass:[UITabBarController class]]) {
//     UITabBarController *tabController = (UITabBarController *)rootVC;
//     UIViewController *selectedVC = tabController.selectedViewController;
//     if ([selectedVC isKindOfClass:[UINavigationController class]]) {
//       return [(UINavigationController *)selectedVC topViewController];
//     }
//     return selectedVC;
//   }

//   if ([rootVC isKindOfClass:[UINavigationController class]]) {
//     return [(UINavigationController *)rootVC topViewController];
//   }

//   return rootVC;
// }

// - (void)loadReportData:(NSString *)report_type detail:(NSString *)detail {
//   if (!self.userid || self.userid.length == 0) {
//     [[self getTopViewController].view makeToast:@"无法举报，用户信息无效"
//                                        duration:1.5
//                                        position:CSToastPositionCenter];
//     return;
//   }

//   typeof(self) strongSelf = self;
//   [ReportModel postReportModel:self.userid
//       report_type:report_type
//       type:@"4"
//       rid:self.lid
//       detail:detail
//       success:^(NSDictionary *resultObject) {
//         dispatch_async(dispatch_get_main_queue(), ^{
//           ReportModel *member =
//               [ReportModel mj_objectWithKeyValues:resultObject];

//           [strongSelf.mainNavigationController
//           popViewControllerAnimated:YES];

//           dispatch_after(
//               dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 *
//               NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//                 UIViewController *topVC = [strongSelf getTopViewController];
//                 NSString *message = [member.success boolValue]
//                                         ? @"举报成功"
//                                         : (member.msg ?: @"举报失败");

//                 [topVC.view makeToast:message
//                              duration:1.5
//                              position:CSToastPositionCenter];

//                 if ([member.success boolValue]) {
//                   dispatch_after(dispatch_time(DISPATCH_TIME_NOW,
//                                                (int64_t)(1.0 *
//                                                NSEC_PER_SEC)),
//                                  dispatch_get_main_queue(), ^{
//                                    [strongSelf
//                                        dismissViewControllerAnimated:YES
//                                                           completion:nil];
//                                  });
//                 }
//               });
//         });
//       }
//       failure:^(NSError *requestErr) {
//         dispatch_async(dispatch_get_main_queue(), ^{
//           [strongSelf.mainNavigationController
//           popViewControllerAnimated:YES];

//           dispatch_after(
//               dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 *
//               NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//                 [[strongSelf getTopViewController].view
//                     makeToast:@"举报失败，请重试"
//                      duration:1.5
//                      position:CSToastPositionCenter];
//               });
//         });
//       }];
// }

// @end
