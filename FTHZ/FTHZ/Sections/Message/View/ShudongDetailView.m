#import "ShudongDetailView.h"
#import "PopoverView.h"
#import "ReportModel.h"
#import "ReportVC.h"
#import "ShudongDetailVC.h"

@interface ShudongDetailView ()

@property(nonatomic, strong) UIView *contentView;
@property(nonatomic, strong) UILabel *messageLabel;
@property(nonatomic, strong) UIButton *closeButton;
@property(nonatomic, strong) UIButton *settingButton;
@property(nonatomic, weak) UINavigationController *mainNavigationController;
@property(nonatomic, strong) id selfRetainer;

@end

@implementation ShudongDetailView

- (void)viewDidLoad {
  [super viewDidLoad];
  self.view.backgroundColor = [UIColor clearColor];

  self.paperView = [[UIView alloc] init];
  self.paperView.backgroundColor = KColor_White;
  self.paperView.layer.cornerRadius = 20 * kWidthFactor;
  self.paperView.layer.shadowColor = [UIColor blackColor].CGColor;
  self.paperView.layer.shadowOffset = CGSizeMake(0, 2);
  self.paperView.layer.shadowOpacity = 0.1;
  self.paperView.layer.shadowRadius = 4;
  [self.view addSubview:self.paperView];

  CGFloat horizontalMargin = 24 * kWidthFactor;
  [self.paperView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.view).offset(horizontalMargin);
    make.right.equalTo(self.view).offset(-horizontalMargin);
    make.centerY.equalTo(self.view);
  }];

  self.settingButton = [UIButton buttonWithType:UIButtonTypeCustom];
  [self.settingButton setImage:[UIImage imageNamed:@"more-black"]
                      forState:UIControlStateNormal];
  [self.settingButton addTarget:self
                         action:@selector(gotoMessagePage:)
               forControlEvents:UIControlEventTouchUpInside];
  [self.paperView addSubview:self.settingButton];

  [self.settingButton mas_makeConstraints:^(MASConstraintMaker *make) {
    make.right.equalTo(self.paperView).offset(-15 * kWidthFactor);
    make.top.equalTo(self.paperView).offset(15 * kWidthFactor);
    make.width.height.equalTo(@(24 * kWidthFactor));
  }];

  UILabel *messageLabel = [[UILabel alloc] init];
  messageLabel.textAlignment = NSTextAlignmentLeft;
  messageLabel.textColor = [UIColor blackColor];
  messageLabel.text = self.message;
  messageLabel.font = SourceHanSerifSemiBoldFont(16 * kWidthFactor);
  messageLabel.numberOfLines = 0;

  CGFloat lineSpacing = 5 * kWidthFactor;
  NSMutableParagraphStyle *paragraphStyle =
      [[NSMutableParagraphStyle alloc] init];
  paragraphStyle.lineSpacing = lineSpacing;
  paragraphStyle.lineBreakMode = messageLabel.lineBreakMode;
  paragraphStyle.alignment = messageLabel.textAlignment;

  NSMutableAttributedString *attributedString =
      [[NSMutableAttributedString alloc] initWithString:messageLabel.text];
  [attributedString addAttribute:NSParagraphStyleAttributeName
                           value:paragraphStyle
                           range:NSMakeRange(0, [messageLabel.text length])];
  [attributedString addAttribute:NSFontAttributeName
                           value:messageLabel.font
                           range:NSMakeRange(0, [messageLabel.text length])];
  messageLabel.attributedText = attributedString;

  [self.paperView addSubview:messageLabel];
  [messageLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.paperView).offset(20 * kWidthFactor);
    make.right.equalTo(self.paperView).offset(-20 * kWidthFactor);
    make.top.equalTo(self.paperView).offset(50 * kWidthFactor);
    make.bottom.equalTo(self.paperView).offset(-50 * kWidthFactor);
  }];

  UISwipeGestureRecognizer *swipeDown = [[UISwipeGestureRecognizer alloc]
      initWithTarget:self
              action:@selector(handleSwipeGesture:)];
  swipeDown.direction = UISwipeGestureRecognizerDirectionDown;
  [self.view addGestureRecognizer:swipeDown];
}

- (void)dismissView {
  [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)gotoMessagePage:(UIButton *)sender {
  PopoverView *popoverView = [PopoverView popoverView];
  popoverView.showShade = YES;
  [popoverView showToView:sender withActions:[self QQActions]];
}

- (NSArray<PopoverAction *> *)QQActions {
  if (!self.userid || [self.userid length] == 0) {
    PopoverAction *errorAction =
        [PopoverAction actionWithImage:[UIImage imageNamed:@"Report"]
                                 title:@"举报"
                               handler:^(PopoverAction *action) {
                                 [self showToastFast:@"无法举报，用户信息无效"];
                               }];
    return @[ errorAction ];
  }

  PopoverAction *multichatAction = [PopoverAction
      actionWithImage:[UIImage imageNamed:@"Report"]
                title:@"举报"
              handler:^(PopoverAction *action) {
                typeof(self) strongSelf = self;
                strongSelf.selfRetainer = strongSelf;

                UINavigationController *mainNav =
                    [self getCurrentNavigationController];
                strongSelf.mainNavigationController = mainNav;

                [strongSelf dismissViewControllerAnimated:NO
                                               completion:^{
                                                 ReportVC *reportVC =
                                                     [[ReportVC alloc] init];
                                                 reportVC.delegate = strongSelf;
                                                 [mainNav
                                                     pushViewController:reportVC
                                                               animated:YES];
                                               }];
              }];

  return @[ multichatAction ];
}

- (UINavigationController *)getMainNavigationController {
  UIWindow *window = [UIApplication sharedApplication].keyWindow;
  UIViewController *rootVC = window.rootViewController;

  if ([rootVC isKindOfClass:[UITabBarController class]]) {
    UITabBarController *tabController = (UITabBarController *)rootVC;
    UIViewController *selectedVC = tabController.selectedViewController;
    if ([selectedVC isKindOfClass:[UINavigationController class]]) {
      return (UINavigationController *)selectedVC;
    }
  }

  if ([rootVC isKindOfClass:[UINavigationController class]]) {
    return (UINavigationController *)rootVC;
  }

  return nil;
}

- (void)didReceiveMemoryWarning {
  [super didReceiveMemoryWarning];
}

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];

  self.view.backgroundColor = [UIColor clearColor];
  self.paperView.transform = CGAffineTransformMakeScale(0.8, 0.8);

  [UIView animateWithDuration:0.3
                   animations:^{
                     self.view.backgroundColor =
                         [[UIColor blackColor] colorWithAlphaComponent:0.5];
                     self.paperView.transform = CGAffineTransformIdentity;
                   }];
}

- (void)viewWillDisappear:(BOOL)animated {
  [super viewWillDisappear:animated];

  [UIView animateWithDuration:0.3
                   animations:^{
                     self.view.backgroundColor = [UIColor clearColor];
                     self.paperView.transform =
                         CGAffineTransformMakeScale(0.8, 0.8);
                   }];
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
  UITouch *touch = [touches anyObject];
  CGPoint locationInPaperView = [touch locationInView:self.paperView];

  if (locationInPaperView.y < 0 ||
      locationInPaperView.y > self.paperView.bounds.size.height ||
      locationInPaperView.x < 0 ||
      locationInPaperView.x > self.paperView.bounds.size.width) {

    [UIView animateWithDuration:0.3
        animations:^{
          self.view.alpha = 0;
          self.paperView.transform = CGAffineTransformMakeScale(0.8, 0.8);
        }
        completion:^(BOOL finished) {
          [self dismissViewControllerAnimated:NO completion:nil];
        }];
  }
}

- (void)addSwipeGesture {
  UISwipeGestureRecognizer *swipeDown = [[UISwipeGestureRecognizer alloc]
      initWithTarget:self
              action:@selector(handleSwipeGesture:)];
  swipeDown.direction = UISwipeGestureRecognizerDirectionDown;
  [self.view addGestureRecognizer:swipeDown];
}

- (void)handleSwipeGesture:(UISwipeGestureRecognizer *)gesture {
  [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)delegateGetReport:(NSInteger)RelationshipType
                   detail:(NSString *)detail {
  typeof(self) strongSelf = self;

  dispatch_async(dispatch_get_main_queue(), ^{
    [strongSelf
        loadReportData:[NSString stringWithFormat:@"%ld", RelationshipType]
                detail:detail];
    strongSelf.selfRetainer = nil;
  });
}

- (UIViewController *)getTopViewController {
  UIViewController *rootVC =
      [UIApplication sharedApplication].keyWindow.rootViewController;
  while (rootVC.presentedViewController) {
    rootVC = rootVC.presentedViewController;
  }

  if ([rootVC isKindOfClass:[UITabBarController class]]) {
    UITabBarController *tabController = (UITabBarController *)rootVC;
    UIViewController *selectedVC = tabController.selectedViewController;
    if ([selectedVC isKindOfClass:[UINavigationController class]]) {
      return [(UINavigationController *)selectedVC topViewController];
    }
    return selectedVC;
  }

  if ([rootVC isKindOfClass:[UINavigationController class]]) {
    return [(UINavigationController *)rootVC topViewController];
  }

  return rootVC;
}

- (void)loadReportData:(NSString *)report_type detail:(NSString *)detail {
  if (!self.userid || self.userid.length == 0) {
    [[self getTopViewController].view makeToast:@"无法举报，用户信息无效"
                                       duration:1.5
                                       position:CSToastPositionCenter];
    return;
  }

  typeof(self) strongSelf = self;
  [ReportModel postReportModel:self.userid
      report_type:report_type
      type:@"5"
      rid:self.sid
      detail:detail
      success:^(NSDictionary *resultObject) {
        dispatch_async(dispatch_get_main_queue(), ^{
          ReportModel *member =
              [ReportModel mj_objectWithKeyValues:resultObject];

          [strongSelf.mainNavigationController popViewControllerAnimated:YES];

          dispatch_after(
              dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)),
              dispatch_get_main_queue(), ^{
                UIViewController *topVC = [strongSelf getTopViewController];
                NSString *message = [member.success boolValue]
                                        ? @"举报成功"
                                        : (member.msg ?: @"举报失败");

                [topVC.view makeToast:message
                             duration:1.5
                             position:CSToastPositionCenter];

                if ([member.success boolValue]) {
                  dispatch_after(dispatch_time(DISPATCH_TIME_NOW,
                                               (int64_t)(1.0 * NSEC_PER_SEC)),
                                 dispatch_get_main_queue(), ^{
                                   [strongSelf
                                       dismissViewControllerAnimated:YES
                                                          completion:nil];
                                 });
                }
              });
        });
      }
      failure:^(NSError *requestErr) {
        dispatch_async(dispatch_get_main_queue(), ^{
          [strongSelf.mainNavigationController popViewControllerAnimated:YES];

          dispatch_after(
              dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)),
              dispatch_get_main_queue(), ^{
                [[strongSelf getTopViewController].view
                    makeToast:@"举报失败，请重试"
                     duration:1.5
                     position:CSToastPositionCenter];
              });
        });
      }];
}

- (UINavigationController *)getCurrentNavigationController {
  UIWindow *window = [UIApplication sharedApplication].keyWindow;
  UIViewController *rootVC = window.rootViewController;

  if ([rootVC isKindOfClass:[UITabBarController class]]) {
    UITabBarController *tabController = (UITabBarController *)rootVC;
    if ([tabController.selectedViewController
            isKindOfClass:[UINavigationController class]]) {
      return (UINavigationController *)tabController.selectedViewController;
    }
  }

  if ([rootVC isKindOfClass:[UINavigationController class]]) {
    return (UINavigationController *)rootVC;
  }

  return nil;
}

@end
