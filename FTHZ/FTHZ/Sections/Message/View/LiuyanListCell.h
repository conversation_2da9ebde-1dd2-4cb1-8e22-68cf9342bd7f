#import "LiuyanModel.h"
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN
@protocol LiuyanListCellDelegate <NSObject>
@optional
- (void)didTapAvatarWithUserId:(NSString *)userId;
- (void)didTapReportWithUserId:(NSString *)userId lid:(NSString *)lid;
- (void)didTapReplyWithUserId:(NSString *)userId lid:(NSString *)lid;
@end
@interface LiuyanListCell : UITableViewCell
@property(nonatomic, strong) LiuyanModel *messageData;
@property(nonatomic, weak) id<LiuyanListCellDelegate> delegate;
@property(nonatomic, copy) NSString *userid;
@property(nonatomic, copy) NSString *lid;
- (void)setSelectionMode:(BOOL)selectionMode selected:(BOOL)selected;
- (void)setSelectionMode:(BOOL)selectionMode
                selected:(BOOL)selected
                animated:(BOOL)animated;
- (void)showBottomViewWithText:(NSString *)text;
- (void)hideBottomView;
@end

NS_ASSUME_NONNULL_END