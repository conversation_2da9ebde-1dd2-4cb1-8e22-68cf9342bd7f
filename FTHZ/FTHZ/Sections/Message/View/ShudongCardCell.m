#import "ShudongCardCell.h"
#import "_2hz-Swift.h"

@interface ShudongCardCell ()

@property(nonatomic, strong) UILabel *nameLabel;
@property(nonatomic, strong) UILabel *timeLabel;
@property(nonatomic, strong) UILabel *contentLabel;
@property(nonatomic, strong) UIView *cardView;
@property(nonatomic, strong) NSString *gender;
@property(nonatomic, strong) ShudongModel *message;

@end

@implementation ShudongCardCell

- (instancetype)initWithFrame:(CGRect)frame {
  self = [super initWithFrame:frame];
  if (self) {
    [self setupUI];
  }
  return self;
}

- (void)setupUI {
  self.cardView = [[UIView alloc] init];
  self.cardView.backgroundColor = [UIColor whiteColor];
  self.cardView.layer.cornerRadius = 12.0 * kWidthFactor;
  self.cardView.clipsToBounds = YES;
  [self.contentView addSubview:self.cardView];

  [self.cardView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.edges.equalTo(self.contentView);
  }];

  self.nameLabel = [[UILabel alloc] init];
  self.nameLabel.font = SourceHanSerifBoldFont(14 * kWidthFactor);
  self.nameLabel.textAlignment = NSTextAlignmentLeft;
  [self.cardView addSubview:self.nameLabel];

  self.timeLabel = [[UILabel alloc] init];
  self.timeLabel.font = [UIFont systemFontOfSize:11 * kWidthFactor];
  self.timeLabel.textColor = KColor_Gray;
  self.timeLabel.textAlignment = NSTextAlignmentLeft;
  [self.cardView addSubview:self.timeLabel];

  self.contentLabel = [[UILabel alloc] init];
  self.contentLabel.font = SourceHanSerifRegularFont(13 * kWidthFactor);
  self.contentLabel.textColor = KColor_White;
  self.contentLabel.numberOfLines = 0;
  self.contentLabel.lineBreakMode = NSLineBreakByTruncatingTail;
  self.contentLabel.textAlignment = NSTextAlignmentLeft;
  [self.cardView addSubview:self.contentLabel];

  [self.nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(self.cardView).offset(10 * kWidthFactor);
    make.left.equalTo(self.cardView).offset(10 * kWidthFactor);
    make.right.equalTo(self.cardView).offset(-10 * kWidthFactor);
    make.height.equalTo(@(14 * kWidthFactor));
  }];

  [self.timeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(self.nameLabel.mas_bottom).offset(8 * kWidthFactor);
    make.left.equalTo(self.cardView).offset(10 * kWidthFactor);
    make.right.equalTo(self.cardView).offset(-10 * kWidthFactor);
    make.height.equalTo(@(12 * kWidthFactor));
  }];

  [self.contentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(self.timeLabel.mas_bottom).offset(8 * kWidthFactor);
    make.left.equalTo(self.cardView).offset(10 * kWidthFactor);
    make.right.equalTo(self.cardView).offset(-10 * kWidthFactor);
    make.bottom.lessThanOrEqualTo(self.cardView).offset(-10 * kWidthFactor);
  }];
}

- (void)setMessageData:(ShudongModel *)message {
  _message = message;
  self.gender = message.gender;
  UIColor *textColor =
      [self.gender isEqualToString:@"1"] ? KColor_Man : KColor_Women;

  self.nameLabel.text = message.whale;
  self.nameLabel.textColor = textColor;

  self.timeLabel.text =
      [NemoUtil distanceTimeWithBeforeTime:[message.created doubleValue]];

  NSMutableParagraphStyle *paragraphStyle =
      [[NSMutableParagraphStyle alloc] init];
  paragraphStyle.lineSpacing = 3 * kWidthFactor;
  paragraphStyle.alignment = NSTextAlignmentLeft;
  paragraphStyle.lineBreakMode = NSLineBreakByTruncatingTail;
  paragraphStyle.firstLineHeadIndent = 0;
  paragraphStyle.paragraphSpacing = 0;
  paragraphStyle.paragraphSpacingBefore = 0;

  NSMutableAttributedString *attributedString =
      [[NSMutableAttributedString alloc] initWithString:message.message];

  [attributedString addAttribute:NSParagraphStyleAttributeName
                           value:paragraphStyle
                           range:NSMakeRange(0, [message.message length])];

  [attributedString addAttribute:NSFontAttributeName
                           value:self.contentLabel.font
                           range:NSMakeRange(0, [message.message length])];

  [attributedString addAttribute:NSForegroundColorAttributeName
                           value:KColor_White
                           range:NSMakeRange(0, [message.message length])];

  self.contentLabel.attributedText = attributedString;

  NSArray *bgColors =
      @[ KColor_HighBlack, KColor_HighBlack2, KColor_HighBlack3 ];
  NSUInteger hashValue = [message.created hash];
  NSUInteger idx = hashValue % bgColors.count;
  self.cardView.backgroundColor = bgColors[idx];
}

@end