#import "AppDelegate.h"
#import "ClearBadgeModel.h"
#import "FTHZTabBarController.h"
#import "GenderVC.h"
#import "LoginVC.h"
#import "TalkingData.h"
#import "UserdeviceTokenModel.h"
#import <Bugly/Bugly.h>
#import <UserNotifications/UserNotifications.h>
@import AVFoundation;
#import "FTHZFeedListViewController.h"
#import "FTHZNetworkTask+OceanAPI.h"
#import "UIImageView+AnimationCompletion.h"

@interface AppDelegate () <UITabBarControllerDelegate> {
  NSInteger ftReloadIndex;
}

@property(nonatomic, strong) FTHZTabBarController *tabBarController;
@property(nonatomic, strong) NSDictionary *userInfo;

@end

@implementation AppDelegate

+ (AppDelegate *)instance {
  return (AppDelegate *)[UIApplication sharedApplication].delegate;
}

- (BOOL)application:(UIApplication *)application
    willFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
  return YES;
}

- (BOOL)application:(UIApplication *)application
    didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
  [NSThread sleepForTimeInterval:1.0];
  IQKeyboardManager *manager = [IQKeyboardManager sharedManager];
  manager.enable = NO;
  manager.shouldResignOnTouchOutside = YES;
  manager.shouldToolbarUsesTextFieldTintColor = YES;
  manager.enableAutoToolbar = NO;
  manager.toolbarManageBehaviour = IQAutoToolbarByTag;

  [Bugly startWithAppId:BuglyAppID];

  [self initIMSDK];

  [TalkingData sessionStarted:@"875735A4E9BE4051B317E23429D7B04F"
                withChannelId:@"App Store"];

  self.window = [[UIWindow alloc] init];
  self.window.backgroundColor = KColor_White;
  self.window.frame = [UIScreen mainScreen].bounds;
  [self.window makeKeyAndVisible];

  ftReloadIndex = 0;
  [self loadToMain];
  [UIApplication sharedApplication].applicationSupportsShakeToEdit = NO;

  if (@available(iOS 13.0, *)) {
    self.window.overrideUserInterfaceStyle = UIUserInterfaceStyleLight;

    UINavigationBarAppearance *appearance =
        [[UINavigationBarAppearance alloc] init];
    [appearance configureWithOpaqueBackground];
    appearance.backgroundColor = [UIColor whiteColor];
    appearance.shadowColor = nil;

    [UINavigationBar appearance].standardAppearance = appearance;
    [UINavigationBar appearance].scrollEdgeAppearance = appearance;
    if (@available(iOS 15.0, *)) {
      [UINavigationBar appearance].compactAppearance = appearance;
      [UINavigationBar appearance].compactScrollEdgeAppearance = appearance;
    }
  } else {
    [[UINavigationBar appearance] setBarTintColor:[UIColor whiteColor]];
    [[UINavigationBar appearance] setTranslucent:NO];
  }

  return YES;
}

#pragma mark - UITabBarControllerDelegate

- (void)initIMSDK {
}

- (BOOL)tabBarController:(UITabBarController *)tabBarController
    shouldSelectViewController:(UIViewController *)viewController {
  if ([tabBarController.viewControllers containsObject:viewController]) {
    NSInteger index =
        [tabBarController.viewControllers indexOfObject:viewController];
    if (index == ftReloadIndex) {
      [self reloadWithFtIndex:index];
    }
    ftReloadIndex = index;
  }
  return YES;
}

- (void)loadToMain {
  self.tabBarController = [[FTHZTabBarController alloc] init];
  self.tabBarController.delegate = self;
  self.window.rootViewController = self.tabBarController;
}

- (void)tabBarController:(UITabBarController *)tabBarController
    didSelectViewController:(UIViewController *)viewController {
  UIImageView *imageView =
      [self getImageViewForTabBarItem:viewController.tabBarItem];
  if (!imageView)
    return;

  [self playTabBarAnimation:imageView];
}

#pragma mark - Animation Helpers

- (UIImageView *)getImageViewForTabBarItem:(UITabBarItem *)tabBarItem {
  UIView *itemView = [tabBarItem valueForKey:@"view"];
  for (UIView *subview in itemView.subviews) {
    if ([subview isKindOfClass:[UIImageView class]]) {
      return (UIImageView *)subview;
    }
  }
  return nil;
}

- (void)playTabBarAnimation:(UIImageView *)imageView {
  [imageView.layer removeAllAnimations];

  CABasicAnimation *scaleAnimation =
      [CABasicAnimation animationWithKeyPath:@"transform.scale"];
  scaleAnimation.fromValue = @1.0;
  scaleAnimation.toValue = @1.2;
  scaleAnimation.duration = 0.1;
  scaleAnimation.autoreverses = YES;
  scaleAnimation.timingFunction = [CAMediaTimingFunction
      functionWithName:kCAMediaTimingFunctionEaseInEaseOut];

  [imageView.layer addAnimation:scaleAnimation forKey:@"scaleAnimation"];
}

#pragma mark - Reload Methods

- (void)reloadWithFtIndex:(NSInteger)ftindex {
  switch (ftindex) {
  case 0:
    [NOTIFICENTER postNotificationName:ReloadIndexOne object:nil];
    break;
  case 1:
    [NOTIFICENTER postNotificationName:ReloadIndexTwo object:nil];
    break;
  case 2:
    [NOTIFICENTER postNotificationName:ReloadIndexThree object:nil];
    break;
  case 3:
    [NOTIFICENTER postNotificationName:ReloadIndexFour object:nil];
    break;
  default:
    break;
  }
}

- (void)getRootViewController {
  [self loadToMain];
}

- (void)applicationWillResignActive:(UIApplication *)application {
}

- (void)applicationDidEnterBackground:(UIApplication *)application {
  [UIApplication sharedApplication].applicationIconBadgeNumber = 0;
}

- (void)applicationWillEnterForeground:(UIApplication *)application {
}

- (void)applicationWillTerminate:(UIApplication *)application {
}

- (UIInterfaceOrientationMask)application:(UIApplication *)application
    supportedInterfaceOrientationsForWindow:(UIWindow *)window {
  return self.shouldRotate ? UIInterfaceOrientationMaskAllButUpsideDown
                           : UIInterfaceOrientationMaskPortrait;
}

@end