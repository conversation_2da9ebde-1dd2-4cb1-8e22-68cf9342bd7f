#ifndef Constants_h
#define Constants_h

#import "NSString+PJR.h"

#import "BaseJsonModel.h"
#import "FlatButton.h"
#import "HUD.h"
#import "Http.h"
#import "MJChiBaoZiFooter.h"
#import "MJChiBaoZiHeader.h"
#import "MJExtension.h"
#import "MJRefresh.h"
#import "Masonry.h"
#import "NemoUtil.h"
#import "UICountingLabel.h"
#import "UIImageView+WebCache.h"
#import <CoreLocation/CoreLocation.h>
#import <IQKeyboardManager/IQKeyboardManager.h>

#define LocalizedStr(key) NSLocalizedString(key, @"")
#define NOTIFICENTER [NSNotificationCenter defaultCenter]
#define USERDEFAULT [NSUserDefaults standardUserDefaults]
#define RGB(r, g, b)                                                           \
  [UIColor colorWithRed:r / 255. green:g / 255. blue:b / 255. alpha:1.]
#define RGBA(r, g, b, a)                                                       \
  [UIColor colorWithRed:r / 255. green:g / 255. blue:b / 255. alpha:a]
#define UIColorFromRGB(rgbValue)                                               \
  [UIColor colorWithRed:((float)((rgbValue & 0xFF0000) >> 16)) / 255.0         \
                  green:((float)((rgbValue & 0xFF00) >> 8)) / 255.0            \
                   blue:((float)(rgbValue & 0xFF)) / 255.0                     \
                  alpha:1.0]
#define UIColorFromRGBorAlpha(rgbValue, alphaValue)                            \
  [UIColor colorWithRed:((float)((rgbValue & 0xFF0000) >> 16)) / 255.0         \
                  green:((float)((rgbValue & 0xFF00) >> 8)) / 255.0            \
                   blue:((float)(rgbValue & 0xFF)) / 255.0                     \
                  alpha:alphaValue]

#define AppVersion                                                             \
  [[[NSBundle mainBundle] infoDictionary]                                      \
      objectForKey:@"CFBundleShortVersionString"]
#define KImage_name(_name) [UIImage imageNamed:_name]

#define KHomeNavHeight 84
#define kTabarHeight 48
#define STATUS_BAR_HEIGHT                                                      \
  [UIApplication sharedApplication].statusBarFrame.size.height

#define kDateFormat @"yy年M月d日"
#define ContenText @"ContenText"

#define kDefaultCellHeight 50
#define RGBACOLOR(r, g, b, a)                                                  \
  [UIColor colorWithRed:r / 255.f green:g / 255.f blue:b / 255.f alpha:a]
#define kDefaultMargin 8
#define CommonRelease(__v)
#define CommonSuperDealloc()

#define SourceHanSerifMediumFont(fontSize)                                     \
  [UIFont systemFontOfSize:fontSize weight:UIFontWeightRegular]

#define SourceHanSerifRegularFont(fontSize)                                    \
  [UIFont systemFontOfSize:fontSize weight:UIFontWeightRegular]

#define SourceHanSerifSemiBoldFont(fontSize)                                   \
  [UIFont systemFontOfSize:fontSize weight:UIFontWeightMedium]

#define SourceHanSerifBoldFont(fontSize)                                       \
  [UIFont systemFontOfSize:fontSize weight:UIFontWeightSemibold]

#define DinCondensedBoldFont(fontSize) [UIFont boldSystemFontOfSize:fontSize]

#define DinCondensedFont(fontSize) [UIFont systemFontOfSize:fontSize]

#define AvenirNextHeavyFont(fontSize)                                          \
  [UIFont fontWithName:@"AvenirNext-Heavy" size:fontSize]

#define IsEnablePersonalCenterVCMainTableViewScroll                            \
  @"IsEnablePersonalCenterVCMainTableViewScroll"
#define CurrentSelectedChildViewControllerIndex                                \
  @"CurrentSelectedChildViewControllerIndex"
#define PersonalCenterVCBackingStatus @"PersonalCenterVCBackingStatus"

#define SegementViewChildVCBackToTop @"segementViewChildVCBackToTop"
#define AttIsEnablePersonalCenterVCMainTableViewScroll                         \
  @"AttIsEnablePersonalCenterVCMainTableViewScroll"
#define AttCurrentSelectedChildViewControllerIndex                             \
  @"AttCurrentSelectedChildViewControllerIndex"
#define AttPersonalCenterVCBackingStatus @"AttPersonalCenterVCBackingStatus"

#define AttSegementViewChildVCBackToTop @"AttsegementViewChildVCBackToTop"
#define ClouseLoginView @"ClouseLoginView"
#define WXloadOfCode @"WXloadOfCode"
#define DyreloadData @"DyreloadData"
#define DySendSussreloadData @"DySendSussreloadData"
#define ChangeMyUserInfoMation @"ChangeMyUserInfoMation"
#define SettingLoginOut @"SettingLoginOut"
#define MyUserAffReload @"MyUserAffReload"
#define MyAttToAttMy @"MyAttToAttMy"
#define AttMyToMyAtt @"AttMyToMyAtt"
#define TacthBtnofCommentReload @"tacthBtnofCommentReload"
#define TacthBtnofCommentForSeaReload @"TacthBtnofCommentForSeaReload"
#define ToutchLikeReloadOfAffair @"ToutchLikeReloadOfAffair"
#define ToutchLikeReloadOfUserAffair @"ToutchLikeReloadOfUserAffair"
#define NewMessage @"NewMessage"
#define ReloadLiuyanList @"ReloadLiuyanList"
#define NewMessageOfisLoading @"NewMessageOfisLoading"
#define AllReloadUI @"AllReloadUI"
#define UserHomeReload @"UserHomeReload"
#define ReloadIndexOne @"ReloadIndexOne"
#define ReloadIndexTwo @"ReloadIndexTwo"
#define ReloadIndexThree @"ReloadIndexThree"
#define ReloadIndexFour @"ReloadIndexFour"
#define ReopenSideMenu @"NotificationForOpenLeftMenu"

#define KColor_Random                                                          \
  [UIColor colorWithHue:(arc4random() % 256 / 256.0)                           \
             saturation:((arc4random() % 128 / 256.0) + 0.5)                   \
             brightness:((arc4random() % 128 / 256.0) + 0.5)                   \
                  alpha:1]

#define KColor_Main RGB(73, 67, 70)
#define KColor_MainGray [UIColor grayColor]
#define KColor_White UIColorFromRGB(0Xffffff)
#define KColor_Red UIColorFromRGB(0Xff5967)
#define KColor_Green UIColorFromRGB(0X33CDC7)
#define KColor_AddressGray UIColorFromRGB(0X9597A0)
#define KColor_Gray UIColorFromRGB(0X656976)
#define KColor_LineGray UIColorFromRGB(0XF2F5F8)
#define KColor_ExtraLightGray UIColorFromRGB(0xF8FAFB)
#define KColor_LightGray UIColorFromRGB(0XF5F5F5)
#define KColor_loginGray UIColorFromRGB(0XE3E4E7)
#define KColor_textGray UIColorFromRGBorAlpha(0X13192C, 0.45)
#define KColor_textDarkGray UIColorFromRGBorAlpha(0X13192C, 0.65)
#define KColor_CommentWhite UIColorFromRGB(0XFCFCFE)
#define KColor_Man UIColorFromRGB(0x4682B4)
#define KColor_Women UIColorFromRGB(0xF1B7B7)
#define KColor_LightMan UIColorFromRGB(0xF0FFFF)
#define KColor_LightWomen UIColorFromRGB(0xFAE5E5)

#define KColor_Blue UIColorFromRGB(0x5BA3BC)

#define KColor_codeGray UIColorFromRGB(0XC1C2C7)
#define KColor_Black UIColorFromRGB(0X363B4B)
// #define KColor_HighBlack UIColorFromRGB(0X13192C)
#define KColor_HighBlack UIColorFromRGB(0X1A223D)
#define KColor_HighBlue UIColorFromRGB(0X445A7E)
#define KColor_HighBlack2 UIColorFromRGB(0X222C4F)
#define KColor_HighBlack3 UIColorFromRGB(0X293560)
#define KColor_TabarColor UIColorFromRGB(0X0A1024)
#define KColor_codeBlue UIColorFromRGB(0X5ECCD6)
#define KColor_heziGreen UIColorFromRGB(0X33BFCC)
#define KColor_heighGray UIColorFromRGB(0XF2F4F7)
#define KColor_heighGray2 UIColorFromRGB(0XEBEEF3)

#define KColor_keyboardBlack UIColorFromRGB(0X3F3F3F)
#define KColor_keyboardTxtGray UIColorFromRGBorAlpha(0Xffffff, 0.54)

#define KColor_switchLightBlue UIColorFromRGB(0x5DADE2)
#define KColor_switchLightPink UIColorFromRGB(0xF28CB2)
#define KColor_textTinyGray UIColorFromRGBorAlpha(0X13192C, 0.27)
#define KColor_titleDarkGray UIColorFromRGBorAlpha(0X13192C, 0.87)
#define KColor_detailDarkGray UIColorFromRGBorAlpha(0X13192C, 0.54)
#define KColor_detailLightGray UIColorFromRGBorAlpha(0X000000, 0.27)
#define KColor_replyDarkGray UIColorFromRGB(0X131415)
#define KColor_replyGray UIColorFromRGBorAlpha(0X131415, 0.87)
#define KColor_replyDateGray UIColorFromRGBorAlpha(0X191919, 0.27)
#define KColor_replyBgBlack UIColorFromRGB(0X2E2F30)
#define KColor_DarkKeyboard UIColorFromRGB(0X4C4D4E)
#define KColor_ImSoundBlack UIColorFromRGB(0X191919)

#define iPhoneX                                                                \
  ([UIScreen instancesRespondToSelector:@selector(currentMode)]                \
       ? CGSizeEqualToSize(CGSizeMake(1125, 2436),                             \
                           [[UIScreen mainScreen] currentMode].size)           \
       : NO)
#define iPhoneXR                                                               \
  ([UIScreen instancesRespondToSelector:@selector(currentMode)]                \
       ? CGSizeEqualToSize(CGSizeMake(828, 1792),                              \
                           [[UIScreen mainScreen] currentMode].size)           \
       : NO)
#define iPhoneXS_Max                                                           \
  ([UIScreen instancesRespondToSelector:@selector(currentMode)]                \
       ? CGSizeEqualToSize(CGSizeMake(1242, 2688),                             \
                           [[UIScreen mainScreen] currentMode].size)           \
       : NO)

#define FT_UI_IS_IPAD (UI_USER_INTERFACE_IDIOM() == UIUserInterfaceIdiomPad)
#define FT_Is_iPhoneX                                                          \
  ([UIScreen instancesRespondToSelector:@selector(currentMode)]                \
       ? CGSizeEqualToSize(CGSizeMake(1125, 2436),                             \
                           [[UIScreen mainScreen] currentMode].size)           \
       : NO)

#define FT_Is_iPhoneXR                                                         \
  ([UIScreen instancesRespondToSelector:@selector(currentMode)]                \
       ? CGSizeEqualToSize(CGSizeMake(828, 1792),                              \
                           [[UIScreen mainScreen] currentMode].size) &&        \
             !FT_UI_IS_IPAD                                                    \
       : NO)

#define FT_Is_iPhoneXS                                                         \
  ([UIScreen instancesRespondToSelector:@selector(currentMode)]                \
       ? CGSizeEqualToSize(CGSizeMake(1125, 2436),                             \
                           [[UIScreen mainScreen] currentMode].size) &&        \
             !FT_UI_IS_IPAD                                                    \
       : NO)

#define FT_Is_iPhoneXS_MAX                                                     \
  ([UIScreen instancesRespondToSelector:@selector(currentMode)]                \
       ? CGSizeEqualToSize(CGSizeMake(1242, 2688),                             \
                           [[UIScreen mainScreen] currentMode].size) &&        \
             !FT_UI_IS_IPAD                                                    \
       : NO)

#define FT_IS_IPhoneX_All                                                      \
  ({                                                                           \
    BOOL isPhoneX = NO;                                                        \
    if (@available(iOS 11.0, *)) {                                             \
      UIWindow *window =                                                       \
          [[[UIApplication sharedApplication] delegate] window];               \
      isPhoneX = window.safeAreaInsets.bottom > 0.0;                           \
    }                                                                          \
    isPhoneX;                                                                  \
  })

#define DocumentPath                                                           \
  [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask,  \
                                       YES) objectAtIndex:0]
#define CachePath                                                              \
  [NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask,    \
                                       YES) objectAtIndex:0]

#define kWidth self.frame.size.width
#define NORMAL_FONT [UIFont systemFontOfSize:16]
#define NORMAL_COLOR [UIColor blackColor]
#define SELECTED_COLOR [UIColor orangeColor]

#endif
