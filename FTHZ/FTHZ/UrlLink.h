#ifndef UrlLink_h
#define UrlLink_h

#define KURLCommonCode @"/n/common/code"
#define KURLValidate_code @"/n/common/validate_code"
#define KURLQWXLogin @"/n/common/wx/login"
#define KURLUser_userinfo @"/n/user/userinfo"
#define KURLRate_Attribute @"/n/rate/attribute"
#define KURLRate_Calculate @"/n/rate/calculate"
#define KURLContent @"/n/content"
#define KURLUserVerify @"/n/common/user/verify"
#define KURLUserVerifyQA @"/n/common/user/verify/qa"
#define KURLPHertzCompute @"/p/hertz/compute"
#define KURLPNEWAffairList @"/52Hz/r/ocean"
#define KURLPNEWAffairListNew @"/52Hz/r/ocean/new"
#define KURLPAffairFollowList @"/p/affair/followed/list"
#define KURLPAffairFollowListNew @"/p/affair/followed/list/new"
#define KURLPAffairTagList @"/n/content/by/tag"
#define KURLPAffairUidList @"/p/affair/list_by_uid"
#define KURLPAffairUidSpecialList @"/n/operations"
#define KURLPAffairUidSpecialInfo @"/n/operations/detail"
#define KURLPAffairDetail @"/n/content"
#define KURLNEWGetAffairDetail @"/n/content/new"
#define KURLUserSimilar @"/n/user/similar"
#define KURLCertificate @"/n/common/certificate"
#define KURLPAffairLikes @"/p/affair/likes"
#define KURLPContentLike @"/n/content/like"
#define KURLPDoAffairComments @"/n/content/comment"
#define KURLPContentComment @"/n/content/comment"
#define KURLUserOtherUserinfo @"/n/user/otherUserinfo"
#define KURLGetFreeActivity @"/n/common/free"
#define KURLAttentionAdd @"/n/attention/add"
#define KURLAttentionRelease @"/n/attention/release"
#define KURLAttentionFans @"/n/fans"
#define KURLAttentionFansNew @"/n/fans/new"
#define KURLMyAttentionRelease @"/n/attention"
#define KURLMyAttentionNewRelease @"/n/attention/new"
#define KURLIMUserInfo @"/n/user/imUserinfo"
#define KURLIMUserReport @"/n/report"
#define KURLGETIP @"/n/common/lpstatus"
#define KURLPOSTIP @"/n/common/lpstatus"
#define KURLPOSTDelDynamic @"/n/content/del"
#define KURLPOSTBlackUser @"/n/user/black"
#define KURLGetBlackUser @"/n/user/black"
#define KURLPOSTOUTBlack @"/n/user/black/cancel"
#define KURLGETMessageNotification @"/n/common/notification"
#define KURLGETShudongNotification @"/n/shudong/list"
#define KURLGETLiuyanList @"/n/liuyan/list"
#define KURLPOSTLiuyanRead @"/n/liuyan/read"
#define KURLGETLiuyanUnreadCount @"/n/liuyan/unread"
#define KURLPOSTLiuyanDelete @"/n/liuyan/delete"
#define KURLPOSTLiuyanClearAll @"/n/user/clear/liuyan"
#define KURLPostChangeMessageStats @"/n/common/notification"
#define KURLPostDelNotification @"/n/common/notification/del"
#define KURLPostChangeAccountName @"/n/user/account"
#define KURLPostChangePassword @"/n/user/password"
#define KURLGetCheckPassword @"/n/user/password/status"
#define KURLGetForgotPassword @"/n/user/forget/code"
#define KURLPostForgotPasswordVerify @"/n/user/validate_code"
#define KURLGetAFL @"/n/user/afl"
#define KURLPostClearNotic @"/n/user/clear/notice"
#define KURLPostAccountLogin @"/n/common/login/password"
#define KURLPostLoadInvitationCode @"/n/common/invitationCode"
#define KURLGETInvitationStatus @"/n/common/invitationStatus"
#define KURLGETUserMessageNum @"/n/common/notification/unread"
#define KURLGETMineUserMessageNum @"/n/user/faccount"
#define KURLGETHomeStats @"/n/common/guidemapstatus"
#define KURLGETChangeHomeStats @"/n/common/guidestatus"
#define KURLPOSTdeviceToken @"/n/user/deviceToken"
#define KURLPOSTIMMessage @"/n/common/push"
#define KURLAffairPostTag @"/n/content/tag"
#define KURLPostHideAffair @"/n/content/hide"
#define KURLPostTopAffair @"/n/content/top"
#define KURLPClearPush @"/n/user/removePush"
#define KURLMusicInfo @"/n/content/musicinfo"
#define KURLMusicPost @"/n/content/music"
#define KURLUpdateSSID @"/n/common/updatessid"
#define KURLWaters @"/n/island/waters"
#define KURLAllDiscuss @"/n/island/alldiscuss"
#define KURLChannelDiscuss @"/n/island/discuss"
#define KURLShudongCreate @"/n/shudong"
#define KURLLiuyanCreate @"/n/liuyan"
#define KURLChannelComment @"/n/island/comment"
#define KURLChannelLikeComment @"/n/island/like"
#define KURLDeletDiscuss @"/n/island/content"
#define KURLGetFriends @"/n/friends"
#define KURLGetCoCreation @"/n/user/cocreation"
#define KURLValidateCodeNew @"/n/common/validate_code/new"
#define KURLWxLoginNew @"/n/common/wx/login/new"
#define KURLInvitCode @"/n/user/invit/code"
#define KURLValidateInvitCode @"/n/common/invitationCode/new"
#define KURLInvitCodeStatus @"/n/common/inviteCode"
#define KURLHeziRecommen @"/n/recommen"
#define KURLHeziMobileJudge @"/n/common/mobile/judge"
#define KURLRecommenBySearch @"/n/user/recommen/by/search"
#define KURLUnreadStatus @"/n/user/unread/status"

#endif
