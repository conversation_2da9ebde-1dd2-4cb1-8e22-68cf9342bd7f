#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <TZImagePickerController/TZImagePickerController.h>

NS_ASSUME_NONNULL_BEGIN

typedef void(^TZImagePickerCompletionHandler)(NSArray<UIImage *> * _Nullable images, NSArray<PHAsset *> * _Nullable assets, BOOL isOriginal);
typedef void(^TZImagePickerCancelHandler)(void);

@interface TZImagePickerManager : NSObject

+ (instancetype)sharedManager;

- (void)presentPhotoPickerWithViewController:(UIViewController *)viewController
                                    maxCount:(NSInteger)maxCount
                                    delegate:(id<TZImagePickerControllerDelegate>)delegate
                          completionHandler:(TZImagePickerCompletionHandler)completionHandler
                               cancelHandler:(TZImagePickerCancelHandler)cancelHandler;

- (void)presentSinglePhotoPickerWithViewController:(UIViewController *)viewController
                                        allowCrop:(BOOL)allowCrop
                                         delegate:(id<TZImagePickerControllerDelegate>)delegate
                               completionHandler:(TZImagePickerCompletionHandler)completionHandler
                                    cancelHandler:(TZImagePickerCancelHandler)cancelHandler;

- (void)presentCameraWithViewController:(UIViewController *)viewController
                      allowCrop:(BOOL)allowCrop
                      delegate:(id<TZImagePickerControllerDelegate>)delegate
            completionHandler:(TZImagePickerCompletionHandler)completionHandler
                 cancelHandler:(TZImagePickerCancelHandler)cancelHandler;

- (void)presentPickerWithViewController:(UIViewController *)viewController
                               maxCount:(NSInteger)maxCount
                          allowPickingVideo:(BOOL)allowPickingVideo
                          allowPickingImage:(BOOL)allowPickingImage
                                delegate:(id<TZImagePickerControllerDelegate>)delegate
                      completionHandler:(TZImagePickerCompletionHandler)completionHandler
                           cancelHandler:(TZImagePickerCancelHandler)cancelHandler;

@end

NS_ASSUME_NONNULL_END