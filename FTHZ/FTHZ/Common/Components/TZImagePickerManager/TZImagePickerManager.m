#import "TZImagePickerManager.h"
#import <Photos/Photos.h>

@interface TZImagePickerManager () <TZImagePickerControllerDelegate,
                                    UINavigationControllerDelegate>

@property(nonatomic, copy) TZImagePickerCompletionHandler completionHandler;
@property(nonatomic, copy) TZImagePickerCancelHandler cancelHandler;

@end

@implementation TZImagePickerManager

+ (instancetype)sharedManager {
  static TZImagePickerManager *instance = nil;
  static dispatch_once_t onceToken;
  dispatch_once(&onceToken, ^{
    instance = [[TZImagePickerManager alloc] init];
  });
  return instance;
}

#pragma mark - Public Methods

- (void)presentPhotoPickerWithViewController:(UIViewController *)viewController
                                    maxCount:(NSInteger)maxCount
                                    delegate:
                                        (id<TZImagePickerControllerDelegate>)
                                            delegate
                           completionHandler:
                               (TZImagePickerCompletionHandler)completionHandler
                               cancelHandler:
                                   (TZImagePickerCancelHandler)cancelHandler {
  self.completionHandler = completionHandler;
  self.cancelHandler = cancelHandler;

  TZImagePickerController *imagePickerVc =
      [[TZImagePickerController alloc] initWithMaxImagesCount:maxCount
                                                     delegate:delegate];

  [self configureImagePicker:imagePickerVc];

  imagePickerVc.allowPickingVideo = YES;
  imagePickerVc.allowPickingGif = YES;
  imagePickerVc.allowPickingOriginalPhoto = NO;

  __weak typeof(self) weakSelf = self;
  [imagePickerVc setDidFinishPickingPhotosHandle:^(NSArray<UIImage *> *photos,
                                                   NSArray *assets,
                                                   BOOL isSelectOriginalPhoto) {
    if (weakSelf.completionHandler) {
      weakSelf.completionHandler(photos, assets, isSelectOriginalPhoto);
    }
  }];

  [viewController presentViewController:imagePickerVc
                               animated:YES
                             completion:nil];
}

- (void)
    presentSinglePhotoPickerWithViewController:
        (UIViewController *)viewController
                                     allowCrop:(BOOL)allowCrop
                                      delegate:
                                          (id<TZImagePickerControllerDelegate>)
                                              delegate
                             completionHandler:(TZImagePickerCompletionHandler)
                                                   completionHandler
                                 cancelHandler:
                                     (TZImagePickerCancelHandler)cancelHandler {
  self.completionHandler = completionHandler;
  self.cancelHandler = cancelHandler;

  TZImagePickerController *imagePickerVc =
      [[TZImagePickerController alloc] initWithMaxImagesCount:1
                                                     delegate:delegate];

  [self configureImagePicker:imagePickerVc];

  imagePickerVc.allowPickingVideo = NO;
  imagePickerVc.allowPickingGif = NO;
  imagePickerVc.allowCrop = allowCrop;
  if (allowCrop) {
    NSInteger screenWidth = [UIScreen mainScreen].bounds.size.width;
    NSInteger screenHeight = [UIScreen mainScreen].bounds.size.height;
    imagePickerVc.cropRect = CGRectMake(0, (screenHeight - screenWidth) / 2,
                                        screenWidth, screenWidth);
  }

  __weak typeof(self) weakSelf = self;
  [imagePickerVc setDidFinishPickingPhotosHandle:^(NSArray<UIImage *> *photos,
                                                   NSArray *assets,
                                                   BOOL isSelectOriginalPhoto) {
    if (weakSelf.completionHandler) {
      weakSelf.completionHandler(photos, assets, isSelectOriginalPhoto);
    }
  }];

  [viewController presentViewController:imagePickerVc
                               animated:YES
                             completion:nil];
}

- (void)presentCameraWithViewController:(UIViewController *)viewController
                              allowCrop:(BOOL)allowCrop
                               delegate:
                                   (id<TZImagePickerControllerDelegate>)delegate
                      completionHandler:
                          (TZImagePickerCompletionHandler)completionHandler
                          cancelHandler:
                              (TZImagePickerCancelHandler)cancelHandler {
  AVAuthorizationStatus authStatus =
      [AVCaptureDevice authorizationStatusForMediaType:AVMediaTypeVideo];
  if (authStatus == AVAuthorizationStatusRestricted ||
      authStatus == AVAuthorizationStatusDenied) {
    UIAlertController *alert = [UIAlertController
        alertControllerWithTitle:@"无法使用相机"
                         message:
                             @"请在iPhone的'设置 - 隐私 - 相机'中允许访问相机"
                  preferredStyle:UIAlertControllerStyleAlert];
    [alert addAction:[UIAlertAction actionWithTitle:@"取消"
                                              style:UIAlertActionStyleCancel
                                            handler:nil]];
    [alert
        addAction:
            [UIAlertAction
                actionWithTitle:@"设置"
                          style:UIAlertActionStyleDefault
                        handler:^(UIAlertAction *_Nonnull action) {
                          [[UIApplication sharedApplication]
                                        openURL:
                                            [NSURL
                                                URLWithString:
                                                    UIApplicationOpenSettingsURLString]
                                        options:@{}
                              completionHandler:nil];
                        }]];
    [viewController presentViewController:alert animated:YES completion:nil];
    return;
  }

  self.completionHandler = completionHandler;
  self.cancelHandler = cancelHandler;

  UIImagePickerController *imagePickerVc =
      [[UIImagePickerController alloc] init];
  imagePickerVc.sourceType = UIImagePickerControllerSourceTypeCamera;
  imagePickerVc.delegate = self;

  [viewController presentViewController:imagePickerVc
                               animated:YES
                             completion:nil];
}

- (void)presentPickerWithViewController:(UIViewController *)viewController
                               maxCount:(NSInteger)maxCount
                      allowPickingVideo:(BOOL)allowPickingVideo
                      allowPickingImage:(BOOL)allowPickingImage
                               delegate:
                                   (id<TZImagePickerControllerDelegate>)delegate
                      completionHandler:
                          (TZImagePickerCompletionHandler)completionHandler
                          cancelHandler:
                              (TZImagePickerCancelHandler)cancelHandler {
  self.completionHandler = completionHandler;
  self.cancelHandler = cancelHandler;

  TZImagePickerController *imagePickerVc =
      [[TZImagePickerController alloc] initWithMaxImagesCount:maxCount
                                                     delegate:delegate];

  [self configureImagePicker:imagePickerVc];

  imagePickerVc.allowPickingVideo = allowPickingVideo;
  imagePickerVc.allowPickingImage = allowPickingImage;
  imagePickerVc.allowPickingGif = YES;
  imagePickerVc.allowPickingMultipleVideo = YES;
  imagePickerVc.allowPickingOriginalPhoto = NO;

  __weak typeof(self) weakSelf = self;
  [imagePickerVc setDidFinishPickingPhotosHandle:^(NSArray<UIImage *> *photos,
                                                   NSArray *assets,
                                                   BOOL isSelectOriginalPhoto) {
    if (weakSelf.completionHandler) {
      weakSelf.completionHandler(photos, assets, isSelectOriginalPhoto);
    }
  }];

  [imagePickerVc
      setDidFinishPickingVideoHandle:^(UIImage *coverImage, id asset) {
        if (weakSelf.completionHandler) {
          weakSelf.completionHandler(@[ coverImage ], @[ asset ], NO);
        }
      }];

  [viewController presentViewController:imagePickerVc
                               animated:YES
                             completion:nil];
}

#pragma mark - Private Methods

- (void)configureImagePicker:(TZImagePickerController *)imagePickerVc {
  imagePickerVc.isSelectOriginalPhoto = NO;
  imagePickerVc.iconThemeColor = [UIColor colorWithRed:31 / 255.0
                                                 green:185 / 255.0
                                                  blue:34 / 255.0
                                                 alpha:1.0];
  imagePickerVc.showPhotoCannotSelectLayer = YES;
  imagePickerVc.showSelectedIndex = YES;

  imagePickerVc.allowTakePicture = YES;
  imagePickerVc.allowTakeVideo = YES;
  imagePickerVc.videoMaximumDuration = 60;

  imagePickerVc.naviBgColor = KColor_HighBlack;
  imagePickerVc.naviTitleColor = KColor_White;

  imagePickerVc.delegate = self;

  if ([imagePickerVc
          respondsToSelector:@selector(photoPickerPageUIConfigBlock)]) {
    imagePickerVc.photoPickerPageUIConfigBlock =
        ^(UICollectionView *collectionView, UIView *bottomToolBar,
          UIButton *previewButton, UIButton *originalPhotoButton,
          UILabel *originalPhotoLabel, UIButton *doneButton,
          UIImageView *numberImageView, UILabel *numberLabel,
          UIView *divideLine) {
          collectionView.backgroundColor = KColor_HighBlack;
          bottomToolBar.backgroundColor = KColor_HighBlack;
          [previewButton setTitleColor:KColor_White
                              forState:UIControlStateNormal];
          [originalPhotoButton setTitleColor:KColor_White
                                    forState:UIControlStateNormal];
          [doneButton setTitleColor:KColor_White forState:UIControlStateNormal];
          previewButton.hidden = YES;
        };
  }
}

#pragma mark - UIImagePickerControllerDelegate

- (void)imagePickerController:(UIImagePickerController *)picker
    didFinishPickingMediaWithInfo:
        (NSDictionary<UIImagePickerControllerInfoKey, id> *)info {
  [picker dismissViewControllerAnimated:YES completion:nil];

  UIImage *image = info[UIImagePickerControllerOriginalImage];

  if (self.completionHandler) {
    self.completionHandler(@[ image ], nil, NO);
  }
}

- (void)imagePickerControllerDidCancel:(TZImagePickerController *)picker {
  if (self.cancelHandler) {
    self.cancelHandler();
  }
}

@end