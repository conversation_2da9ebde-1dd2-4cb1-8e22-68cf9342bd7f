#import "FTHZCoordinator.h"
#import "DynamicDetailVC.h"
#import "WhaleDetailVC.h"

NS_ASSUME_NONNULL_BEGIN

@implementation FTHZCoordinator

+ (instancetype)shared {
  static FTHZCoordinator *mgr;
  static dispatch_once_t onceToken;
  dispatch_once(&onceToken, ^{
    mgr = [[FTHZCoordinator alloc] init];
  });
  return mgr;
}

- (void)openFeedDetailWithModel:(DynamicModelResult *)model
                autoShowComment:(BOOL)autoShowComment {
  UINavigationController *nav = [UIViewController topNavigationController];
  DynamicDetailVC *ddVC = [[DynamicDetailVC alloc] init];
  ddVC.ddy = model;
  ddVC.isTouchComment = autoShowComment;
  [nav pushViewController:ddVC animated:YES];
}

- (void)openUserHomePageWithUserID:(NSString *)userID {
  WhaleDetailVC *infoVC = [[WhaleDetailVC alloc] init];
  infoVC.uid = userID;
  [[UIViewController topNavigationController] pushViewController:infoVC
                                                        animated:YES];
}

@end

NS_ASSUME_NONNULL_END
