#import "FTHZNetworkManager.h"
#import "AppConfig.h"
#import "FTHZRequestEncryptor.h"

NS_ASSUME_NONNULL_BEGIN

NSString *const kHZHTTPMethodGET = @"GET";
NSString *const kHZHTTPMethodPOST = @"POST";
NSString *const kHZHTTPMethodPUT = @"PUT";
NSString *const kHZHTTPMethodDELETE = @"DELETE";
NSString *const kHZHTTPMethodPATCH = @"PATCH";

NSString *const kHTTPHeaderFieldSSIDKey = @"ssid";

extern NSString *AFPercentEscapedStringFromString(NSString *string);

#pragma mark - FTHZHTTPHeaderMaker
@interface FTHZHTTPHeaderMaker ()

@property(nonatomic, strong, readonly)
    NSMutableDictionary<NSString *, NSString *> *headerFields;

@end

@implementation FTHZHTTPHeaderMaker

- (instancetype)init {
  self = [super init];
  if (self) {
    _headerFields = [[NSMutableDictionary alloc] init];
  }
  return self;
}

- (void)setValue:(NSString *_Nullable)value forHeaderField:(NSString *)key {
  self.headerFields[key] = value;
}

- (NSDictionary<NSString *, NSString *> *)headers {
  return [self.headerFields copy];
}

@end

#pragma mark - FTHZURLQueryMaker
@interface FTHZURLQueryMaker ()

@property(nonatomic, strong, readonly)
    NSMutableArray<NSURLQueryItem *> *urlQueries;

@end

@implementation FTHZURLQueryMaker

- (instancetype)init {
  self = [super init];
  if (self) {
    _urlQueries = [[NSMutableArray alloc] init];
  }
  return self;
}

- (void)addValue:(NSString *)value forKey:(NSString *)key {
  [self.urlQueries
      addObject:[[NSURLQueryItem alloc]
                    initWithName:AFPercentEscapedStringFromString(key)
                           value:AFPercentEscapedStringFromString(value)]];
}

- (NSArray<NSURLQueryItem *> *)queries {
  return [self.urlQueries copy];
}
@end

#pragma mark - FTHZNetworkTask
@interface FTHZNetworkTask ()
@property(nonatomic, strong, nullable) NSDictionary *jsonBody;
@end

#pragma mark - FTHZNetworkTask
@implementation FTHZNetworkTask

- (instancetype)initWithMethod:(NSString *)method
                           url:(NSURL *)url
                     bizParser:(id<FTHZBizResponseParser> _Nullable)bizParser {
  NSAssert([method isKindOfClass:[NSString class]],
           @"method should be a String");
  NSAssert([url isKindOfClass:[NSURL class]], @"url should be a NSURL");
  self = [super init];
  if (self) {
    _userInfoPolicy = FTHZNetworkUserInfoPolicyFetchSafely;
    _url = [url copy];
    _method = [method copy];
    _headerMaker = [[FTHZHTTPHeaderMaker alloc] init];
    _queryMaker = [[FTHZURLQueryMaker alloc] init];
    _body = nil;
    _timeout = 30;
    _cachePolicy = NSURLRequestUseProtocolCachePolicy;
    _bizParser = bizParser;
    _bizPreProcessor = [[FTHZUniversalBizCodeProcessor alloc] init];
    _callbackQueue = dispatch_get_main_queue();
    _autoLoginIfNotLogin = YES;
  }
  return self;
}

- (FTHZNetworkTask *)makeHeader:
    (void (^_Nullable)(FTHZHTTPHeaderMaker *_Nonnull))headerMaker {
  headerMaker(self.headerMaker);
  return self;
}

- (FTHZNetworkTask *)makerQuery:
    (void (^_Nullable)(FTHZURLQueryMaker *_Nonnull))queryMaker {
  queryMaker(self.queryMaker);
  return self;
}

- (FTHZNetworkTask *)setJSONBody:(id)json {
  NSAssert([NSJSONSerialization isValidJSONObject:json],
           @"Should be a valid json!");
  NSError *error;
  NSData *data = [NSJSONSerialization dataWithJSONObject:json
                                                 options:0
                                                   error:&error];
  self.body = data;
  if (!error) {
    self.jsonBody = json;
  }
  return self;
}

- (FTHZNetworkTask *)changeUserInfoPolicy:(FTHZNetworkUserInfoPolicy)policy {
  self.userInfoPolicy = policy;
  return self;
}

- (FTHZNetworkTask *)setCallbackInMainQueue {
  self.callbackQueue = dispatch_get_main_queue();
  return self;
}

- (void)requestWithCompletion:
    (FTHZNetworkTaskCompletionHandler _Nullable)completion {
  [[FTHZNetworkManager sharedInstance] requestWithTask:self
                                            completion:completion];
}

- (NSURL *)getFinalURL {
  NSURLComponents *cp = [[NSURLComponents alloc] initWithURL:self.url
                                     resolvingAgainstBaseURL:NO];
  NSMutableString *queryString =
      [cp.percentEncodedQuery mutableCopy] ?: [[NSMutableString alloc] init];
  for (NSURLQueryItem *item in self.queryMaker.queries) {
    if (queryString.length > 0) {
      [queryString appendString:@"&"];
    }
    [queryString appendFormat:@"%@=%@", item.name, item.value];
  }
  cp.percentEncodedQuery = queryString.length > 0 ? [queryString copy] : nil;
  return cp.URL;
}

- (RACSignal *)rac_request {
  return [RACSignal createSignal:^RACDisposable *_Nullable(
                        id<RACSubscriber> _Nonnull subscriber) {
    [self requestWithCompletion:^(
              BOOL isSuccess, NSURLResponse *_Nullable response,
              id _Nullable model, NSError *_Nullable anError) {
      if (isSuccess) {
        [subscriber sendNext:model];
        [subscriber sendCompleted];
      } else {
        [subscriber sendError:anError];
      }
    }];
    return [RACDisposable disposableWithBlock:^{
    }];
  }];
}

@end

#pragma mark - FTHZNetworkManager

@interface FTHZNetworkManager ()

@end

@implementation FTHZNetworkManager

+ (instancetype)sharedInstance {
  static FTHZNetworkManager *manager;
  static dispatch_once_t onceToken;
  dispatch_once(&onceToken, ^{
    manager = [[FTHZNetworkManager alloc] init];
  });
  return manager;
}

- (instancetype)init {

  self = [super initWithBaseURL:baseURL() sessionConfiguration:nil];
  if (self) {
    self.completionQueue = dispatch_queue_create("52hz.NetworkCallbackQueue",
                                                 DISPATCH_QUEUE_SERIAL);
    self.responseSerializer = [AFHTTPResponseSerializer serializer];
  }
  return self;
}

- (void)addDefaultAdditionalInfoToRequest:(NSMutableURLRequest *)req
                               ssidPolicy:
                                   (FTHZNetworkUserInfoPolicy)userInfoPolicy {
  NSString *adid =
      [[[ASIdentifierManager sharedManager] advertisingIdentifier] UUIDString];
  CGRect rect_screen = [[UIScreen mainScreen] bounds];
  CGSize size_screen = rect_screen.size;
  CGFloat scale_screen = [UIScreen mainScreen].scale;
  CGFloat width = size_screen.width * scale_screen;
  CGFloat height = size_screen.height * scale_screen;
  NSString *widthStr = [NSString stringWithFormat:@"%.f", width];
  NSString *heightStr = [NSString stringWithFormat:@"%.f", height];
  NSString *screenStr =
      [NSString stringWithFormat:@"%@X%@", heightStr, widthStr];
  [req setValue:@"application/json" forHTTPHeaderField:@"Content-Type"];
  [req setValue:[NemoUtil getAppVersionint] forHTTPHeaderField:@"App_Version"];
  [req setValue:[NemoUtil getAppVersionint] forHTTPHeaderField:@"App_Version"];
  [req setValue:@"1" forHTTPHeaderField:@"App_Id"];
  [req setValue:@"AppStore" forHTTPHeaderField:@"Channel_Name"];
  [req setValue:screenStr forHTTPHeaderField:@"Resolution"];
  [req setValue:adid forHTTPHeaderField:@"Idfa"];
  [req setValue:[NemoUtil iphoneType] forHTTPHeaderField:@"Iphone_Type"];

  [req setValue:@"Hello, this is a secret message!"
      forHTTPHeaderField:@"secretCon"];
  [req setValue:@"975a461ad5e32f381e046679441aaedaa768456b47ba718701721aeb72caf"
                @"92b0403fd4e9eda6868f52c7d5247c61ed3"
      forHTTPHeaderField:@"secretRs"];

  switch (userInfoPolicy) {
  case FTHZNetworkUserInfoPolicyFetchSafely:
    [req setValue:AccountManager.token
        forHTTPHeaderField:kHTTPHeaderFieldSSIDKey];
    break;

  case FTHZNetworkUserInfoPolicyNone:
    break;

  case FTHZNetworkUserInfoPolicyFetchUnsafe:
    [req setValue:[AccountManager unsafeGetUserToken]
        forHTTPHeaderField:kHTTPHeaderFieldSSIDKey];
    break;
  }
}

- (void)requestWithTask:(FTHZNetworkTask *)theTask
             completion:(FTHZNetworkTaskCompletionHandler _Nullable)completion {
  id<FTHZBizResponseParser> bizParser = theTask.bizParser;
  id<FTHZBizPreProcessor> bizPreprocessor = theTask.bizPreProcessor;
  NSMutableURLRequest *request =
      [[NSMutableURLRequest alloc] initWithURL:[theTask getFinalURL]
                                   cachePolicy:theTask.cachePolicy
                               timeoutInterval:theTask.timeout];
  request.HTTPMethod = theTask.method;
  request.HTTPBody = theTask.body;
  NSString *path = [theTask.url path];
  NSString *encryptedHeader = [FTHZRequestEncryptor
      getEncryptedHeaderValue:theTask.jsonBody
                         path:path
                       method:theTask.method.lowercaseString];
  [request setValue:encryptedHeader forHTTPHeaderField:@"X-Encrypted-Data"];
  dispatch_queue_t callbackQueue = theTask.callbackQueue;
  [self addDefaultAdditionalInfoToRequest:request
                               ssidPolicy:theTask.userInfoPolicy];
  [theTask.headerMaker.headers
      enumerateKeysAndObjectsUsingBlock:^(
          NSString *_Nonnull key, NSString *_Nonnull obj, BOOL *_Nonnull stop) {
        [request addValue:obj forHTTPHeaderField:key];
      }];
  NSURLRequest *req = [request copy];
  NSURLSessionTask *task = [[FTHZNetworkManager sharedInstance]
      dataTaskWithRequest:req
           uploadProgress:nil
         downloadProgress:nil
        completionHandler:^(NSURLResponse *_Nonnull response,
                            id _Nullable responseObject,
                            NSError *_Nullable error) {
          NSCAssert(error || [responseObject isKindOfClass:[NSData class]],
                    @"Response Object must be data.");

          void (^callback)(NSURLResponse *, id _Nullable, NSError *_Nullable) =
              ^(NSURLResponse *resp, id _Nullable obj,
                NSError *_Nullable theError) {
                void (^action)(void) = ^{
                  if (completion) {
                    completion(theError == nil && obj, resp, obj, theError);
                  }
                };
                if (callbackQueue) {
                  dispatch_async(callbackQueue, ^{
                    action();
                  });
                } else {
                  action();
                }
              };
          if (error) {
            callback(response, responseObject, error);
            return;
          }
          if (bizParser) {
            responseObject = [bizParser tryParseResponseData:responseObject
                                                        task:theTask
                                                     request:request
                                                    response:responseObject
                                                       error:&error];
          }
          if (error) {
            callback(response, responseObject, error);
            return;
          }
          if (bizPreprocessor &&
              [bizPreprocessor isSupportProcessObject:responseObject
                                                 task:theTask
                                              request:request
                                             response:response]) {
            [bizPreprocessor processObject:responseObject
                                      task:theTask
                                   request:req
                                  response:response
                                completion:^(BOOL isSuccess,
                                             NSURLResponse *_Nullable response,
                                             id _Nullable model,
                                             NSError *_Nullable anError) {
                                  callback(response, model, anError);
                                }];
          } else {
            callback(response, responseObject, error);
          }
        }];
  [task resume];
}

@end

NS_ASSUME_NONNULL_END
