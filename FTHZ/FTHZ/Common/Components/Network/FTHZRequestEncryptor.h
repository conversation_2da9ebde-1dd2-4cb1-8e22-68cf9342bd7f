#import <Foundation/Foundation.h>

@interface FTHZRequestEncryptor : NSObject

+ (NSString *)getEncryptedHeaderValue:(NSDictionary *)params;

+ (NSString *)getEncryptedHeaderValue:(NSDictionary *)params
                                path:(NSString *)path
                              method:(NSString *)method;

+ (NSString *)generateTimestamp;
+ (NSString *)encryptParams:(NSDictionary *)params
                  timestamp:(NSString *)timestamp
                      path:(NSString *)path
                    method:(NSString *)method;

@end