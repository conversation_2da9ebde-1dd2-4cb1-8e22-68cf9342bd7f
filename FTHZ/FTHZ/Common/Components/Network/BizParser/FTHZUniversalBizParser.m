#import "FTHZUniversalBizParser.h"
#import "FTHZNetworkTask+UserInfo.h"

NS_ASSUME_NONNULL_BEGIN

@implementation FTHZCommonResponse

@end

@implementation FTHZUniversalBizCodeProcessor : NSObject

+ (NSIndexSet *)defaultAcceptCodes {
  return [NSIndexSet indexSetWithIndex:FTHZErrorCodeSuccess];
}

- (BOOL)isSupportProcessObject:(id)object
                          task:(FTHZNetworkTask *)task
                       request:(NSURLRequest *)request
                      response:(NSURLResponse *)response {
  return [object isKindOfClass:[FTHZCommonResponse class]];
}

- (void)processObject:(id)object
                 task:(FTHZNetworkTask *)task
              request:(NSURLRequest *)request
             response:(NSURLResponse *)response
           completion:(FTHZNetworkTaskCompletionHandler)completion {
  if (![object isKindOfClass:[FTHZCommonResponse class]]) {
    NSError *error =
        [NSError errorWithDomain:kFTHZErrorDomain
                            code:FTHZErrorCodeBizParsingFailed
                        userInfo:@{
                          NSLocalizedDescriptionKey :
                              @"Model is not FTHZCommonResponse class"
                        }];
    completion(NO, response, nil, error);
    return;
  }
  FTHZCommonResponse *model = object;
  NSNumber *code = model.code;
  FTHZErrorCode theCode = [code integerValue];
  NSString *message = model.msg;
  NSError *commonError = [NSError
      errorWithDomain:kFTHZErrorDomain
                 code:theCode
             userInfo:@{
               NSLocalizedDescriptionKey : message
                   ?: [NSString stringWithFormat:@"Server return code:%@", code]
             }];
  NSIndexSet *acceptCodes =
      self.acceptCodes ?: [[self class] defaultAcceptCodes];
  if ([acceptCodes containsIndex:theCode] ||
      ((theCode == FTHZErrorCodeUnlogin ||
        theCode == FTHZErrorCodePermissionDenied) &&
       [task.url.path
           isEqualToString:
               kFTHZLogoutPath]) /* 登出时提示未登录，则直接通过 */) {
    completion(YES, response, object, nil);

    return;
  }

  switch ([code integerValue]) {
  case FTHZErrorCodeAccountBanned: {
    FTHZAlertDialogController *dialog =
        [[FTHZAlertDialogController alloc] initWithTitle:@"提示"
                                                 message:message];
    [dialog
        addAction:[FTHZAlertDialogAction
                      actionWithTitle:@"好的，我知道了"
                               action:nil
                                style:FTHZAlertDialogActionStyleHighlighted]];
    [[UIViewController topViewController] presentViewController:dialog
                                                       animated:YES
                                                     completion:nil];
    completion(YES, response, object,
               [NSError errorWithDomain:kFTHZErrorDomain
                                   code:FTHZErrorCodeErrorRecovered
                               userInfo:nil]);
  }
  case FTHZErrorCodeUnlogin: {
    NSString *ssid = [request valueForHTTPHeaderField:kHTTPHeaderFieldSSIDKey];
    void (^tokenVerify)(NSString *) = ^(NSString *nowSSID) {
      if (nowSSID && (!ssid || ![nowSSID isEqualToString:ssid])) {
        [task requestWithCompletion:completion];
      } else {
        dispatch_async(dispatch_get_main_queue(), ^{
          [FTHZBusiness loginWithSSIDPolicy:task.userInfoPolicy
                                 thenAction:^(FTHZLoginResult result,
                                              UserPersonResult *_Nullable user,
                                              NSError *_Nullable error) {
                                   switch (result) {
                                   case FTHZLoginResultSuccess:
                                   case FTHZLoginResultAlreadyLogined: {
                                     [task requestWithCompletion:completion];
                                   } break;

                                   case FTHZLoginResultFailed:
                                   case FTHZLoginResultUserCancelled: {
                                     completion(NO, response, nil, commonError);
                                   } break;
                                   }
                                 }];
        });
      }
    };
    if (!task.autoLoginIfNotLogin) {
      completion(NO, response, nil, commonError);
      return;
    }
    if (AccountManager.isUpdatingToken) {
      [AccountManager runAfterTokenUpdated:^(NSString *_Nullable token,
                                             NSError *_Nullable error) {
        if (error) {
          completion(NO, response, nil, commonError);
          return;
        };
        tokenVerify(token);
      }];
    } else {
      NSString *nowToken;
      switch (task.userInfoPolicy) {
      case FTHZNetworkUserInfoPolicyNone:
        NSAssert(NO, @"The request does not need ssid ,but server think it "
                     @"should have.");

      case FTHZNetworkUserInfoPolicyFetchUnsafe: {
        nowToken = [AccountManager unsafeGetUserToken];
      } break;
      case FTHZNetworkUserInfoPolicyFetchSafely: {
        nowToken = AccountManager.token;
      } break;
      }
      tokenVerify(nowToken);
    }
  } break;

  default: {
    completion(NO, response, nil, commonError);
  } break;
  }
}

@end

@implementation FTHZUniversalBizParser

- (instancetype)init {
  self = [super init];
  if (self) {
    _codeProcessor = [[FTHZUniversalBizCodeProcessor alloc] init];
  }
  return self;
}

+ (instancetype)parserWithDataParser:
                    (id<FTHZBizResponseParser> _Nullable)dataParser
                         flattenData:(BOOL)flattenData {
  FTHZUniversalBizParser *parser = [[FTHZUniversalBizParser alloc] init];
  parser.dataParser = dataParser;
  parser.flattenData = flattenData;
  return parser;
}

- (id _Nullable)tryParseResponseData:(id)data
                                task:(FTHZNetworkTask *)task
                             request:(NSURLRequest *)request
                            response:(NSURLResponse *)response
                               error:
                                   (NSError *__autoreleasing _Nullable *)error {

  NSError *serializeError;
  NSDictionary *resultJSON =
      [NSJSONSerialization JSONObjectWithData:data
                                      options:0
                                        error:&serializeError];
  if (serializeError) {
    *error = serializeError;
    return nil;
  }

  NSString *message =
      [resultJSON objectForKey:NSStringFromSelector(@selector(msg))];
  if (![message isKindOfClass:[NSString class]]) {
    message = @"未知错误";
  }
  NSNumber *code =
      [resultJSON objectForKey:NSStringFromSelector(@selector(code))];
  id _Nullable dataProp =
      [resultJSON objectForKey:NSStringFromSelector(@selector(data))];
  if (dataProp && self.flattenData) {
    NSCAssert([dataProp isKindOfClass:[NSArray class]],
              @"TopLevel item should be array");
    dataProp = [dataProp firstObject];
  }
  id object;
  if ([code isEqualToNumber:@(FTHZErrorCodeSuccess)] && dataProp &&
      self.dataParser) {
    object = [self.dataParser tryParseResponseData:dataProp
                                              task:task
                                           request:request
                                          response:response
                                             error:&serializeError];
    if (serializeError) {
      *error = serializeError;
      return nil;
    }
  } else {
    object = dataProp;
  }
  FTHZCommonResponse *respModel = [[FTHZCommonResponse alloc] init];
  respModel.msg = message;
  respModel.code = code;
  respModel.data = object;
  return respModel;
}

@end

NS_ASSUME_NONNULL_END
