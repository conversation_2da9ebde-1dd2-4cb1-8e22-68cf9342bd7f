#import "FTHZRequestEncryptor.h"
#import <CommonCrypto/CommonCrypto.h>

static NSString *const kEncryptionKey = @"52Hz@2024#Secure";

@implementation FTHZRequestEncryptor

+ (NSString *)generateTimestamp {
  return [NSString
      stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
}

+ (NSString *)getEncryptedHeaderValue:(NSDictionary *)params
                                 path:(NSString *)path
                               method:(NSString *)method {
  NSString *timestamp = [self generateTimestamp];
  NSString *encryptedString = [self encryptParams:path
                                           method:method
                                        timestamp:timestamp];

  if (!encryptedString) {
    return nil;
  }

  NSString *headerValue =
      [NSString stringWithFormat:@"%@_%@", timestamp, encryptedString];
  return headerValue;
}

+ (NSString *)encryptParams:(NSString *)path
                     method:(NSString *)method
                  timestamp:(NSString *)timestamp {
  NSString *stringToEncrypt =
      [NSString stringWithFormat:@"%@%@%@", timestamp, path, method];

  NSData *dataToEncrypt =
      [stringToEncrypt dataUsingEncoding:NSUTF8StringEncoding];
  NSData *encryptedData = [self AES128Encrypt:dataToEncrypt key:kEncryptionKey];

  if (!encryptedData) {
    return nil;
  }

  NSString *base64String = [encryptedData base64EncodedStringWithOptions:0];
  return base64String;
}

#pragma mark - AES Encryption

+ (NSData *)AES128Encrypt:(NSData *)data key:(NSString *)key {
  NSData *keyData = [key dataUsingEncoding:NSUTF8StringEncoding];

  char keyPtr[kCCKeySizeAES128 + 1];
  bzero(keyPtr, sizeof(keyPtr));
  [key getCString:keyPtr
        maxLength:sizeof(keyPtr)
         encoding:NSUTF8StringEncoding];

  NSUInteger dataLength = [data length];
  size_t bufferSize = dataLength + kCCBlockSizeAES128;
  void *buffer = malloc(bufferSize);

  size_t numBytesEncrypted = 0;
  CCCryptorStatus cryptStatus = CCCrypt(
      kCCEncrypt, kCCAlgorithmAES128, kCCOptionPKCS7Padding | kCCOptionECBMode,
      keyPtr, kCCKeySizeAES128, NULL, [data bytes], dataLength, buffer,
      bufferSize, &numBytesEncrypted);

  if (cryptStatus == kCCSuccess) {
    NSData *encryptedData = [NSData dataWithBytesNoCopy:buffer
                                                 length:numBytesEncrypted];
    return encryptedData;
  }

  free(buffer);
  return nil;
}

@end