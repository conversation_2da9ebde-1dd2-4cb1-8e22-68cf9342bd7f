#import <AFNetworking.h>
#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

extern NSString *const kHZHTTPMethodGET;
extern NSString *const kHZHTTPMethodPOST;
extern NSString *const kHZHTTPMethodPUT;
extern NSString *const kHZHTTPMethodDELETE;
extern NSString *const kHZHTTPMethodPATCH;

extern NSString *const kHTTPHeaderFieldSSIDKey;

typedef void (^FTHZNetworkTaskCompletionHandler)(
    BOOL isSuccess, NSURLResponse *_Nullable response, id _Nullable model,
    NSError *_Nullable anError);

@interface FTHZHTTPHeaderMaker : NSObject

- (void)setValue:(NSString *_Nullable)value forHeaderField:(NSString *)key;

- (NSDictionary<NSString *, NSString *> *)headers;

@end

@interface FTHZURLQueryMaker : NSObject

- (void)addValue:(NSString *)value forKey:(NSString *)key;

- (NSArray<NSURLQueryItem *> *)queries;

@end

@class FTHZNetworkTask;

@protocol FTHZBizResponseParser <NSObject>

- (id _Nullable)tryParseResponseData:(id)data
                                task:(FTHZNetworkTask *)task
                             request:(NSURLRequest *)request
                            response:(NSURLResponse *)response
                               error:(NSError *_Nullable *_Nullable)error;

@end

@protocol FTHZBizPreProcessor <NSObject>

- (BOOL)isSupportProcessObject:(id)object
                          task:(FTHZNetworkTask *)task
                       request:(NSURLRequest *)request
                      response:(NSURLResponse *)response;

- (void)processObject:(id)object
                 task:(FTHZNetworkTask *)task
              request:(NSURLRequest *)request
             response:(NSURLResponse *)response
           completion:(FTHZNetworkTaskCompletionHandler)completion;

@end

typedef NS_ENUM(NSInteger, FTHZNetworkUserInfoPolicy) {
  FTHZNetworkUserInfoPolicyNone = 0,

  FTHZNetworkUserInfoPolicyFetchSafely = 1,

  FTHZNetworkUserInfoPolicyFetchUnsafe = 2,
};

@interface FTHZNetworkTask : NSObject

@property(nonatomic, copy, nullable) NSURL *url;

@property(nonatomic, copy) NSString *method;

@property(nonatomic, strong, readonly) FTHZHTTPHeaderMaker *headerMaker;

@property(nonatomic, strong, readonly) FTHZURLQueryMaker *queryMaker;

@property(nonatomic, strong, nullable) NSData *body;

@property(nonatomic, assign) NSTimeInterval timeout;

@property(nonatomic, assign) NSURLRequestCachePolicy cachePolicy;

@property(nonatomic, strong, nullable) id<FTHZBizResponseParser> bizParser;

@property(nonatomic, strong, nullable) id<FTHZBizPreProcessor> bizPreProcessor;

@property(nonatomic, assign) FTHZNetworkUserInfoPolicy userInfoPolicy;

@property(nonatomic, strong, nullable) dispatch_queue_t callbackQueue;

@property(nonatomic, assign) BOOL autoLoginIfNotLogin;

- (instancetype)initWithMethod:(NSString *)method
                           url:(NSURL *)url
                     bizParser:(id<FTHZBizResponseParser> _Nullable)bizParser;

- (FTHZNetworkTask *)makeHeader:
    (void (^_Nullable)(FTHZHTTPHeaderMaker *maker))headerMaker;

- (FTHZNetworkTask *)makerQuery:
    (void (^_Nullable)(FTHZURLQueryMaker *maker))queryMaker;

- (FTHZNetworkTask *)setJSONBody:(id)json;

- (FTHZNetworkTask *)changeUserInfoPolicy:(FTHZNetworkUserInfoPolicy)policy;

- (FTHZNetworkTask *)setCallbackInMainQueue;

- (void)requestWithCompletion:
    (FTHZNetworkTaskCompletionHandler _Nullable)completion;

- (RACSignal *)rac_request;

@end

@interface FTHZNetworkManager : AFHTTPSessionManager

+ (instancetype)sharedInstance;

- (void)requestWithTask:(FTHZNetworkTask *)theTask
             completion:(FTHZNetworkTaskCompletionHandler _Nullable)completion;

@end

NS_ASSUME_NONNULL_END
