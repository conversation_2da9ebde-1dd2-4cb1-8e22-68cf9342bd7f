#import "FTHZLocationManager.h"
#import <CoreLocation/CoreLocation.h>

NS_ASSUME_NONNULL_BEGIN

@interface FTHZLocationManager () <CLLocationManagerDelegate>
@property(nonatomic, strong, readonly) CLLocationManager *locationManager;
@property(nonatomic, strong, readonly) CLGeocoder *geocoder;
@property(nonatomic, assign) CLAuthorizationStatus status;
@property(nonatomic, strong, readonly)
    NSMutableDictionary<NSString *, id> *actionMap;
@property(nonatomic, copy, nullable) FTHZLocationAuthorizationHandler handler;
@end

@implementation FTHZLocationManager

- (instancetype)init {
  self = [super init];
  if (self) {
    _geocoder = [[CLGeocoder alloc] init];
    _locationManager = [[CLLocationManager alloc] init];
    _locationManager.delegate = self;

    if (@available(iOS 14.0, *)) {
      _status = _locationManager.authorizationStatus;
    } else {
      _status = [CLLocationManager authorizationStatus];
    }

    _actionMap = [[NSMutableDictionary alloc] init];
  }
  return self;
}

+ (instancetype)shared {
  static FTHZLocationManager *manager;
  static dispatch_once_t onceToken;
  dispatch_once(&onceToken, ^{
    manager = [[FTHZLocationManager alloc] init];
  });
  return manager;
}

- (BOOL)isPossibleToLocate {
  if (![CLLocationManager locationServicesEnabled]) {
    return NO;
  }
  CLAuthorizationStatus status = [CLLocationManager authorizationStatus];
  if (status == kCLAuthorizationStatusDenied ||
      status == kCLAuthorizationStatusRestricted) {
    return NO;
  }
  return YES;
}

- (void)locationManagerDidChangeAuthorization:(CLLocationManager *)manager
    API_AVAILABLE(ios(14.0)) {
  CLAuthorizationStatus status = manager.authorizationStatus;
  self.status = status;

  FTHZLocationAuthorizationHandler handlerCopy = self.handler;
  if (handlerCopy) {
    self.handler = nil;
    dispatch_async(dispatch_get_main_queue(), ^{
      handlerCopy(status);
    });
  }

  if ((status == kCLAuthorizationStatusAuthorizedAlways ||
       status == kCLAuthorizationStatusAuthorizedWhenInUse) &&
      self.actionMap.count > 0) {
    [self.locationManager startUpdatingLocation];
  }
}

- (void)requestAuthorizationWithCompletion:
    (FTHZLocationAuthorizationHandler _Nullable)completion {
  if (completion) {
    FTHZLocationAuthorizationHandler originHandler = self.handler;
    if (originHandler) {
      self.handler = ^(CLAuthorizationStatus status) {
        originHandler(status);
        completion(status);
      };
    } else {
      self.handler = completion;
    }
  }

  CLAuthorizationStatus currentStatus;
  if (@available(iOS 14.0, *)) {
    currentStatus = self.locationManager.authorizationStatus;
  } else {
    currentStatus = [CLLocationManager authorizationStatus];
  }

  if (currentStatus == kCLAuthorizationStatusNotDetermined) {
    dispatch_async(dispatch_get_main_queue(), ^{
      [self.locationManager requestWhenInUseAuthorization];
    });
  } else {
    if (completion) {
      dispatch_async(dispatch_get_main_queue(), ^{
        completion(currentStatus);
      });
      if (self.handler == completion) {
        self.handler = nil;
      }
    }
  }
}

- (void)beginUpdateLocationWithIdentifier:(NSString *)identifier
                              updateBlock:
                                  (FTHZLocationManagerUpdate _Nullable)update {
  if (![identifier isKindOfClass:[NSString class]] || !update) {
    return;
  }

  self.actionMap[identifier] = [update copy];

  dispatch_async(
      dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        if (![CLLocationManager locationServicesEnabled]) {
          NSError *error = [NSError errorWithDomain:kCLErrorDomain
                                               code:kCLErrorDenied
                                           userInfo:nil];
          [self dispatchUpdate:nil error:error];
          return;
        }

        CLAuthorizationStatus currentStatus;
        if (@available(iOS 14.0, *)) {
          currentStatus = self.locationManager.authorizationStatus;
        } else {
          currentStatus = [CLLocationManager authorizationStatus];
        }

        if (currentStatus == kCLAuthorizationStatusNotDetermined) {
          dispatch_async(dispatch_get_main_queue(), ^{
            [self requestAuthorizationWithCompletion:^(
                      CLAuthorizationStatus status) {
              if (status == kCLAuthorizationStatusAuthorizedAlways ||
                  status == kCLAuthorizationStatusAuthorizedWhenInUse) {
                dispatch_async(dispatch_get_main_queue(), ^{
                  [self.locationManager startUpdatingLocation];
                });
              } else {
                NSError *authError = [NSError errorWithDomain:kCLErrorDomain
                                                         code:kCLErrorDenied
                                                     userInfo:nil];
                [self dispatchUpdate:nil error:authError];
              }
            }];
          });
        } else if (currentStatus == kCLAuthorizationStatusAuthorizedAlways ||
                   currentStatus == kCLAuthorizationStatusAuthorizedWhenInUse) {
          dispatch_async(dispatch_get_main_queue(), ^{
            [self.locationManager startUpdatingLocation];
          });
        } else {
          NSError *authError = [NSError errorWithDomain:kCLErrorDomain
                                                   code:kCLErrorDenied
                                               userInfo:nil];
          [self dispatchUpdate:nil error:authError];
        }
      });
}

- (void)endUpdateLocationWithIdentifier:(NSString *)identifier {
  self.actionMap[identifier] = nil;
  if (self.actionMap.count == 0) {
    [self.locationManager stopUpdatingLocation];
  }
}

- (void)locationManager:(CLLocationManager *)manager
    didChangeAuthorizationStatus:(CLAuthorizationStatus)status {
  if (@available(iOS 14.0, *)) {
    return;
  }

  self.status = status;

  FTHZLocationAuthorizationHandler handlerCopy = self.handler;
  if (handlerCopy) {
    self.handler = nil;
    dispatch_async(dispatch_get_main_queue(), ^{
      handlerCopy(status);
    });
  }

  if ((status == kCLAuthorizationStatusAuthorizedAlways ||
       status == kCLAuthorizationStatusAuthorizedWhenInUse) &&
      self.actionMap.count > 0) {
    [self.locationManager startUpdatingLocation];
  }
}

- (void)locationManager:(CLLocationManager *)manager
       didFailWithError:(NSError *)error {
  if (error.code == kCLErrorLocationUnknown) {
  }
  [self dispatchUpdate:nil error:error];
}

- (void)locationManager:(CLLocationManager *)manager
     didUpdateLocations:(NSArray<CLLocation *> *)locations {
  [self dispatchUpdate:[locations lastObject] error:nil];
}

- (void)dispatchUpdate:(CLLocation *_Nullable)location
                 error:(NSError *_Nullable)error {
  dispatch_async(dispatch_get_main_queue(), ^{
    for (FTHZLocationManagerUpdate anUpdate in self.actionMap.allValues) {
      anUpdate(location, error);
    }
  });
}

#pragma mark - Geo

- (void)reverseGeocodeLocation:(CLLocation *)location
             completionHandler:(CLGeocodeCompletionHandler)completionHandler {
  if (@available(iOS 11.0, *)) {
    [self.geocoder
        reverseGeocodeLocation:location
               preferredLocale:[NSLocale
                                   localeWithLocaleIdentifier:@"zh_Hans_CN"]
             completionHandler:completionHandler];
  } else {
    NSMutableArray *userDefaultLanguages =
        [[NSUserDefaults standardUserDefaults] objectForKey:@"AppleLanguages"];
    [[NSUserDefaults standardUserDefaults]
        setObject:[NSArray arrayWithObjects:@"zh-hans", nil]
           forKey:@"AppleLanguages"];
    [self.geocoder
        reverseGeocodeLocation:location
             completionHandler:^(NSArray<CLPlacemark *> *_Nullable placemarks,
                                 NSError *_Nullable error) {
               [[NSUserDefaults standardUserDefaults]
                   setObject:userDefaultLanguages
                      forKey:@"AppleLanguages"];
               completionHandler(placemarks, error);
             }];
  }
}

@end

NS_ASSUME_NONNULL_END