#import "FTHZAccountManager.h"
#import "FTHZNetworkTask+UserInfo.h"

NS_ASSUME_NONNULL_BEGIN

NSString *const kUMAliasFTHZ = @"userType";
NSString *const kUserDataKey = @"com.52-hz.account.userData";
NSString *const kUserTokenKey = @"com.52-hz.account.userToken";

@interface FTHZAccountMigrator : NSObject

+ (void)migrateOldUserToNewVersion;

@end

@implementation FTHZAccountMigrator

+ (void)migrateOldUserToNewVersion {
  NSDictionary *userJSON =
      [[NSUserDefaults standardUserDefaults] dictionaryForKey:@"kMemberhezi"];
  if (!userJSON) {
    return;
  }

  GetUserinfoModel *member = [GetUserinfoModel mj_objectWithKeyValues:userJSON];
  UserPersonResult *user =
      [UserPersonResult mj_objectWithKeyValues:member.data.firstObject];
  [[NSUserDefaults standardUserDefaults] removeObjectForKey:@"kMemberhezi"];

  NSString *ssid =
      [[NSUserDefaults standardUserDefaults] stringForKey:@"kMemberssid"];
  [[NSUserDefaults standardUserDefaults] removeObjectForKey:@"kMemberssid"];
  if (ssid) {
    user.ssid = ssid;
    [[NSUserDefaults standardUserDefaults] setObject:ssid forKey:kUserTokenKey];
  }
  NSData *data = [user mj_JSONData];
  [[NSUserDefaults standardUserDefaults] setObject:data forKey:kUserDataKey];

  NSString *newMsgCount =
      [[NSUserDefaults standardUserDefaults] stringForKey:@"NewMessageCount"];
  [[NSUserDefaults standardUserDefaults] removeObjectForKey:@"NewMessageCount"];
  if (newMsgCount) {
    NSInteger msgCount = [newMsgCount integerValue];
    FTHZUserConfig *config =
        [[FTHZUserConfig alloc] initWithUserID:user.userid];
    config.unreadMessageCount = msgCount;
    [config saveConfigIfNeeded];
  }

  [[NSUserDefaults standardUserDefaults] synchronize];
}

@end

@interface FTHZAccountManager () {
  dispatch_queue_t _workingQueue;
  NSString *_Nullable _token;
  UserPersonResult *_Nullable _user;
  FTHZUserConfig *_Nullable _userConfig;
  BOOL _isUpdatingToken;
}

@property(nonatomic, strong, readonly)
    NSMutableDictionary<NSString *, FTHZAccountManagerLoginCallback>
        *callbackMap;

@property(nonatomic, strong, readonly)
    NSMutableArray<FTHZAccountManagerTokenCallback> *tokenUpdateActionQueue;

@end

@implementation FTHZAccountManager

@dynamic user;
@dynamic userConfig;
@dynamic token;

+ (instancetype)shared {
  static FTHZAccountManager *mgr;
  static dispatch_once_t onceToken;
  dispatch_once(&onceToken, ^{
    [FTHZAccountMigrator migrateOldUserToNewVersion];
    mgr = [[FTHZAccountManager alloc] init];
  });
  return mgr;
}

- (instancetype)init {
  self = [super init];
  if (self) {
    _workingQueue =
        dispatch_queue_create("FTHZAccountManager", DISPATCH_QUEUE_SERIAL);
    _token = [self loadSavedUserToken];
    _user = [self loadSavedUserData];
    [self autoLoginIM];
    _userConfig = [[FTHZUserConfig alloc] initWithUserID:_user.userid];
    _callbackMap = [[NSMutableDictionary alloc] init];
    _isUpdatingToken = NO;
    _tokenUpdateActionQueue = [[NSMutableArray alloc] init];
  }
  return self;
}

#pragma mark - Network task config

+ (FTHZNetworkTask *)configTask:(FTHZNetworkTask *)task {
  task.userInfoPolicy = FTHZNetworkUserInfoPolicyFetchUnsafe;
  task.callbackQueue = nil;
  return task;
}

#pragma mark - Property

- (NSString *_Nullable)token {
  __block NSString *tk;
  dispatch_sync(_workingQueue, ^{
    tk = [self->_token copy];
  });
  return tk;
}

- (NSString *_Nullable)unsafeGetUserToken {
  return [self->_token copy];
}

- (void)unsafeSetToken:(NSString *_Nullable)token {
  [self willChangeValueForKey:NSStringFromSelector(@selector(token))];
  self->_token = [token copy];
  [self didChangeValueForKey:NSStringFromSelector(@selector(token))];
}

- (UserPersonResult *_Nullable)user {
  __block UserPersonResult *theUser;
  dispatch_sync(_workingQueue, ^{
    theUser = [self->_user copy];
  });
  return theUser;
}

- (UserPersonResult *_Nullable)unsafeGetUser {
  return [self->_user copy];
}

- (void)unsafeSetUser:(UserPersonResult *_Nullable)user {
  [self willChangeValueForKey:NSStringFromSelector(@selector(user))];
  self->_user = [user copy];
  [self didChangeValueForKey:NSStringFromSelector(@selector(user))];
}

- (FTHZUserConfig *_Nullable)userConfig {
  __block FTHZUserConfig *cfg;
  dispatch_sync(_workingQueue, ^{
    cfg = [self->_userConfig copy];
  });
  return cfg;
}

- (void)unsafeSetUserConfig:(FTHZUserConfig *_Nullable)userConfig {
  [self willChangeValueForKey:NSStringFromSelector(@selector(userConfig))];
  self->_userConfig = [userConfig copy];
  [self didChangeValueForKey:NSStringFromSelector(@selector(userConfig))];
}

- (BOOL)isUpdatingToken {
  __block BOOL isUpdating;
  dispatch_sync(_workingQueue, ^{
    isUpdating = self->_isUpdatingToken;
  });
  return isUpdating;
}

#pragma mark - persist

- (void)saveUserData:(UserPersonResult *_Nullable)user {
  if (user) {
    NSData *data = [user mj_JSONData];
    [[NSUserDefaults standardUserDefaults] setObject:data forKey:kUserDataKey];
  } else {
    [[NSUserDefaults standardUserDefaults] removeObjectForKey:kUserDataKey];
  }
  [[NSUserDefaults standardUserDefaults] synchronize];
}

- (UserPersonResult *_Nullable)loadSavedUserData {
  NSData *data =
      [[NSUserDefaults standardUserDefaults] dataForKey:kUserDataKey];
  if (!data) {
    return nil;
  }
  NSError *error;
  id jsonObject = [NSJSONSerialization JSONObjectWithData:data
                                                  options:0
                                                    error:&error];
  if (error || !jsonObject) {
    NSAssert(NO, @"加载数据失败，JSON为:%@，错误为:%@",
             [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding],
             error);
    return nil;
  }
  UserPersonResult *result =
      [UserPersonResult mj_objectWithKeyValues:jsonObject];
  return result;
}

- (NSString *_Nullable)loadSavedUserToken {
  return [[NSUserDefaults standardUserDefaults] stringForKey:kUserTokenKey];
}

- (void)saveUserToken:(NSString *_Nullable)token {
  [[NSUserDefaults standardUserDefaults] setObject:token forKey:kUserTokenKey];
  [[NSUserDefaults standardUserDefaults] synchronize];
}

#pragma mark Private thread unsafe operation

- (void)unsafeUpdateUserToken:(NSString *_Nullable)userToken {
  [self unsafeSetToken:userToken];
  [self saveUserToken:userToken];
}

- (void)unsafeUpdateUser:(UserPersonResult *_Nullable)user {
  [self unsafeSetUser:user];
  [self saveUserData:user];
}

- (void)userDidLogin:(UserPersonResult *_Nullable)user {
  NSString *newToken = user.ssid;
  dispatch_sync(_workingQueue, ^{
    [self unsafeUpdateUserToken:newToken];
    [self unsafeUpdateUser:user];
    FTHZUserConfig *config =
        [[FTHZUserConfig alloc] initWithUserID:user.userid];
    self->_userConfig = config;
  });

  [self loginIMWithUser:user tryCount:0];
}

- (void)userDidLogoutWithPreviousUser:(UserPersonResult *)previousUser {
  dispatch_sync(_workingQueue, ^{
    [self->_userConfig saveConfigIfNeeded];
    self->_userConfig = nil;
    [self unsafeUpdateUser:nil];
    [self unsafeUpdateUserToken:nil];
  });
}

#pragma mark - Login & logout

- (BOOL)isLogined {
  return self.user != nil;
}

- (void)loginWithAccount:(NSString *)account
                password:(NSString *)password
              completion:(FTHZAccountManagerLoginCallback _Nullable)callback {
  @weakify(self);
  [[[self class] configTask:[FTHZNetworkTask loginWithAccount:account
                                                     password:password]]
      requestWithCompletion:^(BOOL isSuccess, NSURLResponse *_Nullable response,
                              FTHZCommonResponse *_Nullable respModel,
                              NSError *_Nullable anError) {
        UserPersonResult *model = respModel.data;
        @strongify(self);
        if (isSuccess) {
          [self userDidLogin:model];
        }

        NSError *resultError = nil;
        if (!isSuccess) {
          NSString *errorMsg = @"登录失败";
          if ([respModel respondsToSelector:@selector(msg)]) {
            NSString *msg = [respModel msg];
            if (![msg isEqualToString:@"success"]) {
              errorMsg = msg;
            }
          }
          resultError =
              anError
                  ?: [NSError errorWithDomain:@"com.52hezi.error"
                                         code:-1
                                     userInfo:@{
                                       NSLocalizedDescriptionKey : errorMsg
                                     }];
        }

        dispatch_async(dispatch_get_main_queue(), ^{
          if (callback) {
            callback(model, resultError);
          }
        });
      }];
}

- (void)loginWithMobile:(NSString *)mobile
       verificationCode:(NSString *)verifyCode
             completion:(FTHZAccountManagerLoginCallback _Nullable)callback {
  @weakify(self);
  [[[self class] configTask:[FTHZNetworkTask loginWithMobile:mobile
                                                        code:verifyCode]]
      requestWithCompletion:^(BOOL isSuccess, NSURLResponse *_Nullable response,
                              FTHZCommonResponse *_Nullable respModel,
                              NSError *_Nullable anError) {
        UserPersonResult *model = respModel.data;
        @strongify(self);
        if (isSuccess) {
          [self userDidLogin:model];
        }
        dispatch_async(dispatch_get_main_queue(), ^{
          if (callback) {
            callback(model, anError);
          }
        });
      }];
}

- (void)loginWechatWithCode:(NSString *)code
                 completion:
                     (FTHZAccountManagerLoginCallback _Nullable)callback {
  @weakify(self);
  [[[self class] configTask:[FTHZNetworkTask loginWithWechatCode:code]]
      requestWithCompletion:^(BOOL isSuccess, NSURLResponse *_Nullable response,
                              FTHZCommonResponse *_Nullable respModel,
                              NSError *_Nullable anError) {
        UserPersonResult *model = respModel.data;
        @strongify(self);
        if (isSuccess) {
          [self userDidLogin:model];
        }
        dispatch_async(dispatch_get_main_queue(), ^{
          if (callback) {
            callback(model, anError);
          }
        });
      }];
}

- (void)logoutWithCompletion:
    (FTHZAccountManagerLoginCallback _Nullable)callback {
  UserPersonResult *prelogoutUser = self->_user;
  [[[self class] configTask:[FTHZNetworkTask logout]]
      requestWithCompletion:^(BOOL isSuccess, NSURLResponse *_Nullable response,
                              UserPersonResult *_Nullable model,
                              NSError *_Nullable anError) {
        if (isSuccess || anError.code == FTHZErrorCodePermissionDenied ||
            anError.code ==
                FTHZErrorCodeUnlogin /* 服务器认为未登录，则也应该是成功的 */) {
          [self userDidLogoutWithPreviousUser:prelogoutUser];
        }
        dispatch_async(dispatch_get_main_queue(), ^{
          if (callback) {
            callback(nil, anError);
          }
        });
      }];
}

- (void)updateCurrendUserTokenWithCompletion:
    (FTHZAccountManagerTokenCallback _Nullable)completion {
  @weakify(self);
  dispatch_sync(_workingQueue, ^{
    NSCAssert(self->_isUpdatingToken == NO, @"SSID has already in updating??");
    self->_isUpdatingToken = YES;
    FTHZNetworkTask *task =
        [[self class] configTask:[FTHZNetworkTask updateUserToken]];
    task.autoLoginIfNotLogin = NO;
    [task requestWithCompletion:^(BOOL isSuccess,
                                  NSURLResponse *_Nullable response,
                                  FTHZCommonResponse *_Nullable respModel,
                                  NSError *_Nullable anError) {
      @strongify(self);
      UserPersonResult *model = respModel.data;
      dispatch_sync(self->_workingQueue, ^{
        NSString *token = nil;
        if (isSuccess) {
          NSAssert([model isKindOfClass:[UserPersonResult class]], @"??");
          token = model.ssid;
          [self unsafeUpdateUserToken:token];
        }
        self->_isUpdatingToken = NO;
        [self unsafeDispatchAllPendingTokenAction:token error:anError];
      });
      dispatch_async(dispatch_get_main_queue(), ^{
        if (completion) {
          completion([model ssid], anError);
        }
      });
    }];
  });
}

- (void)runAfterTokenUpdated:(FTHZAccountManagerTokenCallback)action {
  dispatch_sync(_workingQueue, ^{
    [self.tokenUpdateActionQueue addObject:[action copy]];
  });
}

- (void)unsafeDispatchAllPendingTokenAction:(NSString *_Nullable)token
                                      error:(NSError *_Nullable)error {
  NSArray *allCallback = [self.tokenUpdateActionQueue copy];
  [self.tokenUpdateActionQueue removeAllObjects];
  for (FTHZAccountManagerTokenCallback cb in allCallback) {
    cb(token, error);
  }
}

#pragma mark - User info updating

- (void)updateUserInfoWithName:(NSString *_Nullable)name
                        gender:(NSString *_Nullable)gender
                        avatar:(NSString *_Nullable)avatar
                     signature:(NSString *_Nullable)sig
                         brith:(NSString *_Nullable)birth
                    completion:
                        (FTHZAccountManagerLoginCallback _Nullable)completion {
  @weakify(self);
  [[[self class] configTask:[FTHZNetworkTask updateUserInfoWithName:name
                                                             gender:gender
                                                             avatar:avatar
                                                          signature:sig
                                                              brith:birth]]
      requestWithCompletion:^(BOOL isSuccess, NSURLResponse *_Nullable response,
                              FTHZCommonResponse *_Nullable respModel,
                              NSError *_Nullable anError) {
        @strongify(self);
        UserPersonResult *model = respModel.data;
        if (isSuccess) {
          dispatch_sync(self->_workingQueue, ^{
            [self unsafeUpdateUser:model];
          });
        }
        dispatch_async(dispatch_get_main_queue(), ^{
          if (completion) {
            completion(model, anError);
          }
        });
      }];
}

- (void)syncUserInfoWithCompletion:
    (FTHZAccountManagerLoginCallback _Nullable)completion {
  [[[self class] configTask:[FTHZNetworkTask getCurrentUserInfo]]
      requestWithCompletion:^(BOOL isSuccess, NSURLResponse *_Nullable response,
                              FTHZCommonResponse *_Nullable respModel,
                              NSError *_Nullable anError) {
        UserPersonResult *model = respModel.data;
        if (isSuccess) {
          dispatch_sync(self->_workingQueue, ^{
            [self unsafeUpdateUser:model];
          });
        }
        dispatch_async(dispatch_get_main_queue(), ^{
          if (completion) {
            completion(model, anError);
          }
        });
      }];
}

#pragma mark - IM

- (void)loginIMWithUser:(UserPersonResult *)user tryCount:(NSInteger)tryCount {
  if (user == nil) {
    return;
  }
  NSAssert([user isKindOfClass:[UserPersonResult class]],
           @"User should not be nil");
  if (tryCount > 5) {
    return;
  }
}

#pragma mark URL Handler

- (BOOL)handleOpenURL:(NSURL *)url {
  return NO;
}

#pragma mark - 废弃

- (void)loginIM {

  [self loginIMWithUser:self.user tryCount:0];
}

- (void)autoLoginIM {
}

@end

NS_ASSUME_NONNULL_END
