#import "AppDelegate.h"
#import "SVProgressHUD.h"
#import "UIImage+TintColor.h"
#import "UIView+Toast.h"
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@protocol contentScrollDelegate <NSObject>

@optional

- (void)contentDidScrollTo:(BOOL)up show:(NSString *)label;

- (void)sliderValueToHerz:(int)herz;

@end

@interface CurrencyRootVC : UIViewController <UIGestureRecognizerDelegate> {
}

@property(nonatomic, assign) BOOL showBackBtn;
@property(nonatomic, assign)
    UIEdgeInsets safeAreaInset;
@property(nonatomic, strong) UIView
    *safeContentView; 

- (MASViewAttribute *)setTitleForV199:(NSString *)title;

- (void)appDidEnterBackground:(NSNotification *)_notification;

- (void)appDidiBecomeActive:(NSNotification *)_notification;

- (void)actionBack:(void (^)(void))btnClickBlock;
- (void)actionCustomLeftBtnWithNrlImage:(NSString *)nrlImage
                               htlImage:(NSString *)hltImage
                                  title:(NSString *)title
                                 action:(void (^)(void))btnClickBlock;
- (void)actionCustomRightBtnWithNrlImage:(NSString *)nrlImage
                                htlImage:(NSString *)hltImage
                                   title:(NSString *)title
                                  action:(void (^)(void))btnClickBlock;

- (void)showToast:(NSString *)toast;
- (void)showToastFast:(NSString *)toast;
- (void)showToast:(NSString *)toast time:(NSTimeInterval)time;
- (void)backAction;
#pragma mark - 网络异常
- (BOOL)isNetErrorViewShowing:(UIView *)_viewSuper;

- (void)showNetErrorView:(UIView *)_viewSuper
             withPadding:(UIEdgeInsets)_edge
                  target:(id)_target
                selector:(SEL)_selector;

- (void)hidNetErrorView:(UIView *)_viewSuper;
@end

NS_ASSUME_NONNULL_END
