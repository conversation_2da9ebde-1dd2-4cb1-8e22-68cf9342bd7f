#import "NemoUtil.h"
#import "NSDate+VTB.h"
#import "sys/utsname.h"
#import <AVFoundation/AVFoundation.h>
#import <CommonCrypto/CommonDigest.h>

@implementation NemoUtil {
}

+ (NSString *)readFromFullPath:(NSString *)fileName {
  NSFileManager *fileMgr = [NSFileManager defaultManager];
  NSData *data = [fileMgr contentsAtPath:fileName];
  NSString *content = [[NSString alloc] initWithData:data
                                            encoding:NSUTF8StringEncoding];
  return content;
}

+ (NSString *)randomString {
  int value = (arc4random() % 1000000000) + 1;
  return [NSString stringWithFormat:@"%d", value];
}

+ (BOOL)isEmpty:(NSString *)_str {
  return (!_str || _str.length <= 0) ? YES : NO;
}

#pragma mark - Nemo Private
+ (NSString *)chineseToUTf8Str:(NSString *)chineseStr {
  NSStringEncoding encodingUTF8 = NSUTF8StringEncoding;
  NSData *responseData2 = [chineseStr dataUsingEncoding:encodingUTF8];
  NSString *string = [[NSString alloc] initWithData:responseData2
                                           encoding:encodingUTF8];
  return string;
}

+ (UIColor *)getColorWith16Str:(NSString *)hStr {

  if (hStr.length != 6) {
    return [UIColor whiteColor];
  }

  unsigned int red, green, blue;

  NSRange range;

  range.length = 2;

  range.location = 0;

  [[NSScanner scannerWithString:[hStr substringWithRange:range]]
      scanHexInt:&red];

  range.location = 2;

  [[NSScanner scannerWithString:[hStr substringWithRange:range]]
      scanHexInt:&green];

  range.location = 4;

  [[NSScanner scannerWithString:[hStr substringWithRange:range]]
      scanHexInt:&blue];

  return RGBA(red, green, blue, 1.0);
}

/*!
 @method 计算两个字符串的相似度
 */
+ (int)MiniNum:(int)x SetY:(int)y SetZ:(int)z {
  int tempNum;
  tempNum = x;
  if (y < tempNum) {
    tempNum = y;
  }
  if (z < tempNum) {
    tempNum = z;
  }
  return tempNum;
}
+ (int)DistanceBetweenTwoString:(NSString *)strA
                      StrAbegin:(int)strAbegin
                        StrAend:(int)strAend
                           StrB:(NSString *)strB
                      StrBbegin:(int)strBbegin
                        StrBend:(int)strBend {
  int x, y, z;
  if (strAbegin > strAend) {
    if (strBbegin > strBend) {
      return 0;
    } else {
      return strBend - strBbegin + 1;
    }
  }
  if (strBbegin > strBend) {
    if (strAbegin > strAend) {
      return 0;
    } else {
      return strAend - strAbegin + 1;
    }
  }
  if ([strA characterAtIndex:(NSUInteger)(strAbegin)] ==
      [strB characterAtIndex:(NSUInteger)(strBbegin)]) {
    return [NemoUtil DistanceBetweenTwoString:strA
                                    StrAbegin:strAbegin + 1
                                      StrAend:strAend
                                         StrB:strB
                                    StrBbegin:strBbegin + 1
                                      StrBend:strBend];
  } else {
    x = [NemoUtil DistanceBetweenTwoString:strA
                                 StrAbegin:strAbegin + 1
                                   StrAend:strAend
                                      StrB:strB
                                 StrBbegin:strBbegin + 1
                                   StrBend:strBend];
    y = [NemoUtil DistanceBetweenTwoString:strA
                                 StrAbegin:strAbegin
                                   StrAend:strAend
                                      StrB:strB
                                 StrBbegin:strBbegin + 1
                                   StrBend:strBend];
    z = [NemoUtil DistanceBetweenTwoString:strA
                                 StrAbegin:strAbegin + 1
                                   StrAend:strAend
                                      StrB:strB
                                 StrBbegin:strBbegin
                                   StrBend:strBend];
    return [NemoUtil MiniNum:x SetY:y SetZ:z] + 1;
  }
}

/*!
 @method 随机重新排序NSMutableArray的内容
 */
+ (NSMutableArray *)randomArrayPosition:(NSMutableArray *)_array {
  NSUInteger count = [_array count];
  for (NSUInteger i = 0; i < count; ++i) {
    int nElements = count - i;
    int n = (arc4random() % nElements) + i;
    [_array exchangeObjectAtIndex:i withObjectAtIndex:n];
  }

  return _array;
}

/*!
 * @brief 把格式化的JSON格式的字符串转换成字典
 * @param jsonString JSON格式的字符串
 * @return 返回字典
 */
+ (NSDictionary *)dictionaryWithJsonString:(NSString *)jsonString {
  if (jsonString == nil) {
    return nil;
  }

  NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
  NSError *err;
  NSDictionary *dic =
      [NSJSONSerialization JSONObjectWithData:jsonData
                                      options:NSJSONReadingMutableContainers
                                        error:&err];
  if (err) {
    return nil;
  }
  return dic;
}

+ (CGSize)calculateLabelHeightByText:(UIFont *)_font
                               width:(float)_fWidth
                           heightMax:(float)_fHeightMax
                             content:(NSString *)_strContent {
  CGSize size = CGSizeMake(_fWidth, _fHeightMax);
  NSDictionary *attribute = @{NSFontAttributeName : _font};
  CGSize labelsize =
      [_strContent boundingRectWithSize:size
                                options:NSStringDrawingUsesLineFragmentOrigin
                             attributes:attribute
                                context:nil]
          .size;
  return labelsize;
}

+ (CGSize)calculateLabelHeightByText:(UIFont *)_font
                               width:(float)_fWidth
                           heightMax:(float)_fHeightMax
                             content:(NSString *)_strContent
                         lineSpacing:(CGFloat)lineSpacing {
  if (!_strContent || _strContent.length == 0) {
    return CGSizeZero;
  }

  CGSize size = CGSizeMake(_fWidth, _fHeightMax);

  NSMutableParagraphStyle *paragraphStyle =
      [[NSMutableParagraphStyle alloc] init];
  paragraphStyle.lineSpacing = lineSpacing;

  NSDictionary *attribute = @{
    NSFontAttributeName : _font,
    NSParagraphStyleAttributeName : paragraphStyle
  };

  CGSize labelsize =
      [_strContent boundingRectWithSize:size
                                options:NSStringDrawingUsesLineFragmentOrigin
                             attributes:attribute
                                context:nil]
          .size;
  return labelsize;
}

+ (NSString *)version {
  return [[[NSBundle mainBundle] infoDictionary]
      objectForKey:@"CFBundleShortVersionString"];
}

+ (void)printAllFont {
  NSArray *familyNames = [[NSArray alloc] initWithArray:[UIFont familyNames]];
  NSArray *fontNames;
  NSInteger indFamily, indFont;
  for (indFamily = 0; indFamily < [familyNames count]; ++indFamily) {
    fontNames = [[NSArray alloc]
        initWithArray:[UIFont
                          fontNamesForFamilyName:[familyNames
                                                     objectAtIndex:indFamily]]];
  }
}

/*!
 @abstract 获取视频的持续时间
 */
+ (CGFloat)getVideoDuration:(NSURL *)_URL {
  NSDictionary *opts = [NSDictionary
      dictionaryWithObject:[NSNumber numberWithBool:NO]
                    forKey:AVURLAssetPreferPreciseDurationAndTimingKey];
  AVURLAsset *urlAsset = [AVURLAsset URLAssetWithURL:_URL options:opts];
  float second = 0;
  second = urlAsset.duration.value / urlAsset.duration.timescale;
  return second;
}

+ (BOOL)stringContainsEmoji:(NSString *)string {
  __block BOOL returnValue = NO;
  [string
      enumerateSubstringsInRange:NSMakeRange(0, [string length])
                         options:NSStringEnumerationByComposedCharacterSequences
                      usingBlock:^(NSString *substring, NSRange substringRange,
                                   NSRange enclosingRange, BOOL *stop) {
                        const unichar hs = [substring characterAtIndex:0];
                        if (0xd800 <= hs && hs <= 0xdbff) {
                          if (substring.length > 1) {
                            const unichar ls = [substring characterAtIndex:1];
                            const int uc = ((hs - 0xd800) * 0x400) +
                                           (ls - 0xdc00) + 0x10000;
                            if (0x1d000 <= uc && uc <= 0x1f77f) {
                              returnValue = YES;
                            }
                          }
                        } else if (substring.length > 1) {
                          const unichar ls = [substring characterAtIndex:1];
                          if (ls == 0x20e3) {
                            returnValue = YES;
                          }
                        } else {
                          if (0x2100 <= hs && hs <= 0x27ff) {
                            returnValue = YES;
                          } else if (0x2B05 <= hs && hs <= 0x2b07) {
                            returnValue = YES;
                          } else if (0x2934 <= hs && hs <= 0x2935) {
                            returnValue = YES;
                          } else if (0x3297 <= hs && hs <= 0x3299) {
                            returnValue = YES;
                          } else if (hs == 0xa9 || hs == 0xae || hs == 0x303d ||
                                     hs == 0x3030 || hs == 0x2b55 ||
                                     hs == 0x2b1c || hs == 0x2b1b ||
                                     hs == 0x2b50) {
                            returnValue = YES;
                          }
                        }
                      }];
  return returnValue;
}

+ (NSString *)getAppVersionint {
  NSString *version =
      [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleVersion"];
  NSArray *versionArray = [version componentsSeparatedByString:@"."];
  return version;
}

+ (UIViewController *)rootViewController {
  UIViewController *root = [[[[UIApplication sharedApplication] delegate]
      window] rootViewController];
  return root;
}

+ (UIViewController *)currentRootController {
  UIViewController *root = [[[[UIApplication sharedApplication] delegate]
      window] rootViewController];
  while (root.presentedViewController) {
    root = root.presentedViewController;
  }
  return root;
}

+ (UIViewController *)getCurrentVC {

  UIViewController *result = nil;
  UIWindow *window = [[UIApplication sharedApplication] keyWindow];
  if (window.windowLevel != UIWindowLevelNormal) {
    NSArray *windows = [[UIApplication sharedApplication] windows];
    for (UIWindow *tmpWin in windows) {
      if (tmpWin.windowLevel == UIWindowLevelNormal) {
        window = tmpWin;
        break;
      }
    }
  }
  id nextResponder = nil;
  UIViewController *appRootVC = window.rootViewController;
  if (appRootVC.presentedViewController) {
    nextResponder = appRootVC.presentedViewController;
  } else {
    UIView *frontView = [[window subviews] objectAtIndex:0];
    nextResponder = [frontView nextResponder];
  }

  if ([nextResponder isKindOfClass:[UITabBarController class]]) {
    UITabBarController *tabbar = (UITabBarController *)nextResponder;
    UINavigationController *nav =
        (UINavigationController *)tabbar.viewControllers[tabbar.selectedIndex];

    result = nav.childViewControllers.lastObject;

  } else if ([nextResponder isKindOfClass:[UINavigationController class]]) {
    UIViewController *nav = (UIViewController *)nextResponder;
    result = nav.childViewControllers.lastObject;
  } else {
    result = nextResponder;
  }

  return result;
}

+ (NSString *)RandomNumber:(NSInteger)number {

  NSString *strRandom = @"";
  for (int i = 0; i <= number; i++) {

    strRandom = [strRandom stringByAppendingFormat:@"%i", (arc4random() % 9)];
  }
  return strRandom;
}

+ (NSString *)getmd5By16:(NSString *)str {
  const char *cStr = [str UTF8String];
  unsigned char result[16];
  NSNumber *num = [NSNumber numberWithUnsignedLong:strlen(cStr)];
  CC_MD5(cStr, [num intValue], result);
  NSString *tempStr = [[NSString
      stringWithFormat:
          @"%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X",
          result[0], result[1], result[2], result[3], result[4], result[5],
          result[6], result[7], result[8], result[9], result[10], result[11],
          result[12], result[13], result[14], result[15]] uppercaseString];

  return [tempStr substringWithRange:NSMakeRange(8, 16)];
}

+ (NSString *)distanceTimeWithBeforeTime:(double)beTime {
  NSDate *dat = [NSDate dateWithTimeIntervalSinceNow:0];
  NSTimeInterval a = [dat timeIntervalSince1970];
  double distanceTime = a - beTime;
  NSString *distanceStr;

  NSDate *beDate = [NSDate dateWithTimeIntervalSince1970:beTime];
  NSDateFormatter *df = [[NSDateFormatter alloc] init];
  [df setDateFormat:@"dd"];
  NSString *nowDay = [df stringFromDate:dat];
  NSString *lastDay = [df stringFromDate:beDate];

  if (distanceTime < 60) {
    distanceStr = @"刚刚";
  } else if (distanceTime < 60 * 60) {
    distanceStr =
        [NSString stringWithFormat:@"%ld分钟前", (long)distanceTime / 60];
  } else if (distanceTime < 24 * 60 * 60 &&
             [nowDay integerValue] == [lastDay integerValue]) {
    distanceStr =
        [NSString stringWithFormat:@"%ld小时前", (long)distanceTime / 3600];
  } else if (distanceTime < 24 * 60 * 60 * 30) {
    NSInteger ddd = (long)distanceTime / (3600 * 24);
    if (ddd == 0) {
      ddd = 1;
    }
    distanceStr = [NSString stringWithFormat:@"%ld天前", ddd];

  } else {
    [df setDateFormat:@"yy"];
    NSString *currentYear = [df stringFromDate:dat];
    NSString *messageYear = [df stringFromDate:beDate];

    if ([currentYear isEqualToString:messageYear]) {
      [df setDateFormat:@"M月d日"];
    } else {
      [df setDateFormat:@"yy年M月d日"];
    }
    distanceStr = [df stringFromDate:beDate];
  }
  return distanceStr;
}

+ (NSString *)distanceTimeWithBeforeTimeV2:(double)beTime {
  NSString *distanceStr;
  NSDate *beDate = [NSDate dateWithTimeIntervalSince1970:beTime];
  NSDateFormatter *df = [[NSDateFormatter alloc] init];
  [df setDateFormat:@"yy年M月d日"];
  distanceStr = [df stringFromDate:beDate];
  return distanceStr;
}

+ (NSString *)getTimestrWithTime:(NSInteger)time {
  NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
  [formatter setDateFormat:@"YY年M月d日 HH:mm"];
  NSTimeZone *zone = [NSTimeZone systemTimeZone];
  [formatter setTimeZone:zone];
  NSDate *confromTimesp = [NSDate dateWithTimeIntervalSince1970:time];
  NSString *confromTimespStr = [formatter stringFromDate:confromTimesp];
  return [confromTimespStr substringFromIndex:10];
}

+ (NSString *)getAllTimeWithTime:(NSInteger)time {
  NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
  [formatter setDateFormat:@"YY年M月d日 HH:mm:ss"];
  NSTimeZone *zone = [NSTimeZone systemTimeZone];
  [formatter setTimeZone:zone];
  NSDate *confromTimesp = [NSDate dateWithTimeIntervalSince1970:time];
  NSString *confromTimespStr = [formatter stringFromDate:confromTimesp];
  return confromTimespStr;
}

+ (NSString *)getTimestrWithFirstTime:(NSInteger)time {
  NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
  [formatter setDateFormat:@"YY年M月d日 HH:mm:ss"];
  NSTimeZone *zone = [NSTimeZone systemTimeZone];
  [formatter setTimeZone:zone];
  NSDate *confromTimesp = [NSDate dateWithTimeIntervalSince1970:time];
  NSString *confromTimespStr = [formatter stringFromDate:confromTimesp];
  return [confromTimespStr substringToIndex:10];
}

+ (NSString *)getTimestrWithChangeClassTime:(NSInteger)time {
  NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
  [formatter setDateFormat:@"HH:mm"];
  NSTimeZone *zone = [NSTimeZone systemTimeZone];
  [formatter setTimeZone:zone];
  NSDate *confromTimesp = [NSDate dateWithTimeIntervalSince1970:time];
  NSString *confromTimespStr = [formatter stringFromDate:confromTimesp];
  return confromTimespStr;
}

+ (NSString *)getNowTimeStr {

  NSDate *now = [NSDate date];
  NSDateFormatter *df = [[NSDateFormatter alloc] init];
  df.timeZone = [NSTimeZone systemTimeZone];
  df.dateFormat = @"yy年M月d日 HH:mm:ss";
  NSString *systemTimeZoneStr = [df stringFromDate:now];

  return systemTimeZoneStr;
}

+ (NSString *)getNowTimeIntervalStr {

  NSDate *date = [NSDate dateWithTimeIntervalSinceNow:0];

  NSTimeInterval a = [date timeIntervalSince1970];

  NSString *timeString = [NSString stringWithFormat:@"%0.f", a];

  return timeString;
}
+ (BOOL)isTodayWithStr:(NSString *)dateStr {

  NSString *year_old = [dateStr substringWithRange:(NSMakeRange(0, 4))];
  NSString *month_old = [dateStr substringWithRange:(NSMakeRange(5, 2))];
  NSString *day_old = [dateStr substringWithRange:NSMakeRange(8, 2)];

  NSString *nowTime = [self getNowTimeStr];

  NSString *year_now = [nowTime substringWithRange:(NSMakeRange(0, 4))];
  NSString *month_now = [nowTime substringWithRange:(NSMakeRange(5, 2))];
  NSString *day_now = [nowTime substringWithRange:NSMakeRange(8, 2)];

  return (year_old == year_now) && (month_old == month_now) &&
         (day_old == day_now);
}

+ (void)setExtraCellLineHidden:(UITableView *)tableView {
  UIView *view = [UIView new];
  view.backgroundColor = [UIColor clearColor];
  [tableView setTableFooterView:view];
}

+ (void)setTableSeparInsert:(UITableView *)tableView edge:(UIEdgeInsets)_edge {
  if ([tableView respondsToSelector:@selector(setSeparatorInset:)]) {
    [tableView setSeparatorInset:_edge];
  }
  if ([tableView respondsToSelector:@selector(setLayoutMargins:)]) {
    [tableView setLayoutMargins:_edge];
  }
}

+ (NSMutableAttributedString *)loadColorLabel:(NSString *)str
                                    changeStr:(NSString *)changeStr
                                        color:(UIColor *)color
                                         font:(NSInteger)font {

  NSRange rang = [changeStr rangeOfString:str];
  NSMutableAttributedString *attributStr =
      [[NSMutableAttributedString alloc] initWithString:changeStr];
  [attributStr addAttribute:NSForegroundColorAttributeName
                      value:color
                      range:rang];

  [attributStr addAttribute:NSFontAttributeName
                      value:[UIFont systemFontOfSize:font]
                      range:rang];
  return attributStr;
}

+ (NSString *)iphoneType {
  struct utsname systemInfo;
  uname(&systemInfo);
  NSString *platform = [NSString stringWithCString:systemInfo.machine
                                          encoding:NSASCIIStringEncoding];
  if ([platform isEqualToString:@"iPhone1,1"])
    return @"iPhone 2G";
  if ([platform isEqualToString:@"iPhone1,2"])
    return @"iPhone 3G";
  if ([platform isEqualToString:@"iPhone2,1"])
    return @"iPhone 3GS";
  if ([platform isEqualToString:@"iPhone3,1"])
    return @"iPhone 4";
  if ([platform isEqualToString:@"iPhone3,2"])
    return @"iPhone 4";
  if ([platform isEqualToString:@"iPhone3,3"])
    return @"iPhone 4";
  if ([platform isEqualToString:@"iPhone4,1"])
    return @"iPhone 4S";
  if ([platform isEqualToString:@"iPhone5,1"])
    return @"iPhone 5";
  if ([platform isEqualToString:@"iPhone5,2"])
    return @"iPhone 5";
  if ([platform isEqualToString:@"iPhone5,3"])
    return @"iPhone 5c";
  if ([platform isEqualToString:@"iPhone5,4"])
    return @"iPhone 5c";
  if ([platform isEqualToString:@"iPhone6,1"])
    return @"iPhone 5s";
  if ([platform isEqualToString:@"iPhone6,2"])
    return @"iPhone 5s";
  if ([platform isEqualToString:@"iPhone7,1"])
    return @"iPhone 6 Plus";
  if ([platform isEqualToString:@"iPhone7,2"])
    return @"iPhone 6";
  if ([platform isEqualToString:@"iPhone8,1"])
    return @"iPhone 6s";
  if ([platform isEqualToString:@"iPhone8,2"])
    return @"iPhone 6s Plus";
  if ([platform isEqualToString:@"iPhone8,4"])
    return @"iPhone SE";
  if ([platform isEqualToString:@"iPhone9,1"])
    return @"iPhone 7";
  if ([platform isEqualToString:@"iPhone9,3"])
    return @"iPhone 7";
  if ([platform isEqualToString:@"iPhone9,2"])
    return @"iPhone 7 Plus";
  if ([platform isEqualToString:@"iPhone9,4"])
    return @"iPhone 7 Plus";
  if ([platform isEqualToString:@"iPhone10,1"])
    return @"iPhone 8";
  if ([platform isEqualToString:@"iPhone10,4"])
    return @"iPhone 8";
  if ([platform isEqualToString:@"iPhone10,2"])
    return @"iPhone 8 Plus";
  if ([platform isEqualToString:@"iPhone10,5"])
    return @"iPhone 8 Plus";
  if ([platform isEqualToString:@"iPhone10,3"])
    return @"iPhone X";
  if ([platform isEqualToString:@"iPhone10,6"])
    return @"iPhone X";
  if ([platform isEqualToString:@"iPhone11,2"])
    return @"iPhone XS";
  if ([platform isEqualToString:@"iPhone11,4"])
    return @"iPhone XS Max";
  if ([platform isEqualToString:@"iPhone11,6"])
    return @"iPhone XS Max";
  if ([platform isEqualToString:@"iPhone11,8"])
    return @"iPhone XR";

  if ([platform isEqualToString:@"iPad1,1"])
    return @"iPad 1G";
  if ([platform isEqualToString:@"iPad2,1"])
    return @"iPad 2";
  if ([platform isEqualToString:@"iPad2,2"])
    return @"iPad 2";
  if ([platform isEqualToString:@"iPad2,3"])
    return @"iPad 2";
  if ([platform isEqualToString:@"iPad2,4"])
    return @"iPad 2";
  if ([platform isEqualToString:@"iPad2,5"])
    return @"iPad Mini 1G";
  if ([platform isEqualToString:@"iPad2,6"])
    return @"iPad Mini 1G";
  if ([platform isEqualToString:@"iPad2,7"])
    return @"iPad Mini 1G";
  if ([platform isEqualToString:@"iPad3,1"])
    return @"iPad 3";
  if ([platform isEqualToString:@"iPad3,2"])
    return @"iPad 3";
  if ([platform isEqualToString:@"iPad3,3"])
    return @"iPad 3";
  if ([platform isEqualToString:@"iPad3,4"])
    return @"iPad 4";
  if ([platform isEqualToString:@"iPad3,5"])
    return @"iPad 4";
  if ([platform isEqualToString:@"iPad3,6"])
    return @"iPad 4";
  if ([platform isEqualToString:@"iPad4,1"])
    return @"iPad Air";
  if ([platform isEqualToString:@"iPad4,2"])
    return @"iPad Air";
  if ([platform isEqualToString:@"iPad4,3"])
    return @"iPad Air";
  if ([platform isEqualToString:@"iPad4,4"])
    return @"iPad Mini 2G";
  if ([platform isEqualToString:@"iPad4,5"])
    return @"iPad Mini 2G";
  if ([platform isEqualToString:@"iPad4,6"])
    return @"iPad Mini 2G";
  if ([platform isEqualToString:@"iPad4,7"])
    return @"iPad Mini 3";
  if ([platform isEqualToString:@"iPad4,8"])
    return @"iPad Mini 3";
  if ([platform isEqualToString:@"iPad4,9"])
    return @"iPad Mini 3";
  if ([platform isEqualToString:@"iPad5,1"])
    return @"iPad Mini 4";
  if ([platform isEqualToString:@"iPad5,2"])
    return @"iPad Mini 4";
  if ([platform isEqualToString:@"iPad5,3"])
    return @"iPad Air 2";
  if ([platform isEqualToString:@"iPad5,4"])
    return @"iPad Air 2";
  if ([platform isEqualToString:@"iPad6,3"])
    return @"iPad Pro 9.7";
  if ([platform isEqualToString:@"iPad6,4"])
    return @"iPad Pro 9.7";
  if ([platform isEqualToString:@"iPad6,7"])
    return @"iPad Pro 12.9";
  if ([platform isEqualToString:@"iPad6,8"])
    return @"iPad Pro 12.9";
  if ([platform isEqualToString:@"iPad6,11"])
    return @"iPad 5";
  if ([platform isEqualToString:@"iPad6,12"])
    return @"iPad 5";
  if ([platform isEqualToString:@"iPad7,1"])
    return @"iPad Pro 12.9 2nd";
  if ([platform isEqualToString:@"iPad7,2"])
    return @"iPad Pro 12.9 2nd";
  if ([platform isEqualToString:@"iPad7,3"])
    return @"iPad Pro 10.5";
  if ([platform isEqualToString:@"iPad7,4"])
    return @"iPad Pro 10.5";
  if ([platform isEqualToString:@"iPad7,5"])
    return @"iPad 6";
  if ([platform isEqualToString:@"iPad7,6"])
    return @"iPad 6";
  if ([platform isEqualToString:@"i386"])
    return @"iPhone Simulator";
  if ([platform isEqualToString:@"x86_64"])
    return @"iPhone Simulator";
  return platform;
}

+ (CGFloat)getHZPhotoHeight:(NSInteger)photoCount index:(NSInteger)index {
  if (photoCount == 1) {
    if (index == 1)
      return 228 * kWidthFactor; // 单图高228
    if (index == 2)
      return 280 * kWidthFactor; // 单图高280
    if (index == 3)
      return 365 * kWidthFactor; // 单图高365
  } else if (photoCount == 2) {
    return 174 * kWidthFactor; // 2张图片，1行 单图高174
  } else if (photoCount == 3) {
    return 116.3 * kWidthFactor; // 3张图片，1行 单图高116.3
  } else if (photoCount == 4) {
    return 365 * kWidthFactor; // 4张图片，2x2布局 单图高178.5
  } else if (photoCount <= 6) {
    return 241 * kWidthFactor; // 5-6张图片，2行 单图高116.3
  } else {
    return 365 * kWidthFactor; // 7-9张图片，3行 单图高116.3
  }
  return 0;
}

+ (CGFloat)getHZPhotoHeightNew:(NSInteger)photoCount index:(NSInteger)index {
  return 174 * kWidthFactor;
}

+ (NSInteger)getHZPhotoStringCount:(NSString *)string {
  if ([string isKindOfClass:[NSArray class]]) {
    return [(NSArray *)string count];
  }
  if (string.length <= 0) {
    return 0;
  } else {
    NSArray *tempCountArr = [string componentsSeparatedByString:@","];
    return tempCountArr.count;
  }
}

+ (BOOL)stringIsInArray:(NSString *)string array:(NSMutableArray *)array {
  for (NSString *str in array) {
    if ([string isEqualToString:str]) {
      return YES;
    }
  }
  return NO;
}

+ (NSMutableArray *)getShowTagArray:(NSMutableArray *)allTag
                           isChouse:(NSMutableArray *)isChouse
                           disArray:(NSMutableArray *)disArray {
  NSMutableArray *temppAry = [NSMutableArray new];
  NSInteger tempInter = 0;
  for (TagModelResult *arrStr in allTag) {
    if (arrStr.name.length > 4) {
      tempInter = tempInter + 2;
    } else {
      tempInter = tempInter + 1;
    }
  }
  NSInteger tempInter2 = 0;
  for (TagModelResult *arrStr2 in isChouse) {
    if (arrStr2.name.length > 4) {
      tempInter2 = tempInter2 + 2;
    } else {
      tempInter2 = tempInter2 + 1;
    }
  }
  NSInteger tempInter3 = 0;
  for (TagModelResult *arrStr3 in disArray) {
    if (arrStr3.name.length > 4) {
      tempInter3 = tempInter3 + 2;
    } else {
      tempInter3 = tempInter3 + 1;
    }
  }

  NSMutableArray *tempisChouse = [NSMutableArray new];
  for (TagModelResult *isddy in isChouse) {
    [tempisChouse addObject:isddy.name];
  }
  if (tempInter - tempInter2 + tempInter3 <= 16) {
    for (TagModelResult *tag3 in disArray) {
      [temppAry addObject:tag3];
    }
    for (TagModelResult *tag1 in allTag) {
      if (![self stringIsInArray:tag1.name array:tempisChouse]) {
        [temppAry addObject:tag1];
      }
    }
    return temppAry;
  } else {
    NSInteger ttemp0 = tempInter3;
    for (TagModelResult *tag2 in disArray) {
      [temppAry addObject:tag2];
    }
    for (TagModelResult *tag1 in allTag) {
      if (![self stringIsInArray:tag1.name array:tempisChouse]) {
        [temppAry addObject:tag1];
        if (tag1.name.length > 4) {
          ttemp0 = ttemp0 + 2;
        } else {
          ttemp0 = ttemp0 + 1;
        }
        if (ttemp0 > 20) {
          return temppAry;
        }
      }
    }
  }

  return temppAry;
}

+ (BOOL)compareRGBAColor1:(UIColor *)color1 withColor2:(UIColor *)color2 {

  CGFloat red1, red2, green1, green2, blue1, blue2, alpha1, alpha2;
  [color1 getRed:&red1 green:&green1 blue:&blue1 alpha:&alpha1];
  [color2 getRed:&red2 green:&green2 blue:&blue2 alpha:&alpha2];

  if ((red1 == red2) && (green1 == green2) && (blue1 == blue2) &&
      (alpha1 == alpha2)) {
    return YES;
  } else {
    return NO;
  }
}

+ (NSString *)timeSwitchTimestamp:(NSString *)formatTime
                     andFormatter:(NSString *)format {
  NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
  [formatter setDateStyle:NSDateFormatterMediumStyle];
  [formatter setTimeStyle:NSDateFormatterShortStyle];
  [formatter setDateFormat:format];
  NSTimeZone *timeZone = [NSTimeZone timeZoneWithName:@"Asia/Beijing"];
  [formatter setTimeZone:timeZone];
  NSDate *date = [formatter dateFromString:formatTime];
  NSInteger timeSp =
      [[NSNumber numberWithDouble:[date timeIntervalSince1970]] integerValue];
  return [NSString stringWithFormat:@"%ld", timeSp];
}

+ (NSMutableArray *)stringChangeArray:(NSString *)str {
  NSArray *array = [str componentsSeparatedByString:@","];
  NSMutableArray *tenpArr = [NSMutableArray arrayWithArray:array];
  return tenpArr;
}

+ (NSString *)arrayChangeString:(NSMutableArray *)arr {
  NSMutableArray *temppAry = [NSMutableArray new];
  for (TagModelResult *tag1 in arr) {
    [temppAry addObject:tag1.tagId];
  }
  NSString *str = [temppAry componentsJoinedByString:@","];
  return str;
}

+ (NSString *)arrayOfStrChangeString:(NSMutableArray *)arr {
  if (arr.count == 1) {
    NSString *zerostr = [arr objectAtIndex:0];
    return zerostr;
  }
  NSString *str = [arr componentsJoinedByString:@","];
  return str;
}

+ (NSString *)getAgeOfData:(NSString *)datastr {
  NSTimeInterval interval = [datastr doubleValue] - 86400;
  NSDate *date = [NSDate dateWithTimeIntervalSince1970:interval];
  NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
  [formatter setDateFormat:@"y y/MM/dd"];
  NSString *dateString = [formatter stringFromDate:date];

  NSString *year = [dateString substringWithRange:NSMakeRange(0, 4)];
  NSString *month = [dateString substringWithRange:NSMakeRange(5, 2)];
  NSString *day =
      [dateString substringWithRange:NSMakeRange(dateString.length - 2, 2)];

  NSDate *nowDate = [NSDate date];
  NSCalendar *calendar = [[NSCalendar alloc]
      initWithCalendarIdentifier:NSCalendarIdentifierISO8601];
  NSDateComponents *compomemts =
      [calendar components:NSCalendarUnitYear | NSCalendarUnitMonth |
                           NSCalendarUnitWeekday | NSCalendarUnitDay
                  fromDate:nowDate];
  NSInteger nowYear = compomemts.year;
  NSInteger nowMonth = compomemts.month;
  NSInteger nowDay = compomemts.day;

  NSInteger userAge = nowYear - year.intValue - 1;
  if ((nowMonth > month.intValue) ||
      (nowMonth == month.intValue && nowDay >= day.intValue)) {
    userAge++;
  }
  return [NSString stringWithFormat:@"%ld", userAge];
}

+ (NSString *)getYearMonthDayOfData:(NSString *)datastr {
  NSTimeInterval interval = [datastr doubleValue];
  NSDate *date = [NSDate dateWithTimeIntervalSince1970:interval];
  NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
  [formatter setDateFormat:@"yy年M月d日"];
  NSString *dateString = [formatter stringFromDate:date];

  return dateString;
}

+ (NSString *)getMonthDayHourOfData:(NSString *)datastr {
  NSTimeInterval interval = [datastr doubleValue];
  NSDate *date = [NSDate dateWithTimeIntervalSince1970:interval];
  NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
  [formatter setDateFormat:@"M月d日 HH:mm"];
  NSString *dateString = [formatter stringFromDate:date];

  return dateString;
}

+ (NSURL *)getUrlWithUserSmallIcon:(NSString *)icon {
  NSString *str =
      [NSString stringWithFormat:@"%@?imageView2/1/w/128/h/128", icon];
  return [NSURL URLWithString:str];
}

+ (NSURL *)getUrlWithUserDefartIcon:(NSString *)icon {
  NSString *str =
      [NSString stringWithFormat:@"%@?imageView2/0/w/256/h/256", icon];
  return [NSURL URLWithString:str];
}

+ (NSURL *)getUrlWithUserPictaure:(NSString *)icon {
  NSString *str = [NSString stringWithFormat:@"%@?imageslim", icon];
  return [NSURL URLWithString:str];
}

+ (NSString *)getYearMonthDayWithLineOfData:(NSString *)datastr {
  NSTimeInterval interval = [datastr doubleValue];
  NSDate *date = [NSDate dateWithTimeIntervalSince1970:interval];
  NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
  [formatter setDateFormat:@"yy年M月d日"];
  NSString *dateString = [formatter stringFromDate:date];

  return dateString;
}

+ (NSString *)getYiDuiPaoXieGang:(NSArray *)datastr
                         fenType:(NSString *)fenType {
  if (datastr.count == 1) {
    TagModelResult *tInfo =
        [TagModelResult mj_objectWithKeyValues:[datastr objectAtIndex:0]];
    return tInfo.name;
  } else if (datastr.count >= 2) {
    NSMutableArray *temppAry = [NSMutableArray new];
    for (id tag1 in datastr) {
      if ([tag1 isKindOfClass:[NSDictionary class]]) {
        [temppAry addObject:tag1[@"name"]];
      } else if ([tag1 isKindOfClass:[TagModelResult class]]) {
        [temppAry addObject:((TagModelResult *)tag1).name];
      }
    }
    NSString *str = [temppAry componentsJoinedByString:fenType];
    return str;
  } else {
    return @"";
  }
}

+ (NSString *)getCachePath {
  NSArray *paths = NSSearchPathForDirectoriesInDomains(NSCachesDirectory,
                                                       NSUserDomainMask, YES);
  return [paths objectAtIndex:0];
}

+ (NSMutableArray *)stringChangeArray:(NSString *)str
                              typeStr:(NSString *)typeStr {
  NSArray *array = [str componentsSeparatedByString:typeStr];
  NSMutableArray *tenpArr = [NSMutableArray arrayWithArray:array];
  return tenpArr;
}

+ (NSString *)contentTypeForImageData:(NSData *)data {
  uint8_t c;
  [data getBytes:&c length:1];
  switch (c) {
  case 0xFF:
    return @"jpeg";
  case 0x89:
    return @"png";
  case 0x47:
    return @"gif";
  case 0x49:
  case 0x4D:
    return @"tiff";
  case 0x52:
    if ([data length] < 12) {
      return nil;
    }
    NSString *testString = [[NSString alloc]
        initWithData:[data subdataWithRange:NSMakeRange(0, 12)]
            encoding:NSASCIIStringEncoding];
    if ([testString hasPrefix:@"RIFF"] && [testString hasSuffix:@"WEBP"]) {
      return @"webp";
    }
    return nil;
  }
  return nil;
}

@end
