#import "CurrencyRootVC.h"

static CGFloat const kNavButtonWidth = 60.0f;
static CGFloat const kNavButtonHeight = 44.0f;
static CGFloat const kNavImageInsetLeft = -8.0f;
static CGFloat const kNavImageInsetRight = 24.0f;
static CGFloat const kNetworkErrorIconSize = 150.0f;
static CGFloat const kNetworkErrorButtonHeight = 40.0f;
static CGFloat const kNetworkErrorButtonWidth = 188.0f;
static NSInteger const kNetworkErrorViewTag = 1000;
static char *const btnClickAction = "btnClickAction";

@interface CurrencyRootVC () <UINavigationControllerDelegate>

@property(nonatomic, strong) UIButton *navLeftBtn;
@property(nonatomic, strong) UIButton *navRightBtn;
@property(nonatomic, strong) UILabel *titleLabel;
@property(nonatomic, strong) UIView *networkErrorView;
@property(nonatomic, assign) SEL networkErrorSelector;

@end

@implementation CurrencyRootVC

#pragma mark - Lifecycle Methods

- (void)viewDidLoad {
  [super viewDidLoad];
  [self setupBasicUI];
  [self setupSafeAreaLayout];
  [self setupScrollViewInsets];
  [self setupNavigationBarAppearance];
}

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];
  [self setupNavigationBarAppearance];
}

#pragma mark - Setup Methods

- (void)setupBasicUI {
  self.view.backgroundColor = KColor_White;
  self.navigationController.delegate = self;
}

- (void)setupSafeAreaLayout {
  self.safeContentView = [UIView new];
  [self.view addSubview:self.safeContentView];
  [self.safeContentView mas_makeConstraints:^(MASConstraintMaker *make) {
    if (@available(iOS 11.0, *)) {
      make.left.equalTo(self.view.mas_safeAreaLayoutGuideLeft);
      make.right.equalTo(self.view.mas_safeAreaLayoutGuideRight);
      make.top.equalTo(self.view.mas_safeAreaLayoutGuideTop);
      make.bottom.equalTo(self.view.mas_safeAreaLayoutGuideBottom);
    } else {
      make.edges.equalTo(self.view);
    }
  }];
}

- (void)setupScrollViewInsets {
  if (@available(iOS 11.0, *)) {
    [[UIScrollView appearance] setContentInsetAdjustmentBehavior:
                                   UIScrollViewContentInsetAdjustmentNever];
    UIWindow *mainWindow =
        [[[UIApplication sharedApplication] delegate] window];
    self.safeAreaInset = UIEdgeInsetsMake(mainWindow.safeAreaInsets.top - 20, 0,
                                          mainWindow.safeAreaInsets.bottom, 0);
  } else {
    self.automaticallyAdjustsScrollViewInsets = NO;
    self.safeAreaInset = UIEdgeInsetsZero;
  }
}

- (void)setupNavigationBarAppearance {
  UINavigationBar *navigationBar = self.navigationController.navigationBar;
  navigationBar.translucent = NO;

  if (@available(iOS 13.0, *)) {
    UINavigationBarAppearance *appearance =
        [[UINavigationBarAppearance alloc] init];
    [appearance configureWithOpaqueBackground];
    appearance.backgroundColor = [UIColor whiteColor];
    appearance.shadowColor = nil;

    navigationBar.standardAppearance = appearance;
    navigationBar.scrollEdgeAppearance = appearance;
    if (@available(iOS 15.0, *)) {
      navigationBar.compactAppearance = appearance;
      navigationBar.compactScrollEdgeAppearance = appearance;
    }
  } else {
    [navigationBar setBackgroundImage:nil forBarMetrics:UIBarMetricsDefault];
    navigationBar.barTintColor = [UIColor whiteColor];
    navigationBar.shadowImage = nil;
  }

  navigationBar.titleTextAttributes =
      @{NSForegroundColorAttributeName : KColor_HighBlack};
  navigationBar.backgroundColor = [UIColor whiteColor];
  navigationBar.barStyle = UIBarStyleDefault;
}

#pragma mark - Navigation Methods

- (MASViewAttribute *)setTitleForV199:(NSString *)title {
  UIButton *backButton = [UIButton buttonWithType:UIButtonTypeCustom];
  backButton.frame = CGRectMake(24 * kWidthFactor, 24 * kWidthFactor,
                                10 * kWidthFactor, 16 * kWidthFactor);
  [backButton setImage:[UIImage imageNamed:@"back"]
              forState:UIControlStateNormal];
  [backButton addTarget:self
                 action:@selector(backAction)
       forControlEvents:UIControlEventTouchUpInside];
  [self.safeContentView addSubview:backButton];

  if (title.length > 0) {
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.text = title;
    titleLabel.textColor = KColor_HighBlack;
    titleLabel.font = SourceHanSerifSemiBoldFont(16 * kWidthFactor);
    [self.safeContentView addSubview:titleLabel];
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
      make.centerY.equalTo(backButton);
      make.centerX.equalTo(self.safeContentView);
    }];
  }

  return backButton.mas_bottom;
}

- (void)actionBack:(void (^)(void))btnClickBlock {
  [self actionCustomLeftBtnWithNrlImage:@"back"
                               htlImage:@"back"
                                  title:@""
                                 action:btnClickBlock];
}

- (void)actionCustomLeftBtnWithNrlImage:(NSString *)nrlImage
                               htlImage:(NSString *)hltImage
                                  title:(NSString *)title
                                 action:(void (^)(void))btnClickBlock {
  if (!self.navLeftBtn) {
    self.navLeftBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    self.navLeftBtn.frame = CGRectMake(0, 0, 44, kNavButtonHeight);
    self.navLeftBtn.contentEdgeInsets = UIEdgeInsetsMake(0, -15, 0, 0);
  }

  [self configureNavButton:self.navLeftBtn
                  nrlImage:nrlImage
                  htlImage:hltImage
                     title:title];

  void (^block)(void) = btnClickBlock ?: ^{
    [self.navigationController popViewControllerAnimated:YES];
  };

  objc_setAssociatedObject(self.navLeftBtn, &btnClickAction, block,
                           OBJC_ASSOCIATION_COPY);

  UIBarButtonItem *leftItem =
      [[UIBarButtonItem alloc] initWithCustomView:self.navLeftBtn];
  UIBarButtonItem *negativeSpaceItem = [[UIBarButtonItem alloc]
      initWithBarButtonSystemItem:UIBarButtonSystemItemFixedSpace
                           target:nil
                           action:nil];
  negativeSpaceItem.width = -16;

  self.navigationItem.leftBarButtonItems = @[ negativeSpaceItem, leftItem ];
}

- (void)actionCustomRightBtnWithNrlImage:(NSString *)nrlImage
                                htlImage:(NSString *)hltImage
                                   title:(NSString *)title
                                  action:(void (^)(void))btnClickBlock {
  if (!self.navRightBtn) {
    self.navRightBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    self.navRightBtn.frame = CGRectMake(kMainWidth - kNavButtonWidth, 0,
                                        kNavButtonWidth, kNavButtonHeight);
    self.navRightBtn.contentHorizontalAlignment =
        UIControlContentHorizontalAlignmentRight;
  }

  [self configureNavButton:self.navRightBtn
                  nrlImage:nrlImage
                  htlImage:hltImage
                     title:title];

  objc_setAssociatedObject(self.navRightBtn, &btnClickAction, btnClickBlock,
                           OBJC_ASSOCIATION_COPY);
  self.navigationItem.rightBarButtonItem =
      [[UIBarButtonItem alloc] initWithCustomView:self.navRightBtn];
}

- (void)configureNavButton:(UIButton *)button
                  nrlImage:(NSString *)nrlImage
                  htlImage:(NSString *)hltImage
                     title:(NSString *)title {
  button.imageView.contentMode = UIViewContentModeScaleAspectFit;
  [button setImage:[UIImage imageNamed:nrlImage] forState:UIControlStateNormal];
  [button setImage:[UIImage imageNamed:hltImage ?: nrlImage]
          forState:UIControlStateHighlighted];

  if (title.length > 0) {
    button.titleLabel.font = [UIFont systemFontOfSize:16];
    [button setTitle:title forState:UIControlStateNormal];
    [button setTitle:title forState:UIControlStateHighlighted];
    [button setTitleColor:KColor_Black forState:UIControlStateNormal];
    [button setTitleColor:KColor_Black forState:UIControlStateHighlighted];
  }

  [button addTarget:self
                action:@selector(actionBtnClick:)
      forControlEvents:UIControlEventTouchUpInside];
}

#pragma mark - Network Error View Methods

- (void)showNetErrorView:(UIView *)superView
             withPadding:(UIEdgeInsets)padding
                  target:(id)target
                selector:(SEL)selector {
  if ([self isNetErrorViewShowing:superView]) {
    return;
  }

  self.networkErrorSelector = selector;

  UIView *errorView = [[UIView alloc]
      initWithFrame:CGRectMake(padding.left, padding.top,
                               superView.frame.size.width - padding.left -
                                   padding.right,
                               superView.frame.size.height - padding.top -
                                   padding.bottom + 64)];
  errorView.tag = kNetworkErrorViewTag;
  errorView.backgroundColor = KColor_White;

  [self setupNetworkErrorContent:errorView target:target];
  [superView addSubview:errorView];
  self.networkErrorView = errorView;
}

- (void)setupNetworkErrorContent:(UIView *)errorView target:(id)target {
  UIImageView *errorIcon = [[UIImageView alloc]
      initWithFrame:CGRectMake((kMainWidth - kNetworkErrorIconSize) / 2,
                               145 * kMainTemp,
                               kNetworkErrorIconSize * kMainTemp,
                               kNetworkErrorIconSize * kMainTemp)];
  errorIcon.image = [UIImage imageNamed:@"ico_network_error"];
  errorIcon.contentMode = UIViewContentModeScaleAspectFill;
  [errorView addSubview:errorIcon];

  UILabel *errorLabel = [[UILabel alloc] init];
  errorLabel.text = @"网络不可用,请检查网络连接";
  errorLabel.font = [UIFont systemFontOfSize:16 * kMainTemp];
  errorLabel.textColor = UIColorFromRGB(0XCCCCCC);
  errorLabel.textAlignment = NSTextAlignmentCenter;
  [errorView addSubview:errorLabel];
  [errorLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(errorIcon.mas_bottom);
    make.centerX.equalTo(errorView);
    make.size.mas_equalTo(CGSizeMake(240 * kMainTemp, 17 * kMainTemp));
  }];

  FlatButton *retryButton = [FlatButton buttonWithType:UIButtonTypeCustom];
  [retryButton setTitle:@"点击刷新" forState:UIControlStateNormal];
  [retryButton setTitleColor:KColor_Red forState:UIControlStateNormal];
  retryButton.titleLabel.font = [UIFont systemFontOfSize:17 * kMainTemp];
  retryButton.backgroundColor = KColor_White;
  retryButton.layer.cornerRadius = 20 * kMainTemp;
  retryButton.layer.masksToBounds = YES;
  [retryButton addTarget:target
                  action:self.networkErrorSelector
        forControlEvents:UIControlEventTouchUpInside];
  [errorView addSubview:retryButton];

  [retryButton mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(errorLabel.mas_bottom).offset(42 * kMainTemp);
    make.centerX.equalTo(errorView);
    make.size.mas_equalTo(CGSizeMake(kNetworkErrorButtonWidth * kMainTemp,
                                     kNetworkErrorButtonHeight * kMainTemp));
  }];
}

#pragma mark - Helper Methods

- (void)actionBtnClick:(UIButton *)button {
  void (^action)(void) = objc_getAssociatedObject(button, &btnClickAction);
  if (action) {
    action();
  }
}

- (void)backAction {
  [self.navigationController popViewControllerAnimated:YES];
}

- (void)setShowBackBtn:(BOOL)showBack {
  if (showBack) {
    __weak typeof(self) weakSelf = self;
    [self actionCustomLeftBtnWithNrlImage:@"back"
                                 htlImage:@"back"
                                    title:@""
                                   action:^{
                                     [weakSelf.navigationController
                                         popViewControllerAnimated:YES];
                                   }];
  }
}

- (BOOL)isNetErrorViewShowing:(UIView *)superView {
  return [superView viewWithTag:kNetworkErrorViewTag] != nil;
}

- (void)hidNetErrorView:(UIView *)superView {
  [self.networkErrorView removeFromSuperview];
  self.networkErrorView = nil;
}

#pragma mark - Toast Methods

- (void)showToast:(NSString *)toast {
  [self showToast:toast time:2.0];
}

- (void)showToastFast:(NSString *)toast {
  [self showToast:toast time:1.0];
}

- (void)showToast:(NSString *)toast time:(NSTimeInterval)time {
  [self.view makeToast:toast duration:time position:CSToastPositionCenter];
}

#pragma mark - Memory Management

- (void)dealloc {
  [NSNotificationCenter.defaultCenter removeObserver:self];
}

#pragma mark - UINavigationController Delegate

- (BOOL)hidesBottomBarWhenPushed {
  return self.navigationController.viewControllers.count > 1;
}

@end