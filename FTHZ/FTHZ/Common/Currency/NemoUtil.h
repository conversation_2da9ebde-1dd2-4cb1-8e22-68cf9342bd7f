#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

@interface NemoUtil : NSObject

+ (NSString *)readFromFullPath:(NSString *)fileName;

+ (NSString *)randomString;

+ (BOOL)isEmpty:(NSString *)_str;

+ (NSString *)chineseToUTf8Str:(NSString *)chineseStr;

+ (UIColor *)getColorWith16Str:(NSString *)hStr;

+ (int)DistanceBetweenTwoString:(NSString *)strA
                      StrAbegin:(int)strAbegin
                        StrAend:(int)strAend
                           StrB:(NSString *)strB
                      StrBbegin:(int)strBbegin
                        StrBend:(int)strBend;

+ (NSMutableArray *)randomArrayPosition:(NSMutableArray *)_array;

+ (NSDictionary *)dictionaryWithJsonString:(NSString *)jsonString;

+ (CGSize)calculateLabelHeightByText:(UIFont *)_font
                               width:(float)_fWidth
                           heightMax:(float)_fHeightMax
                             content:(NSString *)_strContent;

+ (CGSize)calculateLabelHeightByText:(UIFont *)_font
                               width:(float)_fWidth
                           heightMax:(float)_fHeightMax
                             content:(NSString *)_strContent
                         lineSpacing:(CGFloat)lineSpacing;
+ (NSString *)version;

+ (void)printAllFont;

+ (CGFloat)getVideoDuration:(NSURL *)_URL;

+ (BOOL)stringContainsEmoji:(NSString *)string;

+ (NSString *)getAppVersionint;

+ (UIViewController *)rootViewController;
+ (UIViewController *)currentRootController;
+ (UIViewController *)getCurrentVC;

+ (NSString *)RandomNumber:(NSInteger)number;

+ (NSString *)getmd5By16:(NSString *)str;

+ (NSString *)distanceTimeWithBeforeTime:(double)beTime;
+ (NSString *)distanceTimeWithBeforeTimeV2:(double)beTime;

+ (NSString *)getTimestrWithTime:(NSInteger)time;
+ (NSString *)getAllTimeWithTime:(NSInteger)time;
+ (NSString *)getTimestrWithFirstTime:(NSInteger)time;
+ (NSString *)getTimestrWithChangeClassTime:(NSInteger)time;

+ (NSString *)getNowTimeStr;

+ (NSString *)getNowTimeIntervalStr;

+ (BOOL)isTodayWithStr:(NSString *)dateStr;

+ (void)setExtraCellLineHidden:(UITableView *)tableView;
+ (void)setTableSeparInsert:(UITableView *)tableView edge:(UIEdgeInsets)_edge;

+ (NSMutableAttributedString *)loadColorLabel:(NSString *)str
                                    changeStr:(NSString *)changeStr
                                        color:(UIColor *)color
                                         font:(NSInteger)font;

+ (NSString *)iphoneType;

+ (CGFloat)getHZPhotoHeight:(NSInteger)photoCount index:(NSInteger)index;

+ (CGFloat)getHZPhotoHeightNew:(NSInteger)photoCount index:(NSInteger)index;

+ (NSInteger)getHZPhotoStringCount:(NSString *)string;

+ (BOOL)stringIsInArray:(NSString *)string array:(NSMutableArray *)array;

+ (NSMutableArray *)getShowTagArray:(NSMutableArray *)allTag
                           isChouse:(NSMutableArray *)isChouse
                           disArray:(NSMutableArray *)disArray;

+ (BOOL)compareRGBAColor1:(UIColor *)color1 withColor2:(UIColor *)color2;

+ (NSString *)timeSwitchTimestamp:(NSString *)formatTime
                     andFormatter:(NSString *)format;

+ (NSMutableArray *)stringChangeArray:(NSString *)str;

+ (NSString *)arrayChangeString:(NSMutableArray *)arr;

+ (NSString *)arrayOfStrChangeString:(NSMutableArray *)arr;

+ (NSString *)getAgeOfData:(NSString *)datastr;

+ (NSString *)getYearMonthDayOfData:(NSString *)datastr;

+ (NSString *)getMonthDayHourOfData:(NSString *)datastr;

+ (NSURL *)getUrlWithUserSmallIcon:(NSString *)icon;

+ (NSURL *)getUrlWithUserDefartIcon:(NSString *)icon;

+ (NSURL *)getUrlWithUserPictaure:(NSString *)icon;

+ (NSString *)getYearMonthDayWithLineOfData:(NSString *)datastr;

+ (NSString *)getYiDuiPaoXieGang:(NSArray *)datastr fenType:(NSString *)fenType;

+ (NSString *)getCachePath;

+ (NSMutableArray *)stringChangeArray:(NSString *)str
                              typeStr:(NSString *)typeStr;

+ (NSString *)contentTypeForImageData:(NSData *)data;
@end
