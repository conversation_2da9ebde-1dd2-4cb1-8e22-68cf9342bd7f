#import "VitNetAPIClient.h"

@implementation VitNetAPIClient

+ (instancetype)sharedClient {
  static VitNetAPIClient *_sharedClient = nil;
  static dispatch_once_t onceToken;
  dispatch_once(&onceToken, ^{
    _sharedClient = [[VitNetAPIClient alloc]
        initWithBaseURL:[NSURL URLWithString:URL_BASE]];
    _sharedClient.securityPolicy =
        [AFSecurityPolicy policyWithPinningMode:AFSSLPinningModeNone];
  });

  return _sharedClient;
}

- (instancetype)initWithBaseURL:(NSURL *)_url {
  self = [super initWithBaseURL:_url];
  if (self) {
    [self initialHttp:nil respone:nil];
  }
  return self;
}

- (void)initialHttp:(AFHTTPRequestSerializer *)request
            respone:(AFHTTPResponseSerializer *)respone {
  AFHTTPRequestSerializer *request_form = [AFHTTPRequestSerializer serializer];
  request_form.timeoutInterval = 30;
  self.requestSerializer = request_form;
  AFJSONResponseSerializer *response_json =
      [AFJSONResponseSerializer serializer];
  self.responseSerializer = response_json;
}

@end
