#import "Http.h"
#import "FTHZRequestEncryptor.h"

@implementation Http

+ (void)getAsynRequestWithUrl:(NSString *)url
                       params:(NSDictionary *)params
                      success:(void (^)(NSDictionary *resultObject))success
                      failure:(void (^)(NSError *requestErr))failure {
  [Http requestAsynRequestWithUrl:url
                           params:params
                          methods:@"get"
                          success:success
                          failure:failure];
}

+ (void)postAsynRequestWithUrl:(NSString *)url
                        params:(NSDictionary *)params
                       success:(void (^)(NSDictionary *resultObject))success
                       failure:(void (^)(NSError *requestErr))failure {
  [Http requestAsynRequestWithUrl:url
                           params:params
                          methods:@"post"
                          success:success
                          failure:failure];
}

+ (void)deleteAsynRequestWithUrl:(NSString *)url
                          params:(NSDictionary *)params
                         success:(void (^)(NSDictionary *resultObject))success
                         failure:(void (^)(NSError *requestErr))failure {
  [Http requestAsynRequestWithUrl:url
                           params:params
                          methods:@"delete"
                          success:success
                          failure:failure];
}

+ (void)requestAsynRequestWithUrl:(NSString *)url
                           params:(NSDictionary *)params
                          methods:(NSString *)method
                          success:(void (^)(NSDictionary *resultObject))success
                          failure:(void (^)(NSError *requestErr))failure {

  __weak typeof(self) wSelf = self;

  void (^successHandler)(NSURLSessionDataTask *, id) =
      ^(NSURLSessionDataTask *task, id responseObject) {
        if (!wSelf) {
          return;
        }

        NSDictionary *dic = nil;
        NSError *jsonError = nil;

        if ([responseObject isKindOfClass:[NSData class]]) {
          dic = [NSJSONSerialization
              JSONObjectWithData:responseObject
                         options:NSJSONReadingMutableContainers
                           error:&jsonError];
        } else if ([responseObject isKindOfClass:[NSDictionary class]]) {
          dic = responseObject;
        }

        if (jsonError) {
          if (failure) {
            failure(jsonError);
          }
          return;
        }

        if (success && dic) {
          success(dic);
        }
      };

  void (^failureHandler)(NSURLSessionDataTask *, NSError *) =
      ^(NSURLSessionDataTask *task, NSError *error) {
        NSLog(@"Request Error: %@", error);
        if (failure) {
          failure(error);
        }
      };

  NSString *adid =
      [[[ASIdentifierManager sharedManager] advertisingIdentifier] UUIDString];
  CGRect rect_screen = [[UIScreen mainScreen] bounds];
  CGSize size_screen = rect_screen.size;
  CGFloat scale_screen = [UIScreen mainScreen].scale;
  CGFloat width = size_screen.width * scale_screen;
  CGFloat height = size_screen.height * scale_screen;
  NSString *screenStr = [NSString stringWithFormat:@"%.0fX%.0f", height, width];

  VitNetAPIClient *session = [VitNetAPIClient sharedClient];
  session.responseSerializer = [AFHTTPResponseSerializer serializer];

  NSDictionary *headers = @{
    @"App_Version" : [NemoUtil getAppVersionint],
    @"App_Id" : @"1",
    @"Channel_Name" : @"AppStore",
    @"Resolution" : screenStr,
    @"Idfa" : adid,
    @"Iphone_Type" : [NemoUtil iphoneType],
    @"secretCon" : @"Hello, this is a secret message!",
    @"secretRs" : @"975a461ad5e32f381e046679441aaedaa768456b47ba718701721aeb72c"
                  @"af92b0403fd4e9eda6868f52c7d5247c61ed3"
  };

  [headers
      enumerateKeysAndObjectsUsingBlock:^(NSString *key, id value, BOOL *stop) {
        [session.requestSerializer setValue:value forHTTPHeaderField:key];
      }];

  void (^executeRequest)(void) = ^{
    NSMutableDictionary *paramsToEncrypt = [NSMutableDictionary dictionary];
    if (params) {
      [paramsToEncrypt addEntriesFromDictionary:params];
    }

    NSString *path = [[NSURL URLWithString:url] path];

    NSString *encryptedHeaderValue =
        [FTHZRequestEncryptor getEncryptedHeaderValue:paramsToEncrypt
                                                 path:path
                                               method:method.lowercaseString];

    NSMutableDictionary *requestHeaders = [NSMutableDictionary dictionary];
    [requestHeaders setValue:encryptedHeaderValue forKey:@"X-Encrypted-Data"];

    if ([method.lowercaseString isEqualToString:@"get"]) {
      [session GET:url
          parameters:params
             headers:requestHeaders
            progress:nil
             success:successHandler
             failure:failureHandler];
    } else if ([method.lowercaseString isEqualToString:@"post"]) {
      [session POST:url
          parameters:params
             headers:requestHeaders
            progress:nil
             success:successHandler
             failure:failureHandler];
    } else if ([method.lowercaseString isEqualToString:@"delete"]) {
      [session DELETE:url
           parameters:params
              headers:requestHeaders
              success:successHandler
              failure:failureHandler];
    }
  };

  if ([FTHZAccountManager.shared isLogined]) {
    if ([url isEqualToString:KURLUpdateSSID]) {
      NSAssert(NO, @"不要走老的这个过了");
      executeRequest();
    } else {
      [session.requestSerializer setValue:AccountManager.token
                       forHTTPHeaderField:@"ssid"];
      executeRequest();
    }
  } else {
    executeRequest();
  }
}

NSString *__nullable OHPathForFileInBundle(NSString *fileName,
                                           NSBundle *bundle) {
  return [bundle pathForResource:[fileName stringByDeletingPathExtension]
                          ofType:[fileName pathExtension]];
}

+ (void)downloadVoiceWithUrl:(NSString *)urlStr
                    complete:(void (^)(NSURL *filePath))voiceFilePath
                        fail:(void (^)(NSString *error))fail {
  NSURLSessionConfiguration *config =
      [NSURLSessionConfiguration defaultSessionConfiguration];
  AFURLSessionManager *manager =
      [[AFURLSessionManager alloc] initWithSessionConfiguration:config];

  NSURL *url = [NSURL URLWithString:urlStr];
  NSURLRequest *request = [NSURLRequest requestWithURL:url];

  NSURLSessionDownloadTask *task = [manager downloadTaskWithRequest:request
      progress:nil
      destination:^NSURL *(NSURL *targetPath, NSURLResponse *response) {
        NSURL *dirURL =
            [[NSFileManager defaultManager] URLForDirectory:NSDocumentDirectory
                                                   inDomain:NSUserDomainMask
                                          appropriateForURL:nil
                                                     create:NO
                                                      error:nil];
        return
            [dirURL URLByAppendingPathComponent:[response suggestedFilename]];
      }
      completionHandler:^(NSURLResponse *response, NSURL *filePath,
                          NSError *error) {
        if (error) {
          NSLog(@"Download Error: %@", error);
          if (fail) {
            fail(@"");
          }
        } else {
          NSLog(@"Download Success - File Path: %@", filePath);
          if (voiceFilePath) {
            voiceFilePath(filePath);
          }
        }
      }];

  [task resume];
}

@end