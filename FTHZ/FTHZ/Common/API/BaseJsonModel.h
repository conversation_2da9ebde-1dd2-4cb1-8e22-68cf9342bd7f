#import <JSONModel/JSONModel.h>

typedef void (^success)(NSDictionary *resultObject);
typedef void (^failure)(NSError *requestErr);

@interface BaseJsonModel : JSONModel

@property(nonatomic, strong) NSNumber<Optional> *success;
@property(nonatomic, strong) NSNumber<Optional> *code;
@property(nonatomic, strong) NSString<Optional> *msg;
@property(nonatomic, strong) NSString<Optional> *isChouseTag;

@end

@interface ColorFont : BaseJsonModel
@property(nonatomic, assign) int type;
@property(nonatomic, assign) int status;
@property(nonatomic, assign) long expire_time;

@end
