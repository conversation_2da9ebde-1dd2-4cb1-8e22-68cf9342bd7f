#import "BaseJsonModel.h"
#import "LoginBusiness.h"

@implementation BaseJsonModel

- (NSString *)msg {

  if ([_msg isKindOfClass:NSString.class] && [_msg isValid]) {

    if ([self.code intValue] == 0) {
      self.msg = @"请求成功";
      self.success = [NSNumber numberWithBool:YES];
    } else {
      self.success = [NSNumber numberWithBool:NO];

      switch ([self.code intValue]) {

      case 1002: {
        self.msg = @"用户验证失败，请重新登录";
        [[FTHZAccountManager shared]
            logoutWithCompletion:^(UserPersonResult *_Nullable user,
                                   NSError *_Nullable error) {
              [FTHZBusiness loginThenAction:^(FTHZLoginResult result,
                                              UserPersonResult *_Nullable user,
                                              NSError *_Nullable error){

              }];
            }];
        break;
      }
      case 999999: {
        break;
      }
      }
    }
  } else {
    if ([self.code intValue] == 0) {
      self.msg = @"请求成功";
      self.success = [NSNumber numberWithBool:YES];
    } else {
      self.msg = @"请求失败";
      self.success = [NSNumber numberWithBool:NO];
    }
  }

  return _msg;
}

- (NSNumber *)success {
  NSString *message = self.msg;
  return _success;
}

@end

@implementation ColorFont

@end
