#import <Foundation/Foundation.h>

#import "BuglyConfig.h"
#import "BuglyLog.h"

BLY_START_NONNULL

@interface Bugly : NSObject

+ (void)startWithAppId:(NSString * BLY_NULLABLE)appId;

+ (void)startWithAppId:(NSString * BLY_NULLABLE)appId
                config:(BuglyConfig * BLY_NULLABLE)config;

+ (void)startWithAppId:(NSString * BLY_NULLABLE)appId
     developmentDevice:(BOOL)development
                config:(BuglyConfig * BLY_NULLABLE)config;

+ (void)setUserIdentifier:(NSString *)userId;

+ (void)updateAppVersion:(NSString *)version;

+ (void)setUserValue:(NSString *)value
              forKey:(NSString *)key;

+ (NSDictionary * BLY_NULLABLE)allUserValues;

+ (void)setTag:(NSUInteger)tag;

+ (NSUInteger)currentTag;

+ (NSString *)buglyDeviceId;

+ (void)reportException:(NSException *)exception;

+ (void)reportError:(NSError *)error;

+ (void)reportExceptionWithCategory:(NSUInteger)category
                               name:(NSString *)aName
                             reason:(NSString *)aReason
                          callStack:(NSArray *)aStackArray
                          extraInfo:(NSDictionary *)info
                       terminateApp:(BOOL)terminate;

+ (NSString *)sdkVersion;

+ (BOOL)isAppCrashedOnStartUpExceedTheLimit;

BLY_END_NONNULL

@end
