#ifndef TLSSDK_TLSOpenQueryListener_h
#define TLSSDK_TLSOpenQueryListener_h

#import <Foundation/Foundation.h>
#import "TLSErrInfo.h"
#import "TLSUserInfo.h"

enum TLSOpenState{
    TLS_OPEN_STATE_UNUSED = 3, //未使用过的第三方帐号
    TLS_OPEN_STATE_USED = 2,   //使用过的第三方帐号
    TLS_OPEN_STATE_BINDED = 1, //已绑定自有帐号的第三方帐号
};
@protocol TLSOpenQueryListener <NSObject>

@required

-(void)	OnOpenQuerySuccess:(enum TLSOpenState)state;

-(void)	OnOpenQueryFail:(TLSErrInfo *) errInfo;

-(void)	OnOpenQueryTimeout:(TLSErrInfo *) errInfo;

@end

#endif
