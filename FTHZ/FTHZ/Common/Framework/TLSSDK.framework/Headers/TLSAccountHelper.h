#ifndef TLSSDK_TLSAccountHelper_h
#define TLSSDK_TLSAccountHelper_h
#import <Foundation/Foundation.h>
#import "TLSErrInfo.h"
#import "TLSPwdRegListener.h"
#import "TLSPwdResetListener.h"
#import "TLSSmsRegListener.h"
#import "TLSStrAccountRegListener.h"
#import "TLSOpenBindListener.h"
#import "TLSOpenQueryListener.h"
#import "TLSDefine.h"

DEPRECATED_ATTRIBUTE
@interface TLSAccountHelper : NSObject 

+(TLSAccountHelper *) getInstance;

-(TLSAccountHelper *) init:(int)sdkAppid
            andAccountType:(int)accountType
                 andAppVer:(NSString *)appVer;

-(void) setTimeOut:(int)timeout;

-(void) setLocalId:(int)localid;

-(void) setCountry:(int)country;

-(NSString *) getSDKVersion;
#pragma mark - 手机号密码注册

-(int) TLSPwdRegAskCode:(NSString *)mobile andTLSPwdRegListener:(id)listener;

-(int) TLSPwdRegAskCode:(NSString *)mobile andAccType:(uint32_t)accType andOpenAppid:(NSString*)openAppid andOpenId:(NSString*)openid andAccessToken:(NSString*)accessToken andTLSPwdRegListener:(id)listener;

-(int) TLSPwdRegReaskCode:(id)listener;

-(int) TLSPwdRegVerifyCode:(NSString *)code andTLSPwdRegListener:(id)listener;

-(int) TLSPwdRegCommit:(NSString *)password andTLSPwdRegListener:(id)listener;
#pragma mark - 手机号重置密码

-(int) TLSPwdResetAskCode:(NSString *)mobile andTLSPwdResetListener:(id)listener;

-(int) TLSPwdResetReaskCode:(id)listener;

-(int) TLSPwdResetVerifyCode:(NSString *)code andTLSPwdResetListener:(id)listener;

-(int) TLSPwdResetCommit:(NSString *)password andTLSPwdResetListener:(id)listener;
#pragma mark - 手机号短信注册

-(int) TLSSmsRegAskCode:(NSString *)mobile andTLSSmsRegListener:(id)listener;

-(int) TLSSmsRegAskCode:(NSString *)mobile andAccType:(uint32_t)accType andOpenAppid:(NSString*)openAppid andOpenId:(NSString*)openid andAccessToken:(NSString*)accessToken andTLSSmsRegListener:(id)listener;

-(int) TLSSmsRegReaskCode:(id)listener;

-(int) TLSSmsRegVerifyCode:(NSString *)code andTLSSmsRegListener:(id)listener;

-(int) TLSSmsRegCommit:(id)listener;
#pragma mark - 字符串帐号注册

-(int) TLSStrAccountReg:(NSString *)account andPassword:(NSString *)password andTLSStrAccountRegListener:(id)listener;

-(int) TLSStrAccountReg:(NSString *)account andPassword:(NSString *)password andAccType:(uint32_t)accType andOpenAppid:(NSString*)openAppid andOpenId:(NSString*)openid andAccessToken:(NSString*)accessToken andTLSStrAccountRegListener:(id)listener;
@end
#endif
