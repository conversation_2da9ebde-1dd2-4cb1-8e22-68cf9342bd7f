#ifndef TLSSDK_TLSLoginHelper_h
#define TLSSDK_TLSLoginHelper_h
#import <Foundation/Foundation.h>
#include <AvailabilityMacros.h>
#import "TLSErrInfo.h"
#import "TLSUserInfo.h"
#import "TLSPwdLoginListener.h"
#import "TLSSmsLoginListener.h"
#import "TLSRefreshTicketListener.h"
#import "TLSExchangeTicketListener.h"
#import "TLSGuestLoginListener.h"

DEPRECATED_ATTRIBUTE
@interface TLSLoginHelper : NSObject
{
#if __has_feature(objc_arc_weak)
    id __weak               defaultDeleaget;
#elif __has_feature(objc_arc)
    id __unsafe_unretained  defaultDeleaget;
#else
    id                      defaultDeleaget;
#endif
    
    uint32_t dwFailedCount;
}

@property (readonly) uint32_t dwFailedCount;

+(TLSLoginHelper *) getInstance;

-(TLSLoginHelper *) init:(int)sdkAppid
          andAccountType:(int)accountType
               andAppVer:(NSString *)appVer;

-(void) setTest;

-(void) setTimeOut:(int)timeout;

-(void) setLocalId:(int)localid;

-(void) setLogcat:(BOOL)show;

-(NSString *) getSDKVersion;

-(NSData *) getGUID;

-(NSArray *) getAllUserInfo;

-(TLSUserInfo *) getLastUserInfo;

-(BOOL) clearUserInfo:(NSString *)identifier withOption:(BOOL)deleteAccount;

-(BOOL) needLogin:(NSString *)identifier;

-(NSDictionary*) getSSOTicket:(NSString *)identifier;

-(NSString*) getTLSUserSig:(NSString *)identifier;

-(int) TLSPwdLogin:(NSString *)identifier andPassword:(NSString *)password andTLSPwdLoginListener:(id)listener;

-(int) TLSPwdLoginReaskImgCode:(id)listener;

-(int) TLSPwdLoginVerifyImgCode:(NSString *)imgCode andTLSPwdLoginListener:(id)listener;

-(int) TLSRefreshTicket:(NSString *)identifier andTLSRefreshTicketListener:(id) listener;

-(int) TLSSmsAskCode:(NSString *)mobile andTLSSmsLoginListener:(id)listener;

-(int) TLSSmsReaskCode:(NSString *)mobile andTLSSmsLoginListener:(id)listener;

-(int) TLSSmsVerifyCode:(NSString *)mobile andCode:(NSString *)code andTLSSmsLoginListener:(id)listener;

-(int) TLSSmsLogin:(NSString *)mobile andTLSSmsLoginListener:(id)listener;

-(int)TLSExchangeTicket:(uint32_t)sdkappid andAccountType:(uint32_t)accountType andIdentifier:(NSString *)identifier andAppidAt3rd:(NSString *)appidAt3rd andUserSig:(NSString *)userSig andTLSExchangeTicketListener:(id)listener;

-(int)TLSGuestLogin:(id<TLSGuestLoginListener>)listener;

-(TLSUserInfo*)getGuestIdentifier;
@end
#endif
