#ifndef TLSSDK_TLSHelper_h
#define TLSSDK_TLSHelper_h
#import <Foundation/Foundation.h>
#include <AvailabilityMacros.h>
#import "TLSUserInfo.h"
#import "TLSSmsLoginListener.h"
#import "TLSSmsRegListener.h"
#import "TLSPwdLoginListener.h"
#import "TLSPwdRegListener.h"
#import "TLSPwdResetListener.h"
#import "TLSStrAccountRegListener.h"
#import "TLSRefreshTicketListener.h"
#import "TLSOpenLoginListener.h"
#import "TLSOpenBindListener.h"
#import "TLSOpenQueryListener.h"
#import "TLSExchangeTicketListener.h"
#import "TLSGuestLoginListener.h"
#import "TLSOpenAccessTokenListener.h"
#import "TLSDefine.h"

@interface TLSHelper : NSObject

@property uint32_t country;

@property uint32_t language;

+(TLSHelper *) getInstance;

-(TLSHelper *) init:(int)sdkAppid
          andAccountType:(int)accountType
               andAppVer:(NSString *)appVer DEPRECATED_ATTRIBUTE;

-(TLSHelper*)init:(int)sdkAppid andAppVer:(NSString *)appVer;

-(void) setTimeOut:(int)timeout;

-(void) setLogcat:(BOOL)show;

-(NSString *) getSDKVersion;

-(NSData *) getGUID;

-(NSArray *) getAllUserInfo;

-(TLSUserInfo *) getLastUserInfo;

-(BOOL) clearUserInfo:(NSString *)identifier withOption:(BOOL)deleteAccount;

-(BOOL) needLogin:(NSString *)identifier;

-(NSString*) getTLSUserSig:(NSString *)identifier;

-(int) TLSRefreshTicket:(NSString *)identifier andTLSRefreshTicketListener:(id<TLSRefreshTicketListener>) listener;

#pragma mark - 密码登录

-(int) TLSPwdLogin:(NSString *)identifier andPassword:(NSString *)password andTLSPwdLoginListener:(id<TLSPwdLoginListener>)listener;

-(int) TLSPwdLoginReaskImgCode:(id<TLSPwdLoginListener>)listener;

-(int) TLSPwdLoginVerifyImgCode:(NSString *)imgCode andTLSPwdLoginListener:(id<TLSPwdLoginListener>)listener;

#pragma mark - 短信登录

-(int) TLSSmsAskCode:(NSString *)mobile andTLSSmsLoginListener:(id<TLSSmsLoginListener>)listener;

-(int) TLSSmsReaskCode:(NSString *)mobile andTLSSmsLoginListener:(id<TLSSmsLoginListener>)listener;

-(int) TLSSmsVerifyCode:(NSString *)mobile andCode:(NSString *)code andTLSSmsLoginListener:(id<TLSSmsLoginListener>)listener;

-(int) TLSSmsLogin:(NSString *)mobile andTLSSmsLoginListener:(id<TLSSmsLoginListener>)listener;

#pragma mark - 匿名登录 游客模式

-(int)TLSGuestLogin:(id<TLSGuestLoginListener>)listener;

-(TLSUserInfo*)getGuestIdentifier;

#pragma mark - 手机号密码注册

-(int) TLSPwdRegAskCode:(NSString *)mobile andTLSPwdRegListener:(id<TLSPwdRegListener>)listener;

-(int) TLSPwdRegAskCode:(NSString *)mobile andAccType:(uint32_t)accType andOpenAppid:(NSString*)openAppid andOpenId:(NSString*)openid andAccessToken:(NSString*)accessToken andTLSPwdRegListener:(id<TLSPwdRegListener>)listener;

-(int) TLSPwdRegReaskCode:(id<TLSPwdRegListener>)listener;

-(int) TLSPwdRegVerifyCode:(NSString *)code andTLSPwdRegListener:(id<TLSPwdRegListener>)listener;

-(int) TLSPwdRegCommit:(NSString *)password andTLSPwdRegListener:(id<TLSPwdRegListener>)listener;
#pragma mark - 手机号重置密码

-(int) TLSPwdResetAskCode:(NSString *)mobile andTLSPwdResetListener:(id<TLSPwdResetListener>)listener;

-(int) TLSPwdResetReaskCode:(id<TLSPwdResetListener>)listener;

-(int) TLSPwdResetVerifyCode:(NSString *)code andTLSPwdResetListener:(id<TLSPwdResetListener>)listener;

-(int) TLSPwdResetCommit:(NSString *)password andTLSPwdResetListener:(id<TLSPwdResetListener>)listener;
#pragma mark - 手机号短信注册

-(int) TLSSmsRegAskCode:(NSString *)mobile andTLSSmsRegListener:(id<TLSSmsRegListener>)listener;

-(int) TLSSmsRegAskCode:(NSString *)mobile andAccType:(uint32_t)accType andOpenAppid:(NSString*)openAppid andOpenId:(NSString*)openid andAccessToken:(NSString*)accessToken andTLSSmsRegListener:(id<TLSSmsRegListener>)listener;

-(int) TLSSmsRegReaskCode:(id<TLSSmsRegListener>)listener;

-(int) TLSSmsRegVerifyCode:(NSString *)code andTLSSmsRegListener:(id<TLSSmsRegListener>)listener;

-(int) TLSSmsRegCommit:(id<TLSSmsRegListener>)listener;
#pragma mark - 字符串帐号注册

-(int) TLSStrAccountReg:(NSString *)account andPassword:(NSString *)password andTLSStrAccountRegListener:(id<TLSStrAccountRegListener>)listener;

-(int) TLSStrAccountReg:(NSString *)account andPassword:(NSString *)password andAccType:(uint32_t)accType andOpenAppid:(NSString*)openAppid andOpenId:(NSString*)openid andAccessToken:(NSString*)accessToken andTLSStrAccountRegListener:(id<TLSStrAccountRegListener>)listener;
#pragma mark - 第三方帐号绑定

-(int) TLSOpenBind:(NSString*)account andUserSig:(NSString*)userSig andAccType:(uint32_t)accType andOpenAppid:(NSString*)openAppid andOpenId:(NSString*)openid andAccessToken:(NSString*)accessToken andListener:(id<TLSOpenBindListener>)listener;

-(int) TLSOpenQuery:(uint32_t)accType andOpenAppid:(NSString*)openAppid andOpenId:(NSString*)openid andAccessToken:(NSString*)accessToken andListener:(id<TLSOpenQueryListener>)listener;

-(int) TLSOpenAccessToken:(uint32_t)accountType andCode:(NSString*)code andListener:(id<TLSOpenAccessTokenListener>)listener;

#pragma mark - 第三方帐号登录

-(int)TLSOpenLogin:(uint32_t)accountType andOpenId:(NSString *)openid andAppid:(NSString *)appid andAccessToken:(NSString *)accessToken andTLSOpenLoginListener:(id<TLSOpenLoginListener>)listener;

#pragma mark - 独立模式

-(int)TLSExchangeTicket:(uint32_t)sdkappid andAccountType:(uint32_t)accountType andIdentifier:(NSString *)identifier andAppidAt3rd:(NSString *)appidAt3rd andUserSig:(NSString *)userSig andTLSExchangeTicketListener:(id<TLSExchangeTicketListener>)listener DEPRECATED_ATTRIBUTE;

-(int)TLSExchangeTicket:(NSString *)identifier andUserSig:(NSString *)userSig andTLSExchangeTicketListener:(id<TLSExchangeTicketListener>)listener;

-(NSDictionary*) getSSOTicket:(NSString *)identifier;

-(int64_t)getLastRefreshTime:(NSString*)identifier;

#pragma mark - 内部接口
-(void)setTest:(BOOL)test;
-(void)setTestHost:(NSString*)host;
-(int)SSOLogin:(id<TLSGuestLoginListener>)listener;
-(TLSUserInfo*)getSSOId;
@end

#endif
