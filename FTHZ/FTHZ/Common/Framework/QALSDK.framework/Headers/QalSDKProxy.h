#ifndef QALSDK_PROXY_H
#define QALSDK_PROXY_H
#import <Foundation/Foundation.h>
#import "QalSDKCallbackProtocol.h"

#define SSOOPEN_REG @"sso_open_status.stat_reg"
#define SSOOPEN_HB  @"sso_open_status.stat_hello"

#define DEFAULT_LOG_SDK_PATH    @"/sdk.log"
#define DEFAULT_LOG_APP_PATH    @"/app.log"
#define log_internal 3600


@class QalStatusAdapter;
#ifdef QALSDK_FOR_TLS
@class TLSRefreshDelegate;

#endif

typedef NS_ENUM(NSInteger, LogLevel) {
    LOG_ERROR               = 1,
    LOG_WARN                = 2,
    LOG_INFO                = 3,
    LOG_DEBUG               = 4,
};

typedef enum _EQALBindFailReason
{
    EQALBindFail_Unknown = 0,   
    EQALBindFail_NoSSOTicket,  
    EQALBindFail_NoNeedToUnbind,
    EQALBindFail_TinyidNULL,
    EQALBindFail_GuidNULL,
    EQALBindFail_UnpackRegPackFail,
    EQALBindFail_RegTimeOut,
    EQALBindFail_isBinding,
    EQALBindFail_sendPackFail
    
}EQALBindFailReason;

typedef enum _EQALPacketFailReason
{
    EQALPacketFail_Unknown = 0,
    EQALPacketFail_NoNetOnReq,
    EQALPacketFail_NoNetOnResp,
    EQALPacketFail_NoAuthOnReq,
    EQALPacketFail_SSOError,
    EQALPacketFail_TimeoutOnReq,
    EQALPacketFail_TimeoutOnResp,
    EQALPacketFail_NoResendOnReq,
    EQALPacketFail_NoResendOnResp,
    EQALPacketFail_FlowSaveFiltered,
    EQALPacketFail_OverLoadOnReq,
    EQALPacketFail_LogicError
}EQALPacketFailReason;


typedef enum _EQALNetworkType
{
    EQALNetworkType_Undefine = -1,
    EQALNetworkType_NotReachable = 0,
    EQALNetworkType_ReachableViaWiFi = 1,
    EQALNetworkType_ReachableViaWWAN = 2,
    EQALNetworkType_UNKNOWN = 100,
    EQALNetworkType_GPRS = 101,
    EQALNetworkType_EDGE = 102,
    EQALNetworkType_UMTS = 103,
    EQALNetworkType_CDMA = 104,
    EQALNetworkType_EVDO0 = 105,
    EQALNetworkType_EVDOA = 106,
    EQALNetworkType_1xRTT = 107,
    EQALNetworkType_HSDPA = 108,
    EQALNetworkType_HSUPA = 109,
    EQALNetworkType_HSPA = 110,
    EQALNetworkType_IDEN = 111,
    EQALNetworkType_EVDOB = 112,
    EQALNetworkType_LTE = 113,
    EQALNetworkType_EHRPD = 114,
    EQALNetworkType_HSPAP = 115,
    
    EQALNetworkType_IOS_BEGIN = 98,
    EQALNetworkType_CDMA1x = 98,
    EQALNetworkType_WCDMA = 99,
    EQALNetworkType_IOS_END = 99,
}EQALNetworkType;




extern uint64_t g_tinyid;
extern uint32_t g_isready;









@interface QalSDKProxy : NSObject
{
    int env;
    int _sdkAppid;
    int _accType;
    id<QalConnListenerProtocol> _conncb;
    id<QalLogListenerProtocol> _logcb;
    id<QalUserStatusListenerProtocol> _statuscb;
    id<QalInitCallbackProtocol> _initcb;
    int shortConn;
    NSString* clientVersion;
}

+(void)setMsfFilePath:(NSString*) filePath;

+ (QalSDKProxy *)sharedInstance;

-(void)setEnv:(int) value;

-(id)initQal:(int) sdkAppid;

-(id)initWithAppid:(int) appid andSDKAppid:(int) sdkAppid andAccType:(int)accType;

-(void)addPushListener:(NSString*) cmd andQalPushListener:(id<QalPushListenerProtocol>) cb;

-(void)setConnectionListener:(id<QalConnListenerProtocol>) cb;

-(void)setLogListener:(id<QalLogListenerProtocol>) cb;

-(void)setUserStatusListener:(id<QalUserStatusListenerProtocol>) cb;

-(void)setInitListener:(id<QalInitCallbackProtocol>) cb;

-(int)sendMsgWithID:(NSString*)identifier AndCmd:(NSString*) cmd andReqbody:(NSData*) reqbody andTimeout:(int) timeout andQalReqCallback:(id<QalReqCallbackProtocol>) cb;

-(void)bindID:(NSString*) user andQalBindCallback:(id<QalBindCallbackProtocol>) cb;

-(void)unbindID:(NSString*) user andQalReqCallback:(id<QalBindCallbackProtocol>) cb;

- (BOOL)isConnected;

-(void)log:(int)level andTag:(NSString*)tag andMsg:(NSString*)msg;

-(void)applog:(int)level andTag:(NSString*)tag andMsg:(NSString*)msg;

-(NSString*)getIdentifier;

-(void)closeLocalLog;

-(NSString*)getSDKVer;

-(void)clearCb:(int)seq;

-(NSString*)getMsfAppid;

- (void)setOpenAppid:(NSString *)aOpenAppid;

- (void)setClientVersion:(NSString*)version;
- (NSString*)getClientVersion;

-(int)getNetType;

-(void)setProxylist:(NSString*)sig andList:(NSArray*)list;

-(void)cancelProxylist:(NSString*)sig;

@end
#endif
