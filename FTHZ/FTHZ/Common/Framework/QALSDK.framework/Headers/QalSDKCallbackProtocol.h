#ifndef QALSDK_CALLBACKPROTOCOL_H
#define QALSDK_CALLBACKPROTOCOL_H
#import <Foundation/Foundation.h>



@protocol QalInitCallbackProtocol <NSObject>

-(void)initFinished;

@end


@protocol QalPushListenerProtocol <NSObject>

@required

-(void)onPushSucc:(NSData*)busibuf andCmd:(NSString*)cmd andId:(NSString*)identifier;

-(void)onPushFail:(int)failReason;

@end

@protocol QalConnListenerProtocol <NSObject>

@required

-(void)onConnSucc:(NSString*)ip andPort:(NSString*)port;

-(void)onDisconnected;

-(void)onConnecting;

-(void)onNeedAuth;

@end

@protocol QalLogListenerProtocol <NSObject>

@required
-(void)log:(int)level andTag:(NSString*)tag andMsg:(NSString*)msg;

@end

@protocol QalUserStatusListenerProtocol <NSObject>

@required

-(void)onRegSucc:(NSString*)identy;

-(void)onRegFail:(int) errCode andErrMsg:(NSString*)errMsg andIdentifier:(NSString*)identy;

-(void)onForceOffLine:(NSString*)identy;

-(void)onTicketError:(NSString*)identy andErrorcode:(NSString*)code;

@end

@protocol QalReqCallbackProtocol <NSObject>

@required

-(void)onReqSucc:(NSData*)busibuf;

-(void)onReqFail:(int)failReason;

@optional
-(void)onInnerReqSucc:(NSString*)tinyID andBusibuf:(NSData*)busibuf;
-(void)onInnerReqFail:(NSString*)tinyID andReason:(int)failReason;


@end

@protocol QalBindCallbackProtocol <NSObject>

@required

-(void)onSucc;

-(void)onFail:(int)failReason;


@end

#endif