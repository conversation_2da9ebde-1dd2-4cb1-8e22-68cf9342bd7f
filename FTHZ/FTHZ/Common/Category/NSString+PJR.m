#import "NSString+PJR.h"

@implementation NSString (PJR)

- (BOOL)isBlank {
  return ([[self removeWhiteSpacesFromString] isEqualToString:@""]) ? YES : NO;
}
- (BOOL)isValid {
  return ([[self removeWhiteSpacesFromString] isEqualToString:@""] ||
          self == nil || [self isEqualToString:@"(null)"])
             ? NO
             : YES;
}

- (NSString *)removeWhiteSpacesFromString {
  NSString *trimmedString = [self
      stringByTrimmingCharactersInSet:[NSCharacterSet
                                          whitespaceAndNewlineCharacterSet]];
  return trimmedString;
}

- (NSUInteger)countNumberOfWords {
  NSScanner *scanner = [NSScanner scannerWithString:self];
  NSCharacterSet *whiteSpace =
      [NSCharacterSet whitespaceAndNewlineCharacterSet];

  NSUInteger count = 0;
  while ([scanner scanUpToCharactersFromSet:whiteSpace intoString:nil]) {
    count++;
  }

  return count;
}

- (BOOL)containsString:(NSString *)subString {
  if (![subString isValid])
    return NO;
  return ([self rangeOfString:subString].location == NSNotFound) ? NO : YES;
}

- (BOOL)isBeginsWith:(NSString *)string {
  return ([self hasPrefix:string]) ? YES : NO;
}

- (BOOL)isEndssWith:(NSString *)string {
  return ([self hasSuffix:string]) ? YES : NO;
}

- (NSString *)replaceCharcter:(NSString *)olderChar
                 withCharcter:(NSString *)newerChar {
  return [self stringByReplacingOccurrencesOfString:olderChar
                                         withString:newerChar];
}

- (NSString *)getSubstringFrom:(NSInteger)begin to:(NSInteger)end {
  NSRange r;
  r.location = begin;
  r.length = end - begin;
  return [self substringWithRange:r];
}

- (NSString *)addString:(NSString *)string {
  if (!string || string.length == 0)
    return self;

  return [self stringByAppendingString:string];
}

- (NSString *)removeSubString:(NSString *)subString {
  if ([self containsString:subString]) {
    NSRange range = [self rangeOfString:subString];
    return [self stringByReplacingCharactersInRange:range withString:@""];
  }
  return self;
}

- (BOOL)containsOnlyLetters {
  NSCharacterSet *letterCharacterset =
      [[NSCharacterSet letterCharacterSet] invertedSet];
  return ([self rangeOfCharacterFromSet:letterCharacterset].location ==
          NSNotFound);
}

- (BOOL)containsOnlyNumbers {
  NSCharacterSet *numbersCharacterSet = [[NSCharacterSet
      characterSetWithCharactersInString:@"0123456789"] invertedSet];
  return ([self rangeOfCharacterFromSet:numbersCharacterSet].location ==
          NSNotFound);
}

- (BOOL)containsOnlyNumbersAndLetters {
  NSCharacterSet *numAndLetterCharSet =
      [[NSCharacterSet alphanumericCharacterSet] invertedSet];
  return ([self rangeOfCharacterFromSet:numAndLetterCharSet].location ==
          NSNotFound);
}

- (BOOL)isInThisarray:(NSArray *)array {
  for (NSString *string in array) {
    if ([self isEqualToString:string]) {
      return YES;
    }
  }
  return NO;
}

+ (NSString *)getStringFromArray:(NSArray *)array {
  return [array componentsJoinedByString:@" "];
}

- (NSArray *)getArray {
  return [self componentsSeparatedByString:@" "];
}

+ (NSString *)getMyApplicationVersion {
  NSDictionary *info = [[NSBundle mainBundle] infoDictionary];
  NSString *version = [info objectForKey:@"CFBundleVersion"];
  return version;
}

+ (NSString *)getMyApplicationName {
  NSDictionary *info = [[NSBundle mainBundle] infoDictionary];
  NSString *name = [info objectForKey:@"CFBundleDisplayName"];
  return name;
}

- (NSData *)convertToData {
  return [self dataUsingEncoding:NSUTF8StringEncoding];
}

+ (NSString *)getStringFromData:(NSData *)data {
  return [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
}

- (BOOL)isValidEmail {
  NSString *regex = @"[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,4}";
  NSPredicate *emailTestPredicate =
      [NSPredicate predicateWithFormat:@"SELF MATCHES %@", regex];
  return [emailTestPredicate evaluateWithObject:self];
}

- (BOOL)isVAlidPhoneNumber {
  NSString *regex = @"1[3|4|5|7|8|][0-9]{9}";
  NSPredicate *test =
      [NSPredicate predicateWithFormat:@"SELF MATCHES %@", regex];
  return [test evaluateWithObject:self];
}

- (BOOL)isValidUrl {
  NSString *regex = @"(http|https)://((\\w)*|([0-9]*)|([-|_])*)+([\\.|/"
                    @"]((\\w)*|([0-9]*)|([-|_])*))+";
  NSPredicate *urlTest =
      [NSPredicate predicateWithFormat:@"SELF MATCHES %@", regex];
  return [urlTest evaluateWithObject:self];
}

- (BOOL)isTheStringDate {
  NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
  [dateFormatter setDateFormat:@"yyyy-MM-dd"];
  NSDate *dateFromString = [dateFormatter dateFromString:self];
  if (dateFromString != nil) {
    return YES;
  }
  [dateFormatter setDateFormat:@"yy年M月d日"];
  dateFromString = [dateFormatter dateFromString:self];
  return (dateFromString != nil);
}

@end
