#import "NSDate+VTB.h"

#define DATE_COMPONENTS                                                        \
  (NSYearCalendarUnit | NSMonthCalendarUnit | NSDayCalendarUnit |              \
   NSWeekCalendarUnit | NSHourCalendarUnit | NSMinuteCalendarUnit |            \
   NSSecondCalendarUnit | NSWeekdayCalendarUnit |                              \
   NSWeekdayOrdinalCalendarUnit)
#define CURRENT_CALENDAR [NSCalendar currentCalendar]

@implementation NSDate (VTB)

+ (NSDate *)dateFromString:(NSString *)string withFormat:(NSString *)format {
  NSDateFormatter *inputFormatter = [[NSDateFormatter alloc] init];
  [inputFormatter setDateFormat:format];
  NSDate *date = [inputFormatter dateFromString:string];
  return date;
}

+ (NSDate *)mondayOfWeek:(NSUInteger)year weekOfYear:(NSUInteger)weekOfYear {
  NSDateComponents *comps1 = [[NSDateComponents alloc] init];
  [comps1 setYear:year];
  [comps1 setWeekOfYear:weekOfYear];
  [comps1 setWeekday:2];

  NSDate *resultDate = [[NSCalendar currentCalendar] dateFromComponents:comps1];

  return resultDate;
}

- (NSString *)stringOfDateWithFormat:(NSString *)format {
  return [[NSDate defaultDateFormatterWithFormat:format] stringFromDate:self];
}

+ (NSDateFormatter *)defaultDateFormatterWithFormat:(NSString *)format {
  NSTimeZone *zone = [NSTimeZone timeZoneWithName:@"Asia/Shanghai"];

  NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
  [dateFormatter setTimeZone:zone];
  [dateFormatter setDateFormat:format];

  return dateFormatter;
}

#pragma mark Adjusting Dates
- (NSDate *)dateByAddingDays:(NSInteger)dDays {
  NSTimeInterval aTimeInterval =
      [self timeIntervalSinceReferenceDate] + D_DAY * dDays;
  NSDate *newDate =
      [NSDate dateWithTimeIntervalSinceReferenceDate:aTimeInterval];
  return newDate;
}

- (NSDate *)dateBySubtractingDays:(NSInteger)dDays {
  return [self dateByAddingDays:(dDays * -1)];
}

- (NSDate *)dateByAddingHours:(NSInteger)dHours {
  NSTimeInterval aTimeInterval =
      [self timeIntervalSinceReferenceDate] + D_HOUR * dHours;
  NSDate *newDate =
      [NSDate dateWithTimeIntervalSinceReferenceDate:aTimeInterval];
  return newDate;
}

- (NSDate *)dateBySubtractingHours:(NSInteger)dHours {
  return [self dateByAddingHours:(dHours * -1)];
}

- (NSDate *)dateByAddingMinutes:(NSInteger)dMinutes {
  NSTimeInterval aTimeInterval =
      [self timeIntervalSinceReferenceDate] + D_MINUTE * dMinutes;
  NSDate *newDate =
      [NSDate dateWithTimeIntervalSinceReferenceDate:aTimeInterval];
  return newDate;
}

- (NSDate *)dateBySubtractingMinutes:(NSInteger)dMinutes {
  return [self dateByAddingMinutes:(dMinutes * -1)];
}

#pragma mark Relative Dates

+ (NSDate *)dateWithDaysFromNow:(NSInteger)days {
  return [[NSDate localDate] dateByAddingDays:days];
}

+ (NSDate *)dateWithDaysBeforeNow:(NSInteger)days {
  return [[NSDate localDate] dateBySubtractingDays:days];
}

+ (NSDate *)dateTomorrow {
  return [NSDate dateWithDaysFromNow:1];
}

+ (NSDate *)dateYesterday {
  return [NSDate dateWithDaysBeforeNow:1];
}

+ (NSDate *)dateYesterdayYesterday

{
  return [NSDate dateWithDaysBeforeNow:2];
}

+ (NSDate *)dateWithHoursFromNow:(NSInteger)dHours {
  NSTimeInterval aTimeInterval =
      [[NSDate localDate] timeIntervalSinceReferenceDate] + D_HOUR * dHours;
  NSDate *newDate =
      [NSDate dateWithTimeIntervalSinceReferenceDate:aTimeInterval];
  return newDate;
}

+ (NSDate *)dateWithHoursBeforeNow:(NSInteger)dHours {
  NSTimeInterval aTimeInterval =
      [[NSDate localDate] timeIntervalSinceReferenceDate] - D_HOUR * dHours;
  NSDate *newDate =
      [NSDate dateWithTimeIntervalSinceReferenceDate:aTimeInterval];
  return newDate;
}

+ (NSDate *)dateWithMinutesFromNow:(NSInteger)dMinutes {
  NSTimeInterval aTimeInterval =
      [[NSDate localDate] timeIntervalSinceReferenceDate] + D_MINUTE * dMinutes;
  NSDate *newDate =
      [NSDate dateWithTimeIntervalSinceReferenceDate:aTimeInterval];
  return newDate;
}

+ (NSDate *)dateWithMinutesBeforeNow:(NSInteger)dMinutes {
  NSTimeInterval aTimeInterval =
      [[NSDate localDate] timeIntervalSinceReferenceDate] - D_MINUTE * dMinutes;
  NSDate *newDate =
      [NSDate dateWithTimeIntervalSinceReferenceDate:aTimeInterval];
  return newDate;
}

+ (NSString *)changedateForstr:(NSString *)str {
  NSString *tempdate = @"";
  switch ([str intValue]) {
  case 1:
    tempdate = @"一月";
    break;
  case 2:
    tempdate = @"二月";
    break;
  case 3:
    tempdate = @"三月";
    break;
  case 4:
    tempdate = @"四月";
    break;
  case 5:
    tempdate = @"五月";
    break;
  case 6:
    tempdate = @"六月";
    break;
  case 7:
    tempdate = @"七月";
    break;
  case 8:
    tempdate = @"八月";
    break;
  case 9:
    tempdate = @"九月";
    break;
  case 10:
    tempdate = @"十月";
    break;
  case 11:
    tempdate = @"十一月";
    break;
  case 12:
    tempdate = @"十二月";
    break;
  default:
    break;
  }

  return tempdate;
}

#pragma mark Comparing Dates

- (BOOL)isEarlierThanDate:(NSDate *)aDate {
  return ([self compare:aDate] == NSOrderedAscending);
}

- (BOOL)isLaterThanDate:(NSDate *)aDate {
  return ([self compare:aDate] == NSOrderedDescending);
}

- (BOOL)isInFuture {
  return ([self isLaterThanDate:[NSDate localDate]]);
}

- (BOOL)isInPast {
  return ([self isEarlierThanDate:[NSDate localDate]]);
}

+ (NSDate *)localNowDate {
  NSDate *date = [NSDate date];

  return [self getNowDateFromatAnDate:date];
}

+ (NSDate *)localDate {
  NSDate *date = [NSDate date];
  return date;
}

+ (NSDate *)getNowDateFromatAnDate:(NSDate *)anyDate {
  NSTimeZone *sourceTimeZone = [NSTimeZone timeZoneWithAbbreviation:@"UTC"];
  NSTimeZone *destinationTimeZone = [NSTimeZone localTimeZone];
  NSInteger sourceGMTOffset = [sourceTimeZone secondsFromGMTForDate:anyDate];
  NSInteger destinationGMTOffset =
      [destinationTimeZone secondsFromGMTForDate:anyDate];
  NSTimeInterval interval = destinationGMTOffset - sourceGMTOffset;
  NSDate *destinationDateNow = [[NSDate alloc] initWithTimeInterval:interval
                                                          sinceDate:anyDate];
  return destinationDateNow;
}

@end
