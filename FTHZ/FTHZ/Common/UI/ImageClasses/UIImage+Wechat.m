#import "UIImage+Wechat.h"

@implementation UIImage (Wechat)

- (NSData *)wcSessionCompress {
  return [self wcCompres:YES];
}

- (NSData *)wcTimelineCompress {
  return [self wcCompres:NO];
}

- (NSData *)wcCompres:(Boolean)isSession {
  CGSize size = [self wxImageSize:isSession];
  UIImage *reImage = [self resizedImage:size];
  NSData *data = UIImageJPEGRepresentation(reImage, 0.5);
  return data;
}

- (CGSize)wxImageSize:(Boolean)isSession {
  CGFloat width = self.size.width;
  CGFloat height = self.size.height;
  CGFloat boundary = 1600;

  if (width < boundary && height < boundary) {
    return CGSizeMake(width, height);
  }

  CGFloat ratio = MAX(width, height) / MIN(width, height);
  if (ratio <= 2) {
    CGFloat x = MAX(width, height) / boundary;
    if (width > height) {
      width = boundary;
      height = height / x;
    } else {
      height = boundary;
      width = width / x;
    }
  } else {
    if (MIN(width, height) >= boundary) {
      boundary = isSession ? 1280 : 1600;
      CGFloat x = MIN(width, height) / boundary;
      if (width < height) {
        width = boundary;
        height = height / x;
      } else {
        height = boundary;
        width = width / x;
      }
    }
  }

  return CGSizeMake(width, height);
}

- (UIImage *)resizedImage:(CGSize)newSize {
  CGRect newRect = CGRectMake(0, 0, newSize.width, newSize.height);
  if (newSize.width <= 0 || newSize.height <= 0) {
    return self;
  }
  UIGraphicsBeginImageContext(newRect.size);
  UIImage *newImage = [[UIImage alloc] initWithCGImage:self.CGImage
                                                 scale:1
                                           orientation:self.imageOrientation];
  [newImage drawInRect:newRect];
  newImage = UIGraphicsGetImageFromCurrentImageContext();
  UIGraphicsEndImageContext();
  return newImage;
}

@end
