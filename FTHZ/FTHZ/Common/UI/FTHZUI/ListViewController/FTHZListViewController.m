#import "FTHZListViewController.h"
#import "FTHZRefreshFooter.h"
#import "FTHZRefreshHeader.h"
#import "UIView+Frame.h"

#pragma mark FTHZListViewModel

@implementation FTHZListOperation

+ (instancetype)operationWithType:(FTHZListOperationType)type
                       indexPaths:(NSArray<NSIndexPath *> *_Nullable)indexPaths
                        animation:(UITableViewRowAnimation)animation {
  FTHZListOperation *ope = [[FTHZListOperation alloc] init];
  ope.type = type;
  ope.indexPaths = indexPaths;
  ope.animation = animation;
  return ope;
}

+ (instancetype)operationWithType:(FTHZListOperationType)type
                         sections:(NSIndexSet *_Nullable)sections
                        animation:(UITableViewRowAnimation)animation {
  FTHZListOperation *ope = [[FTHZListOperation alloc] init];
  ope.type = type;
  ope.sections = sections;
  ope.animation = animation;
  return ope;
}

@end

@interface FTHZListViewModel () {
  RACBehaviorSubject *_refreshStyle;
  RACBehaviorSubject *_loadStyle;
}

@property(nonatomic, strong) NSMutableArray<FTHZListSectionModel *> *sections;

@property(nonatomic, strong)
    RACSubject<FTHZListOperation *> *listOperationSignal;

@end

@implementation FTHZListViewModel

@dynamic refreshStyle;
@dynamic loadStyle;

- (void)viewDidLoad {
}

- (void)viewWillAppear:(BOOL)animated {
}

- (void)viewDidAppear:(BOOL)animated {
}

- (void)viewWillDisappear:(BOOL)animated {
}

- (void)viewDidDisappear:(BOOL)animated {
}

- (instancetype)init {
  self = [super init];
  if (self) {
    _sections = [[NSMutableArray alloc] init];
    _refreshStyle = [RACBehaviorSubject
        behaviorSubjectWithDefaultValue:@(FTHZRefreshControlStyleDisable)];
    _loadStyle = [RACBehaviorSubject
        behaviorSubjectWithDefaultValue:@(FTHZRefreshControlStyleDisable)];
    _listOperationSignal = [RACSubject subject];
    _loadMoreAction = nil;
    _refreshAction = nil;
  }
  return self;
}

- (RACSignal *)refreshStyle {
  return _refreshStyle;
}

- (RACSignal *)loadStyle {
  return _loadStyle;
}

- (void)triggerRefreshBegin {
  [self changeRefreshStyle:FTHZRefreshControlStyleRefreshing];
}

- (void)changeRefreshStyle:(FTHZRefreshControlStyle)style {
  [_refreshStyle sendNext:@(style)];
}

- (void)changeLoadStyle:(FTHZRefreshControlStyle)style {
  [_loadStyle sendNext:@(style)];
}

- (NSMutableArray *)kvoSection {
  return
      [self mutableArrayValueForKey:NSStringFromSelector(@selector(sections))];
}

- (void)reloadRows:(NSArray<FTHZListRowModel *> *_Nullable)rows {
  FTHZListSectionModel *aSection = [[FTHZListSectionModel alloc] init];
  aSection.rows = rows;
  [self reloadSections:@[ aSection ]];
}

- (void)reloadSections:(NSArray<FTHZListSectionModel *> *_Nullable)sections {
  [[self kvoSection] setArray:sections ?: [[NSArray alloc] init]];
  [self.listOperationSignal
      sendNext:[FTHZListOperation
                   operationWithType:FTHZListOperationTypeReload
                            sections:nil
                           animation:UITableViewRowAnimationNone]];
}

- (void)appendRows:(NSArray<FTHZListRowModel *> *_Nullable)rows {
  FTHZListSectionModel *aSection =
      [self kvoSection].firstObject ?: [[FTHZListSectionModel alloc] init];
  NSMutableArray *allRows =
      [aSection.rows mutableCopy] ?: [[NSMutableArray alloc] init];
  [allRows addObjectsFromArray:rows];
  aSection.rows = [allRows copy];
  [self reloadSections:@[ aSection ]];
}

- (void)appendSections:(NSArray<FTHZListSectionModel *> *_Nullable)sections {
  [[self kvoSection]
      addObjectsFromArray:sections ?: [[NSMutableArray alloc] init]];
  [self.listOperationSignal
      sendNext:[FTHZListOperation
                   operationWithType:FTHZListOperationTypeReload
                            sections:nil
                           animation:UITableViewRowAnimationNone]];
}

@end

#pragma mark FTHZListViewController

@interface FTHZListViewController ()
@end

@implementation FTHZListViewController

+ (FTHZTableView *)loadTableView {
  return [[FTHZTableView alloc] initWithFrame:CGRectZero
                                        style:UITableViewStyleGrouped];
}

- (instancetype)initWithViewModel:(FTHZListViewModel *)viewModel {
  self = [super initWithNibName:nil bundle:nil];
  if (self) {
    _viewModel = viewModel;
    _listView = [[self class] loadTableView];
  }
  return self;
}

- (void)viewDidLoad {
  [super viewDidLoad];
  [self.view addSubview:self.listView];
  self.listView.delegate = self;
  self.listView.dataSource = self;
  [self initPipeline];
  [self.viewModel viewDidLoad];
}

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];
  [self.viewModel viewWillAppear:animated];
}

- (void)viewDidAppear:(BOOL)animated {
  [super viewDidAppear:animated];
  [self.viewModel viewDidAppear:animated];
}

- (void)viewWillDisappear:(BOOL)animated {
  [super viewWillDisappear:animated];
  [self.viewModel viewWillDisappear:animated];
}

- (void)viewDidDisappear:(BOOL)animated {
  [super viewDidDisappear:animated];
  [self.viewModel viewDidDisappear:animated];
}

- (void)viewDidLayoutSubviews {
  [super viewDidLayoutSubviews];
  UIEdgeInsets insets = self.safeAreaInsets;
  self.listView.frame = CGRectMake(
      CGRectGetMinX(self.view.bounds) + insets.left,
      CGRectGetMinY(self.view.bounds) + insets.top,
      CGRectGetWidth(self.view.bounds) - insets.left - insets.right,
      CGRectGetHeight(self.view.bounds) - insets.top - insets.bottom);
}

#pragma mark Biz

- (void)initPipeline {

  @weakify(self);
  [[self.viewModel.refreshStyle takeUntil:self.rac_willDeallocSignal]
      subscribeNext:^(id _Nullable x) {
        @strongify(self);
        NSCAssert([x isKindOfClass:[NSNumber class]], @"value is not number?");
        FTHZRefreshControlStyle style = [x integerValue];
        switch (style) {
        case FTHZRefreshControlStyleDisable: {
          if ([self.listView.mj_header isRefreshing]) {
            [self.listView.mj_header endRefreshing];
          }
          self.listView.mj_header = nil;
        } break;

        case FTHZRefreshControlStyleEnable: {
          if (!self.listView.mj_header) {
            self.listView.mj_header = [FTHZRefreshHeader
                headerWithRefreshingTarget:self
                          refreshingAction:@selector(doRefresh)];
          }
          if ([self.listView.mj_header isRefreshing]) {
            [self.listView.mj_header endRefreshing];
          }
        } break;

        case FTHZRefreshControlStyleRefreshing: {
          if (![self.listView.mj_header isRefreshing]) {
            [self.listView.mj_header beginRefreshing];
          }
        } break;

        case FTHZRefreshControlStyleNoMoreData:
          NSCAssert(NO, @"Refresh header does not support this style");
          break;
        }
      }];

  [[self.viewModel.loadStyle takeUntil:self.rac_willDeallocSignal]
      subscribeNext:^(id _Nullable x) {
        @strongify(self);
        NSCAssert([x isKindOfClass:[NSNumber class]], @"value is not number?");
        FTHZRefreshControlStyle style = [x integerValue];
        switch (style) {
        case FTHZRefreshControlStyleDisable: {
          if ([self.listView.mj_footer isRefreshing]) {
            [self.listView.mj_footer endRefreshing];
          }
          self.listView.mj_footer = nil;
        } break;

        case FTHZRefreshControlStyleEnable: {
          if (!self.listView.mj_footer) {
            self.listView.mj_footer = [FTHZRefreshFooter
                footerWithRefreshingTarget:self
                          refreshingAction:@selector(doLoadMore)];
          }
          if ([self.listView.mj_footer isRefreshing]) {
            [self.listView.mj_footer endRefreshing];
          }
          if (self.listView.mj_footer.state == MJRefreshStateNoMoreData) {
            [self.listView.mj_footer resetNoMoreData];
          }
        } break;

        case FTHZRefreshControlStyleRefreshing: {
          if (![self.listView.mj_footer isRefreshing]) {
            [self.listView.mj_footer beginRefreshing];
          }
        } break;

        case FTHZRefreshControlStyleNoMoreData: {
          if (!self.listView.mj_footer) {
            self.listView.mj_footer = [FTHZRefreshFooter
                footerWithRefreshingTarget:self
                          refreshingAction:@selector(doLoadMore)];
          }
          [self.listView.mj_footer endRefreshingWithNoMoreData];
        } break;
        }
      }];

  [[[self.viewModel listOperationSignal] takeUntil:self.rac_willDeallocSignal]
      subscribeNext:^(FTHZListOperation *_Nullable x) {
        @strongify(self);
        NSAssert([x isKindOfClass:[FTHZListOperation class]],
                 @"x should be FTHZListOperation");
        switch (x.type) {
        case FTHZListOperationTypeReload: {
          if (x.indexPaths) {
            [self.listView reloadRowsAtIndexPaths:x.indexPaths
                                 withRowAnimation:x.animation];
          } else if (x.sections) {
            [self.listView reloadSections:x.sections
                         withRowAnimation:x.animation];
          } else {
            [self.listView reloadData];
          }
        } break;
        }
      }];
}

#pragma mark Refresh Action

- (void)doRefresh {
  [self.viewModel.refreshAction execute:self];
}

- (void)doLoadMore {
  [self.viewModel.loadMoreAction execute:self];
}

#pragma mark TableView Delegate & Datasource

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
  return self.viewModel.sections.count;
}

- (NSInteger)tableView:(UITableView *)tableView
    numberOfRowsInSection:(NSInteger)section {
  return self.viewModel.sections[section].rows.count;
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForRowAtIndexPath:(NSIndexPath *)indexPath {
  FTHZListRowModel *model =
      self.viewModel.sections[indexPath.section].rows[indexPath.row];
  return model.height;
}

- (UITableViewCell *)tableView:(UITableView *)tableView
         cellForRowAtIndexPath:(NSIndexPath *)indexPath {
  FTHZListRowModel *model =
      self.viewModel.sections[indexPath.section].rows[indexPath.row];
  NSString *idn = NSStringFromClass(model.viewClass);
  FTHZListCell *cell = [tableView dequeueReusableCellWithIdentifier:idn];
  if (!cell) {
    cell = [[model.viewClass alloc] initWithStyle:UITableViewCellStyleDefault
                                  reuseIdentifier:idn];
  }
  NSAssert(!cell || [cell isKindOfClass:[FTHZListCell class]],
           @"Cell should be subclass of FTHZListCell");
  NSAssert(tableView == self.listView,
           @"ListViewController does not support multi tableview");
  [cell bindWithModel:model
           atIndexPath:indexPath
            inListView:(FTHZTableView *)tableView
      ofViewController:self];
  return cell;
}

- (UIView *)tableView:(UITableView *)tableView
    viewForHeaderFooterInSection:(NSInteger)section
                        isHeader:(BOOL)isHeader {
  FTHZListSectionModel *aSection = self.viewModel.sections[section];
  FTHZListHeaderFooterViewModel *model =
      isHeader ? aSection.header : aSection.footer;
  NSString *idn = NSStringFromClass(model.class);
  FTHZListHeaderFooterView *view =
      [tableView dequeueReusableHeaderFooterViewWithIdentifier:idn];
  if (!view) {
    view = [[model.viewClass alloc] initWithReuseIdentifier:idn];
  }
  NSAssert([view isKindOfClass:[FTHZListHeaderFooterView class]],
           @"HeaderFooterView should be subclass of FTHZListHeaderFooterView");
  NSAssert(tableView == self.listView,
           @"ListViewController does not support multi tableview");
  [view bindWithModel:model
             atSection:section
            inListView:(FTHZTableView *)tableView
      ofViewController:self];
  return view;
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForHeaderFooterInSection:(NSInteger)section
                          isHeader:(BOOL)isHeader {
  FTHZListSectionModel *aSection = self.viewModel.sections[section];
  FTHZListHeaderFooterViewModel *model =
      isHeader ? aSection.header : aSection.footer;
  return model ? model.height : CGFLOAT_MIN;
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForHeaderInSection:(NSInteger)section {
  return [self tableView:tableView
      heightForHeaderFooterInSection:section
                            isHeader:YES];
}

- (UIView *_Nullable)tableView:(UITableView *)tableView
        viewForHeaderInSection:(NSInteger)section {
  return [self tableView:tableView
      viewForHeaderFooterInSection:section
                          isHeader:YES];
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForFooterInSection:(NSInteger)section {
  return [self tableView:tableView
      heightForHeaderFooterInSection:section
                            isHeader:NO];
}

- (UIView *_Nullable)tableView:(UITableView *)tableView
        viewForFooterInSection:(NSInteger)section {
  return [self tableView:tableView
      viewForHeaderFooterInSection:section
                          isHeader:NO];
}

- (void)tableView:(UITableView *)tableView
    didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
  FTHZListRowModel *model =
      self.viewModel.sections[indexPath.section].rows[indexPath.row];
  [model didSelectedAtIndexPath:indexPath];
}

@end
