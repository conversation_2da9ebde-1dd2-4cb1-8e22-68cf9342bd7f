#import "FTHZListViewElement.h"
#import "FTHZTableView.h"
#import "FTHZViewController.h"

NS_ASSUME_NONNULL_BEGIN

@protocol FTHZViewControllerEventHandler <NSObject>

@optional
- (void)viewDidLoad;
- (void)viewWillAppear:(BOOL)animated;
- (void)viewDidAppear:(BOOL)animated;
- (void)viewWillDisappear:(BOOL)animated;
- (void)viewDidDisappear:(BOOL)animated;

@end

typedef NS_ENUM(NSInteger, FTHZRefreshControlStyle) {
  FTHZRefreshControlStyleDisable = 0,

  FTHZRefreshControlStyleEnable = 1,

  FTHZRefreshControlStyleRefreshing = 2,

  FTHZRefreshControlStyleNoMoreData = 3,
};

typedef NS_ENUM(NSInteger, FTHZListOperationType) {
  FTHZListOperationTypeReload = 0,
};

@interface FTHZListOperation : NSObject

@property(nonatomic, assign) FTHZListOperationType type;

@property(nonatomic, copy, nullable) NSArray<NSIndexPath *> *indexPaths;

@property(nonatomic, copy, nullable) NSIndexSet *sections;

@property(nonatomic, assign) UITableViewRowAnimation animation;

+ (instancetype)operationWithType:(FTHZListOperationType)type
                       indexPaths:(NSArray<NSIndexPath *> *_Nullable)indexPaths
                        animation:(UITableViewRowAnimation)animation;

+ (instancetype)operationWithType:(FTHZListOperationType)type
                         sections:(NSIndexSet *_Nullable)sections
                        animation:(UITableViewRowAnimation)animation;

@end

@class FTHZListViewController;
@interface FTHZListViewModel : NSObject <FTHZViewControllerEventHandler>

@property(nonatomic, strong, readonly) RACSignal<NSNumber *> *refreshStyle;

@property(nonatomic, strong, nullable)
    RACCommand<FTHZListViewController *, id> *refreshAction;

@property(nonatomic, strong, readonly) RACSignal<NSNumber *> *loadStyle;

@property(nonatomic, strong, nullable)
    RACCommand<FTHZListViewController *, id> *loadMoreAction;

- (void)triggerRefreshBegin;

- (void)changeRefreshStyle:(FTHZRefreshControlStyle)style;
- (void)changeLoadStyle:(FTHZRefreshControlStyle)style;

- (void)reloadSections:(NSArray<FTHZListSectionModel *> *_Nullable)sections;
- (void)reloadRows:(NSArray<FTHZListRowModel *> *_Nullable)rows;

- (void)appendRows:(NSArray<FTHZListRowModel *> *_Nullable)rows;
- (void)appendSections:(NSArray<FTHZListSectionModel *> *_Nullable)sections;

@end

@interface FTHZListViewController
    : FTHZViewController <UITableViewDelegate, UITableViewDataSource>

@property(nonatomic, strong, readonly) FTHZTableView *listView;

@property(nonatomic, strong, readonly) FTHZListViewModel *viewModel;

+ (FTHZTableView *)loadTableView;

- (instancetype)initWithViewModel:(FTHZListViewModel *)viewModel;

- (void)initPipeline;

@end

NS_ASSUME_NONNULL_END
