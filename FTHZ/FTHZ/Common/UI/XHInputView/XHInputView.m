#import "XHInputView.h"

#define XHInputView_ScreenW [UIScreen mainScreen].bounds.size.width
#define XHInputView_ScreenH [UIScreen mainScreen].bounds.size.height
#define XHInputView_StyleLarge_LRSpace 10
#define XHInputView_StyleLarge_TBSpace 8
#define XHInputView_StyleDefault_LRSpace 5
#define XHInputView_StyleDefault_TBSpace 5
#define XHInputView_CountLabHeight 20
// #define XHInputView_BgViewColor                                                \
//   [UIColor colorWithRed:0 green:0 blue:0 alpha:0.3]
#define XHInputView_BgViewColor [UIColor clearColor]
#define XHInputView_StyleLarge_Height 170
#define XHInputView_StyleDefault_Height 45 * kWidthFactor

static CGFloat keyboardAnimationDuration = 0.5;

@interface XHInputView () <UITextViewDelegate, UIGestureRecognizerDelegate> {
  BOOL selimBtn;
  BOOL uptype;
  BOOL hiddenofView;
}

@property(nonatomic, strong) UITextView *textView;
@property(nonatomic, strong) UIView *textBgView;
@property(nonatomic, strong) UIView *inputView;
@property(nonatomic, strong) UILabel *countLab;
@property(nonatomic, strong) UILabel *placeholderLab;
@property(nonatomic, strong) UIButton *sendButton;
@property(nonatomic, assign) InputViewStyle style;

@property(nonatomic, assign) CGRect showFrameDefault;
@property(nonatomic, assign) CGRect sendButtonFrameDefault;
@property(nonatomic, assign) CGRect textViewFrameDefault;
@property(nonatomic, assign) CGRect inputFrameDefault;

@property(nonatomic, strong) UIButton *
    rebugBtn; // 这个button关系很微妙,主要是用来处理bug的,响应下点击事件,但不处理

/** 发送按钮点击回调 */
@property(nonatomic, copy) BOOL (^sendBlcok)(NSString *text, BOOL selTemp);

@end

@implementation XHInputView

- (void)dealloc {
  if (_style == InputViewStyleDefault ||
      _style == InputViewStyleNoSendDefault) {
    [_textView removeObserver:self forKeyPath:@"contentSize"];
  }
}
+ (void)showWithStyle:(InputViewStyle)style
    configurationBlock:(void (^)(XHInputView *inputView))configurationBlock
             sendBlock:(BOOL (^)(NSString *text, BOOL selTemp))sendBlock {
  XHInputView *inputView = [[XHInputView alloc] initWithStyle:style];
  UIWindow *window = [UIApplication sharedApplication].delegate.window;
  [window addSubview:inputView];
  if (configurationBlock)
    configurationBlock(inputView);
  inputView.sendBlcok = [sendBlock copy];
  [inputView show];
}
#pragma mark - private
- (void)show {
  if ([self.delegate respondsToSelector:@selector(xhInputViewWillShow:)]) {
    [self.delegate xhInputViewWillShow:self];
  }
  _textView.text = nil;
  _placeholderLab.hidden = NO;

  // 直接设置为透明，不使用动画
  self.backgroundColor = [UIColor clearColor];
  self.alpha = 1;
  if (_style == InputViewStyleLarge) {
    if (_maxCount > 0)
      _countLab.text = [NSString stringWithFormat:@"0/%ld", (long)_maxCount];
  }
  hiddenofView = NO;
  // [_textView becomeFirstResponder];
  // 延迟一帧再弹出键盘，避免动画冲突
  dispatch_async(dispatch_get_main_queue(), ^{
    [self->_textView becomeFirstResponder];
  });
  if ([[USERDEFAULT objectForKey:ContenText] isValid]) {
    NSString *contentText = [USERDEFAULT objectForKey:ContenText];
    _textView.text = contentText;
    if (_textView.text.length > 0) {
      _placeholderLab.hidden = YES;
    } else {
      _placeholderLab.hidden = NO;
    }
  }
}

- (void)hide {
  if (self.delegate &&
      [self.delegate respondsToSelector:@selector(xhInputViewWillHide:)]) {
    [self.delegate xhInputViewWillHide:self];
  }
  hiddenofView = YES;
  [_textView resignFirstResponder];
}

- (instancetype)initWithStyle:(InputViewStyle)style {
  self = [super init];
  if (self) {

    _style = style;
    /** 创建UI */
    [self setupUI];
    /** 键盘监听 */
    [[NSNotificationCenter defaultCenter]
        addObserver:self
           selector:@selector(keyboardWillAppear:)
               name:UIKeyboardWillShowNotification
             object:nil];
    [[NSNotificationCenter defaultCenter]
        addObserver:self
           selector:@selector(keyboardWillDisappear:)
               name:UIKeyboardWillHideNotification
             object:nil];
  }
  return self;
}

- (void)setupUI {
  // 基础UI设置
  self.backgroundColor = [UIColor clearColor];
  self.alpha = 0;
  self.frame = [UIScreen mainScreen].bounds;

  // 添加手势
  UITapGestureRecognizer *tap =
      [[UITapGestureRecognizer alloc] initWithTarget:self
                                              action:@selector(bgViewClick)];
  tap.delegate = self;
  [self addGestureRecognizer:tap];

  // 输入框容器视图
  _inputView = [[UIView alloc] init];
  _inputView.userInteractionEnabled = YES;
  [self addSubview:_inputView];

  switch (_style) {
  case InputViewStyleDefault:
  case InputViewStyleNoSendDefault: {
    // 设置输入框容器
    _inputView.frame = CGRectMake(0, XHInputView_ScreenH, XHInputView_ScreenW,
                                  XHInputView_StyleDefault_Height);
    _inputView.backgroundColor = KColor_DarkKeyboard;

    selimBtn = NO;

    // 设置发送按钮
    [self setupDefaultSendButton];

    // 设置文本输入框
    [self setupDefaultTextView];

    // 保存默认frame
    _sendButtonFrameDefault = _sendButton.frame;
    _textViewFrameDefault = _textView.frame;
    _showFrameDefault = _inputView.frame;
  } break;

  case InputViewStyleLarge: {
    [self setupLargeStyle];
  } break;

  default:
    break;
  }
}

- (void)setupDefaultSendButton {
  CGFloat sendButtonWidth = 45 * kWidthFactor;
  CGFloat sendButtonHeight =
      _inputView.bounds.size.height - 2 * XHInputView_StyleDefault_TBSpace;

  _sendButton = [UIButton buttonWithType:UIButtonTypeCustom];
  _sendButton.frame = CGRectMake(
      XHInputView_ScreenW - XHInputView_StyleDefault_LRSpace - sendButtonWidth,
      XHInputView_StyleDefault_TBSpace, sendButtonWidth, sendButtonHeight);

  // 设置图片
  UIImage *sendImage = [UIImage imageNamed:@"fasong"];
  [_sendButton setImage:sendImage forState:UIControlStateNormal];
  _sendButton.imageView.contentMode = UIViewContentModeScaleAspectFit;

  // 保持背景色和圆角
  [_sendButton setBackgroundColor:KColor_HighBlack];
  _sendButton.layer.cornerRadius = 4.0;
  _sendButton.layer.masksToBounds = YES;

  [_sendButton addTarget:self
                  action:@selector(sendButtonClick:)
        forControlEvents:UIControlEventTouchUpInside];
  [_inputView addSubview:_sendButton];
}

- (void)setupDefaultTextView {
  _textView = [[UITextView alloc]
      initWithFrame:CGRectMake(XHInputView_StyleDefault_LRSpace,
                               XHInputView_StyleDefault_TBSpace,
                               XHInputView_ScreenW -
                                   3 * XHInputView_StyleDefault_LRSpace -
                                   _sendButton.frame.size.width,
                               self.inputView.bounds.size.height -
                                   2 * XHInputView_StyleDefault_TBSpace)];

  // 统一文本框样式
  _textView.font = SourceHanSerifMediumFont(14 * kWidthFactor);
  _textView.backgroundColor = [UIColor whiteColor];
  _textView.layer.cornerRadius = 4.0;
  _textView.layer.borderWidth = 0.5;
  _textView.layer.borderColor = [UIColor lightGrayColor].CGColor;
  _textView.keyboardAppearance = UIKeyboardAppearanceDark;
  _textView.delegate = self;

  [_inputView addSubview:_textView];
  [_textView addObserver:self
              forKeyPath:@"contentSize"
                 options:NSKeyValueObservingOptionNew
                 context:NULL];

  // 设置占位符
  [self setupPlaceholder];
}

- (void)setupPlaceholder {
  _placeholderLab = [[UILabel alloc]
      initWithFrame:CGRectMake(7, 0, _textView.bounds.size.width - 14,
                               _textView.bounds.size.height)];

  _placeholderLab.font = _textView.font;
  _placeholderLab.text = @"请输入...";
  _placeholderLab.textColor = [UIColor colorWithRed:0.7
                                              green:0.7
                                               blue:0.7
                                              alpha:1.0];
  [_textView addSubview:_placeholderLab];
}

- (void)setupLargeStyle {
  // 设置输入框容器
  _inputView.frame = CGRectMake(0, XHInputView_ScreenH, XHInputView_ScreenW,
                                XHInputView_StyleLarge_Height);
  _inputView.backgroundColor = [UIColor whiteColor];

  // 设置文本背景视图
  _textBgView = [[UIView alloc]
      initWithFrame:CGRectMake(XHInputView_StyleLarge_LRSpace,
                               XHInputView_StyleLarge_TBSpace,
                               XHInputView_ScreenW -
                                   2 * XHInputView_StyleLarge_LRSpace,
                               XHInputView_StyleLarge_Height -
                                   2 * XHInputView_StyleLarge_TBSpace)];
  _textBgView.backgroundColor = [UIColor whiteColor];
  _textBgView.layer.cornerRadius = 4.0;
  _textBgView.layer.borderWidth = 0.5;
  _textBgView.layer.borderColor = [UIColor lightGrayColor].CGColor;
  [_inputView addSubview:_textBgView];

  // 设置文本输入框
  _textView = [[UITextView alloc]
      initWithFrame:CGRectMake(10, 10, _textBgView.bounds.size.width - 20,
                               _textBgView.bounds.size.height -
                                   XHInputView_CountLabHeight - 20)];
  _textView.font = SourceHanSerifMediumFont(14 * kWidthFactor);
  _textView.backgroundColor = [UIColor clearColor];
  _textView.delegate = self;
  [_textBgView addSubview:_textView];

  // 设置字数统计标签
  _countLab = [[UILabel alloc]
      initWithFrame:CGRectMake(_textBgView.bounds.size.width - 60,
                               _textBgView.bounds.size.height -
                                   XHInputView_CountLabHeight,
                               50, XHInputView_CountLabHeight)];
  _countLab.font = SourceHanSerifMediumFont(12 * kWidthFactor);
  _countLab.textAlignment = NSTextAlignmentRight;
  _countLab.textColor = [UIColor colorWithRed:0.7 green:0.7 blue:0.7 alpha:1.0];
  [_textBgView addSubview:_countLab];

  // 设置占位符
  _placeholderLab = [[UILabel alloc]
      initWithFrame:CGRectMake(5, 8, _textView.bounds.size.width - 10, 20)];
  _placeholderLab.font = _textView.font;
  _placeholderLab.text = @"请输入...";
  _placeholderLab.textColor = [UIColor colorWithRed:0.7
                                              green:0.7
                                               blue:0.7
                                              alpha:1.0];
  [_textView addSubview:_placeholderLab];

  // 设置发送按钮
  CGFloat sendButtonWidth = 60 * kWidthFactor;
  CGFloat sendButtonHeight = 35 * kWidthFactor;
  _sendButton = [UIButton buttonWithType:UIButtonTypeCustom];
  _sendButton.frame = CGRectMake(
      XHInputView_ScreenW - XHInputView_StyleLarge_LRSpace - sendButtonWidth,
      XHInputView_StyleLarge_Height - XHInputView_StyleLarge_TBSpace -
          sendButtonHeight,
      sendButtonWidth, sendButtonHeight);

  // 统一按钮样式
  [_sendButton setTitle:@"发送" forState:UIControlStateNormal];
  [_sendButton setTitleColor:KColor_White forState:UIControlStateNormal];
  [_sendButton setBackgroundColor:KColor_HighBlack];
  _sendButton.titleLabel.font = SourceHanSerifMediumFont(15 * kWidthFactor);
  _sendButton.layer.cornerRadius = 4.0;
  _sendButton.layer.masksToBounds = YES;

  [_sendButton addTarget:self
                  action:@selector(sendButtonClick:)
        forControlEvents:UIControlEventTouchUpInside];
  [_inputView addSubview:_sendButton];
}

#pragma mark - KVO
- (void)observeValueForKeyPath:(NSString *)keyPath
                      ofObject:(id)object
                        change:(NSDictionary *)change
                       context:(void *)context {
  if (object == _textView && [keyPath isEqualToString:@"contentSize"]) {
    CGFloat height = _textView.contentSize.height;
    CGFloat heightDefault = XHInputView_StyleDefault_Height;
    if (height >= heightDefault) {
      [UIView
          animateWithDuration:0.3
                   animations:^{
                     // 调整frame
                     CGRect frame = self.showFrameDefault;
                     frame.size.height = height;
                     frame.origin.y =
                         self.showFrameDefault.origin.y -
                         (height - self.showFrameDefault.size.height);
                     self.inputView.frame = frame;
                     // 调整sendButton frame
                     self.sendButton.frame =
                         CGRectMake(XHInputView_ScreenW -
                                        XHInputView_StyleDefault_LRSpace -
                                        self.sendButton.frame.size.width,
                                    self.inputView.bounds.size.height -
                                        self.sendButton.bounds.size.height -
                                        XHInputView_StyleDefault_TBSpace,
                                    self.sendButton.bounds.size.width,
                                    self.sendButton.bounds.size.height);
                     // 调整textView frame
                     self.textView.frame =
                         CGRectMake(XHInputView_StyleDefault_LRSpace,
                                    XHInputView_StyleDefault_TBSpace,
                                    self.textView.bounds.size.width,
                                    self.inputView.bounds.size.height -
                                        2 * XHInputView_StyleDefault_TBSpace);

                     self.rebugBtn.frame =
                         CGRectMake(0, 20, kMainWidth,
                                    self.inputView.bounds.size.height -
                                        XHInputView_StyleDefault_TBSpace + 10);
                   }];

    } else {
      [UIView
          animateWithDuration:0.3
                   animations:^{
                     [self resetFrameDefault]; // 恢复到,键盘弹出时,视图初始位置
                   }];
    }
  } else {
    [super observeValueForKeyPath:keyPath
                         ofObject:object
                           change:change
                          context:context];
  }
}

#pragma mark - UIGestureRecognizerDelegate
- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer
       shouldReceiveTouch:(UITouch *)touch {
  if ([touch.view isDescendantOfView:_inputView]) {
    return NO;
  }
  return YES;
}
- (void)resetFrameDefault {
  self.inputView.frame = _showFrameDefault;
  self.sendButton.frame = _sendButtonFrameDefault;
  self.textView.frame = _textViewFrameDefault;
}

- (void)textViewDidChange:(UITextView *)textView {
  NSString *contentText = textView.text;
  [USERDEFAULT setObject:contentText forKey:ContenText];
  [USERDEFAULT synchronize];
  if (textView.text.length) {
    _placeholderLab.hidden = YES;
  } else {
    _placeholderLab.hidden = NO;
  }
  if (_maxCount > 0) {
    if (textView.text.length >= _maxCount) {
      textView.text = [textView.text substringToIndex:_maxCount];
    }
    if (_style == InputViewStyleLarge) {
      _countLab.text =
          [NSString stringWithFormat:@"%ld/%ld", (long)textView.text.length,
                                     (long)_maxCount];
    }
  }
}

#pragma mark - Action
- (void)bgViewClick {
  self.alpha = 0; // 直接设置透明，不使用动画
  [self hide];
}

- (void)sendButtonClick:(UIButton *)button {
  if (self.sendBlcok) {
    //        BOOL hideKeyBoard = self.sendBlcok(self.textView.text,selimBtn);
    BOOL hideKeyBoard = self.sendBlcok(self.textView.text, NO);
    if (hideKeyBoard) {
      [self hide];
    }
  }
}
#pragma mark - 监听键盘
- (void)keyboardWillAppear:(NSNotification *)noti {
  if (_textView.isFirstResponder) {
    NSDictionary *info = [noti userInfo];
    NSValue *value = [info objectForKey:UIKeyboardFrameEndUserInfoKey];
    keyboardAnimationDuration =
        [[info objectForKey:UIKeyboardAnimationDurationUserInfoKey] floatValue];
    CGSize keyboardSize = [value CGRectValue].size;

    [UIView animateWithDuration:keyboardAnimationDuration
                     animations:^{
                       CGRect frame = self.inputView.frame;
                       //            CGRect frame = self.inputFrameDefault;
                       frame.origin.y = XHInputView_ScreenH -
                                        keyboardSize.height - frame.size.height;
                       self.inputView.frame = frame;
                       self.backgroundColor = XHInputView_BgViewColor;
                       self.showFrameDefault = self.inputView.frame;
                     }];
  }
}
- (void)keyboardWillDisappear:(NSNotification *)noti {
  if (_textView.isFirstResponder && hiddenofView) {
    [UIView animateWithDuration:keyboardAnimationDuration
        animations:^{
          CGRect frame = self.inputView.frame;
          frame.origin.y = XHInputView_ScreenH;
          self.inputView.frame = frame;
          self.alpha = 0;
          // self.backgroundColor = [UIColor clearColor];
        }
        completion:^(BOOL finished) {
          [self removeFromSuperview];
        }];
  }
}

#pragma mark - set
- (void)setMaxCount:(NSInteger)maxCount {
  _maxCount = maxCount;
  if (_style == InputViewStyleLarge) {
    _countLab.text = [NSString stringWithFormat:@"0/%ld", (long)maxCount];
  }
}
- (void)setTextViewBackgroundColor:(UIColor *)textViewBackgroundColor {
  _textViewBackgroundColor = textViewBackgroundColor;
  _textBgView.backgroundColor = textViewBackgroundColor;
}
- (void)setFont:(UIFont *)font {
  _font = font;
  _textView.font = font;
  _placeholderLab.font = _textView.font;
}
- (void)setPlaceholder:(NSString *)placeholder {
  _placeholder = placeholder;
  _placeholderLab.text = placeholder;
}
- (void)setPlaceholderColor:(UIColor *)placeholderColor {
  _placeholderColor = placeholderColor;
  _placeholderLab.textColor = placeholderColor;
  _countLab.textColor = placeholderColor;
}
- (void)setSendButtonBackgroundColor:(UIColor *)sendButtonBackgroundColor {
  _sendButtonBackgroundColor = sendButtonBackgroundColor;
  _sendButton.backgroundColor = sendButtonBackgroundColor;
}
- (void)setSendButtonTitle:(NSString *)sendButtonTitle {
  _sendButtonTitle = sendButtonTitle;
  [_sendButton setTitle:sendButtonTitle forState:UIControlStateNormal];
}
- (void)setSendButtonCornerRadius:(CGFloat)sendButtonCornerRadius {
  _sendButtonCornerRadius = sendButtonCornerRadius;
  _sendButton.layer.cornerRadius = sendButtonCornerRadius;
}
- (void)setSendButtonFont:(UIFont *)sendButtonFont {
  _sendButtonFont = sendButtonFont;
  _sendButton.titleLabel.font = sendButtonFont;
}

@end
