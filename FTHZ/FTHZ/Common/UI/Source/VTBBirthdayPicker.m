#import "VTBBirthdayPicker.h"
#import "UIView+Frame.h"

#import "NSDate+VTB.h"

@interface VTBBirthdayPicker ()
@property(nonatomic, strong) UIView *toolBar;
@property(nonatomic, strong) UIDatePicker *picker;

@end

@implementation VTBBirthdayPicker

- (UIView *)toolBar {
  if (!_toolBar) {
    CGFloat width = [[UIScreen mainScreen] bounds].size.width;
    _toolBar = [[UIView alloc] initWithFrame:CGRectMake(0, 0, width, 40)];
    _toolBar.backgroundColor = KColor_keyboardBlack;
    UIButton *cancelBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    cancelBtn.frame = CGRectMake(0, 0, 80, 40);
    [cancelBtn setTitle:@"取消" forState:UIControlStateNormal];
    [cancelBtn setTitleColor:[UIColor whiteColor]
                    forState:UIControlStateNormal];
    [cancelBtn addTarget:self
                  action:@selector(hide)
        forControlEvents:UIControlEventTouchUpInside];
    [_toolBar addSubview:cancelBtn];

    UIButton *submitBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    submitBtn.frame = CGRectMake(width - 80, 0, 80, 40);
    [submitBtn setTitle:@"确定" forState:UIControlStateNormal];
    [submitBtn setTitleColor:[UIColor whiteColor]
                    forState:UIControlStateNormal];
    [submitBtn addTarget:self
                  action:@selector(submitSelectedPickerValue)
        forControlEvents:UIControlEventTouchUpInside];
    [_toolBar addSubview:submitBtn];
    [self addSubview:_toolBar];
  }
  return _toolBar;
}

- (instancetype)initWithFrame:(CGRect)frame {
  self = [super initWithFrame:frame];
  if (self) {
    [self create];
  }
  return self;
}

- (void)create {
  self.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.3];
  UITapGestureRecognizer *tap =
      [[UITapGestureRecognizer alloc] initWithTarget:self
                                              action:@selector(hide)];
  [self addGestureRecognizer:tap];

  _picker = [[UIDatePicker alloc] init];
  _picker.backgroundColor = KColor_keyboardBlack; //[UIColor whiteColor];
  _picker.datePickerMode = UIDatePickerModeDate;
  _picker.maximumDate = [NSDate localNowDate];
  _picker.minimumDate = [[NSDate localDate] dateBySubtractingDays:365 * 50];
  [self addSubview:_picker];
}

- (void)setDefaultBirthday:(NSString *)defaultBirthday {
  _picker.date = [NSDate dateFromString:defaultBirthday withFormat:@"yy-MM-dd"];
}

- (void)submitSelectedPickerValue {

  if (self.delegate &&
      [self.delegate respondsToSelector:@selector(birthdayPickerSelected:)]) {
    NSString *selectDateString = [_picker.date
        stringOfDateWithFormat:kDateFormat]; //  [_picker.date formattedDate];
    [self.delegate birthdayPickerSelected:selectDateString];
  }

  [self hide];
}

- (void)hide {
  __weak typeof(self) wSelf = self;
  [UIView animateWithDuration:0.3
                   animations:^{
                     wSelf.picker.y = wSelf.height + 40;
                     wSelf.toolBar.y = wSelf.picker.y - 40;
                     self.alpha = 0;
                   }];
}

- (void)show {
  __weak typeof(self) wSelf = self;
  _picker.frame = CGRectMake(0, self.height + 40, self.width, 180);
  self.toolBar.frame = CGRectMake(0, _picker.y - 40, self.width, 40);
  [UIView animateWithDuration:0.3
                   animations:^{
                     self.alpha = 1;
                     wSelf.picker.y = self.height - wSelf.picker.height;
                     wSelf.toolBar.y = wSelf.picker.y - 40;
                   }];
}

@end
