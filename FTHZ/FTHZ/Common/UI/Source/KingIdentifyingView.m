#import "KingIdentifyingView.h"

static const NSUInteger tag_base = 20;
NSString *value_labelSize = @"labelSize";
NSString *value_labelSpace = @"labelSpace";

@interface KingIdentifyingView () <UITextFieldDelegate>
@property(nonatomic, strong) UITextField *useTextField;
@property(nonatomic, strong) NSDictionary *setting;
@property(nonatomic, copy) NSString *cerCode;
@property(nonatomic, assign) BOOL isSecure;
@property(nonatomic, assign) NSInteger labelSize;
@property(nonatomic, assign) NSInteger labelSpace;
@property(nonatomic, copy) NSString *agoString;
@property(nonatomic, strong) UIView *fakeCursor;
@end

@implementation KingIdentifyingView

#pragma mark - 初始化方法
- (instancetype)initWithFrame:(CGRect)frame
                      cerCode:(NSString *)cerCode
                      setting:(NSDictionary *)setting
                     isSecure:(BOOL)isSecure {
  if (self = [super initWithFrame:frame]) {
    self.cerCode = cerCode;
    self.setting = setting;
    self.isSecure = isSecure;
    [self verData];
  }
  return self;
}

#pragma mark - 搭建
- (void)verData {
  if (self.cerCode.length <= 0) {
    return;
  }

  if (self.setting != nil) {

    self.labelSize = 50 * kMainTemp;
    self.labelSpace = 11.5 * kMainTemp;

    if (self.setting[value_labelSize]) {
      self.labelSize = [self.setting[value_labelSize] floatValue];
    }
    if (self.setting[value_labelSpace]) {
      self.labelSpace = [self.setting[value_labelSpace] floatValue];
    }
  }

  [self make_ui];
}

#pragma mark - 基本uI
- (void)make_ui {

  self.backgroundColor = [UIColor whiteColor];

  if (self.cerCode.length <= 0) {
    return;
  }
  NSInteger count = self.cerCode.length;
  for (int i = 0; i < count; i++) {

    UILabel *label = [[UILabel alloc] init];
    label.font = [UIFont boldSystemFontOfSize:24 * kMainTemp];
    label.backgroundColor = KColor_HighBlack;
    label.layer.cornerRadius = 10;
    label.layer.masksToBounds = YES;
    label.layer.shadowColor = UIColorFromRGB(0X102F5F).CGColor;
    label.layer.shadowOffset = CGSizeMake(0, 0);
    label.layer.shadowOpacity = 0.1f;
    label.layer.shadowRadius = 10.f;

    label.textAlignment = NSTextAlignmentCenter;
    label.textColor = KColor_White;
    label.tag = tag_base + i;
    label.frame =
        CGRectMake(70 * kMainTemp + (50 * kMainTemp + 11.5 * kMainTemp) * i,
                   (self.frame.size.height - self.labelSize) / 2,
                   self.labelSize, self.labelSize);
    [self addSubview:label];
  }
  self.fakeCursor =
      [[UIView alloc] initWithFrame:CGRectMake(0, 0, 2, self.labelSize * 0.7)];
  self.fakeCursor.backgroundColor = KColor_White;
  self.fakeCursor.layer.cornerRadius = 1;
  self.fakeCursor.hidden = YES;
  [self addSubview:self.fakeCursor];

  self.useTextField = [[UITextField alloc] init];
  self.useTextField.textColor = [UIColor clearColor];
  self.useTextField.backgroundColor = [UIColor clearColor];
  self.useTextField.tintColor = KColor_White;
  self.useTextField.keyboardType = UIKeyboardTypeNumberPad;
  self.useTextField.delegate = self;
  if (@available(iOS 12.0, *)) {
    self.useTextField.textContentType = UITextContentTypeOneTimeCode;
  }
  self.useTextField.frame =
      CGRectMake(0, 0, self.frame.size.width, self.frame.size.height);
  [self.useTextField addTarget:self
                        action:@selector(valueChange:)
              forControlEvents:UIControlEventEditingChanged];
  [self addSubview:self.useTextField];

  UIButton *btn =
      [[UIButton alloc] initWithFrame:CGRectMake(0, 0, self.frame.size.width,
                                                 self.frame.size.height)];
  btn.backgroundColor = [UIColor clearColor];
  [btn addTarget:self
                action:@selector(becameFirstResponder2)
      forControlEvents:UIControlEventTouchUpInside];
  [self addSubview:btn];
  [self valueChange:self.useTextField];
}

- (void)becameFirstResponder2 {
  [self.useTextField becomeFirstResponder];
}

- (void)valueChange:(UITextField *)textField {
  NSString *string = textField.text;

  NSInteger cursorIndex = string.length;
  if (cursorIndex < self.cerCode.length) {
    UILabel *label = [self viewWithTag:tag_base + cursorIndex];
    if (label) {
      CGRect labelFrame = label.frame;
      CGFloat cursorHeight = labelFrame.size.height * 0.5;
      self.fakeCursor.frame = CGRectMake(
          CGRectGetMidX(labelFrame) - 1,
          CGRectGetMidY(labelFrame) - cursorHeight / 2, 2, cursorHeight);
      self.fakeCursor.hidden = NO;
      if (![self.fakeCursor.layer animationForKey:@"opacity"]) {
        CABasicAnimation *blink =
            [CABasicAnimation animationWithKeyPath:@"opacity"];
        blink.fromValue = @1.0;
        blink.toValue = @0.0;
        blink.duration = 0.7;
        blink.timingFunction = [CAMediaTimingFunction
            functionWithName:kCAMediaTimingFunctionEaseInEaseOut];
        blink.autoreverses = YES;
        blink.repeatCount = HUGE_VALF;
        [self.fakeCursor.layer addAnimation:blink forKey:@"opacity"];
      }
    }
  } else {
    self.fakeCursor.hidden = YES;
    [self.fakeCursor.layer removeAnimationForKey:@"opacity"];
  }

  for (int i = 0; i <= self.cerCode.length; i++) {
    UILabel *label = [self viewWithTag:tag_base + i];
    if (i < string.length) {
      if (_isSecure) {
        if ([label.text isEqualToString:@"*"] ||
            [label.text
                isEqualToString:[string
                                    substringWithRange:NSMakeRange(i, 1)]]) {

        } else {
          label.text =
              [string substringWithRange:NSMakeRange(string.length - 1, 1)];
          dispatch_after(
              dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)),
              dispatch_get_main_queue(), ^{
                label.text = @"*";
              });
        }
      } else {
        label.text = [string substringWithRange:NSMakeRange(i, 1)];
      }
      if (i == self.cerCode.length - 1) {
        NSString *num = string;
        if (self.block != nil) {
          self.block(num);
        }
      }
    } else {
      label.text = @"";
    }
  }
}

- (void)clearInput {
  for (int i = 0; i <= self.cerCode.length; i++) {
    UILabel *label = [self viewWithTag:tag_base + i];
    label.text = @"";
  }
  self.useTextField.text = @"";
  self.fakeCursor.hidden = YES;
  [self.fakeCursor.layer removeAnimationForKey:@"opacity"];
}

#pragma mark - 键盘代理：验证字符、处理字符长度
- (BOOL)textField:(UITextField *)textField
    shouldChangeCharactersInRange:(NSRange)range
                replacementString:(NSString *)string {
  if (range.location + string.length > self.cerCode.length) {
    return NO;
  }
  return [self validateNumber:string];
}

- (BOOL)validateNumber:(NSString *)number {
  BOOL res = YES;
  NSCharacterSet *tmpSet =
      [NSCharacterSet characterSetWithCharactersInString:@"0123456789"];
  int i = 0;
  while (i < number.length) {
    NSString *string = [number substringWithRange:NSMakeRange(i, 1)];
    NSRange range = [string rangeOfCharacterFromSet:tmpSet];
    if (range.length == 0) {
      res = NO;
      break;
    }
    i++;
  }
  return res;
}

#pragma mark - 代码块
- (void)makeRebackBlock:(identifying2Verification)block {
  self.block = block;
}

@end
