#import "MJChiBaoZiHeader.h"
#import <Masonry/Masonry.h>

@implementation MJChiBaoZiHeader {
  UIImageView *_loadingImageView;
}

- (void)prepare {
  [super prepare];

  _loadingImageView = [[UIImageView alloc] init];
  _loadingImageView.image = [UIImage imageNamed:@"s0"];
  [self addSubview:_loadingImageView];

  [_loadingImageView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.center.equalTo(self);
    make.size.mas_equalTo(CGSizeMake(50, 50));
  }];
}

- (void)scrollViewContentOffsetDidChange:(NSDictionary *)change {
  [super scrollViewContentOffsetDidChange:change];

  CGFloat offsetY = self.scrollView.contentOffset.y;
  CGFloat angle = (offsetY / 100.0) * M_PI * 2;
  _loadingImageView.transform = CGAffineTransformMakeRotation(angle);
}

- (void)setState:(MJRefreshState)state {
  MJRefreshCheckState;

  switch (state) {
  case MJRefreshStateIdle: {
    [_loadingImageView.layer removeAllAnimations];
    _loadingImageView.transform = CGAffineTransformIdentity;
    break;
  }

  case MJRefreshStatePulling: {
    break;
  }

  case MJRefreshStateRefreshing: {
    CABasicAnimation *rotationAnimation =
        [CABasicAnimation animationWithKeyPath:@"transform.rotation.z"];
    rotationAnimation.fromValue = @(0);
    rotationAnimation.toValue = @(M_PI * 2);
    rotationAnimation.duration = 1.0;
    rotationAnimation.repeatCount = HUGE_VALF;
    rotationAnimation.removedOnCompletion = NO;
    rotationAnimation.fillMode = kCAFillModeForwards;
    rotationAnimation.timingFunction =
        [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionLinear];
    [_loadingImageView.layer addAnimation:rotationAnimation
                                   forKey:@"rotationAnimation"];
    break;
  }

  default:
    break;
  }
}

@end
