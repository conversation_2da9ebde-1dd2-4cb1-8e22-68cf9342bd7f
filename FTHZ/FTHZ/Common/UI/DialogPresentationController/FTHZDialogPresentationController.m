#import "FTHZDialogPresentationController.h"

@interface FTHZDialogPresentationController ()

@property(nonatomic, strong, readonly) UIView *coverLayer;

@end

@implementation FTHZDialogPresentationController

- (instancetype)
    initWithPresentedViewController:(UIViewController *)presentedViewController
           presentingViewController:
               (UIViewController *_Nullable)presentingViewController {
  self = [super initWithPresentedViewController:presentedViewController
                       presentingViewController:presentingViewController];
  if (self) {
    presentedViewController.modalPresentationStyle = UIModalPresentationCustom;
    _coverLayer = [[UIView alloc] init];
  }
  return self;
}

- (void)presentationTransitionWillBegin {
  self.coverLayer.backgroundColor = [UIColor blackColor];
  self.coverLayer.frame = self.containerView.bounds;
  self.coverLayer.alpha = 0.0;

  UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc]
      initWithTarget:self
              action:@selector(coverLayerTapped:)];
  [self.coverLayer addGestureRecognizer:tapGesture];

  [self.containerView addSubview:self.coverLayer];
  [self.presentingViewController.transitionCoordinator
      animateAlongsideTransition:^(
          id<UIViewControllerTransitionCoordinatorContext> _Nonnull context) {
        self.coverLayer.alpha = 0.6;
      }
                      completion:^(
                          id<UIViewControllerTransitionCoordinatorContext> _Nonnull context){
                      }];
}

- (void)presentationTransitionDidEnd:(BOOL)completed {
  if (!completed) {
    [self.coverLayer removeFromSuperview];
  }
}

- (void)dismissalTransitionWillBegin {
  [self.presentingViewController.transitionCoordinator
      animateAlongsideTransition:^(
          id<UIViewControllerTransitionCoordinatorContext> _Nonnull context) {
        self.coverLayer.alpha = 0.0;
      }
                      completion:^(
                          id<UIViewControllerTransitionCoordinatorContext> _Nonnull context){
                      }];
}

- (void)dismissalTransitionDidEnd:(BOOL)completed {
  if (completed) {
    [self.coverLayer removeFromSuperview];
  }
}

- (CGRect)frameOfPresentedViewInContainerView {
  if (![self.presentedViewController
          conformsToProtocol:@protocol(FTHZDialogViewControllerType)]) {
    return [super frameOfPresentedViewInContainerView];
  }
  UIViewController<FTHZDialogViewControllerType> *dialogController =
      (UIViewController<FTHZDialogViewControllerType> *)
          self.presentedViewController;
  return [dialogController frameInContainerView:self.containerView
                         presentationController:self];
}

- (void)coverLayerTapped:(UITapGestureRecognizer *)gesture {
  [self.presentedViewController dismissViewControllerAnimated:YES
                                                   completion:nil];
}

@end
