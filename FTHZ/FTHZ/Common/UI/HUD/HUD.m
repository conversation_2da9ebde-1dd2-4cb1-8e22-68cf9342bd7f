#import "HUD.h"
#import "SVProgressHUD.h"

@implementation HUD

+ (void)show {
  [SVProgressHUD setDefaultMaskType:SVProgressHUDMaskTypeClear];
  //    [SVProgressHUD setForegroundColor:[UIColor blackColor]];
  [SVProgressHUD setBackgroundColor:[UIColor clearColor]];
  [SVProgressHUD show];
}

+ (void)dissmiss {
  [SVProgressHUD dismiss];
}

+ (void)showWithTime:(NSInteger)time {
  [SVProgressHUD dismissWithDelay:time];
}

+ (void)showStringWithTime:(NSString *)str time:(NSInteger)time {
  [SVProgressHUD showWithStatus:str];
  [SVProgressHUD dismissWithDelay:time];
}

+ (BOOL)isVisible {
  return [SVProgressHUD isVisible];
}

@end
