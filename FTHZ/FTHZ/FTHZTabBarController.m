#import "FTHZTabBarController.h"
#import <UIKit/UIKit.h>

#import "Common/Components/AccountManager/FTHZAccountManager.h"
#import "Constants.h"
#import "CreateShudongVC.h"
#import "FTHZRadialMenuView.h"
#import "_2hz-Swift.h"

static CGFloat const kTabBarHeight = 40.0f;

@interface FTHZBaseNavigationController : UINavigationController
@end

@implementation FTHZBaseNavigationController

- (void)pushViewController:(UIViewController *)viewController
                  animated:(BOOL)animated {
  if (self.viewControllers.count > 0) {
    viewController.hidesBottomBarWhenPushed = YES;
  }
  [super pushViewController:viewController animated:animated];
}

@end

@interface FTHZTabBarController () <UITabBarDelegate>
@property(nonatomic, strong) UIButton *centerActionButton;
@property(nonatomic, strong) NSArray<UIView *> *tabBarButtons;
@property(nonatomic, assign) CGRect centerButtonFrame;
@property(nonatomic, assign) BOOL isCenterButtonVisible;
@property(nonatomic, strong) NSTimer *autoRestoreTimer;
@property(nonatomic, strong) FTHZRadialMenuView *radialMenuView;
@property(nonatomic, strong)
    UISwipeGestureRecognizer *tabBarSwipeLeftWhenCenterBtn;
@property(nonatomic, strong)
    UISwipeGestureRecognizer *tabBarSwipeRightWhenCenterBtn;
@property(nonatomic, strong) UIButton *leftArrowButton;
@property(nonatomic, strong) UIButton *rightArrowButton;
@property(nonatomic, strong) UIView *liuyanDot;
@property(nonatomic, strong) UIView *notionDot;
@end

@implementation FTHZTabBarController

- (void)viewDidLoad {
  [super viewDidLoad];
  [self setupTabBarAppearance];
  [self setupViewControllers];
  [self addTabBarSwipeGesture];
  [self setupArrowButtons];
  self.tabBar.delegate = self;
  [self setupUnreadDots];
  if ([[FTHZAccountManager shared] isLogined] && self.selectedIndex != 2) {
    [self fetchUnreadStatus];
  }
}

- (void)setupTabBarAppearance {
  UIBlurEffect *blurEffect =
      [UIBlurEffect effectWithStyle:UIBlurEffectStyleExtraLight];
  UIVisualEffectView *visualEffectView =
      [[UIVisualEffectView alloc] initWithEffect:blurEffect];
  visualEffectView.frame = self.tabBar.bounds;
  visualEffectView.autoresizingMask =
      UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;

  visualEffectView.layer.cornerRadius = 20.0 * kWidthFactor;
  visualEffectView.layer.maskedCorners =
      kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
  visualEffectView.clipsToBounds = YES;

  self.tabBar.backgroundImage = [UIImage new];
  self.tabBar.shadowImage = [UIImage new];

  [self.tabBar insertSubview:visualEffectView atIndex:0];

  CALayer *topBorder = [CALayer layer];

  topBorder.frame =
      CGRectMake(0, 0, self.tabBar.frame.size.width, 0.5 * kWidthFactor);
  topBorder.backgroundColor = [UIColor colorWithWhite:0.0 alpha:0.1].CGColor;
  [visualEffectView.layer addSublayer:topBorder];

  self.tabBar.layer.shadowColor = [UIColor blackColor].CGColor;
  self.tabBar.layer.shadowOffset = CGSizeMake(0, -2.0 * kWidthFactor);
  self.tabBar.layer.shadowOpacity = 0.2 * kWidthFactor;
  self.tabBar.layer.shadowRadius = 4.0 * kWidthFactor;

  self.tabBar.tintColor = KColor_TabarColor;
}

- (void)setupViewControllers {
  FZFindVC *findVC = [[FZFindVC alloc] init];
  FZPlayVC *playVC = [[FZPlayVC alloc] init];
  FZMessageVC *messageVC = [[FZMessageVC alloc] init];
  FTHZBaseNavigationController *findNav =
      [[FTHZBaseNavigationController alloc] initWithRootViewController:findVC];
  FTHZBaseNavigationController *playNav =
      [[FTHZBaseNavigationController alloc] initWithRootViewController:playVC];
  FTHZBaseNavigationController *messageNav =
      [[FTHZBaseNavigationController alloc]
          initWithRootViewController:messageVC];

  UIEdgeInsets imageInsets =
      UIEdgeInsetsMake(12 * kWidthFactor, 0, -12 * kWidthFactor, 0);

  findNav.tabBarItem = [[UITabBarItem alloc]
      initWithTitle:@""
              image:
                  [[UIImage imageNamed:@"unaa"]
                      imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal]
      selectedImage:
          [[UIImage imageNamed:@"_0"]
              imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal]];
  findNav.tabBarItem.imageInsets = imageInsets;

  playNav.tabBarItem = [[UITabBarItem alloc]
      initWithTitle:@""
              image:
                  [[UIImage imageNamed:@"unbb"]
                      imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal]
      selectedImage:
          [[UIImage imageNamed:@"_1"]
              imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal]];
  playNav.tabBarItem.imageInsets = imageInsets;

  messageNav.tabBarItem = [[UITabBarItem alloc]
      initWithTitle:@""
              image:
                  [[UIImage imageNamed:@"uncc"]
                      imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal]
      selectedImage:
          [[UIImage imageNamed:@"_2"]
              imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal]];
  messageNav.tabBarItem.imageInsets = imageInsets;

  self.viewControllers = @[ findNav, playNav, messageNav ];
}

#pragma mark - 手势与动画
- (void)addTabBarSwipeGesture {
  UISwipeGestureRecognizer *swipeLeft = [[UISwipeGestureRecognizer alloc]
      initWithTarget:self
              action:@selector(handleTabBarSwipe:)];
  swipeLeft.direction = UISwipeGestureRecognizerDirectionLeft;
  [self.tabBar addGestureRecognizer:swipeLeft];

  UISwipeGestureRecognizer *swipeRight = [[UISwipeGestureRecognizer alloc]
      initWithTarget:self
              action:@selector(handleTabBarSwipe:)];
  swipeRight.direction = UISwipeGestureRecognizerDirectionRight;
  [self.tabBar addGestureRecognizer:swipeRight];
}

- (void)handleTabBarSwipe:(UISwipeGestureRecognizer *)gesture {
  if (@available(iOS 10.0, *)) {
    UIImpactFeedbackGenerator *generator = [[UIImpactFeedbackGenerator alloc]
        initWithStyle:UIImpactFeedbackStyleMedium];
    [generator impactOccurred];
  }
  if (self.isCenterButtonVisible)
    return;
  NSMutableArray *tabButtons = [NSMutableArray array];
  for (UIView *subview in self.tabBar.subviews) {
    if ([subview isKindOfClass:NSClassFromString(@"UITabBarButton")]) {
      [tabButtons addObject:subview];
    }
  }
  if (tabButtons.count != 3)
    return;
  [tabButtons sortUsingComparator:^NSComparisonResult(UIView *a, UIView *b) {
    return a.frame.origin.x > b.frame.origin.x ? 1 : -1;
  }];
  self.tabBarButtons = tabButtons;
  UIView *centerBtn = tabButtons[1];
  self.centerButtonFrame = centerBtn.frame;
  [UIView animateWithDuration:0.25
      animations:^{
        for (UIView *btn in tabButtons) {
          btn.alpha = 0;
        }
      }
      completion:^(BOOL finished) {
        [self showCenterActionButton];
      }];
}

- (void)showCenterActionButton {
  if (self.centerActionButton) {
    [self.centerActionButton removeFromSuperview];
    self.centerActionButton = nil;
  }
  [self invalidateAutoRestoreTimer];
  UIButton *actionBtn = [UIButton buttonWithType:UIButtonTypeCustom];
  CGFloat btnHeight = self.centerButtonFrame.size.height;
  CGFloat btnY = 12 * kWidthFactor;
  CGFloat btnWidth = 320 * kWidthFactor;
  CGFloat btnX = (self.tabBar.bounds.size.width - btnWidth) / 2.0;
  actionBtn.frame = CGRectMake(btnX, btnY, btnWidth, btnHeight);
  actionBtn.alpha = 0;
  actionBtn.userInteractionEnabled = YES;
  actionBtn.enabled = YES;
  [actionBtn setTitle:@"按住" forState:UIControlStateNormal];
  [actionBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
  actionBtn.titleLabel.font = SourceHanSerifSemiBoldFont(20 * kWidthFactor);
  actionBtn.backgroundColor = KColor_HighBlack;
  actionBtn.layer.cornerRadius = btnHeight / 2.0;
  actionBtn.layer.masksToBounds = YES;
  actionBtn.contentVerticalAlignment = UIControlContentVerticalAlignmentCenter;
  actionBtn.contentHorizontalAlignment =
      UIControlContentHorizontalAlignmentCenter;
  UILongPressGestureRecognizer *longPress =
      [[UILongPressGestureRecognizer alloc]
          initWithTarget:self
                  action:@selector(centerActionButtonLongPressed:)];
  longPress.minimumPressDuration = 0.3;
  [actionBtn addGestureRecognizer:longPress];
  [self.tabBar addSubview:actionBtn];
  self.centerActionButton = actionBtn;
  self.isCenterButtonVisible = YES;
  UISwipeGestureRecognizer *swipeDown = [[UISwipeGestureRecognizer alloc]
      initWithTarget:self
              action:@selector(handleTabBarSwipeDown:)];
  swipeDown.direction = UISwipeGestureRecognizerDirectionDown;
  [actionBtn addGestureRecognizer:swipeDown];

  if (self.tabBarSwipeLeftWhenCenterBtn) {
    [self.tabBar removeGestureRecognizer:self.tabBarSwipeLeftWhenCenterBtn];
    self.tabBarSwipeLeftWhenCenterBtn = nil;
  }
  if (self.tabBarSwipeRightWhenCenterBtn) {
    [self.tabBar removeGestureRecognizer:self.tabBarSwipeRightWhenCenterBtn];
    self.tabBarSwipeRightWhenCenterBtn = nil;
  }
  self.tabBarSwipeLeftWhenCenterBtn = [[UISwipeGestureRecognizer alloc]
      initWithTarget:self
              action:@selector(handleCenterButtonSwipe:)];
  self.tabBarSwipeLeftWhenCenterBtn.direction =
      UISwipeGestureRecognizerDirectionLeft;
  [self.tabBar addGestureRecognizer:self.tabBarSwipeLeftWhenCenterBtn];

  self.tabBarSwipeRightWhenCenterBtn = [[UISwipeGestureRecognizer alloc]
      initWithTarget:self
              action:@selector(handleCenterButtonSwipe:)];
  self.tabBarSwipeRightWhenCenterBtn.direction =
      UISwipeGestureRecognizerDirectionRight;
  [self.tabBar addGestureRecognizer:self.tabBarSwipeRightWhenCenterBtn];

  [UIView animateWithDuration:0.25
                   animations:^{
                     actionBtn.alpha = 1;
                   }];
  self.autoRestoreTimer = [NSTimer
      scheduledTimerWithTimeInterval:5.0
                              target:self
                            selector:@selector(autoRestoreTabBarButtons)
                            userInfo:nil
                             repeats:NO];
}

- (void)centerActionButtonLongPressed:(UILongPressGestureRecognizer *)gesture {
  if (gesture.state == UIGestureRecognizerStateBegan) {
    if (@available(iOS 10.0, *)) {
      UIImpactFeedbackGenerator *generator = [[UIImpactFeedbackGenerator alloc]
          initWithStyle:UIImpactFeedbackStyleMedium];
      [generator impactOccurred];
    }
    [self invalidateAutoRestoreTimer];
    CGFloat tabBarY = CGRectGetMinY(self.tabBar.frame);
    CGFloat menuOriginX = self.view.bounds.size.width;
    CGFloat menuOriginY = tabBarY;
    CGPoint touchPointInButton =
        [gesture locationInView:self.centerActionButton];
    BOOL isRight = (touchPointInButton.x >
                    self.centerActionButton.bounds.size.width / 2.0);
    if (!self.radialMenuView) {
      self.radialMenuView =
          [[FTHZRadialMenuView alloc] initWithFrame:CGRectZero];
    }
    self.radialMenuView.onSelection = ^(FTHZRadialMenuSelection selection) {
      FTHZRadialMenuSelection mappedSelection = selection;
      if (self.radialMenuView.isRight) {
        mappedSelection = 3 + 1 - selection;
      }
      if (mappedSelection == 1) {
        CreateShudongVC *ddVC = [[CreateShudongVC alloc] init];
        ddVC.modalPresentationStyle = UIModalPresentationFullScreen;
        [self presentViewController:ddVC animated:YES completion:nil];
      } else if (mappedSelection == 2) {
        FTHZCreateChannelVC *vc = [[FTHZCreateChannelVC alloc] init];
        vc.isVoiceType = YES;
        vc.modalPresentationStyle = UIModalPresentationFullScreen;
        [self presentViewController:vc animated:YES completion:nil];
      } else if (mappedSelection == 3) {
        ReleaseDynamicVC *postVC = [[ReleaseDynamicVC alloc] init];
        postVC.modalPresentationStyle = UIModalPresentationFullScreen;
        [self presentViewController:postVC animated:YES completion:nil];
      }
    };
    [self.radialMenuView showInView:self.view
                          fromPoint:CGPointMake(menuOriginX, menuOriginY)
                            isRight:isRight];
  } else if (gesture.state == UIGestureRecognizerStateChanged) {
    CGPoint touchPoint = [gesture locationInView:self.view];
    if (self.radialMenuView) {
      [self.radialMenuView updateHighlightWithTouchPoint:touchPoint];
    }
  } else if (gesture.state == UIGestureRecognizerStateEnded ||
             gesture.state == UIGestureRecognizerStateCancelled) {
    if (self.radialMenuView) {
      FTHZRadialMenuSelection sel = self.radialMenuView.currentSelection;
      if (self.radialMenuView.onSelection) {
        self.radialMenuView.onSelection(sel);
      }
      [self.radialMenuView dismiss];
      self.radialMenuView = nil;
    }
    if (self.isCenterButtonVisible) {
      self.autoRestoreTimer = [NSTimer
          scheduledTimerWithTimeInterval:5.0
                                  target:self
                                selector:@selector(autoRestoreTabBarButtons)
                                userInfo:nil
                                 repeats:NO];
    }
  }
}

- (void)handleTabBarSwipeDown:(UISwipeGestureRecognizer *)gesture {
  [self invalidateAutoRestoreTimer];
  [self restoreTabBarButtons];
}

- (void)autoRestoreTabBarButtons {
  [self restoreTabBarButtons];
}

- (void)restoreTabBarButtons {
  if (!self.isCenterButtonVisible)
    return;
  [self invalidateAutoRestoreTimer];
  if (self.tabBarSwipeLeftWhenCenterBtn) {
    [self.tabBar removeGestureRecognizer:self.tabBarSwipeLeftWhenCenterBtn];
    self.tabBarSwipeLeftWhenCenterBtn = nil;
  }
  if (self.tabBarSwipeRightWhenCenterBtn) {
    [self.tabBar removeGestureRecognizer:self.tabBarSwipeRightWhenCenterBtn];
    self.tabBarSwipeRightWhenCenterBtn = nil;
  }
  [UIView animateWithDuration:0.25
      animations:^{
        self.centerActionButton.alpha = 0;
      }
      completion:^(BOOL finished) {
        [self.centerActionButton removeFromSuperview];
        self.centerActionButton = nil;
        self.isCenterButtonVisible = NO;
        [UIView animateWithDuration:0.25
                         animations:^{
                           for (UIView *btn in self.tabBarButtons) {
                             btn.alpha = 1;
                           }
                         }];
      }];
}

- (void)invalidateAutoRestoreTimer {
  if (self.autoRestoreTimer) {
    [self.autoRestoreTimer invalidate];
    self.autoRestoreTimer = nil;
  }
}

- (void)handleCenterButtonSwipe:(UISwipeGestureRecognizer *)gesture {
  if (@available(iOS 10.0, *)) {
    UIImpactFeedbackGenerator *generator = [[UIImpactFeedbackGenerator alloc]
        initWithStyle:UIImpactFeedbackStyleMedium];
    [generator impactOccurred];
  }
  [self invalidateAutoRestoreTimer];
  [self restoreTabBarButtons];
}

- (void)setupArrowButtons {
  if (!self.leftArrowButton) {
    self.leftArrowButton = [UIButton buttonWithType:UIButtonTypeCustom];
    UIImage *leftImage = [UIImage imageNamed:@"daohangjiantou_l"];
    [self.leftArrowButton setImage:leftImage forState:UIControlStateNormal];
    self.leftArrowButton.backgroundColor = [UIColor clearColor];
    self.leftArrowButton.layer.cornerRadius = 0;
    self.leftArrowButton.layer.masksToBounds = NO;
    [self.leftArrowButton addTarget:self
                             action:@selector(leftArrowTapped)
                   forControlEvents:UIControlEventTouchUpInside];
    [self.tabBar addSubview:self.leftArrowButton];
  }
  if (!self.rightArrowButton) {
    self.rightArrowButton = [UIButton buttonWithType:UIButtonTypeCustom];
    UIImage *rightImage = [UIImage imageNamed:@"daohangjiantou_r"];
    [self.rightArrowButton setImage:rightImage forState:UIControlStateNormal];
    self.rightArrowButton.backgroundColor = [UIColor clearColor];
    self.rightArrowButton.layer.cornerRadius = 0;
    self.rightArrowButton.layer.masksToBounds = NO;
    [self.rightArrowButton addTarget:self
                              action:@selector(rightArrowTapped)
                    forControlEvents:UIControlEventTouchUpInside];
    [self.tabBar addSubview:self.rightArrowButton];
  }
  [self layoutArrowButtons];
}

- (void)layoutArrowButtons {
  CGFloat arrowSize = 16 * kWidthFactor;
  CGFloat centerY = CGRectGetMidY(self.tabBar.bounds);
  self.leftArrowButton.frame = CGRectMake(
      8 * kWidthFactor, centerY - 12 * kWidthFactor, arrowSize, arrowSize);
  self.rightArrowButton.frame =
      CGRectMake(self.tabBar.bounds.size.width - arrowSize - 8 * kWidthFactor,
                 centerY - 12 * kWidthFactor, arrowSize, arrowSize);
  [self.tabBar bringSubviewToFront:self.leftArrowButton];
  [self.tabBar bringSubviewToFront:self.rightArrowButton];
}

- (void)viewDidLayoutSubviews {
  [super viewDidLayoutSubviews];
  [self layoutArrowButtons];
}

- (void)leftArrowTapped {
  if (self.isCenterButtonVisible) {
    if (@available(iOS 10.0, *)) {
      UIImpactFeedbackGenerator *generator = [[UIImpactFeedbackGenerator alloc]
          initWithStyle:UIImpactFeedbackStyleMedium];
      [generator impactOccurred];
    }
    [self restoreTabBarButtons];
  } else {
    [self handleTabBarSwipe:nil];
  }
}

- (void)rightArrowTapped {
  if (self.isCenterButtonVisible) {
    if (@available(iOS 10.0, *)) {
      UIImpactFeedbackGenerator *generator = [[UIImpactFeedbackGenerator alloc]
          initWithStyle:UIImpactFeedbackStyleMedium];
      [generator impactOccurred];
    }
    [self restoreTabBarButtons];
  } else {
    [self handleTabBarSwipe:nil];
  }
}

#pragma mark - 红点视图初始化
- (void)setupUnreadDots {
  dispatch_after(
      dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)),
      dispatch_get_main_queue(), ^{
        NSMutableArray *tabButtons = [NSMutableArray array];
        for (UIView *subview in self.tabBar.subviews) {
          if ([subview isKindOfClass:NSClassFromString(@"UITabBarButton")]) {
            [tabButtons addObject:subview];
          }
        }
        if (tabButtons.count < 3)
          return;
        [tabButtons
            sortUsingComparator:^NSComparisonResult(UIView *a, UIView *b) {
              return a.frame.origin.x > b.frame.origin.x ? 1 : -1;
            }];
        UIView *thirdBtn = tabButtons[2];
        UIImageView *imageView = nil;
        for (UIView *sub in thirdBtn.subviews) {
          if ([sub isKindOfClass:[UIImageView class]]) {
            imageView = (UIImageView *)sub;
            break;
          }
        }
        if (!imageView) {
          dispatch_after(
              dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)),
              dispatch_get_main_queue(), ^{
                [self setupUnreadDots];
              });
          return;
        }
        CGFloat dotSize = roundf(6 * kWidthFactor);
        CGFloat centerY = imageView.bounds.size.height / 2.0;
        CGFloat liuyanX = -dotSize / 2;
        CGFloat liuyanY = centerY - dotSize / 2;
        CGFloat notionX = imageView.bounds.size.width - dotSize / 2;
        CGFloat notionY = centerY - dotSize / 2;
        self.liuyanDot = [[UIView alloc]
            initWithFrame:CGRectMake(liuyanX, liuyanY, dotSize, dotSize)];
        self.liuyanDot.backgroundColor = [UIColor redColor];
        self.liuyanDot.layer.cornerRadius = dotSize / 2.0;
        self.liuyanDot.clipsToBounds = YES;
        self.liuyanDot.hidden = YES;
        [imageView addSubview:self.liuyanDot];
        self.notionDot = [[UIView alloc]
            initWithFrame:CGRectMake(notionX, notionY, dotSize, dotSize)];
        self.notionDot.backgroundColor = [UIColor redColor];
        self.notionDot.layer.cornerRadius = dotSize / 2.0;
        self.notionDot.clipsToBounds = YES;
        self.notionDot.hidden = YES;
        [imageView addSubview:self.notionDot];
        self.liuyanDot.autoresizingMask =
            UIViewAutoresizingFlexibleRightMargin |
            UIViewAutoresizingFlexibleBottomMargin;
        self.notionDot.autoresizingMask =
            UIViewAutoresizingFlexibleLeftMargin |
            UIViewAutoresizingFlexibleBottomMargin;
      });
}

#pragma mark - 红点显示控制
- (void)updateUnreadIndicatorsWithLiuyan:(BOOL)liuyan notion:(BOOL)notion {
  BOOL isThirdSelected = (self.selectedIndex == 2);
  NSString *gender = [CurrentUser.gender isEqualToString:@"1"] ? @"1" : @"2";
  UIColor *dotColor = [gender isEqualToString:@"1"] ? KColor_switchLightBlue
                                                    : KColor_switchLightPink;
  self.liuyanDot.backgroundColor = dotColor;
  self.notionDot.backgroundColor = dotColor;
  self.liuyanDot.hidden = isThirdSelected || !liuyan;
  self.notionDot.hidden = isThirdSelected || !notion;
}

#pragma mark - UITabBarDelegate
- (void)tabBar:(UITabBar *)tabBar didSelectItem:(UITabBarItem *)item {
  if (@available(iOS 10.0, *)) {
    UIImpactFeedbackGenerator *generator = [[UIImpactFeedbackGenerator alloc]
        initWithStyle:UIImpactFeedbackStyleMedium];
    [generator impactOccurred];
  }
  NSUInteger idx = [self.tabBar.items indexOfObject:item];
  if (idx == NSNotFound || idx > 2)
    return;
  if (![[FTHZAccountManager shared] isLogined]) {
    [self updateUnreadIndicatorsWithLiuyan:NO notion:NO];
    return;
  }
  if (idx == 2) {
    [self updateUnreadIndicatorsWithLiuyan:NO notion:NO];
  } else {
    [self fetchUnreadStatus];
  }
}

#pragma mark - 请求未读状态
- (void)fetchUnreadStatus {
  [Http getAsynRequestWithUrl:KURLUnreadStatus
      params:@{}
      success:^(NSDictionary *resultObject) {
        NSDictionary *data = resultObject[@"data"];
        BOOL liuyan = [data[@"liuyan"] boolValue];
        BOOL notion = [data[@"notion"] boolValue];
        [self updateUnreadIndicatorsWithLiuyan:liuyan notion:notion];
      }
      failure:^(NSError *requestErr) {
        [self updateUnreadIndicatorsWithLiuyan:NO notion:NO];
      }];
}

@end