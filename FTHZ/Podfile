use_frameworks!

source 'https://mirrors.tuna.tsinghua.edu.cn/git/CocoaPods/Specs.git'
platform :ios, '12.0'

def allpods
  
  pod 'AFNetworking', '~> 4.0'
  pod 'MJExtension', '~> 3.0.15.1'
  pod 'ReactiveObjC', '~> 3.1.0'
  pod 'Bugly', '~> 2.5.2'
  pod 'FreeStreamer', '~> 3.9.0'
  pod 'JSONModel', '~> 1.7.0'
  pod 'Toast', '~> 4.0.0'
  pod 'MJRefresh', '~> 3.1.15.6'
  pod 'Masonry', '~> 1.1.0'
  pod 'SDWebImage', '~> 5.0.0'
  pod 'FLAnimatedImage', '~> 1.0.12'
  pod 'pop', '~> 1.0.10'
  pod 'SVProgressHUD', '~> 2.2.5'
  pod 'JXCategoryView', '~> 1.5.5'
  pod 'Qiniu', '~> 7.2.5'
  
  pod 'TZImagePickerController', '~> 3.8.8'

  pod 'IQKeyboardManager', '~> 6.2.0'
  pod 'UICountingLabel'
  pod 'Popover.OC'
  
  pod 'YYCache' #缓存

  pod 'SideMenu','~> 5.0'
  pod 'SnapKit', '~> 4.0.0'
  pod 'Moya/ReactiveSwift', '~> 13.0'
  pod 'TTTAttributedLabel'
end

target 'FTHZ' do
  allpods
end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '12.0'
      config.build_settings['ENABLE_BITCODE'] = 'NO'      
      config.build_settings['EXCLUDED_ARCHS[sdk=iphonesimulator*]'] = 'arm64'
    end
  end
end