<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleAllowMixedLocalizations</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLIconFile</key>
			<string>logo</string>
			<key>CFBundleURLName</key>
			<string>wechat</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wx09d09b1ce4d983b0</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>talkingData</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>talkingdata.875735a4e9be4051b317e23429d7b04f</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>Fabric</key>
	<dict>
		<key>APIKey</key>
		<string>9331d52d1f5bb56d4c24ef99a09211cd6de1abdd</string>
		<key>Kits</key>
		<array>
			<dict>
				<key>KitInfo</key>
				<dict/>
				<key>KitName</key>
				<string>Crashlytics</string>
			</dict>
		</array>
	</dict>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>weixinULAPI</string>
		<string>weixin</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>52赫兹想访问你的相册，以便在聊天中向好友发送图片或发布图片动态</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>52赫兹想访问你的地理位置，以便在发布动态时展示你的瞬间定位</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>52赫兹想访问你的地理位置，以便在发布动态时展示你的瞬间定位</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>52赫兹想访问你的地理位置，以便在发布动态时展示你的瞬间定位</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>52赫兹想访问你的麦克风，以便在聊天中向好友发起语音消息交流</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>52赫兹想访问你的相册，以便在保存您想要保存的图片</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>52赫兹想访问你的相册，以便在聊天中向好友发送图片或发布图片动态</string>
	<key>UIBackgroundModes</key>
	<array>
		<string>audio</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UIStatusBarStyle</key>
	<string>UIStatusBarStyleDefault</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
