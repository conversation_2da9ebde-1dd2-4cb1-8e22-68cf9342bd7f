// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 70;
	objects = {

/* Begin PBXBuildFile section */
		6302B4F124371FCA00FCB8C4 /* SearchCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6302B4F024371FCA00FCB8C4 /* SearchCell.swift */; };
		6302B4F32437399800FCB8C4 /* UILabel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6302B4F22437399800FCB8C4 /* UILabel.swift */; };
		630536A422A912D0005B3878 /* FTHZChannelListVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 630536A322A912D0005B3878 /* FTHZChannelListVC.m */; };
		630536A822AAC472005B3878 /* FTHZCreateChannelVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 630536A722AAC472005B3878 /* FTHZCreateChannelVC.m */; };
		630536AC22AF327D005B3878 /* FTHZChannelDetailVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 630536AB22AF327D005B3878 /* FTHZChannelDetailVC.m */; };
		6313C15E24BDEB3100385E9C /* CALayer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6313C15D24BDEB3100385E9C /* CALayer.swift */; };
		6328EEAE248CE514009168A3 /* UINavigationController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6328EEAD248CE514009168A3 /* UINavigationController.swift */; };
		6328EED4249BC5C2009168A3 /* FZGiftVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6328EED3249BC5C2009168A3 /* FZGiftVC.swift */; };
		6328EED624A11A18009168A3 /* GradientLabel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6328EED524A11A18009168A3 /* GradientLabel.swift */; };
		6328EEDE24A27671009168A3 /* NSDate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6328EEDD24A27671009168A3 /* NSDate.swift */; };
		6328EEE024AB14AE009168A3 /* SwipeScrollView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6328EEDF24AB14AE009168A3 /* SwipeScrollView.swift */; };
		6328EEE624B4232E009168A3 /* libsqlite3.0.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 6328EEE524B4232D009168A3 /* libsqlite3.0.tbd */; };
		6334870024477F990080D3F0 /* FZMessageVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 633486FF24477F990080D3F0 /* FZMessageVC.swift */; };
		6334B5722439EB13001C4F35 /* FZMomentVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6334B5712439EB13001C4F35 /* FZMomentVC.swift */; };
		634014C02457571B00961DE8 /* GameAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = 634014BF2457571B00961DE8 /* GameAPI.swift */; };
		634014C52458AD8000961DE8 /* TLSSDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 634014C42458AD8000961DE8 /* TLSSDK.framework */; };
		634E11A12441CDA600175027 /* LabelLayoutManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 634E11A02441CDA600175027 /* LabelLayoutManager.swift */; };
		634E11A32441CDDE00175027 /* LabelWithLineSpace.swift in Sources */ = {isa = PBXBuildFile; fileRef = 634E11A22441CDDE00175027 /* LabelWithLineSpace.swift */; };
		634E11A92442473B00175027 /* FZTopicVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 634E11A82442473B00175027 /* FZTopicVC.swift */; };
		634E11AB2442480E00175027 /* TopicNavCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 634E11AA2442480E00175027 /* TopicNavCell.swift */; };
		634E11AD24424FA900175027 /* TopicNavLayout.swift in Sources */ = {isa = PBXBuildFile; fileRef = 634E11AC24424FA900175027 /* TopicNavLayout.swift */; };
		634E11AF2442DFEF00175027 /* FZHerzVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 634E11AE2442DFEF00175027 /* FZHerzVC.swift */; };
		634E11B1244305A000175027 /* HerzCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 634E11B0244305A000175027 /* HerzCardView.swift */; };
		634E11B324434FA500175027 /* StraitCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 634E11B224434FA500175027 /* StraitCell.swift */; };
		634E11B62444D82E00175027 /* TouchAreaButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 634E11B52444D82E00175027 /* TouchAreaButton.swift */; };
		634E11BE2444EE7400175027 /* TNTextLayer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 634E11BA2444EE7100175027 /* TNTextLayer.swift */; };
		634E11BF2444EE7400175027 /* TNSlider.swift in Sources */ = {isa = PBXBuildFile; fileRef = 634E11BB2444EE7200175027 /* TNSlider.swift */; };
		634E11C02444EE7400175027 /* TNTrackLayer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 634E11BC2444EE7300175027 /* TNTrackLayer.swift */; };
		634E11C12444EE7400175027 /* TNConstants.swift in Sources */ = {isa = PBXBuildFile; fileRef = 634E11BD2444EE7400175027 /* TNConstants.swift */; };
		6355FCB8243F804A00224A7F /* MyLikedModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6355FCB7243F804A00224A7F /* MyLikedModel.swift */; };
		6355FCBA243F953900224A7F /* MyLikedCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6355FCB9243F953900224A7F /* MyLikedCell.swift */; };
		6355FCBE243FA03300224A7F /* FZPlayVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6355FCBD243FA03300224A7F /* FZPlayVC.swift */; };
		6355FCC72440C54E00224A7F /* ZCycleView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6355FCC32440C54E00224A7F /* ZCycleView.swift */; };
		6355FCC82440C54E00224A7F /* ZCycleViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6355FCC42440C54E00224A7F /* ZCycleViewCell.swift */; };
		6355FCC92440C54E00224A7F /* ZCycleLayout.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6355FCC52440C54E00224A7F /* ZCycleLayout.swift */; };
		6355FCCA2440C54E00224A7F /* ZPageControl.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6355FCC62440C54E00224A7F /* ZPageControl.swift */; };
		6355FCCE2440ED8A00224A7F /* FZCollectionMenu.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6355FCCD2440ED8A00224A7F /* FZCollectionMenu.swift */; };
		6355FCD02440EEF600224A7F /* FZCollectionLayout.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6355FCCF2440EEF600224A7F /* FZCollectionLayout.swift */; };
		6355FCD2244162FF00224A7F /* FZStraitVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6355FCD1244162FF00224A7F /* FZStraitVC.swift */; };
		6355FCD62441637200224A7F /* StriatNavLayout.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6355FCD52441637200224A7F /* StriatNavLayout.swift */; };
		6355FCD82441642300224A7F /* StraitNavCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6355FCD72441642300224A7F /* StraitNavCell.swift */; };
		6359326623FEA70700ABB2A1 /* Bugly.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6359326523FEA70700ABB2A1 /* Bugly.framework */; };
		6359326E2423EFEB00ABB2A1 /* FZMineVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6359326D2423EFEB00ABB2A1 /* FZMineVC.swift */; };
		6359326F2423FF7D00ABB2A1 /* libTalkingData.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 696D73862297CA6300BEB77D /* libTalkingData.a */; };
		6359328A2425F12A00ABB2A1 /* FZConstants.swift in Sources */ = {isa = PBXBuildFile; fileRef = 635932892425F12A00ABB2A1 /* FZConstants.swift */; };
		6359328D2425F19400ABB2A1 /* UIColor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6359328C2425F19400ABB2A1 /* UIColor.swift */; };
		6359328F242703B200ABB2A1 /* FZFindVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6359328E242703B200ABB2A1 /* FZFindVC.swift */; };
		6359329224271D8900ABB2A1 /* UIFont.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6359329124271D8900ABB2A1 /* UIFont.swift */; };
		63593295242729FD00ABB2A1 /* FZBaseVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 63593294242729FD00ABB2A1 /* FZBaseVC.swift */; };
		635932972427481D00ABB2A1 /* UIImageView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 635932962427481D00ABB2A1 /* UIImageView.swift */; };
		6359329924274A5900ABB2A1 /* UIImage.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6359329824274A5900ABB2A1 /* UIImage.swift */; };
		6359329C242750A700ABB2A1 /* UIView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6359329B242750A700ABB2A1 /* UIView.swift */; };
		635932D5242A856D00ABB2A1 /* UIButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 635932D4242A856D00ABB2A1 /* UIButton.swift */; };
		635932D8242BC7CD00ABB2A1 /* FZSearchVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 635932D7242BC7CD00ABB2A1 /* FZSearchVC.swift */; };
		635932DC242CC34500ABB2A1 /* Codextended.swift in Sources */ = {isa = PBXBuildFile; fileRef = 635932DB242CC34500ABB2A1 /* Codextended.swift */; };
		635932DE242CC53600ABB2A1 /* FZNetWorkManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 635932DD242CC53600ABB2A1 /* FZNetWorkManager.swift */; };
		635932E5242D01DE00ABB2A1 /* MoyaConfig.swift in Sources */ = {isa = PBXBuildFile; fileRef = 635932E4242D01DE00ABB2A1 /* MoyaConfig.swift */; };
		635932EB242D1BE700ABB2A1 /* NetWorkError.swift in Sources */ = {isa = PBXBuildFile; fileRef = 635932EA242D1BE700ABB2A1 /* NetWorkError.swift */; };
		635932ED242D1C2A00ABB2A1 /* Response.swift in Sources */ = {isa = PBXBuildFile; fileRef = 635932EC242D1C2A00ABB2A1 /* Response.swift */; };
		635932EF242D1C5300ABB2A1 /* NetworkManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 635932EE242D1C5300ABB2A1 /* NetworkManager.swift */; };
		635932F2242D1CBA00ABB2A1 /* FindAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = 635932F1242D1CBA00ABB2A1 /* FindAPI.swift */; };
		635932F4242D207100ABB2A1 /* SearchResModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 635932F3242D207100ABB2A1 /* SearchResModel.swift */; };
		635932F6242D319A00ABB2A1 /* MomentModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 635932F5242D319A00ABB2A1 /* MomentModel.swift */; };
		635932F8242DCD2B00ABB2A1 /* FZDraftVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 635932F7242DCD2A00ABB2A1 /* FZDraftVC.swift */; };
		635932FE242DF2E300ABB2A1 /* CellConfigurator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 635932FB242DF2E200ABB2A1 /* CellConfigurator.swift */; };
		635932FF242DF2E300ABB2A1 /* Updatable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 635932FC242DF2E300ABB2A1 /* Updatable.swift */; };
		63593300242DF2E300ABB2A1 /* ConfigurableTableViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 635932FD242DF2E300ABB2A1 /* ConfigurableTableViewController.swift */; };
		63593303242DF3EE00ABB2A1 /* DraftCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 63593302242DF3EE00ABB2A1 /* DraftCell.swift */; };
		63593309242EF6FD00ABB2A1 /* FZAddMusic.swift in Sources */ = {isa = PBXBuildFile; fileRef = 63593308242EF6FD00ABB2A1 /* FZAddMusic.swift */; };
		6359330B242EFD5C00ABB2A1 /* CopyLabel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6359330A242EFD5C00ABB2A1 /* CopyLabel.swift */; };
		63593310242F552C00ABB2A1 /* NKCacheManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6359330E242F552C00ABB2A1 /* NKCacheManager.swift */; };
		63593314242F615A00ABB2A1 /* FZKeyedArchiver.m in Sources */ = {isa = PBXBuildFile; fileRef = 63593313242F615A00ABB2A1 /* FZKeyedArchiver.m */; };
		63593317242F621B00ABB2A1 /* FZDraftModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 63593316242F621B00ABB2A1 /* FZDraftModel.m */; };
		635933192432531C00ABB2A1 /* FZMyHomePageVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 635933182432531C00ABB2A1 /* FZMyHomePageVC.swift */; };
		6359331B2434779600ABB2A1 /* homepageHeadView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6359331A2434779600ABB2A1 /* homepageHeadView.swift */; };
		6359331D2434A61800ABB2A1 /* otherUserHeadView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6359331C2434A61800ABB2A1 /* otherUserHeadView.swift */; };
		6372A16C2465D00C00F998E1 /* ImportedGestures.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6372A1662465D00C00F998E1 /* ImportedGestures.swift */; };
		6372A16D2465D00C00F998E1 /* AndroidGesturesImporter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6372A1672465D00C00F998E1 /* AndroidGesturesImporter.swift */; };
		6372A16E2465D00C00F998E1 /* PennyPincherGestureRecognizer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6372A1682465D00C00F998E1 /* PennyPincherGestureRecognizer.swift */; };
		6372A16F2465D00C00F998E1 /* PennyPincherTemplate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6372A1692465D00C00F998E1 /* PennyPincherTemplate.swift */; };
		6372A1702465D00C00F998E1 /* PennyPincher.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6372A16A2465D00C00F998E1 /* PennyPincher.swift */; };
		6372A19B246C49B800F998E1 /* gestures in Resources */ = {isa = PBXBuildFile; fileRef = 6372A19A246C49B800F998E1 /* gestures */; };
		6372A19D246DBB5F00F998E1 /* CGFloat.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6372A19C246DBB5F00F998E1 /* CGFloat.swift */; };
		637AE944244BE52E005ABD3B /* FZAttributeString.swift in Sources */ = {isa = PBXBuildFile; fileRef = 637AE943244BE52E005ABD3B /* FZAttributeString.swift */; };
		6396D2FB244BF2F500B7B2EE /* (null) in Sources */ = {isa = PBXBuildFile; };
		63AB043024D0A61D00BA3BE6 /* MaskTriangleView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 63AB042F24D0A61D00BA3BE6 /* MaskTriangleView.swift */; };
		63AFBAD023A7843C000251ED /* FTHZInviteCodeVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 63AFBACF23A7843C000251ED /* FTHZInviteCodeVC.m */; };
		63AFBAD723B7C743000251ED /* FTHZHeziVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 63AFBAD523B7C743000251ED /* FTHZHeziVC.m */; };
		63AFBADB23B7CF64000251ED /* FTHZHeziModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 63AFBAD923B7CF64000251ED /* FTHZHeziModel.m */; };
		63AFBADF23B7D312000251ED /* FTHZInvitModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 63AFBADD23B7D312000251ED /* FTHZInvitModel.m */; };
		63AFBAE323B7EB62000251ED /* FTHZInviteShareVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 63AFBAE123B7EB62000251ED /* FTHZInviteShareVC.m */; };
		63AFBAEB23B99774000251ED /* HZPhotoSkipGroup.m in Sources */ = {isa = PBXBuildFile; fileRef = 63AFBAE923B99774000251ED /* HZPhotoSkipGroup.m */; };
		63B2646A24B70603005DF1C9 /* GameAudioPlayer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 63B2646624B70603005DF1C9 /* GameAudioPlayer.swift */; };
		63B2646B24B70603005DF1C9 /* README.md in Resources */ = {isa = PBXBuildFile; fileRef = 63B2646724B70603005DF1C9 /* README.md */; };
		63CD86D92475AC30001DA4FF /* Toast.swift in Sources */ = {isa = PBXBuildFile; fileRef = 63CD86D82475AC30001DA4FF /* Toast.swift */; };
		63D92904243A4D2700943130 /* momentDetailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 63D92903243A4D2700943130 /* momentDetailView.swift */; };
		63D92906243ADD5B00943130 /* String.swift in Sources */ = {isa = PBXBuildFile; fileRef = 63D92905243ADD5B00943130 /* String.swift */; };
		63D9290C243AFF0F00943130 /* UIImageView+Web.m in Sources */ = {isa = PBXBuildFile; fileRef = 63D92908243AFF0F00943130 /* UIImageView+Web.m */; };
		63D9290D243AFF0F00943130 /* UIImage+TColor.m in Sources */ = {isa = PBXBuildFile; fileRef = 63D92909243AFF0F00943130 /* UIImage+TColor.m */; };
		63D92913243E29C700943130 /* FZMomentLikeListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 63D92912243E29C700943130 /* FZMomentLikeListVC.swift */; };
		63D92915243E2C2D00943130 /* LikeUserCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 63D92914243E2C2D00943130 /* LikeUserCell.swift */; };
		63D92918243EEFA000943130 /* FZMyLikeListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 63D92917243EEFA000943130 /* FZMyLikeListVC.swift */; };
		63DC11542437A78600A1035A /* AttentionSegmentHeaderView.m in Sources */ = {isa = PBXBuildFile; fileRef = 63DC11522437A78600A1035A /* AttentionSegmentHeaderView.m */; };
		63DC11552437A78600A1035A /* AttentionSegmentView.m in Sources */ = {isa = PBXBuildFile; fileRef = 63DC11532437A78600A1035A /* AttentionSegmentView.m */; };
		63DC11582437A79C00A1035A /* AttentionSegmentViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 63DC11572437A79C00A1035A /* AttentionSegmentViewController.m */; };
		63FDD8B023BB33BC00817917 /* HQFlowView.m in Sources */ = {isa = PBXBuildFile; fileRef = 63FDD8AD23BB33BC00817917 /* HQFlowView.m */; };
		63FDD8B223BB33BC00817917 /* HQIndexBannerSubview.m in Sources */ = {isa = PBXBuildFile; fileRef = 63FDD8AE23BB33BC00817917 /* HQIndexBannerSubview.m */; };
		63FDD8B623BB369000817917 /* FTHZHerzDetailVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 63FDD8B423BB369000817917 /* FTHZHerzDetailVC.m */; };
		63FDD8BA23BB773800817917 /* FTHZHerzListCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 63FDD8B823BB773800817917 /* FTHZHerzListCell.m */; };
		650A086421BF9D9A0004A588 /* ChangeTagVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 650A086321BF9D9A0004A588 /* ChangeTagVC.m */; };
		650A086721BFE31F0004A588 /* WhaleHeaderView.m in Sources */ = {isa = PBXBuildFile; fileRef = 650A086621BFE31F0004A588 /* WhaleHeaderView.m */; };
		650A086F21C0B6040004A588 /* UserAffairListModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 650A086E21C0B6040004A588 /* UserAffairListModel.m */; };
		650A087521C0C4EC0004A588 /* 52yinsi.txt in Resources */ = {isa = PBXBuildFile; fileRef = 650A087421C0C4EC0004A588 /* 52yinsi.txt */; };
		650A087B21C0D67F0004A588 /* 52fuwu.txt in Resources */ = {isa = PBXBuildFile; fileRef = 650A087A21C0D67E0004A588 /* 52fuwu.txt */; };
		650A087E21C0E0AC0004A588 /* OtherUserInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 650A087D21C0E0AC0004A588 /* OtherUserInfo.m */; };
		650A088121C236FA0004A588 /* ChangeLikeTagVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 650A088021C236FA0004A588 /* ChangeLikeTagVC.m */; };
		650A088421C23E180004A588 /* ChangeSigelVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 650A088321C23E170004A588 /* ChangeSigelVC.m */; };
		650A088721C253AA0004A588 /* PaoPaoCollectionViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 650A088621C253AA0004A588 /* PaoPaoCollectionViewCell.m */; };
		650A088A21C28CF60004A588 /* ChangeUserVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 650A088921C28CF60004A588 /* ChangeUserVC.m */; };
		65168B3B21E5F4CF002CF1D3 /* UIImage+Wechat.m in Sources */ = {isa = PBXBuildFile; fileRef = 65168B3821E5F4CE002CF1D3 /* UIImage+Wechat.m */; };
		65168B3F21E63578002CF1D3 /* GetMessageNumberModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 65168B3E21E63578002CF1D3 /* GetMessageNumberModel.m */; };
		65168B4221E6F4CB002CF1D3 /* UserMessageNumberModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 65168B4121E6F4CB002CF1D3 /* UserMessageNumberModel.m */; };
		65168B4521E7AC94002CF1D3 /* UserStatsModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 65168B4421E7AC94002CF1D3 /* UserStatsModel.m */; };
		65168B4821E7B5D8002CF1D3 /* ActivityWebVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 65168B4721E7B5D8002CF1D3 /* ActivityWebVC.m */; };
		65168B8921E7D32C002CF1D3 /* ChangeStatsModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 65168B8821E7D32C002CF1D3 /* ChangeStatsModel.m */; };
		65168B8C21E84692002CF1D3 /* UserNotifications.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 65168B8A21E84112002CF1D3 /* UserNotifications.framework */; };
		6516CE1121A8205000703E14 /* AttentionLikeVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 6516CE1021A8205000703E14 /* AttentionLikeVC.m */; };
		6516CE1421A8207900703E14 /* AttentionCommentVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 6516CE1321A8207900703E14 /* AttentionCommentVC.m */; };
		6516CE1721A8212100703E14 /* AttentionLikeTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 6516CE1621A8212100703E14 /* AttentionLikeTableViewCell.m */; };
		6516CE1A21A8213900703E14 /* AttentionCommentTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 6516CE1921A8213900703E14 /* AttentionCommentTableViewCell.m */; };
		6516CE1D21A8269100703E14 /* AttentionHeaderView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6516CE1C21A8269100703E14 /* AttentionHeaderView.m */; };
		6516CE2021A83A7500703E14 /* DynamicLikeVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 6516CE1F21A83A7500703E14 /* DynamicLikeVC.m */; };
		6516CE2321A83A9600703E14 /* DynamicCommentVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 6516CE2221A83A9600703E14 /* DynamicCommentVC.m */; };
		6516CE2621A83AB300703E14 /* DynamicHeaderView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6516CE2521A83AB300703E14 /* DynamicHeaderView.m */; };
		652B107C218AAA2900D31B3F /* AttentionTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 652B107B218AAA2900D31B3F /* AttentionTableViewCell.m */; };
		652B107F218ACC8B00D31B3F /* WhaleTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 652B107E218ACC8B00D31B3F /* WhaleTableViewCell.m */; };
		652B1082218AD92300D31B3F /* DynamicDetailVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 652B1081218AD92300D31B3F /* DynamicDetailVC.m */; };
		652B1088218EA99D00D31B3F /* AffairDetailModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 652B1087218EA99D00D31B3F /* AffairDetailModel.m */; };
		6530FBB52184456700166841 /* ValidateCode.m in Sources */ = {isa = PBXBuildFile; fileRef = 6530FBB42184456700166841 /* ValidateCode.m */; };
		6530FBB8218489FE00166841 /* Compute.m in Sources */ = {isa = PBXBuildFile; fileRef = 6530FBB7218489FE00166841 /* Compute.m */; };
		6530FBC32185C3C600166841 /* CoreTelephony.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6530FBC22185C3C600166841 /* CoreTelephony.framework */; };
		6530FBC52185C3D800166841 /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6530FBC42185C3D800166841 /* SystemConfiguration.framework */; };
		6530FBC82185C68300166841 /* libc++.1.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 6530FBC62185C60700166841 /* libc++.1.dylib */; };
		6530FBCA2185C6A200166841 /* libz.1.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 6530FBC92185C6A200166841 /* libz.1.dylib */; };
		6530FBCC2185C73200166841 /* libsqlite3.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 6530FBCB2185C73200166841 /* libsqlite3.dylib */; };
		6530FBCF2187FA3000166841 /* DynamicVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 6530FBCE2187FA3000166841 /* DynamicVC.m */; };
		6530FBD5218858AB00166841 /* DynamicTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 6530FBD4218858AB00166841 /* DynamicTableViewCell.m */; };
		653136E2217F0C0A0008DD48 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 653136E1217F0C0A0008DD48 /* AppDelegate.m */; };
		653136F0217F0C0C0008DD48 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 653136EF217F0C0C0008DD48 /* main.m */; };
		65313716217F0FB90008DD48 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 65313715217F0FB90008DD48 /* Assets.xcassets */; };
		6533695621D9222F004E1782 /* DynamicSegmentView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6533695521D9222F004E1782 /* DynamicSegmentView.m */; };
		6533695921D9256A004E1782 /* DynamicSegmentHeaderView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6533695821D9256A004E1782 /* DynamicSegmentHeaderView.m */; };
		6533696321DB30D0004E1782 /* XHInputView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6533696221DB30D0004E1782 /* XHInputView.m */; };
		6533696621DC9B55004E1782 /* FindSegmentView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6533696521DC9B55004E1782 /* FindSegmentView.m */; };
		6533696921DC9BDB004E1782 /* FindSegmentHeaderView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6533696821DC9BDB004E1782 /* FindSegmentHeaderView.m */; };
		6533696F21DF36A9004E1782 /* IMNewAffairDetailModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6533696E21DF36A9004E1782 /* IMNewAffairDetailModel.m */; };
		6534E5F721C38247002DBEF6 /* AddAttentionModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6534E5F621C38247002DBEF6 /* AddAttentionModel.m */; };
		6534E5FA21C3825E002DBEF6 /* ReleaseAttentionModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6534E5F921C3825E002DBEF6 /* ReleaseAttentionModel.m */; };
		6534E60021C39BB9002DBEF6 /* MyAttentionModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6534E5FF21C39BB9002DBEF6 /* MyAttentionModel.m */; };
		6534E60321C39BC4002DBEF6 /* AttentionMeModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6534E60221C39BC4002DBEF6 /* AttentionMeModel.m */; };
		6534E60A21C3C726002DBEF6 /* AffairFollowListModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6534E60921C3C726002DBEF6 /* AffairFollowListModel.m */; };
		653D54D721D383440054B986 /* LGAudioPlayer.m in Sources */ = {isa = PBXBuildFile; fileRef = 653D54B921D383440054B986 /* LGAudioPlayer.m */; };
		653D54D821D383440054B986 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 653D54BC21D383440054B986 /* <EMAIL> */; };
		653D54D921D383440054B986 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 653D54BD21D383440054B986 /* <EMAIL> */; };
		653D54DA21D383440054B986 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 653D54BE21D383440054B986 /* <EMAIL> */; };
		653D54DB21D383440054B986 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 653D54BF21D383440054B986 /* <EMAIL> */; };
		653D54DC21D383440054B986 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 653D54C021D383440054B986 /* <EMAIL> */; };
		653D54DD21D383440054B986 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 653D54C121D383440054B986 /* <EMAIL> */; };
		653D54DE21D383440054B986 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 653D54C221D383440054B986 /* <EMAIL> */; };
		653D54DF21D383440054B986 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 653D54C321D383440054B986 /* <EMAIL> */; };
		653D54E021D383440054B986 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 653D54C421D383440054B986 /* <EMAIL> */; };
		653D54E121D383450054B986 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 653D54C521D383440054B986 /* <EMAIL> */; };
		653D54E221D383450054B986 /* LGSoundRecorder.m in Sources */ = {isa = PBXBuildFile; fileRef = 653D54C721D383440054B986 /* LGSoundRecorder.m */; };
		653D54E321D383450054B986 /* libopencore-amrwb.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 653D54D421D383440054B986 /* libopencore-amrwb.a */; };
		653D54E421D383450054B986 /* libopencore-amrnb.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 653D54D521D383440054B986 /* libopencore-amrnb.a */; };
		653D54E521D383450054B986 /* amrFileCodec.m in Sources */ = {isa = PBXBuildFile; fileRef = 653D54D621D383440054B986 /* amrFileCodec.m */; };
		653D54E821D38A270054B986 /* LGMessageModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 653D54E721D38A270054B986 /* LGMessageModel.m */; };
		653D54EB21D461340054B986 /* MessageChangeStatsModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 653D54EA21D461340054B986 /* MessageChangeStatsModel.m */; };
		653D54EE21D4A98F0054B986 /* InvitationCodeVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 653D54ED21D4A98F0054B986 /* InvitationCodeVC.m */; };
		653D54F121D4AF4C0054B986 /* InvitationCodeView.m in Sources */ = {isa = PBXBuildFile; fileRef = 653D54F021D4AF4C0054B986 /* InvitationCodeView.m */; };
		653D54F421D4BEB20054B986 /* LoadInvitationCodeModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 653D54F321D4BEB20054B986 /* LoadInvitationCodeModel.m */; };
		653D54F721D4D2F40054B986 /* InvitationStatusModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 653D54F621D4D2F40054B986 /* InvitationStatusModel.m */; };
		6547B55121A6898C005E7DA2 /* CertificateModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6547B55021A6898C005E7DA2 /* CertificateModel.m */; };
		6547B55621A7A9FE005E7DA2 /* AttentionDetailVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 6547B55521A7A9FE005E7DA2 /* AttentionDetailVC.m */; };
		6547B55921A7E176005E7DA2 /* WhaleDetailVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 6547B55821A7E176005E7DA2 /* WhaleDetailVC.m */; };
		6557451921CCD06C00855BE2 /* DeleteDynamicModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6557451821CCD06C00855BE2 /* DeleteDynamicModel.m */; };
		6557451C21CCD34D00855BE2 /* BlackUserModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6557451B21CCD34D00855BE2 /* BlackUserModel.m */; };
		6557452221CCDFC900855BE2 /* BlackUserVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 6557452121CCDFC900855BE2 /* BlackUserVC.m */; };
		6557452521CD152100855BE2 /* BlackUserTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 6557452421CD152100855BE2 /* BlackUserTableViewCell.m */; };
		6557452821CD172900855BE2 /* GetBlackUserModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6557452721CD172900855BE2 /* GetBlackUserModel.m */; };
		6557452B21CD22F000855BE2 /* DoOutBlackModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6557452A21CD22F000855BE2 /* DoOutBlackModel.m */; };
		6557452E21CF7CAC00855BE2 /* MessageNotificationModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6557452D21CF7CAC00855BE2 /* MessageNotificationModel.m */; };
		6557453121CF817600855BE2 /* MessageNotificationTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 6557453021CF817600855BE2 /* MessageNotificationTableViewCell.m */; };
		6557453421CFC0D000855BE2 /* MessageDynaficDetailVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 6557453321CFC0D000855BE2 /* MessageDynaficDetailVC.m */; };
		656328C321D27F7E006FED9C /* QALSDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 656328C221D27F7E006FED9C /* QALSDK.framework */; };
		656459C121E89FFC0089660C /* WhalePageVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 656459C021E89FFC0089660C /* WhalePageVC.m */; };
		656459C421E9AA5B0089660C /* UserdeviceTokenModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 656459C321E9AA5B0089660C /* UserdeviceTokenModel.m */; };
		656459C721E9C0590089660C /* CommenPushModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 656459C621E9C0590089660C /* CommenPushModel.m */; };
		656459CD21FC7E290089660C /* AttentionMePageModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 656459CC21FC7E290089660C /* AttentionMePageModel.m */; };
		656459D021FC7E450089660C /* MyAttentionPageModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 656459CF21FC7E450089660C /* MyAttentionPageModel.m */; };
		656459D321FF3D210089660C /* pageTageCollectionViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 656459D221FF3D210089660C /* pageTageCollectionViewCell.m */; };
		656459D621FFE57B0089660C /* AffairTagmodel.m in Sources */ = {isa = PBXBuildFile; fileRef = 656459D521FFE57B0089660C /* AffairTagmodel.m */; };
		656459D922006E110089660C /* TagAffairVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 656459D822006E110089660C /* TagAffairVC.m */; };
		656459DC220073510089660C /* AffairTagListModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 656459DB220073510089660C /* AffairTagListModel.m */; };
		656459DF22007EAF0089660C /* ClearBadgeModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 656459DE22007EAF0089660C /* ClearBadgeModel.m */; };
		657AC6A221CA54D500674FE2 /* IPStatusModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 657AC6A121CA54D500674FE2 /* IPStatusModel.m */; };
		657AC6A521CA54F600674FE2 /* POSTIPStatusModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 657AC6A421CA54F600674FE2 /* POSTIPStatusModel.m */; };
		657AC6A821CB3EE100674FE2 /* SettingTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 657AC6A721CB3EE100674FE2 /* SettingTableViewCell.m */; };
		657C9AA021CA482E00681717 /* MessageDetailVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 657C9A9F21CA482E00681717 /* MessageDetailVC.m */; };
		657E9B7421C93537009029E6 /* MsgDetailTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 657E9B7321C93537009029E6 /* MsgDetailTableViewCell.m */; };
		657E9B7721C9F21C009029E6 /* MJChiBaoZiFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 657E9B7521C9F21C009029E6 /* MJChiBaoZiFooter.m */; };
		657E9B7A21CA180F009029E6 /* ReportVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 657E9B7921CA180F009029E6 /* ReportVC.m */; };
		657E9B7D21CA21B5009029E6 /* ReportModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 657E9B7C21CA21B5009029E6 /* ReportModel.m */; };
		6581C9F7219A73DF00424564 /* SegmentView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6581C9F6219A73DF00424564 /* SegmentView.m */; };
		6581C9FA219A73F400424564 /* SegmentHeaderView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6581C9F9219A73F400424564 /* SegmentHeaderView.m */; };
		6581C9FD219A742C00424564 /* CenterTouchTableView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6581C9FB219A742C00424564 /* CenterTouchTableView.m */; };
		6581CA00219A94AD00424564 /* SegmentViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 6581C9FF219A94AD00424564 /* SegmentViewController.m */; };
		6581CA03219A960700424564 /* AttentionMeVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 6581CA02219A960700424564 /* AttentionMeVC.m */; };
		6581CA06219A962400424564 /* MyAttentionVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 6581CA05219A962400424564 /* MyAttentionVC.m */; };
		6581CA0C219A992500424564 /* MyDynamicVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 6581CA0B219A992500424564 /* MyDynamicVC.m */; };
		6581CA14219D176A00424564 /* LoginVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 6581CA13219D176A00424564 /* LoginVC.m */; };
		6581CA17219D18DE00424564 /* VerificationCodeVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 6581CA16219D18DE00424564 /* VerificationCodeVC.m */; };
		6581CA1A219D190700424564 /* GenderVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 6581CA19219D190700424564 /* GenderVC.m */; };
		6581CA1D219D192D00424564 /* InformationVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 6581CA1C219D192D00424564 /* InformationVC.m */; };
		6581CA21219D819E00424564 /* KingIdentifyingView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6581CA1F219D819D00424564 /* KingIdentifyingView.m */; };
		658CCB7521ABF6F4003A93CC /* CommonCodeModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 658CCB7421ABF6F4003A93CC /* CommonCodeModel.m */; };
		658CCB7821AC43A0003A93CC /* UserUserinfoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 658CCB7721AC43A0003A93CC /* UserUserinfoModel.m */; };
		658CCB7B21AC4E1E003A93CC /* RateAttributeModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 658CCB7A21AC4E1E003A93CC /* RateAttributeModel.m */; };
		658CCB7E21AD27D5003A93CC /* RateCalculateModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 658CCB7D21AD27D5003A93CC /* RateCalculateModel.m */; };
		659890FE2181B78500BF1327 /* NemoUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 659890FC2181B78500BF1327 /* NemoUtil.m */; };
		659891012181B7A600BF1327 /* NSDate+VTB.m in Sources */ = {isa = PBXBuildFile; fileRef = 659891002181B7A600BF1327 /* NSDate+VTB.m */; };
		659891042181BA3E00BF1327 /* CurrencyRootVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 659891032181BA3E00BF1327 /* CurrencyRootVC.m */; };
		659891072181BB2D00BF1327 /* UIImage+TintColor.m in Sources */ = {isa = PBXBuildFile; fileRef = 659891052181BB2D00BF1327 /* UIImage+TintColor.m */; };
		6598910A2181C44A00BF1327 /* CurrencyNavVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 659891092181C44A00BF1327 /* CurrencyNavVC.m */; };
		6598910D2181D8A100BF1327 /* FindVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 6598910C2181D8A100BF1327 /* FindVC.m */; };
		659891102181D8BD00BF1327 /* AttentionVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 6598910F2181D8BD00BF1327 /* AttentionVC.m */; };
		659891162181D8DB00BF1327 /* MineVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 659891152181D8DB00BF1327 /* MineVC.m */; };
		659980852181AA4300FB7EEA /* Http.m in Sources */ = {isa = PBXBuildFile; fileRef = 6599807D2181AA4200FB7EEA /* Http.m */; };
		659980862181AA4300FB7EEA /* BaseJsonModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6599807E2181AA4200FB7EEA /* BaseJsonModel.m */; };
		659980872181AA4300FB7EEA /* JNFHTTPManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 659980802181AA4300FB7EEA /* JNFHTTPManager.m */; };
		659980892181AA4300FB7EEA /* VitNetAPIClient.m in Sources */ = {isa = PBXBuildFile; fileRef = 659980842181AA4300FB7EEA /* VitNetAPIClient.m */; };
		6599808C2181AAC000FB7EEA /* NSString+PJR.m in Sources */ = {isa = PBXBuildFile; fileRef = 6599808A2181AAC000FB7EEA /* NSString+PJR.m */; };
		659980952181AF6B00FB7EEA /* FlatButton.m in Sources */ = {isa = PBXBuildFile; fileRef = 659980932181AF6B00FB7EEA /* FlatButton.m */; };
		659980962181AF6B00FB7EEA /* UITextView+YLTextView.m in Sources */ = {isa = PBXBuildFile; fileRef = 659980942181AF6B00FB7EEA /* UITextView+YLTextView.m */; };
		659B64DC21A280C600DC13B8 /* VTBBirthdayPicker.m in Sources */ = {isa = PBXBuildFile; fileRef = 659B64DA21A280C600DC13B8 /* VTBBirthdayPicker.m */; };
		659B64DF21A2826000DC13B8 /* UIView+Frame.m in Sources */ = {isa = PBXBuildFile; fileRef = 659B64DD21A2826000DC13B8 /* UIView+Frame.m */; };
		659B652021A2A97C00DC13B8 /* SNSCodeCountdownButton.m in Sources */ = {isa = PBXBuildFile; fileRef = 659B651F21A2A97C00DC13B8 /* SNSCodeCountdownButton.m */; };
		659B652321A3B64000DC13B8 /* UserHeaderView.m in Sources */ = {isa = PBXBuildFile; fileRef = 659B652221A3B64000DC13B8 /* UserHeaderView.m */; };
		659B652621A430EC00DC13B8 /* MyAttentionTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 659B652521A430EC00DC13B8 /* MyAttentionTableViewCell.m */; };
		659B652921A43B0F00DC13B8 /* AttentionMeTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 659B652821A43B0F00DC13B8 /* AttentionMeTableViewCell.m */; };
		659B652C21A4FB4400DC13B8 /* SettingVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 659B652B21A4FB4400DC13B8 /* SettingVC.m */; };
		659B653221A5202B00DC13B8 /* UserInformationVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 659B653121A5202B00DC13B8 /* UserInformationVC.m */; };
		659B653521A5512200DC13B8 /* UserInformationTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 659B653421A5512200DC13B8 /* UserInformationTableViewCell.m */; };
		659B9F8321C8E4C60040651F /* MessageTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 659B9F7F21C8E4C50040651F /* MessageTableViewCell.m */; };
		659B9F8421C8E4C60040651F /* IMUserInfoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 659B9F8121C8E4C60040651F /* IMUserInfoModel.m */; };
		65C0763421AE8BD200F5EB1F /* HUD.m in Sources */ = {isa = PBXBuildFile; fileRef = 65C0763321AE8BD200F5EB1F /* HUD.m */; };
		65C0763721AECE5F00F5EB1F /* ContentModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 65C0763621AECE5F00F5EB1F /* ContentModel.m */; };
		65C0763B21AF8B6F00F5EB1F /* MJChiBaoZiHeader.m in Sources */ = {isa = PBXBuildFile; fileRef = 65C0763A21AF8B6F00F5EB1F /* MJChiBaoZiHeader.m */; };
		65C0764121B0FDE600F5EB1F /* WXLoginModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 65C0764021B0FDE500F5EB1F /* WXLoginModel.m */; };
		65C0764421B11DC900F5EB1F /* GetUserinfoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 65C0764321B11DC900F5EB1F /* GetUserinfoModel.m */; };
		65C0764721B3B4C600F5EB1F /* WhaleListModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 65C0764621B3B4C600F5EB1F /* WhaleListModel.m */; };
		65C0764A21B434A600F5EB1F /* AffairLikesModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 65C0764921B434A600F5EB1F /* AffairLikesModel.m */; };
		65C0764D21B5276500F5EB1F /* DoLikeModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 65C0764C21B5276500F5EB1F /* DoLikeModel.m */; };
		65C0765021B57A6300F5EB1F /* AffairCommentsModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 65C0764F21B57A6300F5EB1F /* AffairCommentsModel.m */; };
		65C0765321B57FEE00F5EB1F /* DoCommentModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 65C0765221B57FEE00F5EB1F /* DoCommentModel.m */; };
		65C0765621B62BA400F5EB1F /* DyLikeTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 65C0765521B62BA400F5EB1F /* DyLikeTableViewCell.m */; };
		65C0765921B6517200F5EB1F /* DyCommentTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 65C0765821B6517200F5EB1F /* DyCommentTableViewCell.m */; };
		65C0765B21B6A83700F5EB1F /* libz.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 65C0765A21B6A83600F5EB1F /* libz.tbd */; };
		65C0766621BC88E100F5EB1F /* DJStatusBarHUD.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 65C0766421BC88E100F5EB1F /* DJStatusBarHUD.bundle */; };
		65C0766721BC88E100F5EB1F /* DJStatusBarHUD.m in Sources */ = {isa = PBXBuildFile; fileRef = 65C0766521BC88E100F5EB1F /* DJStatusBarHUD.m */; };
		65C0766A21BC88FA00F5EB1F /* WhaleVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 65C0766921BC88F900F5EB1F /* WhaleVC.m */; };
		65C0766D21BCAA8900F5EB1F /* ChangeNameVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 65C0766C21BCAA8900F5EB1F /* ChangeNameVC.m */; };
		65C0767021BCAAA800F5EB1F /* ChangeGenderVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 65C0766F21BCAAA800F5EB1F /* ChangeGenderVC.m */; };
		65DC0C3E21A9868E009C7673 /* ReleaseDynamicVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 65DC0C3D21A9868E009C7673 /* ReleaseDynamicVC.m */; };
		65DC0CCE21AAA114009C7673 /* HeziChouseOneVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 65DC0CCD21AAA114009C7673 /* HeziChouseOneVC.m */; };
		65DC0CD421AAA1AD009C7673 /* HeziChouseTwoVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 65DC0CD321AAA1AD009C7673 /* HeziChouseTwoVC.m */; };
		65DC0CD721AAA1C5009C7673 /* HeziChouseThreeVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 65DC0CD621AAA1C5009C7673 /* HeziChouseThreeVC.m */; };
		65DC0CDA21AACD63009C7673 /* HeziChouseCollectionViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 65DC0CD921AACD63009C7673 /* HeziChouseCollectionViewCell.m */; };
		65DC0CDF21AAD886009C7673 /* UICollectionViewLeftAlignedLayout.m in Sources */ = {isa = PBXBuildFile; fileRef = 65DC0CDD21AAD886009C7673 /* UICollectionViewLeftAlignedLayout.m */; };
		65DC0CE321ABE55E009C7673 /* HeziShareVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 65DC0CE221ABE55E009C7673 /* HeziShareVC.m */; };
		65F685B521898F23003D37F6 /* AffairListModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 65F685B421898F23003D37F6 /* AffairListModel.m */; };
		65F6864A2189A2F0003D37F6 /* HZWaitingView.m in Sources */ = {isa = PBXBuildFile; fileRef = 65F6863F2189A2F0003D37F6 /* HZWaitingView.m */; };
		65F6864B2189A2F0003D37F6 /* HZPhotoBrowser.m in Sources */ = {isa = PBXBuildFile; fileRef = 65F686412189A2F0003D37F6 /* HZPhotoBrowser.m */; };
		65F6864C2189A2F0003D37F6 /* HZPhotoGroup.m in Sources */ = {isa = PBXBuildFile; fileRef = 65F686442189A2F0003D37F6 /* HZPhotoGroup.m */; };
		65F6864E2189A2F0003D37F6 /* HZPhotoBrowser.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 65F686472189A2F0003D37F6 /* HZPhotoBrowser.bundle */; };
		65F6864F2189A2F0003D37F6 /* HZPhotoBrowserView.m in Sources */ = {isa = PBXBuildFile; fileRef = 65F686482189A2F0003D37F6 /* HZPhotoBrowserView.m */; };
		65FF4CB421C36F58008FAAC4 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 65FF4CB321C36F58008FAAC4 /* LaunchScreen.storyboard */; };
		8C2E412C22A763420045A50C /* FTHZStraitVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8C2E412B22A763420045A50C /* FTHZStraitVC.m */; };
		8C2E412F22A7A7400045A50C /* FTHZStraitModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 8C2E412E22A7A7400045A50C /* FTHZStraitModel.m */; };
		8C2E413322A7BDE80045A50C /* StraitListCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 8C2E413222A7BDE70045A50C /* StraitListCell.m */; };
		8C2E413A22A7E28E0045A50C /* UIImageView+AnimationCompletion.m in Sources */ = {isa = PBXBuildFile; fileRef = 8C2E413822A7E28E0045A50C /* UIImageView+AnimationCompletion.m */; };
		8C9F1D7C22BBACC40047DC08 /* FTHZChannelVoiceReplyVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8C9F1D7B22BBACC40047DC08 /* FTHZChannelVoiceReplyVC.m */; };
		8C9F1D7F22BBAEE70047DC08 /* FTHZChannelReplyModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 8C9F1D7E22BBAEE70047DC08 /* FTHZChannelReplyModel.m */; };
		8C9F1D8322BC7E750047DC08 /* FTHZChannelImgReplyVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8C9F1D8122BC7E750047DC08 /* FTHZChannelImgReplyVC.m */; };
		8C9F1D8822BCE4270047DC08 /* ChannelCommentCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 8C9F1D8622BCE4270047DC08 /* ChannelCommentCell.m */; };
		920E326922523D9A000AC41D /* NSAttributedString+BoundingRect.m in Sources */ = {isa = PBXBuildFile; fileRef = 920E326722523D9A000AC41D /* NSAttributedString+BoundingRect.m */; };
		921B651722282F0100D8E2DF /* FTHZInputMusicURLDialogController.m in Sources */ = {isa = PBXBuildFile; fileRef = 921B651622282F0100D8E2DF /* FTHZInputMusicURLDialogController.m */; };
		9230686D22531F050034DF02 /* FTHZFeedNormalCardView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9230686C22531F050034DF02 /* FTHZFeedNormalCardView.m */; };
		92306872225393A40034DF02 /* FTHZOceanFeedListViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 92306871225393A40034DF02 /* FTHZOceanFeedListViewController.m */; };
		92444E8A224A77380092BCF8 /* AppConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 92444E88224A77380092BCF8 /* AppConfig.m */; };
		924E5EEB222953A20002C231 /* FTHZDialogPresentationController.m in Sources */ = {isa = PBXBuildFile; fileRef = 924E5EEA222953A20002C231 /* FTHZDialogPresentationController.m */; };
		9254FF382249188D007D5569 /* FTHZNetworkManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9254FF372249188D007D5569 /* FTHZNetworkManager.m */; };
		925CAABC2250692C00E19218 /* FTHZViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 925CAABA2250692C00E19218 /* FTHZViewController.m */; };
		925CAAC222506CBB00E19218 /* FTHZTableView.m in Sources */ = {isa = PBXBuildFile; fileRef = 925CAAC022506CBB00E19218 /* FTHZTableView.m */; };
		925CAAC622506D4500E19218 /* FTHZListViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 925CAAC422506D4500E19218 /* FTHZListViewController.m */; };
		925CAACA225070F500E19218 /* FTHZListViewElement.m in Sources */ = {isa = PBXBuildFile; fileRef = 925CAAC8225070F500E19218 /* FTHZListViewElement.m */; };
		925CAAD022508EA100E19218 /* FTHZFeedListViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 925CAACE22508EA100E19218 /* FTHZFeedListViewController.m */; };
		925CAAD52250900000E19218 /* FTHZFeedCardContentView.m in Sources */ = {isa = PBXBuildFile; fileRef = 925CAAD32250900000E19218 /* FTHZFeedCardContentView.m */; };
		925CAAE32251063E00E19218 /* FTHZToolbarButton.m in Sources */ = {isa = PBXBuildFile; fileRef = 925CAAE12251063E00E19218 /* FTHZToolbarButton.m */; };
		92652817225994CE00A84B21 /* ErrorCode.m in Sources */ = {isa = PBXBuildFile; fileRef = 92652815225994CE00A84B21 /* ErrorCode.m */; };
		9265282022599A0A00A84B21 /* FTHZNetworkTask+OceanAPI.m in Sources */ = {isa = PBXBuildFile; fileRef = 9265281E22599A0A00A84B21 /* FTHZNetworkTask+OceanAPI.m */; };
		926528252259BAC300A84B21 /* FTHZUniversalBizParser.m in Sources */ = {isa = PBXBuildFile; fileRef = 926528232259BAC300A84B21 /* FTHZUniversalBizParser.m */; };
		926528292259C4C300A84B21 /* FTHZListDataParser.m in Sources */ = {isa = PBXBuildFile; fileRef = 926528272259C4C300A84B21 /* FTHZListDataParser.m */; };
		9265282E2259D18500A84B21 /* FTHZAccountManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9265282C2259D18500A84B21 /* FTHZAccountManager.m */; };
		926528322259FD5A00A84B21 /* FTHZNetworkTask+UserInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 926528302259FD5A00A84B21 /* FTHZNetworkTask+UserInfo.m */; };
		926528362259FEB500A84B21 /* FTHZObjectDataParser.m in Sources */ = {isa = PBXBuildFile; fileRef = 926528342259FEB500A84B21 /* FTHZObjectDataParser.m */; };
		9265283A225A37D200A84B21 /* FTHZGlobalConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 92652838225A37D200A84B21 /* FTHZGlobalConfig.m */; };
		9265283F225A458600A84B21 /* LoginBusiness.m in Sources */ = {isa = PBXBuildFile; fileRef = 9265283D225A458600A84B21 /* LoginBusiness.m */; };
		92652843225AD3F000A84B21 /* FTHZBusiness.m in Sources */ = {isa = PBXBuildFile; fileRef = 92652841225AD3F000A84B21 /* FTHZBusiness.m */; };
		92652848225AF98600A84B21 /* UIViewController+ViewHierarchy.m in Sources */ = {isa = PBXBuildFile; fileRef = 92652846225AF98600A84B21 /* UIViewController+ViewHierarchy.m */; };
		9265284D225B700000A84B21 /* DispatchQueue+Mark.m in Sources */ = {isa = PBXBuildFile; fileRef = 9265284B225B700000A84B21 /* DispatchQueue+Mark.m */; };
		926AF278225CCE4A003D0435 /* UIView+RAC.m in Sources */ = {isa = PBXBuildFile; fileRef = 926AF276225CCE4A003D0435 /* UIView+RAC.m */; };
		927CC26F222B76C000654DE3 /* FTHZMusicPlayer.m in Sources */ = {isa = PBXBuildFile; fileRef = 927CC26E222B76C000654DE3 /* FTHZMusicPlayer.m */; };
		928B5646226C061F0035BE45 /* FTHZNetworkTask+Topic.m in Sources */ = {isa = PBXBuildFile; fileRef = 928B5644226C061F0035BE45 /* FTHZNetworkTask+Topic.m */; };
		928B564A226C0AF50035BE45 /* TopicModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 928B5648226C0AF50035BE45 /* TopicModel.m */; };
		928B564E226C38960035BE45 /* TopicVCViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 928B564C226C38960035BE45 /* TopicVCViewController.m */; };
		928B5652226C44700035BE45 /* AttentionContainerViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 928B5650226C44700035BE45 /* AttentionContainerViewController.m */; };
		92B1F4EC22264D80004ADD4A /* FTHZMusicPlayerView.m in Sources */ = {isa = PBXBuildFile; fileRef = 92B1F4EB22264D80004ADD4A /* FTHZMusicPlayerView.m */; };
		92B1F4EF22280E26004ADD4A /* MusicInfoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 92B1F4EE22280E26004ADD4A /* MusicInfoModel.m */; };
		92B5C65522544216002ABA55 /* FTHZRefreshHeader.m in Sources */ = {isa = PBXBuildFile; fileRef = 92B5C65322544216002ABA55 /* FTHZRefreshHeader.m */; };
		92B5C6592254AF5F002ABA55 /* FTHZRefreshFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 92B5C6572254AF5F002ABA55 /* FTHZRefreshFooter.m */; };
		92BE1539222EBAD900380B3D /* FTHZUserConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 92BE1538222EBAD900380B3D /* FTHZUserConfig.m */; };
		92C4D0B5222E04AE00A58687 /* FTHZLocationManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 92C4D0B4222E04AE00A58687 /* FTHZLocationManager.m */; };
		92D4849B2261E56E00221A7C /* FeedBusiness.m in Sources */ = {isa = PBXBuildFile; fileRef = 92D484992261E56E00221A7C /* FeedBusiness.m */; };
		92D4849F2261E5BA00221A7C /* FTHZNetworkTask+Feed.m in Sources */ = {isa = PBXBuildFile; fileRef = 92D4849D2261E5BA00221A7C /* FTHZNetworkTask+Feed.m */; };
		92D484A42262079200221A7C /* FTHZCoordinator.m in Sources */ = {isa = PBXBuildFile; fileRef = 92D484A22262079200221A7C /* FTHZCoordinator.m */; };
		92D484A92262CD6C00221A7C /* FeedDetailViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 92D484A72262CD6C00221A7C /* FeedDetailViewController.m */; };
		92D484AF2262CE7300221A7C /* FTHZUpvoteUserRecordView.m in Sources */ = {isa = PBXBuildFile; fileRef = 92D484AD2262CE7300221A7C /* FTHZUpvoteUserRecordView.m */; };
		92D484B52262CED800221A7C /* FTHZAvatarView.m in Sources */ = {isa = PBXBuildFile; fileRef = 92D484B32262CED800221A7C /* FTHZAvatarView.m */; };
		92D484BA2262DFAD00221A7C /* UserTopLevelCommentRecordView.m in Sources */ = {isa = PBXBuildFile; fileRef = 92D484B82262DFAD00221A7C /* UserTopLevelCommentRecordView.m */; };
		92F1DBF322649FE1009C6737 /* FTHZNestSegmentListViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 92F1DBF122649FE1009C6737 /* FTHZNestSegmentListViewController.m */; };
		92F1DBF72264B777009C6737 /* FTHZSegmentView.m in Sources */ = {isa = PBXBuildFile; fileRef = 92F1DBF52264B777009C6737 /* FTHZSegmentView.m */; };
		92F1DBFC2266F9AE009C6737 /* FTHZPopMenuController.m in Sources */ = {isa = PBXBuildFile; fileRef = 92F1DBFA2266F9AE009C6737 /* FTHZPopMenuController.m */; };
		92F1DC012267F8EC009C6737 /* FTHZAlertDialog.m in Sources */ = {isa = PBXBuildFile; fileRef = 92F1DBFF2267F8EC009C6737 /* FTHZAlertDialog.m */; };
		9CDB1BC32B8094BD532EA65E /* Pods_FTHZ.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0AF71898A267C41AD9671FF2 /* Pods_FTHZ.framework */; };
		C5E9C60F21D26B0C0019D2AC /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C5E9C60E21D26B0C0019D2AC /* AVFoundation.framework */; };
		FC03DDAB2E0E9E3C005469DD /* LiuyanDetailTableCell.m in Sources */ = {isa = PBXBuildFile; fileRef = FC03DDAA2E0E9E3C005469DD /* LiuyanDetailTableCell.m */; };
		FC089B362E090916000DDB5D /* FTHZRadialMenuView.m in Sources */ = {isa = PBXBuildFile; fileRef = FC089B352E090916000DDB5D /* FTHZRadialMenuView.m */; };
		FC0E17152D76D0F4008430CF /* OperationsModel.m in Sources */ = {isa = PBXBuildFile; fileRef = FC0E17142D76D0F4008430CF /* OperationsModel.m */; };
		FC0E17182D76DA6C008430CF /* OperationTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = FC0E17172D76DA6C008430CF /* OperationTableViewCell.m */; };
		FC0E171A2D77E984008430CF /* FZOperationDetailVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = FC0E17192D77E984008430CF /* FZOperationDetailVC.swift */; };
		FC0E171C2D7822FB008430CF /* OperationDetailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = FC0E171B2D7822FB008430CF /* OperationDetailView.swift */; };
		FC0E17252D7AC2C9008430CF /* WaveView.m in Sources */ = {isa = PBXBuildFile; fileRef = FC0E17242D7AC2C9008430CF /* WaveView.m */; };
		FC0FB1482D8CFC2A0024E5B2 /* FTHZTabBarController.m in Sources */ = {isa = PBXBuildFile; fileRef = FC0FB1472D8CFC2A0024E5B2 /* FTHZTabBarController.m */; };
		FC0FB1592D8D3CA50024E5B2 /* DynamicTitleModel.m in Sources */ = {isa = PBXBuildFile; fileRef = FC0FB1582D8D3CA50024E5B2 /* DynamicTitleModel.m */; };
		FC0FB15F2D8D65770024E5B2 /* FZDynamicWebVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = FC0FB15E2D8D65770024E5B2 /* FZDynamicWebVC.swift */; };
		FC0FB1662D9553690024E5B2 /* FriendSelectionVC.m in Sources */ = {isa = PBXBuildFile; fileRef = FC0FB1652D9553690024E5B2 /* FriendSelectionVC.m */; };
		FC0FB1692D9556F60024E5B2 /* SearchFriendsModel.m in Sources */ = {isa = PBXBuildFile; fileRef = FC0FB1682D9556F60024E5B2 /* SearchFriendsModel.m */; };
		************************ /* AccountLoginVC.m in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* AccountLoginVC.m */; };
		************************ /* FZFollowVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* FZFollowVC.swift */; };
		************************ /* FZCocreationVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* FZCocreationVC.swift */; };
		************************ /* ShudongCardCell.m in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* ShudongCardCell.m */; };
		************************ /* ClearNotificationsModel.m in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* ClearNotificationsModel.m */; };
		************************ /* ShudongModel.m in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* ShudongModel.m */; };
		************************ /* ShudongTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* ShudongTableViewCell.m */; };
		************************ /* FTHZVideoPlayerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = FC79450E2D5C9D7F00CA6A6C /* FTHZVideoPlayerView.swift */; };
		************************ /* FTHZSimpleVideoPlayer.m in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* FTHZSimpleVideoPlayer.m */; };
		FC8C71E82D646FFC00651BD1 /* ChangePasswordModel.m in Sources */ = {isa = PBXBuildFile; fileRef = FC8C71E72D646FFC00651BD1 /* ChangePasswordModel.m */; };
		FC8C71EB2D64700900651BD1 /* ChangePasswordVC.m in Sources */ = {isa = PBXBuildFile; fileRef = FC8C71EA2D64700900651BD1 /* ChangePasswordVC.m */; };
		FC8C71EE2D6576B800651BD1 /* ChangeAccountVC.m in Sources */ = {isa = PBXBuildFile; fileRef = FC8C71ED2D6576B800651BD1 /* ChangeAccountVC.m */; };
		FC8C71F12D657A5500651BD1 /* ChangeAccountModel.m in Sources */ = {isa = PBXBuildFile; fileRef = FC8C71F02D657A5500651BD1 /* ChangeAccountModel.m */; };
		************************ /* LiuyanTimeSeparatorModel.m in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* LiuyanTimeSeparatorModel.m */; };
		************************ /* LiuyanTimeSeparatorCell.m in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* LiuyanTimeSeparatorCell.m */; };
		************************ /* RecommendSearchModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* RecommendSearchModel.swift */; };
		************************ /* HZPhotoGroupOld.m in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* HZPhotoGroupOld.m */; };
		************************ /* DescPopupView.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* DescPopupView.swift */; };
		FCA89E602A6E0CD200B79271 /* LiuyanVC.m in Sources */ = {isa = PBXBuildFile; fileRef = FCA89E5F2A6E0CD200B79271 /* LiuyanVC.m */; };
		FCA89E632A6FB75600B79271 /* LiuyanModel.m in Sources */ = {isa = PBXBuildFile; fileRef = FCA89E622A6FB75600B79271 /* LiuyanModel.m */; };
		FCA89E692A6FC4C100B79271 /* LiuyanListCell.m in Sources */ = {isa = PBXBuildFile; fileRef = FCA89E682A6FC4C100B79271 /* LiuyanListCell.m */; };
		FCA89E6C2A71035D00B79271 /* LiuyanDetailVC.m in Sources */ = {isa = PBXBuildFile; fileRef = FCA89E6B2A71035D00B79271 /* LiuyanDetailVC.m */; };
		FCAD95E42A77975F001E2D3A /* XGAuthCode.m in Sources */ = {isa = PBXBuildFile; fileRef = FCAD95E32A77975F001E2D3A /* XGAuthCode.m */; };
		FCAF832A2A667D8500B787B6 /* ShudongDetailVC.m in Sources */ = {isa = PBXBuildFile; fileRef = FCAF83292A667D8500B787B6 /* ShudongDetailVC.m */; };
		FCB2CA072DD58A1500E4F222 /* FTHZRequestEncryptor.m in Sources */ = {isa = PBXBuildFile; fileRef = FCB2CA062DD58A1500E4F222 /* FTHZRequestEncryptor.m */; };
		FCB2CA1B2DD70AEB00E4F222 /* TZImagePickerManager.m in Sources */ = {isa = PBXBuildFile; fileRef = FCB2CA192DD70AEB00E4F222 /* TZImagePickerManager.m */; };
		FCC5B2202A6A25D3001C65E5 /* ShudongDetailView.m in Sources */ = {isa = PBXBuildFile; fileRef = FCC5B21F2A6A25D3001C65E5 /* ShudongDetailView.m */; };
		FCC5B2232A6A5DBB001C65E5 /* CreateShudongVC.m in Sources */ = {isa = PBXBuildFile; fileRef = FCC5B2222A6A5DBB001C65E5 /* CreateShudongVC.m */; };
		FCDD07302D7EE8A5003B7FF4 /* VerifyCodeVC.m in Sources */ = {isa = PBXBuildFile; fileRef = FCDD072F2D7EE8A5003B7FF4 /* VerifyCodeVC.m */; };
		FCDD07332D7EE8B0003B7FF4 /* VerifyCodeModel.m in Sources */ = {isa = PBXBuildFile; fileRef = FCDD07322D7EE8B0003B7FF4 /* VerifyCodeModel.m */; };
		FCF3B4E82D59A5B10050133E /* LiuyanDetailView.m in Sources */ = {isa = PBXBuildFile; fileRef = FCF3B4E72D59A5B10050133E /* LiuyanDetailView.m */; };
		FCF3C3EA2A7B46DC00599FA4 /* verifyViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = FCF3C3E92A7B46DC00599FA4 /* verifyViewController.m */; };
		FCF3C3ED2A7B7A1F00599FA4 /* verifyModel.m in Sources */ = {isa = PBXBuildFile; fileRef = FCF3C3EC2A7B7A1F00599FA4 /* verifyModel.m */; };
		FCF3C3F32A7C959900599FA4 /* VerifyTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = FCF3C3F22A7C959900599FA4 /* VerifyTableViewCell.m */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		634014C3245759BD00961DE8 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		0AF71898A267C41AD9671FF2 /* Pods_FTHZ.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_FTHZ.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		1FDF9C03FC338A0029699403 /* Pods-FTHZ.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-FTHZ.debug.xcconfig"; path = "Target Support Files/Pods-FTHZ/Pods-FTHZ.debug.xcconfig"; sourceTree = "<group>"; };
		6302B4F024371FCA00FCB8C4 /* SearchCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SearchCell.swift; sourceTree = "<group>"; };
		6302B4F22437399800FCB8C4 /* UILabel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UILabel.swift; sourceTree = "<group>"; };
		630536A222A912D0005B3878 /* FTHZChannelListVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZChannelListVC.h; sourceTree = "<group>"; };
		630536A322A912D0005B3878 /* FTHZChannelListVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZChannelListVC.m; sourceTree = "<group>"; };
		630536A622AAC472005B3878 /* FTHZCreateChannelVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZCreateChannelVC.h; sourceTree = "<group>"; };
		630536A722AAC472005B3878 /* FTHZCreateChannelVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZCreateChannelVC.m; sourceTree = "<group>"; };
		630536AA22AF327D005B3878 /* FTHZChannelDetailVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZChannelDetailVC.h; sourceTree = "<group>"; };
		630536AB22AF327D005B3878 /* FTHZChannelDetailVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZChannelDetailVC.m; sourceTree = "<group>"; };
		6313C15D24BDEB3100385E9C /* CALayer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CALayer.swift; sourceTree = "<group>"; };
		6328EEAD248CE514009168A3 /* UINavigationController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UINavigationController.swift; sourceTree = "<group>"; };
		6328EED3249BC5C2009168A3 /* FZGiftVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FZGiftVC.swift; sourceTree = "<group>"; };
		6328EED524A11A18009168A3 /* GradientLabel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GradientLabel.swift; sourceTree = "<group>"; };
		6328EEDD24A27671009168A3 /* NSDate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NSDate.swift; sourceTree = "<group>"; };
		6328EEDF24AB14AE009168A3 /* SwipeScrollView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SwipeScrollView.swift; sourceTree = "<group>"; };
		6328EEE524B4232D009168A3 /* libsqlite3.0.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libsqlite3.0.tbd; path = usr/lib/libsqlite3.0.tbd; sourceTree = SDKROOT; };
		633486FF24477F990080D3F0 /* FZMessageVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FZMessageVC.swift; sourceTree = "<group>"; };
		6334B5712439EB13001C4F35 /* FZMomentVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FZMomentVC.swift; sourceTree = "<group>"; };
		634014BF2457571B00961DE8 /* GameAPI.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GameAPI.swift; sourceTree = "<group>"; };
		634014C42458AD8000961DE8 /* TLSSDK.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = TLSSDK.framework; sourceTree = "<group>"; };
		634E11A02441CDA600175027 /* LabelLayoutManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LabelLayoutManager.swift; sourceTree = "<group>"; };
		634E11A22441CDDE00175027 /* LabelWithLineSpace.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LabelWithLineSpace.swift; sourceTree = "<group>"; };
		634E11A82442473B00175027 /* FZTopicVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FZTopicVC.swift; sourceTree = "<group>"; };
		634E11AA2442480E00175027 /* TopicNavCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TopicNavCell.swift; sourceTree = "<group>"; };
		634E11AC24424FA900175027 /* TopicNavLayout.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TopicNavLayout.swift; sourceTree = "<group>"; };
		634E11AE2442DFEF00175027 /* FZHerzVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FZHerzVC.swift; sourceTree = "<group>"; };
		634E11B0244305A000175027 /* HerzCardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HerzCardView.swift; sourceTree = "<group>"; };
		634E11B224434FA500175027 /* StraitCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StraitCell.swift; sourceTree = "<group>"; };
		634E11B52444D82E00175027 /* TouchAreaButton.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TouchAreaButton.swift; sourceTree = "<group>"; };
		634E11BA2444EE7100175027 /* TNTextLayer.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = TNTextLayer.swift; sourceTree = "<group>"; };
		634E11BB2444EE7200175027 /* TNSlider.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = TNSlider.swift; sourceTree = "<group>"; };
		634E11BC2444EE7300175027 /* TNTrackLayer.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = TNTrackLayer.swift; sourceTree = "<group>"; };
		634E11BD2444EE7400175027 /* TNConstants.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = TNConstants.swift; sourceTree = "<group>"; };
		6355FCB7243F804A00224A7F /* MyLikedModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MyLikedModel.swift; sourceTree = "<group>"; };
		6355FCB9243F953900224A7F /* MyLikedCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MyLikedCell.swift; sourceTree = "<group>"; };
		6355FCBD243FA03300224A7F /* FZPlayVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FZPlayVC.swift; sourceTree = "<group>"; };
		6355FCC32440C54E00224A7F /* ZCycleView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZCycleView.swift; sourceTree = "<group>"; };
		6355FCC42440C54E00224A7F /* ZCycleViewCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZCycleViewCell.swift; sourceTree = "<group>"; };
		6355FCC52440C54E00224A7F /* ZCycleLayout.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZCycleLayout.swift; sourceTree = "<group>"; };
		6355FCC62440C54E00224A7F /* ZPageControl.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZPageControl.swift; sourceTree = "<group>"; };
		6355FCCD2440ED8A00224A7F /* FZCollectionMenu.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FZCollectionMenu.swift; sourceTree = "<group>"; };
		6355FCCF2440EEF600224A7F /* FZCollectionLayout.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FZCollectionLayout.swift; sourceTree = "<group>"; };
		6355FCD1244162FF00224A7F /* FZStraitVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FZStraitVC.swift; sourceTree = "<group>"; };
		6355FCD52441637200224A7F /* StriatNavLayout.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StriatNavLayout.swift; sourceTree = "<group>"; };
		6355FCD72441642300224A7F /* StraitNavCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StraitNavCell.swift; sourceTree = "<group>"; };
		6359326523FEA70700ABB2A1 /* Bugly.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = Bugly.framework; sourceTree = "<group>"; };
		635932692418114500ABB2A1 /* FTHZ_Bridging_Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZ_Bridging_Header.h; sourceTree = "<group>"; };
		6359326D2423EFEB00ABB2A1 /* FZMineVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FZMineVC.swift; sourceTree = "<group>"; };
		635932892425F12A00ABB2A1 /* FZConstants.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FZConstants.swift; sourceTree = "<group>"; };
		6359328C2425F19400ABB2A1 /* UIColor.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UIColor.swift; sourceTree = "<group>"; };
		6359328E242703B200ABB2A1 /* FZFindVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FZFindVC.swift; sourceTree = "<group>"; };
		6359329124271D8900ABB2A1 /* UIFont.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UIFont.swift; sourceTree = "<group>"; };
		63593294242729FD00ABB2A1 /* FZBaseVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FZBaseVC.swift; sourceTree = "<group>"; };
		635932962427481D00ABB2A1 /* UIImageView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UIImageView.swift; sourceTree = "<group>"; };
		6359329824274A5900ABB2A1 /* UIImage.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UIImage.swift; sourceTree = "<group>"; };
		6359329B242750A700ABB2A1 /* UIView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UIView.swift; sourceTree = "<group>"; };
		635932D4242A856D00ABB2A1 /* UIButton.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UIButton.swift; sourceTree = "<group>"; };
		635932D7242BC7CD00ABB2A1 /* FZSearchVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FZSearchVC.swift; sourceTree = "<group>"; };
		635932DB242CC34500ABB2A1 /* Codextended.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Codextended.swift; sourceTree = "<group>"; };
		635932DD242CC53600ABB2A1 /* FZNetWorkManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FZNetWorkManager.swift; sourceTree = "<group>"; };
		635932E4242D01DE00ABB2A1 /* MoyaConfig.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MoyaConfig.swift; sourceTree = "<group>"; };
		635932EA242D1BE700ABB2A1 /* NetWorkError.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NetWorkError.swift; sourceTree = "<group>"; };
		635932EC242D1C2A00ABB2A1 /* Response.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Response.swift; sourceTree = "<group>"; };
		635932EE242D1C5300ABB2A1 /* NetworkManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NetworkManager.swift; sourceTree = "<group>"; };
		635932F1242D1CBA00ABB2A1 /* FindAPI.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FindAPI.swift; sourceTree = "<group>"; };
		635932F3242D207100ABB2A1 /* SearchResModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SearchResModel.swift; sourceTree = "<group>"; };
		635932F5242D319A00ABB2A1 /* MomentModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MomentModel.swift; sourceTree = "<group>"; };
		635932F7242DCD2A00ABB2A1 /* FZDraftVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FZDraftVC.swift; sourceTree = "<group>"; };
		635932FA242DF2E100ABB2A1 /* ConfigurableTableViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ConfigurableTableViewController.h; sourceTree = "<group>"; };
		635932FB242DF2E200ABB2A1 /* CellConfigurator.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CellConfigurator.swift; sourceTree = "<group>"; };
		635932FC242DF2E300ABB2A1 /* Updatable.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Updatable.swift; sourceTree = "<group>"; };
		635932FD242DF2E300ABB2A1 /* ConfigurableTableViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ConfigurableTableViewController.swift; sourceTree = "<group>"; };
		63593302242DF3EE00ABB2A1 /* DraftCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DraftCell.swift; sourceTree = "<group>"; };
		63593308242EF6FD00ABB2A1 /* FZAddMusic.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FZAddMusic.swift; sourceTree = "<group>"; };
		6359330A242EFD5C00ABB2A1 /* CopyLabel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CopyLabel.swift; sourceTree = "<group>"; };
		6359330E242F552C00ABB2A1 /* NKCacheManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NKCacheManager.swift; sourceTree = "<group>"; };
		63593312242F615A00ABB2A1 /* FZKeyedArchiver.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FZKeyedArchiver.h; sourceTree = "<group>"; };
		63593313242F615A00ABB2A1 /* FZKeyedArchiver.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FZKeyedArchiver.m; sourceTree = "<group>"; };
		63593315242F621B00ABB2A1 /* FZDraftModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FZDraftModel.h; sourceTree = "<group>"; };
		63593316242F621B00ABB2A1 /* FZDraftModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FZDraftModel.m; sourceTree = "<group>"; };
		635933182432531C00ABB2A1 /* FZMyHomePageVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FZMyHomePageVC.swift; sourceTree = "<group>"; };
		6359331A2434779600ABB2A1 /* homepageHeadView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = homepageHeadView.swift; sourceTree = "<group>"; };
		6359331C2434A61800ABB2A1 /* otherUserHeadView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = otherUserHeadView.swift; sourceTree = "<group>"; };
		6372A1662465D00C00F998E1 /* ImportedGestures.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ImportedGestures.swift; sourceTree = "<group>"; };
		6372A1672465D00C00F998E1 /* AndroidGesturesImporter.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AndroidGesturesImporter.swift; sourceTree = "<group>"; };
		6372A1682465D00C00F998E1 /* PennyPincherGestureRecognizer.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PennyPincherGestureRecognizer.swift; sourceTree = "<group>"; };
		6372A1692465D00C00F998E1 /* PennyPincherTemplate.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PennyPincherTemplate.swift; sourceTree = "<group>"; };
		6372A16A2465D00C00F998E1 /* PennyPincher.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PennyPincher.swift; sourceTree = "<group>"; };
		6372A19A246C49B800F998E1 /* gestures */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xml; path = gestures; sourceTree = "<group>"; };
		6372A19C246DBB5F00F998E1 /* CGFloat.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CGFloat.swift; sourceTree = "<group>"; };
		637AE943244BE52E005ABD3B /* FZAttributeString.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FZAttributeString.swift; sourceTree = "<group>"; };
		63AB042F24D0A61D00BA3BE6 /* MaskTriangleView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MaskTriangleView.swift; sourceTree = "<group>"; };
		63AFBACE23A7843C000251ED /* FTHZInviteCodeVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZInviteCodeVC.h; sourceTree = "<group>"; };
		63AFBACF23A7843C000251ED /* FTHZInviteCodeVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZInviteCodeVC.m; sourceTree = "<group>"; };
		63AFBAD423B7C743000251ED /* FTHZHeziVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZHeziVC.h; sourceTree = "<group>"; };
		63AFBAD523B7C743000251ED /* FTHZHeziVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZHeziVC.m; sourceTree = "<group>"; };
		63AFBAD823B7CF64000251ED /* FTHZHeziModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZHeziModel.h; sourceTree = "<group>"; };
		63AFBAD923B7CF64000251ED /* FTHZHeziModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZHeziModel.m; sourceTree = "<group>"; };
		63AFBADC23B7D312000251ED /* FTHZInvitModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZInvitModel.h; sourceTree = "<group>"; };
		63AFBADD23B7D312000251ED /* FTHZInvitModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZInvitModel.m; sourceTree = "<group>"; };
		63AFBAE023B7EB62000251ED /* FTHZInviteShareVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZInviteShareVC.h; sourceTree = "<group>"; };
		63AFBAE123B7EB62000251ED /* FTHZInviteShareVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZInviteShareVC.m; sourceTree = "<group>"; };
		63AFBAE823B99774000251ED /* HZPhotoSkipGroup.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HZPhotoSkipGroup.h; sourceTree = "<group>"; };
		63AFBAE923B99774000251ED /* HZPhotoSkipGroup.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HZPhotoSkipGroup.m; sourceTree = "<group>"; };
		63B2646624B70603005DF1C9 /* GameAudioPlayer.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GameAudioPlayer.swift; sourceTree = "<group>"; };
		63B2646724B70603005DF1C9 /* README.md */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = net.daringfireball.markdown; path = README.md; sourceTree = "<group>"; };
		63CD86D82475AC30001DA4FF /* Toast.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Toast.swift; sourceTree = "<group>"; };
		63D92903243A4D2700943130 /* momentDetailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = momentDetailView.swift; sourceTree = "<group>"; };
		63D92905243ADD5B00943130 /* String.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = String.swift; sourceTree = "<group>"; };
		63D92908243AFF0F00943130 /* UIImageView+Web.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIImageView+Web.m"; sourceTree = "<group>"; };
		63D92909243AFF0F00943130 /* UIImage+TColor.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIImage+TColor.m"; sourceTree = "<group>"; };
		63D9290A243AFF0F00943130 /* UIImage+TColor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImage+TColor.h"; sourceTree = "<group>"; };
		63D9290B243AFF0F00943130 /* UIImageView+Web.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImageView+Web.h"; sourceTree = "<group>"; };
		63D92912243E29C700943130 /* FZMomentLikeListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FZMomentLikeListVC.swift; sourceTree = "<group>"; };
		63D92914243E2C2D00943130 /* LikeUserCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LikeUserCell.swift; sourceTree = "<group>"; };
		63D92917243EEFA000943130 /* FZMyLikeListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FZMyLikeListVC.swift; sourceTree = "<group>"; };
		63DC11502437A78600A1035A /* AttentionSegmentHeaderView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AttentionSegmentHeaderView.h; sourceTree = "<group>"; };
		63DC11512437A78600A1035A /* AttentionSegmentView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AttentionSegmentView.h; sourceTree = "<group>"; };
		63DC11522437A78600A1035A /* AttentionSegmentHeaderView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AttentionSegmentHeaderView.m; sourceTree = "<group>"; };
		63DC11532437A78600A1035A /* AttentionSegmentView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AttentionSegmentView.m; sourceTree = "<group>"; };
		63DC11562437A79C00A1035A /* AttentionSegmentViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AttentionSegmentViewController.h; sourceTree = "<group>"; };
		63DC11572437A79C00A1035A /* AttentionSegmentViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AttentionSegmentViewController.m; sourceTree = "<group>"; };
		63FDD8AB23BB33BB00817917 /* HQFlowView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HQFlowView.h; sourceTree = "<group>"; };
		63FDD8AC23BB33BB00817917 /* HQIndexBannerSubview.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HQIndexBannerSubview.h; sourceTree = "<group>"; };
		63FDD8AD23BB33BC00817917 /* HQFlowView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HQFlowView.m; sourceTree = "<group>"; };
		63FDD8AE23BB33BC00817917 /* HQIndexBannerSubview.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HQIndexBannerSubview.m; sourceTree = "<group>"; };
		63FDD8B323BB369000817917 /* FTHZHerzDetailVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZHerzDetailVC.h; sourceTree = "<group>"; };
		63FDD8B423BB369000817917 /* FTHZHerzDetailVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZHerzDetailVC.m; sourceTree = "<group>"; };
		63FDD8B723BB773800817917 /* FTHZHerzListCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZHerzListCell.h; sourceTree = "<group>"; };
		63FDD8B823BB773800817917 /* FTHZHerzListCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZHerzListCell.m; sourceTree = "<group>"; };
		650A086221BF9D9A0004A588 /* ChangeTagVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ChangeTagVC.h; sourceTree = "<group>"; };
		650A086321BF9D9A0004A588 /* ChangeTagVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ChangeTagVC.m; sourceTree = "<group>"; };
		650A086521BFE31F0004A588 /* WhaleHeaderView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WhaleHeaderView.h; sourceTree = "<group>"; };
		650A086621BFE31F0004A588 /* WhaleHeaderView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WhaleHeaderView.m; sourceTree = "<group>"; };
		650A086D21C0B6040004A588 /* UserAffairListModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UserAffairListModel.h; sourceTree = "<group>"; };
		650A086E21C0B6040004A588 /* UserAffairListModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UserAffairListModel.m; sourceTree = "<group>"; };
		650A087421C0C4EC0004A588 /* 52yinsi.txt */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = 52yinsi.txt; sourceTree = "<group>"; };
		650A087A21C0D67E0004A588 /* 52fuwu.txt */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = 52fuwu.txt; sourceTree = "<group>"; };
		650A087C21C0E0AC0004A588 /* OtherUserInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = OtherUserInfo.h; sourceTree = "<group>"; };
		650A087D21C0E0AC0004A588 /* OtherUserInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = OtherUserInfo.m; sourceTree = "<group>"; };
		650A087F21C236FA0004A588 /* ChangeLikeTagVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ChangeLikeTagVC.h; sourceTree = "<group>"; };
		650A088021C236FA0004A588 /* ChangeLikeTagVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ChangeLikeTagVC.m; sourceTree = "<group>"; };
		650A088221C23E170004A588 /* ChangeSigelVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ChangeSigelVC.h; sourceTree = "<group>"; };
		650A088321C23E170004A588 /* ChangeSigelVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ChangeSigelVC.m; sourceTree = "<group>"; };
		650A088521C253AA0004A588 /* PaoPaoCollectionViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PaoPaoCollectionViewCell.h; sourceTree = "<group>"; };
		650A088621C253AA0004A588 /* PaoPaoCollectionViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PaoPaoCollectionViewCell.m; sourceTree = "<group>"; };
		650A088821C28CF60004A588 /* ChangeUserVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ChangeUserVC.h; sourceTree = "<group>"; };
		650A088921C28CF60004A588 /* ChangeUserVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ChangeUserVC.m; sourceTree = "<group>"; };
		65168B3821E5F4CE002CF1D3 /* UIImage+Wechat.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIImage+Wechat.m"; sourceTree = "<group>"; };
		65168B3A21E5F4CE002CF1D3 /* UIImage+Wechat.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImage+Wechat.h"; sourceTree = "<group>"; };
		65168B3D21E63578002CF1D3 /* GetMessageNumberModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = GetMessageNumberModel.h; sourceTree = "<group>"; };
		65168B3E21E63578002CF1D3 /* GetMessageNumberModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = GetMessageNumberModel.m; sourceTree = "<group>"; };
		65168B4021E6F4CB002CF1D3 /* UserMessageNumberModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UserMessageNumberModel.h; sourceTree = "<group>"; };
		65168B4121E6F4CB002CF1D3 /* UserMessageNumberModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UserMessageNumberModel.m; sourceTree = "<group>"; };
		65168B4321E7AC94002CF1D3 /* UserStatsModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UserStatsModel.h; sourceTree = "<group>"; };
		65168B4421E7AC94002CF1D3 /* UserStatsModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UserStatsModel.m; sourceTree = "<group>"; };
		65168B4621E7B5D8002CF1D3 /* ActivityWebVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ActivityWebVC.h; sourceTree = "<group>"; };
		65168B4721E7B5D8002CF1D3 /* ActivityWebVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ActivityWebVC.m; sourceTree = "<group>"; };
		65168B8721E7D32C002CF1D3 /* ChangeStatsModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ChangeStatsModel.h; sourceTree = "<group>"; };
		65168B8821E7D32C002CF1D3 /* ChangeStatsModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ChangeStatsModel.m; sourceTree = "<group>"; };
		65168B8A21E84112002CF1D3 /* UserNotifications.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UserNotifications.framework; path = System/Library/Frameworks/UserNotifications.framework; sourceTree = SDKROOT; };
		6516CE0F21A8205000703E14 /* AttentionLikeVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AttentionLikeVC.h; sourceTree = "<group>"; };
		6516CE1021A8205000703E14 /* AttentionLikeVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AttentionLikeVC.m; sourceTree = "<group>"; };
		6516CE1221A8207900703E14 /* AttentionCommentVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AttentionCommentVC.h; sourceTree = "<group>"; };
		6516CE1321A8207900703E14 /* AttentionCommentVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AttentionCommentVC.m; sourceTree = "<group>"; };
		6516CE1521A8212100703E14 /* AttentionLikeTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AttentionLikeTableViewCell.h; sourceTree = "<group>"; };
		6516CE1621A8212100703E14 /* AttentionLikeTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AttentionLikeTableViewCell.m; sourceTree = "<group>"; };
		6516CE1821A8213900703E14 /* AttentionCommentTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AttentionCommentTableViewCell.h; sourceTree = "<group>"; };
		6516CE1921A8213900703E14 /* AttentionCommentTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AttentionCommentTableViewCell.m; sourceTree = "<group>"; };
		6516CE1B21A8269100703E14 /* AttentionHeaderView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AttentionHeaderView.h; sourceTree = "<group>"; };
		6516CE1C21A8269100703E14 /* AttentionHeaderView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AttentionHeaderView.m; sourceTree = "<group>"; };
		6516CE1E21A83A7500703E14 /* DynamicLikeVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DynamicLikeVC.h; sourceTree = "<group>"; };
		6516CE1F21A83A7500703E14 /* DynamicLikeVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DynamicLikeVC.m; sourceTree = "<group>"; };
		6516CE2121A83A9600703E14 /* DynamicCommentVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DynamicCommentVC.h; sourceTree = "<group>"; };
		6516CE2221A83A9600703E14 /* DynamicCommentVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DynamicCommentVC.m; sourceTree = "<group>"; };
		6516CE2421A83AB300703E14 /* DynamicHeaderView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DynamicHeaderView.h; sourceTree = "<group>"; };
		6516CE2521A83AB300703E14 /* DynamicHeaderView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DynamicHeaderView.m; sourceTree = "<group>"; };
		652B107A218AAA2900D31B3F /* AttentionTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AttentionTableViewCell.h; sourceTree = "<group>"; };
		652B107B218AAA2900D31B3F /* AttentionTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AttentionTableViewCell.m; sourceTree = "<group>"; };
		652B107D218ACC8B00D31B3F /* WhaleTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WhaleTableViewCell.h; sourceTree = "<group>"; };
		652B107E218ACC8B00D31B3F /* WhaleTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WhaleTableViewCell.m; sourceTree = "<group>"; };
		652B1080218AD92300D31B3F /* DynamicDetailVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DynamicDetailVC.h; sourceTree = "<group>"; };
		652B1081218AD92300D31B3F /* DynamicDetailVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DynamicDetailVC.m; sourceTree = "<group>"; };
		652B1086218EA99D00D31B3F /* AffairDetailModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AffairDetailModel.h; sourceTree = "<group>"; };
		652B1087218EA99D00D31B3F /* AffairDetailModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AffairDetailModel.m; sourceTree = "<group>"; };
		6530FBB32184456700166841 /* ValidateCode.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ValidateCode.h; sourceTree = "<group>"; };
		6530FBB42184456700166841 /* ValidateCode.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ValidateCode.m; sourceTree = "<group>"; };
		6530FBB6218489FE00166841 /* Compute.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = Compute.h; sourceTree = "<group>"; };
		6530FBB7218489FE00166841 /* Compute.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = Compute.m; sourceTree = "<group>"; };
		6530FBC22185C3C600166841 /* CoreTelephony.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreTelephony.framework; path = System/Library/Frameworks/CoreTelephony.framework; sourceTree = SDKROOT; };
		6530FBC42185C3D800166841 /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = System/Library/Frameworks/SystemConfiguration.framework; sourceTree = SDKROOT; };
		6530FBC62185C60700166841 /* libc++.1.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = "libc++.1.dylib"; path = "../../../../usr/lib/libc++.1.dylib"; sourceTree = "<group>"; };
		6530FBC92185C6A200166841 /* libz.1.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libz.1.dylib; path = ../../../../usr/lib/libz.1.dylib; sourceTree = "<group>"; };
		6530FBCB2185C73200166841 /* libsqlite3.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libsqlite3.dylib; path = ../../../../usr/lib/libsqlite3.dylib; sourceTree = "<group>"; };
		6530FBCD2187FA3000166841 /* DynamicVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DynamicVC.h; sourceTree = "<group>"; };
		6530FBCE2187FA3000166841 /* DynamicVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DynamicVC.m; sourceTree = "<group>"; };
		6530FBD3218858AB00166841 /* DynamicTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DynamicTableViewCell.h; sourceTree = "<group>"; };
		6530FBD4218858AB00166841 /* DynamicTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DynamicTableViewCell.m; sourceTree = "<group>"; };
		653136DD217F0C0A0008DD48 /* 52hz.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = 52hz.app; sourceTree = BUILT_PRODUCTS_DIR; };
		653136E0217F0C0A0008DD48 /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		653136E1217F0C0A0008DD48 /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		653136EE217F0C0C0008DD48 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		653136EF217F0C0C0008DD48 /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		65313715217F0FB90008DD48 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		6533695421D9222F004E1782 /* DynamicSegmentView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DynamicSegmentView.h; sourceTree = "<group>"; };
		6533695521D9222F004E1782 /* DynamicSegmentView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DynamicSegmentView.m; sourceTree = "<group>"; };
		6533695721D9256A004E1782 /* DynamicSegmentHeaderView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DynamicSegmentHeaderView.h; sourceTree = "<group>"; };
		6533695821D9256A004E1782 /* DynamicSegmentHeaderView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DynamicSegmentHeaderView.m; sourceTree = "<group>"; };
		6533696121DB30D0004E1782 /* XHInputView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XHInputView.h; sourceTree = "<group>"; };
		6533696221DB30D0004E1782 /* XHInputView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = XHInputView.m; sourceTree = "<group>"; };
		6533696421DC9B55004E1782 /* FindSegmentView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FindSegmentView.h; sourceTree = "<group>"; };
		6533696521DC9B55004E1782 /* FindSegmentView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FindSegmentView.m; sourceTree = "<group>"; };
		6533696721DC9BDB004E1782 /* FindSegmentHeaderView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FindSegmentHeaderView.h; sourceTree = "<group>"; };
		6533696821DC9BDB004E1782 /* FindSegmentHeaderView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FindSegmentHeaderView.m; sourceTree = "<group>"; };
		6533696D21DF36A9004E1782 /* IMNewAffairDetailModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = IMNewAffairDetailModel.h; sourceTree = "<group>"; };
		6533696E21DF36A9004E1782 /* IMNewAffairDetailModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = IMNewAffairDetailModel.m; sourceTree = "<group>"; };
		6534E5F521C38247002DBEF6 /* AddAttentionModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AddAttentionModel.h; sourceTree = "<group>"; };
		6534E5F621C38247002DBEF6 /* AddAttentionModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AddAttentionModel.m; sourceTree = "<group>"; };
		6534E5F821C3825E002DBEF6 /* ReleaseAttentionModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ReleaseAttentionModel.h; sourceTree = "<group>"; };
		6534E5F921C3825E002DBEF6 /* ReleaseAttentionModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ReleaseAttentionModel.m; sourceTree = "<group>"; };
		6534E5FE21C39BB9002DBEF6 /* MyAttentionModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MyAttentionModel.h; sourceTree = "<group>"; };
		6534E5FF21C39BB9002DBEF6 /* MyAttentionModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MyAttentionModel.m; sourceTree = "<group>"; };
		6534E60121C39BC4002DBEF6 /* AttentionMeModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AttentionMeModel.h; sourceTree = "<group>"; };
		6534E60221C39BC4002DBEF6 /* AttentionMeModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AttentionMeModel.m; sourceTree = "<group>"; };
		6534E60821C3C726002DBEF6 /* AffairFollowListModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AffairFollowListModel.h; sourceTree = "<group>"; };
		6534E60921C3C726002DBEF6 /* AffairFollowListModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AffairFollowListModel.m; sourceTree = "<group>"; };
		653D54B721D383440054B986 /* LGAudioKit.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = LGAudioKit.h; sourceTree = "<group>"; };
		653D54B921D383440054B986 /* LGAudioPlayer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = LGAudioPlayer.m; sourceTree = "<group>"; };
		653D54BA21D383440054B986 /* LGAudioPlayer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = LGAudioPlayer.h; sourceTree = "<group>"; };
		653D54BC21D383440054B986 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		653D54BD21D383440054B986 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		653D54BE21D383440054B986 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		653D54BF21D383440054B986 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		653D54C021D383440054B986 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		653D54C121D383440054B986 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		653D54C221D383440054B986 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		653D54C321D383440054B986 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		653D54C421D383440054B986 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		653D54C521D383440054B986 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		653D54C721D383440054B986 /* LGSoundRecorder.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = LGSoundRecorder.m; sourceTree = "<group>"; };
		653D54C821D383440054B986 /* LGSoundRecorder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = LGSoundRecorder.h; sourceTree = "<group>"; };
		653D54CB21D383440054B986 /* amrFileCodec.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = amrFileCodec.h; sourceTree = "<group>"; };
		653D54CE21D383440054B986 /* if_rom.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = if_rom.h; sourceTree = "<group>"; };
		653D54CF21D383440054B986 /* dec_if.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = dec_if.h; sourceTree = "<group>"; };
		653D54D121D383440054B986 /* interf_dec.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = interf_dec.h; sourceTree = "<group>"; };
		653D54D221D383440054B986 /* interf_enc.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = interf_enc.h; sourceTree = "<group>"; };
		653D54D421D383440054B986 /* libopencore-amrwb.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = "libopencore-amrwb.a"; sourceTree = "<group>"; };
		653D54D521D383440054B986 /* libopencore-amrnb.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = "libopencore-amrnb.a"; sourceTree = "<group>"; };
		653D54D621D383440054B986 /* amrFileCodec.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = amrFileCodec.m; sourceTree = "<group>"; };
		653D54E621D38A260054B986 /* LGMessageModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = LGMessageModel.h; sourceTree = "<group>"; };
		653D54E721D38A270054B986 /* LGMessageModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = LGMessageModel.m; sourceTree = "<group>"; };
		653D54E921D461340054B986 /* MessageChangeStatsModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MessageChangeStatsModel.h; sourceTree = "<group>"; };
		653D54EA21D461340054B986 /* MessageChangeStatsModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MessageChangeStatsModel.m; sourceTree = "<group>"; };
		653D54EC21D4A98F0054B986 /* InvitationCodeVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = InvitationCodeVC.h; sourceTree = "<group>"; };
		653D54ED21D4A98F0054B986 /* InvitationCodeVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = InvitationCodeVC.m; sourceTree = "<group>"; };
		653D54EF21D4AF4C0054B986 /* InvitationCodeView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = InvitationCodeView.h; sourceTree = "<group>"; };
		653D54F021D4AF4C0054B986 /* InvitationCodeView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = InvitationCodeView.m; sourceTree = "<group>"; };
		653D54F221D4BEB20054B986 /* LoadInvitationCodeModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = LoadInvitationCodeModel.h; sourceTree = "<group>"; };
		653D54F321D4BEB20054B986 /* LoadInvitationCodeModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = LoadInvitationCodeModel.m; sourceTree = "<group>"; };
		653D54F521D4D2F40054B986 /* InvitationStatusModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = InvitationStatusModel.h; sourceTree = "<group>"; };
		653D54F621D4D2F40054B986 /* InvitationStatusModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = InvitationStatusModel.m; sourceTree = "<group>"; };
		653D54F821D8A5150054B986 /* CoreLocation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreLocation.framework; path = System/Library/Frameworks/CoreLocation.framework; sourceTree = SDKROOT; };
		6547B54F21A6898C005E7DA2 /* CertificateModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CertificateModel.h; sourceTree = "<group>"; };
		6547B55021A6898C005E7DA2 /* CertificateModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CertificateModel.m; sourceTree = "<group>"; };
		6547B55421A7A9FE005E7DA2 /* AttentionDetailVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AttentionDetailVC.h; sourceTree = "<group>"; };
		6547B55521A7A9FE005E7DA2 /* AttentionDetailVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AttentionDetailVC.m; sourceTree = "<group>"; };
		6547B55721A7E176005E7DA2 /* WhaleDetailVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WhaleDetailVC.h; sourceTree = "<group>"; };
		6547B55821A7E176005E7DA2 /* WhaleDetailVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WhaleDetailVC.m; sourceTree = "<group>"; };
		6557451721CCD06C00855BE2 /* DeleteDynamicModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DeleteDynamicModel.h; sourceTree = "<group>"; };
		6557451821CCD06C00855BE2 /* DeleteDynamicModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DeleteDynamicModel.m; sourceTree = "<group>"; };
		6557451A21CCD34D00855BE2 /* BlackUserModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BlackUserModel.h; sourceTree = "<group>"; };
		6557451B21CCD34D00855BE2 /* BlackUserModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BlackUserModel.m; sourceTree = "<group>"; };
		6557452021CCDFC900855BE2 /* BlackUserVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BlackUserVC.h; sourceTree = "<group>"; };
		6557452121CCDFC900855BE2 /* BlackUserVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BlackUserVC.m; sourceTree = "<group>"; };
		6557452321CD152100855BE2 /* BlackUserTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BlackUserTableViewCell.h; sourceTree = "<group>"; };
		6557452421CD152100855BE2 /* BlackUserTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BlackUserTableViewCell.m; sourceTree = "<group>"; };
		6557452621CD172900855BE2 /* GetBlackUserModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = GetBlackUserModel.h; sourceTree = "<group>"; };
		6557452721CD172900855BE2 /* GetBlackUserModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = GetBlackUserModel.m; sourceTree = "<group>"; };
		6557452921CD22F000855BE2 /* DoOutBlackModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DoOutBlackModel.h; sourceTree = "<group>"; };
		6557452A21CD22F000855BE2 /* DoOutBlackModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DoOutBlackModel.m; sourceTree = "<group>"; };
		6557452C21CF7CAC00855BE2 /* MessageNotificationModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MessageNotificationModel.h; sourceTree = "<group>"; };
		6557452D21CF7CAC00855BE2 /* MessageNotificationModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MessageNotificationModel.m; sourceTree = "<group>"; };
		6557452F21CF817600855BE2 /* MessageNotificationTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MessageNotificationTableViewCell.h; sourceTree = "<group>"; };
		6557453021CF817600855BE2 /* MessageNotificationTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MessageNotificationTableViewCell.m; sourceTree = "<group>"; };
		6557453221CFC0D000855BE2 /* MessageDynaficDetailVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MessageDynaficDetailVC.h; sourceTree = "<group>"; };
		6557453321CFC0D000855BE2 /* MessageDynaficDetailVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MessageDynaficDetailVC.m; sourceTree = "<group>"; };
		6557453521D0BB3A00855BE2 /* FTHZ.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = FTHZ.entitlements; sourceTree = "<group>"; };
		656328C221D27F7E006FED9C /* QALSDK.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = QALSDK.framework; sourceTree = "<group>"; };
		656459BF21E89FFC0089660C /* WhalePageVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WhalePageVC.h; sourceTree = "<group>"; };
		656459C021E89FFC0089660C /* WhalePageVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WhalePageVC.m; sourceTree = "<group>"; };
		656459C221E9AA5B0089660C /* UserdeviceTokenModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UserdeviceTokenModel.h; sourceTree = "<group>"; };
		656459C321E9AA5B0089660C /* UserdeviceTokenModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UserdeviceTokenModel.m; sourceTree = "<group>"; };
		656459C521E9C0590089660C /* CommenPushModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CommenPushModel.h; sourceTree = "<group>"; };
		656459C621E9C0590089660C /* CommenPushModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CommenPushModel.m; sourceTree = "<group>"; };
		656459CB21FC7E290089660C /* AttentionMePageModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AttentionMePageModel.h; sourceTree = "<group>"; };
		656459CC21FC7E290089660C /* AttentionMePageModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AttentionMePageModel.m; sourceTree = "<group>"; };
		656459CE21FC7E450089660C /* MyAttentionPageModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MyAttentionPageModel.h; sourceTree = "<group>"; };
		656459CF21FC7E450089660C /* MyAttentionPageModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MyAttentionPageModel.m; sourceTree = "<group>"; };
		656459D121FF3D210089660C /* pageTageCollectionViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = pageTageCollectionViewCell.h; sourceTree = "<group>"; };
		656459D221FF3D210089660C /* pageTageCollectionViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = pageTageCollectionViewCell.m; sourceTree = "<group>"; };
		656459D421FFE57A0089660C /* AffairTagmodel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AffairTagmodel.h; sourceTree = "<group>"; };
		656459D521FFE57B0089660C /* AffairTagmodel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AffairTagmodel.m; sourceTree = "<group>"; };
		656459D722006E110089660C /* TagAffairVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TagAffairVC.h; sourceTree = "<group>"; };
		656459D822006E110089660C /* TagAffairVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TagAffairVC.m; sourceTree = "<group>"; };
		656459DA220073510089660C /* AffairTagListModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AffairTagListModel.h; sourceTree = "<group>"; };
		656459DB220073510089660C /* AffairTagListModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AffairTagListModel.m; sourceTree = "<group>"; };
		656459DD22007EAF0089660C /* ClearBadgeModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ClearBadgeModel.h; sourceTree = "<group>"; };
		656459DE22007EAF0089660C /* ClearBadgeModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ClearBadgeModel.m; sourceTree = "<group>"; };
		657AC6A021CA54D500674FE2 /* IPStatusModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = IPStatusModel.h; sourceTree = "<group>"; };
		657AC6A121CA54D500674FE2 /* IPStatusModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = IPStatusModel.m; sourceTree = "<group>"; };
		657AC6A321CA54F600674FE2 /* POSTIPStatusModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = POSTIPStatusModel.h; sourceTree = "<group>"; };
		657AC6A421CA54F600674FE2 /* POSTIPStatusModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = POSTIPStatusModel.m; sourceTree = "<group>"; };
		657AC6A621CB3EE100674FE2 /* SettingTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SettingTableViewCell.h; sourceTree = "<group>"; };
		657AC6A721CB3EE100674FE2 /* SettingTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SettingTableViewCell.m; sourceTree = "<group>"; };
		657C9A9E21CA482E00681717 /* MessageDetailVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MessageDetailVC.h; sourceTree = "<group>"; };
		657C9A9F21CA482E00681717 /* MessageDetailVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MessageDetailVC.m; sourceTree = "<group>"; };
		657E9B7221C93537009029E6 /* MsgDetailTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MsgDetailTableViewCell.h; sourceTree = "<group>"; };
		657E9B7321C93537009029E6 /* MsgDetailTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MsgDetailTableViewCell.m; sourceTree = "<group>"; };
		657E9B7521C9F21C009029E6 /* MJChiBaoZiFooter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MJChiBaoZiFooter.m; sourceTree = "<group>"; };
		657E9B7621C9F21C009029E6 /* MJChiBaoZiFooter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MJChiBaoZiFooter.h; sourceTree = "<group>"; };
		657E9B7821CA180F009029E6 /* ReportVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ReportVC.h; sourceTree = "<group>"; };
		657E9B7921CA180F009029E6 /* ReportVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ReportVC.m; sourceTree = "<group>"; };
		657E9B7B21CA21B5009029E6 /* ReportModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ReportModel.h; sourceTree = "<group>"; };
		657E9B7C21CA21B5009029E6 /* ReportModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ReportModel.m; sourceTree = "<group>"; };
		6581C9F5219A73DF00424564 /* SegmentView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SegmentView.h; sourceTree = "<group>"; };
		6581C9F6219A73DF00424564 /* SegmentView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SegmentView.m; sourceTree = "<group>"; };
		6581C9F8219A73F400424564 /* SegmentHeaderView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SegmentHeaderView.h; sourceTree = "<group>"; };
		6581C9F9219A73F400424564 /* SegmentHeaderView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SegmentHeaderView.m; sourceTree = "<group>"; };
		6581C9FB219A742C00424564 /* CenterTouchTableView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CenterTouchTableView.m; sourceTree = "<group>"; };
		6581C9FC219A742C00424564 /* CenterTouchTableView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CenterTouchTableView.h; sourceTree = "<group>"; };
		6581C9FE219A94AD00424564 /* SegmentViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SegmentViewController.h; sourceTree = "<group>"; };
		6581C9FF219A94AD00424564 /* SegmentViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SegmentViewController.m; sourceTree = "<group>"; };
		6581CA01219A960700424564 /* AttentionMeVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AttentionMeVC.h; sourceTree = "<group>"; };
		6581CA02219A960700424564 /* AttentionMeVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AttentionMeVC.m; sourceTree = "<group>"; };
		6581CA04219A962400424564 /* MyAttentionVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MyAttentionVC.h; sourceTree = "<group>"; };
		6581CA05219A962400424564 /* MyAttentionVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MyAttentionVC.m; sourceTree = "<group>"; };
		6581CA0A219A992500424564 /* MyDynamicVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MyDynamicVC.h; sourceTree = "<group>"; };
		6581CA0B219A992500424564 /* MyDynamicVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MyDynamicVC.m; sourceTree = "<group>"; };
		6581CA12219D176A00424564 /* LoginVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = LoginVC.h; sourceTree = "<group>"; };
		6581CA13219D176A00424564 /* LoginVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = LoginVC.m; sourceTree = "<group>"; };
		6581CA15219D18DE00424564 /* VerificationCodeVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = VerificationCodeVC.h; sourceTree = "<group>"; };
		6581CA16219D18DE00424564 /* VerificationCodeVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = VerificationCodeVC.m; sourceTree = "<group>"; };
		6581CA18219D190700424564 /* GenderVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = GenderVC.h; sourceTree = "<group>"; };
		6581CA19219D190700424564 /* GenderVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = GenderVC.m; sourceTree = "<group>"; };
		6581CA1B219D192D00424564 /* InformationVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = InformationVC.h; sourceTree = "<group>"; };
		6581CA1C219D192D00424564 /* InformationVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = InformationVC.m; sourceTree = "<group>"; };
		6581CA1F219D819D00424564 /* KingIdentifyingView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = KingIdentifyingView.m; sourceTree = "<group>"; };
		6581CA20219D819D00424564 /* KingIdentifyingView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = KingIdentifyingView.h; sourceTree = "<group>"; };
		658CCB7321ABF6F4003A93CC /* CommonCodeModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CommonCodeModel.h; sourceTree = "<group>"; };
		658CCB7421ABF6F4003A93CC /* CommonCodeModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CommonCodeModel.m; sourceTree = "<group>"; };
		658CCB7621AC43A0003A93CC /* UserUserinfoModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UserUserinfoModel.h; sourceTree = "<group>"; };
		658CCB7721AC43A0003A93CC /* UserUserinfoModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UserUserinfoModel.m; sourceTree = "<group>"; };
		658CCB7921AC4E1E003A93CC /* RateAttributeModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RateAttributeModel.h; sourceTree = "<group>"; };
		658CCB7A21AC4E1E003A93CC /* RateAttributeModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RateAttributeModel.m; sourceTree = "<group>"; };
		658CCB7C21AD27D5003A93CC /* RateCalculateModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RateCalculateModel.h; sourceTree = "<group>"; };
		658CCB7D21AD27D5003A93CC /* RateCalculateModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RateCalculateModel.m; sourceTree = "<group>"; };
		659890FC2181B78500BF1327 /* NemoUtil.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = NemoUtil.m; sourceTree = "<group>"; };
		659890FD2181B78500BF1327 /* NemoUtil.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = NemoUtil.h; sourceTree = "<group>"; };
		659890FF2181B7A600BF1327 /* NSDate+VTB.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSDate+VTB.h"; sourceTree = "<group>"; };
		659891002181B7A600BF1327 /* NSDate+VTB.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSDate+VTB.m"; sourceTree = "<group>"; };
		659891022181BA3E00BF1327 /* CurrencyRootVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CurrencyRootVC.h; sourceTree = "<group>"; };
		659891032181BA3E00BF1327 /* CurrencyRootVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CurrencyRootVC.m; sourceTree = "<group>"; };
		659891052181BB2D00BF1327 /* UIImage+TintColor.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIImage+TintColor.m"; sourceTree = "<group>"; };
		659891062181BB2D00BF1327 /* UIImage+TintColor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImage+TintColor.h"; sourceTree = "<group>"; };
		659891082181C44A00BF1327 /* CurrencyNavVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CurrencyNavVC.h; sourceTree = "<group>"; };
		659891092181C44A00BF1327 /* CurrencyNavVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CurrencyNavVC.m; sourceTree = "<group>"; };
		6598910B2181D8A100BF1327 /* FindVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FindVC.h; sourceTree = "<group>"; };
		6598910C2181D8A100BF1327 /* FindVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FindVC.m; sourceTree = "<group>"; };
		6598910E2181D8BD00BF1327 /* AttentionVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AttentionVC.h; sourceTree = "<group>"; };
		6598910F2181D8BD00BF1327 /* AttentionVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AttentionVC.m; sourceTree = "<group>"; };
		659891112181D8CE00BF1327 /* MessageVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MessageVC.h; sourceTree = "<group>"; };
		659891122181D8CE00BF1327 /* MessageVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MessageVC.m; sourceTree = "<group>"; };
		659891142181D8DB00BF1327 /* MineVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MineVC.h; sourceTree = "<group>"; };
		659891152181D8DB00BF1327 /* MineVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MineVC.m; sourceTree = "<group>"; };
		659980752181987200FB7EEA /* PrefixHeader.pch */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PrefixHeader.pch; sourceTree = "<group>"; };
		65998076218198C200FB7EEA /* UrlLink.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UrlLink.h; sourceTree = "<group>"; };
		65998077218198D600FB7EEA /* Constants.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = Constants.h; sourceTree = "<group>"; };
		659980792181991600FB7EEA /* Common_AppStore.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = Common_AppStore.h; sourceTree = "<group>"; };
		6599807A2181993100FB7EEA /* Common_Test.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = Common_Test.h; sourceTree = "<group>"; };
		6599807B2181AA4200FB7EEA /* Http.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Http.h; sourceTree = "<group>"; };
		6599807C2181AA4200FB7EEA /* VitNetAPIClient.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VitNetAPIClient.h; sourceTree = "<group>"; };
		6599807D2181AA4200FB7EEA /* Http.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = Http.m; sourceTree = "<group>"; };
		6599807E2181AA4200FB7EEA /* BaseJsonModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BaseJsonModel.m; sourceTree = "<group>"; };
		6599807F2181AA4200FB7EEA /* JNFHTTPManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JNFHTTPManager.h; sourceTree = "<group>"; };
		659980802181AA4300FB7EEA /* JNFHTTPManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JNFHTTPManager.m; sourceTree = "<group>"; };
		659980832181AA4300FB7EEA /* BaseJsonModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BaseJsonModel.h; sourceTree = "<group>"; };
		659980842181AA4300FB7EEA /* VitNetAPIClient.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VitNetAPIClient.m; sourceTree = "<group>"; };
		6599808A2181AAC000FB7EEA /* NSString+PJR.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSString+PJR.m"; sourceTree = "<group>"; };
		6599808B2181AAC000FB7EEA /* NSString+PJR.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSString+PJR.h"; sourceTree = "<group>"; };
		659980912181AF6B00FB7EEA /* UITextView+YLTextView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UITextView+YLTextView.h"; sourceTree = "<group>"; };
		659980922181AF6B00FB7EEA /* FlatButton.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FlatButton.h; sourceTree = "<group>"; };
		659980932181AF6B00FB7EEA /* FlatButton.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FlatButton.m; sourceTree = "<group>"; };
		659980942181AF6B00FB7EEA /* UITextView+YLTextView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UITextView+YLTextView.m"; sourceTree = "<group>"; };
		659B64DA21A280C600DC13B8 /* VTBBirthdayPicker.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VTBBirthdayPicker.m; sourceTree = "<group>"; };
		659B64DB21A280C600DC13B8 /* VTBBirthdayPicker.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VTBBirthdayPicker.h; sourceTree = "<group>"; };
		659B64DD21A2826000DC13B8 /* UIView+Frame.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIView+Frame.m"; sourceTree = "<group>"; };
		659B64DE21A2826000DC13B8 /* UIView+Frame.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIView+Frame.h"; sourceTree = "<group>"; };
		659B651E21A2A97C00DC13B8 /* SNSCodeCountdownButton.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SNSCodeCountdownButton.h; sourceTree = "<group>"; };
		659B651F21A2A97C00DC13B8 /* SNSCodeCountdownButton.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SNSCodeCountdownButton.m; sourceTree = "<group>"; };
		659B652121A3B64000DC13B8 /* UserHeaderView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UserHeaderView.h; sourceTree = "<group>"; };
		659B652221A3B64000DC13B8 /* UserHeaderView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UserHeaderView.m; sourceTree = "<group>"; };
		659B652421A430EC00DC13B8 /* MyAttentionTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MyAttentionTableViewCell.h; sourceTree = "<group>"; };
		659B652521A430EC00DC13B8 /* MyAttentionTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MyAttentionTableViewCell.m; sourceTree = "<group>"; };
		659B652721A43B0F00DC13B8 /* AttentionMeTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AttentionMeTableViewCell.h; sourceTree = "<group>"; };
		659B652821A43B0F00DC13B8 /* AttentionMeTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AttentionMeTableViewCell.m; sourceTree = "<group>"; };
		659B652A21A4FB4400DC13B8 /* SettingVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SettingVC.h; sourceTree = "<group>"; };
		659B652B21A4FB4400DC13B8 /* SettingVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SettingVC.m; sourceTree = "<group>"; };
		659B653021A5202B00DC13B8 /* UserInformationVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UserInformationVC.h; sourceTree = "<group>"; };
		659B653121A5202B00DC13B8 /* UserInformationVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UserInformationVC.m; sourceTree = "<group>"; };
		659B653321A5512200DC13B8 /* UserInformationTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UserInformationTableViewCell.h; sourceTree = "<group>"; };
		659B653421A5512200DC13B8 /* UserInformationTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UserInformationTableViewCell.m; sourceTree = "<group>"; };
		659B9F7E21C8E4C50040651F /* MessageTableViewCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MessageTableViewCell.h; sourceTree = "<group>"; };
		659B9F7F21C8E4C50040651F /* MessageTableViewCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MessageTableViewCell.m; sourceTree = "<group>"; };
		659B9F8121C8E4C60040651F /* IMUserInfoModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IMUserInfoModel.m; sourceTree = "<group>"; };
		659B9F8221C8E4C60040651F /* IMUserInfoModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IMUserInfoModel.h; sourceTree = "<group>"; };
		65C0763221AE8BD200F5EB1F /* HUD.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HUD.h; sourceTree = "<group>"; };
		65C0763321AE8BD200F5EB1F /* HUD.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HUD.m; sourceTree = "<group>"; };
		65C0763521AECE5F00F5EB1F /* ContentModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ContentModel.h; sourceTree = "<group>"; };
		65C0763621AECE5F00F5EB1F /* ContentModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ContentModel.m; sourceTree = "<group>"; };
		65C0763921AF8B6F00F5EB1F /* MJChiBaoZiHeader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MJChiBaoZiHeader.h; sourceTree = "<group>"; };
		65C0763A21AF8B6F00F5EB1F /* MJChiBaoZiHeader.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MJChiBaoZiHeader.m; sourceTree = "<group>"; };
		65C0763F21B0FDE500F5EB1F /* WXLoginModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WXLoginModel.h; sourceTree = "<group>"; };
		65C0764021B0FDE500F5EB1F /* WXLoginModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WXLoginModel.m; sourceTree = "<group>"; };
		65C0764221B11DC900F5EB1F /* GetUserinfoModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = GetUserinfoModel.h; sourceTree = "<group>"; };
		65C0764321B11DC900F5EB1F /* GetUserinfoModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = GetUserinfoModel.m; sourceTree = "<group>"; };
		65C0764521B3B4C600F5EB1F /* WhaleListModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WhaleListModel.h; sourceTree = "<group>"; };
		65C0764621B3B4C600F5EB1F /* WhaleListModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WhaleListModel.m; sourceTree = "<group>"; };
		65C0764821B434A600F5EB1F /* AffairLikesModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AffairLikesModel.h; sourceTree = "<group>"; };
		65C0764921B434A600F5EB1F /* AffairLikesModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AffairLikesModel.m; sourceTree = "<group>"; };
		65C0764B21B5276500F5EB1F /* DoLikeModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DoLikeModel.h; sourceTree = "<group>"; };
		65C0764C21B5276500F5EB1F /* DoLikeModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DoLikeModel.m; sourceTree = "<group>"; };
		65C0764E21B57A6300F5EB1F /* AffairCommentsModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AffairCommentsModel.h; sourceTree = "<group>"; };
		65C0764F21B57A6300F5EB1F /* AffairCommentsModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AffairCommentsModel.m; sourceTree = "<group>"; };
		65C0765121B57FEE00F5EB1F /* DoCommentModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DoCommentModel.h; sourceTree = "<group>"; };
		65C0765221B57FEE00F5EB1F /* DoCommentModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DoCommentModel.m; sourceTree = "<group>"; };
		65C0765421B62BA400F5EB1F /* DyLikeTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DyLikeTableViewCell.h; sourceTree = "<group>"; };
		65C0765521B62BA400F5EB1F /* DyLikeTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DyLikeTableViewCell.m; sourceTree = "<group>"; };
		65C0765721B6517200F5EB1F /* DyCommentTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DyCommentTableViewCell.h; sourceTree = "<group>"; };
		65C0765821B6517200F5EB1F /* DyCommentTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DyCommentTableViewCell.m; sourceTree = "<group>"; };
		65C0765A21B6A83600F5EB1F /* libz.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libz.tbd; path = usr/lib/libz.tbd; sourceTree = SDKROOT; };
		65C0766321BC88E100F5EB1F /* DJStatusBarHUD.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DJStatusBarHUD.h; sourceTree = "<group>"; };
		65C0766421BC88E100F5EB1F /* DJStatusBarHUD.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = DJStatusBarHUD.bundle; sourceTree = "<group>"; };
		65C0766521BC88E100F5EB1F /* DJStatusBarHUD.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DJStatusBarHUD.m; sourceTree = "<group>"; };
		65C0766821BC88F900F5EB1F /* WhaleVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WhaleVC.h; sourceTree = "<group>"; };
		65C0766921BC88F900F5EB1F /* WhaleVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = WhaleVC.m; sourceTree = "<group>"; };
		65C0766B21BCAA8900F5EB1F /* ChangeNameVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ChangeNameVC.h; sourceTree = "<group>"; };
		65C0766C21BCAA8900F5EB1F /* ChangeNameVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ChangeNameVC.m; sourceTree = "<group>"; };
		65C0766E21BCAAA800F5EB1F /* ChangeGenderVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ChangeGenderVC.h; sourceTree = "<group>"; };
		65C0766F21BCAAA800F5EB1F /* ChangeGenderVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ChangeGenderVC.m; sourceTree = "<group>"; };
		65DC0C3C21A9868E009C7673 /* ReleaseDynamicVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ReleaseDynamicVC.h; sourceTree = "<group>"; };
		65DC0C3D21A9868E009C7673 /* ReleaseDynamicVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ReleaseDynamicVC.m; sourceTree = "<group>"; };
		65DC0CCC21AAA114009C7673 /* HeziChouseOneVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HeziChouseOneVC.h; sourceTree = "<group>"; };
		65DC0CCD21AAA114009C7673 /* HeziChouseOneVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HeziChouseOneVC.m; sourceTree = "<group>"; };
		65DC0CD221AAA1AD009C7673 /* HeziChouseTwoVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HeziChouseTwoVC.h; sourceTree = "<group>"; };
		65DC0CD321AAA1AD009C7673 /* HeziChouseTwoVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HeziChouseTwoVC.m; sourceTree = "<group>"; };
		65DC0CD521AAA1C5009C7673 /* HeziChouseThreeVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HeziChouseThreeVC.h; sourceTree = "<group>"; };
		65DC0CD621AAA1C5009C7673 /* HeziChouseThreeVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HeziChouseThreeVC.m; sourceTree = "<group>"; };
		65DC0CD821AACD63009C7673 /* HeziChouseCollectionViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HeziChouseCollectionViewCell.h; sourceTree = "<group>"; };
		65DC0CD921AACD63009C7673 /* HeziChouseCollectionViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HeziChouseCollectionViewCell.m; sourceTree = "<group>"; };
		65DC0CDC21AAD886009C7673 /* UICollectionViewLeftAlignedLayout.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UICollectionViewLeftAlignedLayout.h; sourceTree = "<group>"; };
		65DC0CDD21AAD886009C7673 /* UICollectionViewLeftAlignedLayout.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UICollectionViewLeftAlignedLayout.m; sourceTree = "<group>"; };
		65DC0CDE21AAD886009C7673 /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		65DC0CE121ABE55E009C7673 /* HeziShareVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HeziShareVC.h; sourceTree = "<group>"; };
		65DC0CE221ABE55E009C7673 /* HeziShareVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HeziShareVC.m; sourceTree = "<group>"; };
		65F685B321898F23003D37F6 /* AffairListModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AffairListModel.h; sourceTree = "<group>"; };
		65F685B421898F23003D37F6 /* AffairListModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AffairListModel.m; sourceTree = "<group>"; };
		65F6863E2189A2F0003D37F6 /* HZPhotoGroup.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HZPhotoGroup.h; sourceTree = "<group>"; };
		65F6863F2189A2F0003D37F6 /* HZWaitingView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HZWaitingView.m; sourceTree = "<group>"; };
		65F686412189A2F0003D37F6 /* HZPhotoBrowser.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HZPhotoBrowser.m; sourceTree = "<group>"; };
		65F686422189A2F0003D37F6 /* HZPhotoBrowserView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HZPhotoBrowserView.h; sourceTree = "<group>"; };
		65F686442189A2F0003D37F6 /* HZPhotoGroup.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HZPhotoGroup.m; sourceTree = "<group>"; };
		65F686452189A2F0003D37F6 /* HZWaitingView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HZWaitingView.h; sourceTree = "<group>"; };
		65F686472189A2F0003D37F6 /* HZPhotoBrowser.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = HZPhotoBrowser.bundle; sourceTree = "<group>"; };
		65F686482189A2F0003D37F6 /* HZPhotoBrowserView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HZPhotoBrowserView.m; sourceTree = "<group>"; };
		65F686492189A2F0003D37F6 /* HZPhotoBrowser.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HZPhotoBrowser.h; sourceTree = "<group>"; };
		65F686502189A6EF003D37F6 /* HZPhotoBrowserConfig.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HZPhotoBrowserConfig.h; sourceTree = "<group>"; };
		65FF4CB321C36F58008FAAC4 /* LaunchScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = LaunchScreen.storyboard; sourceTree = "<group>"; };
		696D73862297CA6300BEB77D /* libTalkingData.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = libTalkingData.a; sourceTree = "<group>"; };
		696D73872297CA6300BEB77D /* TalkingData.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TalkingData.h; sourceTree = "<group>"; };
		696D738A2297CAEB00BEB77D /* AdSupport.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AdSupport.framework; path = System/Library/Frameworks/AdSupport.framework; sourceTree = SDKROOT; };
		696D738C2297CB0900BEB77D /* CoreMotion.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMotion.framework; path = System/Library/Frameworks/CoreMotion.framework; sourceTree = SDKROOT; };
		696D738E2297CB1200BEB77D /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
		696D73902297CB2B00BEB77D /* libc++.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++.tbd"; path = "usr/lib/libc++.tbd"; sourceTree = SDKROOT; };
		8C2E412A22A763420045A50C /* FTHZStraitVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZStraitVC.h; sourceTree = "<group>"; };
		8C2E412B22A763420045A50C /* FTHZStraitVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZStraitVC.m; sourceTree = "<group>"; };
		8C2E412D22A7A7400045A50C /* FTHZStraitModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZStraitModel.h; sourceTree = "<group>"; };
		8C2E412E22A7A7400045A50C /* FTHZStraitModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZStraitModel.m; sourceTree = "<group>"; };
		8C2E413122A7BDE70045A50C /* StraitListCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = StraitListCell.h; sourceTree = "<group>"; };
		8C2E413222A7BDE70045A50C /* StraitListCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = StraitListCell.m; sourceTree = "<group>"; };
		8C2E413722A7E28E0045A50C /* UIImageView+AnimationCompletion.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImageView+AnimationCompletion.h"; sourceTree = "<group>"; };
		8C2E413822A7E28E0045A50C /* UIImageView+AnimationCompletion.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImageView+AnimationCompletion.m"; sourceTree = "<group>"; };
		8C9F1D7A22BBACC40047DC08 /* FTHZChannelVoiceReplyVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZChannelVoiceReplyVC.h; sourceTree = "<group>"; };
		8C9F1D7B22BBACC40047DC08 /* FTHZChannelVoiceReplyVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZChannelVoiceReplyVC.m; sourceTree = "<group>"; };
		8C9F1D7D22BBAEE70047DC08 /* FTHZChannelReplyModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZChannelReplyModel.h; sourceTree = "<group>"; };
		8C9F1D7E22BBAEE70047DC08 /* FTHZChannelReplyModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZChannelReplyModel.m; sourceTree = "<group>"; };
		8C9F1D8022BC7E750047DC08 /* FTHZChannelImgReplyVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZChannelImgReplyVC.h; sourceTree = "<group>"; };
		8C9F1D8122BC7E750047DC08 /* FTHZChannelImgReplyVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZChannelImgReplyVC.m; sourceTree = "<group>"; };
		8C9F1D8522BCE4270047DC08 /* ChannelCommentCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ChannelCommentCell.h; sourceTree = "<group>"; };
		8C9F1D8622BCE4270047DC08 /* ChannelCommentCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ChannelCommentCell.m; sourceTree = "<group>"; };
		920E326622523D9A000AC41D /* NSAttributedString+BoundingRect.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSAttributedString+BoundingRect.h"; sourceTree = "<group>"; };
		920E326722523D9A000AC41D /* NSAttributedString+BoundingRect.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSAttributedString+BoundingRect.m"; sourceTree = "<group>"; };
		921B651522282F0100D8E2DF /* FTHZInputMusicURLDialogController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZInputMusicURLDialogController.h; sourceTree = "<group>"; };
		921B651622282F0100D8E2DF /* FTHZInputMusicURLDialogController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZInputMusicURLDialogController.m; sourceTree = "<group>"; };
		9230686B22531F050034DF02 /* FTHZFeedNormalCardView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZFeedNormalCardView.h; sourceTree = "<group>"; };
		9230686C22531F050034DF02 /* FTHZFeedNormalCardView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZFeedNormalCardView.m; sourceTree = "<group>"; };
		92306870225393A40034DF02 /* FTHZOceanFeedListViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZOceanFeedListViewController.h; sourceTree = "<group>"; };
		92306871225393A40034DF02 /* FTHZOceanFeedListViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZOceanFeedListViewController.m; sourceTree = "<group>"; };
		92444E87224A77380092BCF8 /* AppConfig.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppConfig.h; sourceTree = "<group>"; };
		92444E88224A77380092BCF8 /* AppConfig.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppConfig.m; sourceTree = "<group>"; };
		924E5EE9222953A20002C231 /* FTHZDialogPresentationController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FTHZDialogPresentationController.h; sourceTree = "<group>"; };
		924E5EEA222953A20002C231 /* FTHZDialogPresentationController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FTHZDialogPresentationController.m; sourceTree = "<group>"; };
		9253B102223C881F00798AE9 /* Config-Dev-Debug.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; path = "Config-Dev-Debug.xcconfig"; sourceTree = "<group>"; };
		9253B103223C882800798AE9 /* Config-Release-Debug.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; path = "Config-Release-Debug.xcconfig"; sourceTree = "<group>"; };
		9253B104223C88BB00798AE9 /* Config-Dev-Release.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; path = "Config-Dev-Release.xcconfig"; sourceTree = "<group>"; };
		9253B105223C88C600798AE9 /* Config-Release-Release.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; path = "Config-Release-Release.xcconfig"; sourceTree = "<group>"; };
		9254FF362249188D007D5569 /* FTHZNetworkManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZNetworkManager.h; sourceTree = "<group>"; };
		9254FF372249188D007D5569 /* FTHZNetworkManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZNetworkManager.m; sourceTree = "<group>"; };
		9254FF3A2249D1D1007D5569 /* AppConfig.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; path = AppConfig.xcconfig; sourceTree = "<group>"; };
		925CAAB92250692C00E19218 /* FTHZViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZViewController.h; sourceTree = "<group>"; };
		925CAABA2250692C00E19218 /* FTHZViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZViewController.m; sourceTree = "<group>"; };
		925CAABF22506CBB00E19218 /* FTHZTableView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZTableView.h; sourceTree = "<group>"; };
		925CAAC022506CBB00E19218 /* FTHZTableView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZTableView.m; sourceTree = "<group>"; };
		925CAAC322506D4500E19218 /* FTHZListViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZListViewController.h; sourceTree = "<group>"; };
		925CAAC422506D4500E19218 /* FTHZListViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZListViewController.m; sourceTree = "<group>"; };
		925CAAC7225070F500E19218 /* FTHZListViewElement.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZListViewElement.h; sourceTree = "<group>"; };
		925CAAC8225070F500E19218 /* FTHZListViewElement.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZListViewElement.m; sourceTree = "<group>"; };
		925CAACD22508EA100E19218 /* FTHZFeedListViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZFeedListViewController.h; sourceTree = "<group>"; };
		925CAACE22508EA100E19218 /* FTHZFeedListViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZFeedListViewController.m; sourceTree = "<group>"; };
		925CAAD22250900000E19218 /* FTHZFeedCardContentView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZFeedCardContentView.h; sourceTree = "<group>"; };
		925CAAD32250900000E19218 /* FTHZFeedCardContentView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZFeedCardContentView.m; sourceTree = "<group>"; };
		925CAAE02251063E00E19218 /* FTHZToolbarButton.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZToolbarButton.h; sourceTree = "<group>"; };
		925CAAE12251063E00E19218 /* FTHZToolbarButton.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZToolbarButton.m; sourceTree = "<group>"; };
		92652814225994CE00A84B21 /* ErrorCode.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ErrorCode.h; sourceTree = "<group>"; };
		92652815225994CE00A84B21 /* ErrorCode.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ErrorCode.m; sourceTree = "<group>"; };
		9265281D22599A0A00A84B21 /* FTHZNetworkTask+OceanAPI.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "FTHZNetworkTask+OceanAPI.h"; sourceTree = "<group>"; };
		9265281E22599A0A00A84B21 /* FTHZNetworkTask+OceanAPI.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "FTHZNetworkTask+OceanAPI.m"; sourceTree = "<group>"; };
		926528222259BAC300A84B21 /* FTHZUniversalBizParser.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZUniversalBizParser.h; sourceTree = "<group>"; };
		926528232259BAC300A84B21 /* FTHZUniversalBizParser.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZUniversalBizParser.m; sourceTree = "<group>"; };
		926528262259C4C300A84B21 /* FTHZListDataParser.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZListDataParser.h; sourceTree = "<group>"; };
		926528272259C4C300A84B21 /* FTHZListDataParser.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZListDataParser.m; sourceTree = "<group>"; };
		9265282B2259D18500A84B21 /* FTHZAccountManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZAccountManager.h; sourceTree = "<group>"; };
		9265282C2259D18500A84B21 /* FTHZAccountManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZAccountManager.m; sourceTree = "<group>"; };
		9265282F2259FD5A00A84B21 /* FTHZNetworkTask+UserInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "FTHZNetworkTask+UserInfo.h"; sourceTree = "<group>"; };
		926528302259FD5A00A84B21 /* FTHZNetworkTask+UserInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "FTHZNetworkTask+UserInfo.m"; sourceTree = "<group>"; };
		926528332259FEB500A84B21 /* FTHZObjectDataParser.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZObjectDataParser.h; sourceTree = "<group>"; };
		926528342259FEB500A84B21 /* FTHZObjectDataParser.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZObjectDataParser.m; sourceTree = "<group>"; };
		92652837225A37D200A84B21 /* FTHZGlobalConfig.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZGlobalConfig.h; sourceTree = "<group>"; };
		92652838225A37D200A84B21 /* FTHZGlobalConfig.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZGlobalConfig.m; sourceTree = "<group>"; };
		9265283C225A458600A84B21 /* LoginBusiness.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = LoginBusiness.h; sourceTree = "<group>"; };
		9265283D225A458600A84B21 /* LoginBusiness.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = LoginBusiness.m; sourceTree = "<group>"; };
		92652840225AD3F000A84B21 /* FTHZBusiness.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZBusiness.h; sourceTree = "<group>"; };
		92652841225AD3F000A84B21 /* FTHZBusiness.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZBusiness.m; sourceTree = "<group>"; };
		92652845225AF98600A84B21 /* UIViewController+ViewHierarchy.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIViewController+ViewHierarchy.h"; sourceTree = "<group>"; };
		92652846225AF98600A84B21 /* UIViewController+ViewHierarchy.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIViewController+ViewHierarchy.m"; sourceTree = "<group>"; };
		9265284A225B700000A84B21 /* DispatchQueue+Mark.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "DispatchQueue+Mark.h"; sourceTree = "<group>"; };
		9265284B225B700000A84B21 /* DispatchQueue+Mark.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "DispatchQueue+Mark.m"; sourceTree = "<group>"; };
		926AF275225CCE4A003D0435 /* UIView+RAC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIView+RAC.h"; sourceTree = "<group>"; };
		926AF276225CCE4A003D0435 /* UIView+RAC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIView+RAC.m"; sourceTree = "<group>"; };
		927CC26D222B76C000654DE3 /* FTHZMusicPlayer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZMusicPlayer.h; sourceTree = "<group>"; };
		927CC26E222B76C000654DE3 /* FTHZMusicPlayer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZMusicPlayer.m; sourceTree = "<group>"; };
		928B5643226C061F0035BE45 /* FTHZNetworkTask+Topic.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "FTHZNetworkTask+Topic.h"; sourceTree = "<group>"; };
		928B5644226C061F0035BE45 /* FTHZNetworkTask+Topic.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "FTHZNetworkTask+Topic.m"; sourceTree = "<group>"; };
		928B5647226C0AF50035BE45 /* TopicModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TopicModel.h; sourceTree = "<group>"; };
		928B5648226C0AF50035BE45 /* TopicModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TopicModel.m; sourceTree = "<group>"; };
		928B564B226C38960035BE45 /* TopicVCViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TopicVCViewController.h; sourceTree = "<group>"; };
		928B564C226C38960035BE45 /* TopicVCViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TopicVCViewController.m; sourceTree = "<group>"; };
		928B564F226C44700035BE45 /* AttentionContainerViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AttentionContainerViewController.h; sourceTree = "<group>"; };
		928B5650226C44700035BE45 /* AttentionContainerViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AttentionContainerViewController.m; sourceTree = "<group>"; };
		92B1F4EA22264D80004ADD4A /* FTHZMusicPlayerView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZMusicPlayerView.h; sourceTree = "<group>"; };
		92B1F4EB22264D80004ADD4A /* FTHZMusicPlayerView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZMusicPlayerView.m; sourceTree = "<group>"; };
		92B1F4ED22280E26004ADD4A /* MusicInfoModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MusicInfoModel.h; sourceTree = "<group>"; };
		92B1F4EE22280E26004ADD4A /* MusicInfoModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MusicInfoModel.m; sourceTree = "<group>"; };
		92B5C65222544216002ABA55 /* FTHZRefreshHeader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZRefreshHeader.h; sourceTree = "<group>"; };
		92B5C65322544216002ABA55 /* FTHZRefreshHeader.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZRefreshHeader.m; sourceTree = "<group>"; };
		92B5C6562254AF5F002ABA55 /* FTHZRefreshFooter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZRefreshFooter.h; sourceTree = "<group>"; };
		92B5C6572254AF5F002ABA55 /* FTHZRefreshFooter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZRefreshFooter.m; sourceTree = "<group>"; };
		92BE1537222EBAD900380B3D /* FTHZUserConfig.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZUserConfig.h; sourceTree = "<group>"; };
		92BE1538222EBAD900380B3D /* FTHZUserConfig.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZUserConfig.m; sourceTree = "<group>"; };
		92C4D0B3222E04AE00A58687 /* FTHZLocationManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZLocationManager.h; sourceTree = "<group>"; };
		92C4D0B4222E04AE00A58687 /* FTHZLocationManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZLocationManager.m; sourceTree = "<group>"; };
		92D484982261E56E00221A7C /* FeedBusiness.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FeedBusiness.h; sourceTree = "<group>"; };
		92D484992261E56E00221A7C /* FeedBusiness.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FeedBusiness.m; sourceTree = "<group>"; };
		92D4849C2261E5BA00221A7C /* FTHZNetworkTask+Feed.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "FTHZNetworkTask+Feed.h"; sourceTree = "<group>"; };
		92D4849D2261E5BA00221A7C /* FTHZNetworkTask+Feed.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "FTHZNetworkTask+Feed.m"; sourceTree = "<group>"; };
		92D484A12262079200221A7C /* FTHZCoordinator.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZCoordinator.h; sourceTree = "<group>"; };
		92D484A22262079200221A7C /* FTHZCoordinator.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZCoordinator.m; sourceTree = "<group>"; };
		92D484A62262CD6C00221A7C /* FeedDetailViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FeedDetailViewController.h; sourceTree = "<group>"; };
		92D484A72262CD6C00221A7C /* FeedDetailViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FeedDetailViewController.m; sourceTree = "<group>"; };
		92D484AC2262CE7300221A7C /* FTHZUpvoteUserRecordView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZUpvoteUserRecordView.h; sourceTree = "<group>"; };
		92D484AD2262CE7300221A7C /* FTHZUpvoteUserRecordView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZUpvoteUserRecordView.m; sourceTree = "<group>"; };
		92D484B22262CED800221A7C /* FTHZAvatarView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZAvatarView.h; sourceTree = "<group>"; };
		92D484B32262CED800221A7C /* FTHZAvatarView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZAvatarView.m; sourceTree = "<group>"; };
		92D484B72262DFAD00221A7C /* UserTopLevelCommentRecordView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UserTopLevelCommentRecordView.h; sourceTree = "<group>"; };
		92D484B82262DFAD00221A7C /* UserTopLevelCommentRecordView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UserTopLevelCommentRecordView.m; sourceTree = "<group>"; };
		92F1DBF022649FE1009C6737 /* FTHZNestSegmentListViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZNestSegmentListViewController.h; sourceTree = "<group>"; };
		92F1DBF122649FE1009C6737 /* FTHZNestSegmentListViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZNestSegmentListViewController.m; sourceTree = "<group>"; };
		92F1DBF42264B777009C6737 /* FTHZSegmentView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZSegmentView.h; sourceTree = "<group>"; };
		92F1DBF52264B777009C6737 /* FTHZSegmentView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZSegmentView.m; sourceTree = "<group>"; };
		92F1DBF92266F9AE009C6737 /* FTHZPopMenuController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZPopMenuController.h; sourceTree = "<group>"; };
		92F1DBFA2266F9AE009C6737 /* FTHZPopMenuController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZPopMenuController.m; sourceTree = "<group>"; };
		92F1DBFE2267F8EC009C6737 /* FTHZAlertDialog.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZAlertDialog.h; sourceTree = "<group>"; };
		92F1DBFF2267F8EC009C6737 /* FTHZAlertDialog.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZAlertDialog.m; sourceTree = "<group>"; };
		C4058E13AEE36B1213321B1D /* Pods-FTHZ.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-FTHZ.release.xcconfig"; path = "Target Support Files/Pods-FTHZ/Pods-FTHZ.release.xcconfig"; sourceTree = "<group>"; };
		C5E9C60E21D26B0C0019D2AC /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = System/Library/Frameworks/AVFoundation.framework; sourceTree = SDKROOT; };
		FC03DDA92E0E9E3C005469DD /* LiuyanDetailTableCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = LiuyanDetailTableCell.h; sourceTree = "<group>"; };
		FC03DDAA2E0E9E3C005469DD /* LiuyanDetailTableCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = LiuyanDetailTableCell.m; sourceTree = "<group>"; };
		FC089B342E090916000DDB5D /* FTHZRadialMenuView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZRadialMenuView.h; sourceTree = "<group>"; };
		FC089B352E090916000DDB5D /* FTHZRadialMenuView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZRadialMenuView.m; sourceTree = "<group>"; };
		FC0E17132D76D0F4008430CF /* OperationsModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = OperationsModel.h; sourceTree = "<group>"; };
		FC0E17142D76D0F4008430CF /* OperationsModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = OperationsModel.m; sourceTree = "<group>"; };
		FC0E17162D76DA6C008430CF /* OperationTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = OperationTableViewCell.h; sourceTree = "<group>"; };
		FC0E17172D76DA6C008430CF /* OperationTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = OperationTableViewCell.m; sourceTree = "<group>"; };
		FC0E17192D77E984008430CF /* FZOperationDetailVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FZOperationDetailVC.swift; sourceTree = "<group>"; };
		FC0E171B2D7822FB008430CF /* OperationDetailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OperationDetailView.swift; sourceTree = "<group>"; };
		FC0E17232D7AC2C9008430CF /* WaveView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WaveView.h; sourceTree = "<group>"; };
		FC0E17242D7AC2C9008430CF /* WaveView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WaveView.m; sourceTree = "<group>"; };
		FC0FB1462D8CFC2A0024E5B2 /* FTHZTabBarController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZTabBarController.h; sourceTree = "<group>"; };
		FC0FB1472D8CFC2A0024E5B2 /* FTHZTabBarController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZTabBarController.m; sourceTree = "<group>"; };
		FC0FB1572D8D3CA50024E5B2 /* DynamicTitleModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DynamicTitleModel.h; sourceTree = "<group>"; };
		FC0FB1582D8D3CA50024E5B2 /* DynamicTitleModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DynamicTitleModel.m; sourceTree = "<group>"; };
		FC0FB15E2D8D65770024E5B2 /* FZDynamicWebVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FZDynamicWebVC.swift; sourceTree = "<group>"; };
		FC0FB1642D9553690024E5B2 /* FriendSelectionVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FriendSelectionVC.h; sourceTree = "<group>"; };
		FC0FB1652D9553690024E5B2 /* FriendSelectionVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FriendSelectionVC.m; sourceTree = "<group>"; };
		FC0FB1672D9556F60024E5B2 /* SearchFriendsModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SearchFriendsModel.h; sourceTree = "<group>"; };
		FC0FB1682D9556F60024E5B2 /* SearchFriendsModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SearchFriendsModel.m; sourceTree = "<group>"; };
		************************ /* AccountLoginVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AccountLoginVC.h; sourceTree = "<group>"; };
		************************ /* AccountLoginVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AccountLoginVC.m; sourceTree = "<group>"; };
		************************ /* FZFollowVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FZFollowVC.swift; sourceTree = "<group>"; };
		************************ /* FZCocreationVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FZCocreationVC.swift; sourceTree = "<group>"; };
		************************ /* ShudongCardCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ShudongCardCell.h; sourceTree = "<group>"; };
		************************ /* ShudongCardCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ShudongCardCell.m; sourceTree = "<group>"; };
		************************ /* ClearNotificationsModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ClearNotificationsModel.h; sourceTree = "<group>"; };
		************************ /* ClearNotificationsModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ClearNotificationsModel.m; sourceTree = "<group>"; };
		FC519AD22A67861600A066B2 /* ShudongModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ShudongModel.h; sourceTree = "<group>"; };
		************************ /* ShudongModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ShudongModel.m; sourceTree = "<group>"; };
		FC519AD52A68DFA000A066B2 /* ShudongTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ShudongTableViewCell.h; sourceTree = "<group>"; };
		************************ /* ShudongTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ShudongTableViewCell.m; sourceTree = "<group>"; };
		FC79450E2D5C9D7F00CA6A6C /* FTHZVideoPlayerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FTHZVideoPlayerView.swift; sourceTree = "<group>"; };
		FC7945132D5DA10400CA6A6C /* FTHZSimpleVideoPlayer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZSimpleVideoPlayer.h; sourceTree = "<group>"; };
		************************ /* FTHZSimpleVideoPlayer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZSimpleVideoPlayer.m; sourceTree = "<group>"; };
		FC8C71E62D646FFC00651BD1 /* ChangePasswordModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ChangePasswordModel.h; sourceTree = "<group>"; };
		FC8C71E72D646FFC00651BD1 /* ChangePasswordModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ChangePasswordModel.m; sourceTree = "<group>"; };
		FC8C71E92D64700900651BD1 /* ChangePasswordVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ChangePasswordVC.h; sourceTree = "<group>"; };
		FC8C71EA2D64700900651BD1 /* ChangePasswordVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ChangePasswordVC.m; sourceTree = "<group>"; };
		FC8C71EC2D6576B800651BD1 /* ChangeAccountVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ChangeAccountVC.h; sourceTree = "<group>"; };
		FC8C71ED2D6576B800651BD1 /* ChangeAccountVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ChangeAccountVC.m; sourceTree = "<group>"; };
		FC8C71EF2D657A5500651BD1 /* ChangeAccountModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ChangeAccountModel.h; sourceTree = "<group>"; };
		FC8C71F02D657A5500651BD1 /* ChangeAccountModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ChangeAccountModel.m; sourceTree = "<group>"; };
		************************ /* LiuyanTimeSeparatorModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = LiuyanTimeSeparatorModel.h; sourceTree = "<group>"; };
		************************ /* LiuyanTimeSeparatorModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = LiuyanTimeSeparatorModel.m; sourceTree = "<group>"; };
		************************ /* LiuyanTimeSeparatorCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = LiuyanTimeSeparatorCell.h; sourceTree = "<group>"; };
		************************ /* LiuyanTimeSeparatorCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = LiuyanTimeSeparatorCell.m; sourceTree = "<group>"; };
		************************ /* RecommendSearchModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecommendSearchModel.swift; sourceTree = "<group>"; };
		************************ /* HZPhotoGroupOld.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HZPhotoGroupOld.h; sourceTree = "<group>"; };
		************************ /* HZPhotoGroupOld.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HZPhotoGroupOld.m; sourceTree = "<group>"; };
		************************ /* DescPopupView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DescPopupView.swift; sourceTree = "<group>"; };
		FCA89E5E2A6E0CD200B79271 /* LiuyanVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = LiuyanVC.h; sourceTree = "<group>"; };
		FCA89E5F2A6E0CD200B79271 /* LiuyanVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = LiuyanVC.m; sourceTree = "<group>"; };
		FCA89E612A6FB75600B79271 /* LiuyanModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = LiuyanModel.h; sourceTree = "<group>"; };
		FCA89E622A6FB75600B79271 /* LiuyanModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = LiuyanModel.m; sourceTree = "<group>"; };
		FCA89E672A6FC4C100B79271 /* LiuyanListCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = LiuyanListCell.h; sourceTree = "<group>"; };
		FCA89E682A6FC4C100B79271 /* LiuyanListCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = LiuyanListCell.m; sourceTree = "<group>"; };
		FCA89E6A2A71035D00B79271 /* LiuyanDetailVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = LiuyanDetailVC.h; sourceTree = "<group>"; };
		FCA89E6B2A71035D00B79271 /* LiuyanDetailVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = LiuyanDetailVC.m; sourceTree = "<group>"; };
		FCAD95E22A77975F001E2D3A /* XGAuthCode.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XGAuthCode.h; sourceTree = "<group>"; };
		FCAD95E32A77975F001E2D3A /* XGAuthCode.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XGAuthCode.m; sourceTree = "<group>"; };
		FCAF83282A667D8500B787B6 /* ShudongDetailVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ShudongDetailVC.h; sourceTree = "<group>"; };
		FCAF83292A667D8500B787B6 /* ShudongDetailVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ShudongDetailVC.m; sourceTree = "<group>"; };
		FCB2CA052DD58A1500E4F222 /* FTHZRequestEncryptor.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FTHZRequestEncryptor.h; sourceTree = "<group>"; };
		FCB2CA062DD58A1500E4F222 /* FTHZRequestEncryptor.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FTHZRequestEncryptor.m; sourceTree = "<group>"; };
		FCB2CA182DD70AEB00E4F222 /* TZImagePickerManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TZImagePickerManager.h; sourceTree = "<group>"; };
		FCB2CA192DD70AEB00E4F222 /* TZImagePickerManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TZImagePickerManager.m; sourceTree = "<group>"; };
		FCC5B21E2A6A25D3001C65E5 /* ShudongDetailView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ShudongDetailView.h; sourceTree = "<group>"; };
		FCC5B21F2A6A25D3001C65E5 /* ShudongDetailView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ShudongDetailView.m; sourceTree = "<group>"; };
		FCC5B2212A6A5DBB001C65E5 /* CreateShudongVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CreateShudongVC.h; sourceTree = "<group>"; };
		FCC5B2222A6A5DBB001C65E5 /* CreateShudongVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CreateShudongVC.m; sourceTree = "<group>"; };
		FCDD072E2D7EE8A5003B7FF4 /* VerifyCodeVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = VerifyCodeVC.h; sourceTree = "<group>"; };
		FCDD072F2D7EE8A5003B7FF4 /* VerifyCodeVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = VerifyCodeVC.m; sourceTree = "<group>"; };
		FCDD07312D7EE8B0003B7FF4 /* VerifyCodeModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = VerifyCodeModel.h; sourceTree = "<group>"; };
		FCDD07322D7EE8B0003B7FF4 /* VerifyCodeModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = VerifyCodeModel.m; sourceTree = "<group>"; };
		FCF3B4E62D59A5B10050133E /* LiuyanDetailView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = LiuyanDetailView.h; sourceTree = "<group>"; };
		FCF3B4E72D59A5B10050133E /* LiuyanDetailView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = LiuyanDetailView.m; sourceTree = "<group>"; };
		FCF3C3E82A7B46DC00599FA4 /* verifyViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = verifyViewController.h; sourceTree = "<group>"; };
		FCF3C3E92A7B46DC00599FA4 /* verifyViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = verifyViewController.m; sourceTree = "<group>"; };
		FCF3C3EB2A7B7A1F00599FA4 /* verifyModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = verifyModel.h; sourceTree = "<group>"; };
		FCF3C3EC2A7B7A1F00599FA4 /* verifyModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = verifyModel.m; sourceTree = "<group>"; };
		FCF3C3F12A7C959900599FA4 /* VerifyTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = VerifyTableViewCell.h; sourceTree = "<group>"; };
		FCF3C3F22A7C959900599FA4 /* VerifyTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = VerifyTableViewCell.m; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		FC892CF42D6D52BF0048DFA4 /* Models */ = {isa = PBXFileSystemSynchronizedRootGroup; explicitFileTypes = {}; explicitFolders = (); path = Models; sourceTree = "<group>"; };
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		653136DA217F0C0A0008DD48 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6328EEE624B4232E009168A3 /* libsqlite3.0.tbd in Frameworks */,
				65168B8C21E84692002CF1D3 /* UserNotifications.framework in Frameworks */,
				656328C321D27F7E006FED9C /* QALSDK.framework in Frameworks */,
				6359326623FEA70700ABB2A1 /* Bugly.framework in Frameworks */,
				634014C52458AD8000961DE8 /* TLSSDK.framework in Frameworks */,
				C5E9C60F21D26B0C0019D2AC /* AVFoundation.framework in Frameworks */,
				65C0765B21B6A83700F5EB1F /* libz.tbd in Frameworks */,
				6530FBC52185C3D800166841 /* SystemConfiguration.framework in Frameworks */,
				6530FBCC2185C73200166841 /* libsqlite3.dylib in Frameworks */,
				653D54E321D383450054B986 /* libopencore-amrwb.a in Frameworks */,
				6530FBCA2185C6A200166841 /* libz.1.dylib in Frameworks */,
				6530FBC82185C68300166841 /* libc++.1.dylib in Frameworks */,
				6530FBC32185C3C600166841 /* CoreTelephony.framework in Frameworks */,
				6359326F2423FF7D00ABB2A1 /* libTalkingData.a in Frameworks */,
				653D54E421D383450054B986 /* libopencore-amrnb.a in Frameworks */,
				9CDB1BC32B8094BD532EA65E /* Pods_FTHZ.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		369023B64E52C2DE1BC925A7 /* Pods */ = {
			isa = PBXGroup;
			children = (
				1FDF9C03FC338A0029699403 /* Pods-FTHZ.debug.xcconfig */,
				C4058E13AEE36B1213321B1D /* Pods-FTHZ.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		6302B4EF24371FBA00FCB8C4 /* view */ = {
			isa = PBXGroup;
			children = (
				FC79450E2D5C9D7F00CA6A6C /* FTHZVideoPlayerView.swift */,
				6302B4F024371FCA00FCB8C4 /* SearchCell.swift */,
				63D92903243A4D2700943130 /* momentDetailView.swift */,
				63D92914243E2C2D00943130 /* LikeUserCell.swift */,
			);
			path = view;
			sourceTree = "<group>";
		};
		633486FE24477F7E0080D3F0 /* Message */ = {
			isa = PBXGroup;
			children = (
				633486FF24477F990080D3F0 /* FZMessageVC.swift */,
			);
			path = Message;
			sourceTree = "<group>";
		};
		634E119F2441CD8000175027 /* subclass */ = {
			isa = PBXGroup;
			children = (
				6359330A242EFD5C00ABB2A1 /* CopyLabel.swift */,
				634E11A02441CDA600175027 /* LabelLayoutManager.swift */,
				634E11A22441CDDE00175027 /* LabelWithLineSpace.swift */,
				634E11B52444D82E00175027 /* TouchAreaButton.swift */,
				637AE943244BE52E005ABD3B /* FZAttributeString.swift */,
				6328EED524A11A18009168A3 /* GradientLabel.swift */,
				6328EEDF24AB14AE009168A3 /* SwipeScrollView.swift */,
				63AB042F24D0A61D00BA3BE6 /* MaskTriangleView.swift */,
			);
			path = subclass;
			sourceTree = "<group>";
		};
		634E11B92444EE5E00175027 /* TNSlider */ = {
			isa = PBXGroup;
			children = (
				634E11BD2444EE7400175027 /* TNConstants.swift */,
				634E11BB2444EE7200175027 /* TNSlider.swift */,
				634E11BA2444EE7100175027 /* TNTextLayer.swift */,
				634E11BC2444EE7300175027 /* TNTrackLayer.swift */,
			);
			path = TNSlider;
			sourceTree = "<group>";
		};
		6355FCBB243F9FFC00224A7F /* Play */ = {
			isa = PBXGroup;
			children = (
				6355FCD32441634E00224A7F /* view */,
				6355FCBD243FA03300224A7F /* FZPlayVC.swift */,
				6355FCD1244162FF00224A7F /* FZStraitVC.swift */,
				634E11A82442473B00175027 /* FZTopicVC.swift */,
				634E11AE2442DFEF00175027 /* FZHerzVC.swift */,
			);
			path = Play;
			sourceTree = "<group>";
		};
		6355FCC22440C54E00224A7F /* ZCycleView */ = {
			isa = PBXGroup;
			children = (
				6355FCC32440C54E00224A7F /* ZCycleView.swift */,
				6355FCC42440C54E00224A7F /* ZCycleViewCell.swift */,
				6355FCC52440C54E00224A7F /* ZCycleLayout.swift */,
				6355FCC62440C54E00224A7F /* ZPageControl.swift */,
			);
			path = ZCycleView;
			sourceTree = "<group>";
		};
		6355FCCB2440ED2100224A7F /* Component */ = {
			isa = PBXGroup;
			children = (
				6355FCCC2440ED3600224A7F /* CollectionMenu */,
			);
			path = Component;
			sourceTree = "<group>";
		};
		6355FCCC2440ED3600224A7F /* CollectionMenu */ = {
			isa = PBXGroup;
			children = (
				6355FCCD2440ED8A00224A7F /* FZCollectionMenu.swift */,
				6355FCCF2440EEF600224A7F /* FZCollectionLayout.swift */,
			);
			path = CollectionMenu;
			sourceTree = "<group>";
		};
		6355FCD32441634E00224A7F /* view */ = {
			isa = PBXGroup;
			children = (
				6355FCD52441637200224A7F /* StriatNavLayout.swift */,
				6355FCD72441642300224A7F /* StraitNavCell.swift */,
				634E11AA2442480E00175027 /* TopicNavCell.swift */,
				634E11AC24424FA900175027 /* TopicNavLayout.swift */,
				634E11B0244305A000175027 /* HerzCardView.swift */,
				634E11B224434FA500175027 /* StraitCell.swift */,
			);
			path = view;
			sourceTree = "<group>";
		};
		6359326C2423EFA700ABB2A1 /* SectionsSwift */ = {
			isa = PBXGroup;
			children = (
				63593307242EF6DD00ABB2A1 /* PostMoment */,
				635932D9242CC12C00ABB2A1 /* vendors */,
				63593293242729BB00ABB2A1 /* Base */,
				6359328B2425F16E00ABB2A1 /* Extensions */,
				635932882425F10F00ABB2A1 /* Module */,
				635932872425F0FD00ABB2A1 /* Mine */,
				635932862425F0EF00ABB2A1 /* Find */,
				633486FE24477F7E0080D3F0 /* Message */,
				6355FCBB243F9FFC00224A7F /* Play */,
			);
			path = SectionsSwift;
			sourceTree = "<group>";
		};
		635932862425F0EF00ABB2A1 /* Find */ = {
			isa = PBXGroup;
			children = (
				FC0FB15E2D8D65770024E5B2 /* FZDynamicWebVC.swift */,
				FC0E171B2D7822FB008430CF /* OperationDetailView.swift */,
				6302B4EF24371FBA00FCB8C4 /* view */,
				635932E6242D0AAB00ABB2A1 /* model */,
				6359328E242703B200ABB2A1 /* FZFindVC.swift */,
				FC0E17192D77E984008430CF /* FZOperationDetailVC.swift */,
				635932D7242BC7CD00ABB2A1 /* FZSearchVC.swift */,
				6334B5712439EB13001C4F35 /* FZMomentVC.swift */,
				63D92912243E29C700943130 /* FZMomentLikeListVC.swift */,
			);
			path = Find;
			sourceTree = "<group>";
		};
		635932872425F0FD00ABB2A1 /* Mine */ = {
			isa = PBXGroup;
			children = (
				************************ /* DescPopupView.swift */,
				************************ /* FZFollowVC.swift */,
				************************ /* FZCocreationVC.swift */,
				63593304242DF43B00ABB2A1 /* model */,
				63593301242DF3DA00ABB2A1 /* view */,
				6359326D2423EFEB00ABB2A1 /* FZMineVC.swift */,
				635932F7242DCD2A00ABB2A1 /* FZDraftVC.swift */,
				635933182432531C00ABB2A1 /* FZMyHomePageVC.swift */,
				6328EED3249BC5C2009168A3 /* FZGiftVC.swift */,
				63D92917243EEFA000943130 /* FZMyLikeListVC.swift */,
			);
			path = Mine;
			sourceTree = "<group>";
		};
		635932882425F10F00ABB2A1 /* Module */ = {
			isa = PBXGroup;
			children = (
				635932DF242D01A800ABB2A1 /* Network */,
				635932892425F12A00ABB2A1 /* FZConstants.swift */,
			);
			path = Module;
			sourceTree = "<group>";
		};
		6359328B2425F16E00ABB2A1 /* Extensions */ = {
			isa = PBXGroup;
			children = (
				634E119F2441CD8000175027 /* subclass */,
				6359328C2425F19400ABB2A1 /* UIColor.swift */,
				6359329124271D8900ABB2A1 /* UIFont.swift */,
				635932962427481D00ABB2A1 /* UIImageView.swift */,
				6313C15D24BDEB3100385E9C /* CALayer.swift */,
				6359329824274A5900ABB2A1 /* UIImage.swift */,
				6359329B242750A700ABB2A1 /* UIView.swift */,
				635932D4242A856D00ABB2A1 /* UIButton.swift */,
				6302B4F22437399800FCB8C4 /* UILabel.swift */,
				63D92905243ADD5B00943130 /* String.swift */,
				6328EEDD24A27671009168A3 /* NSDate.swift */,
				6372A19C246DBB5F00F998E1 /* CGFloat.swift */,
				6328EEAD248CE514009168A3 /* UINavigationController.swift */,
			);
			path = Extensions;
			sourceTree = "<group>";
		};
		63593293242729BB00ABB2A1 /* Base */ = {
			isa = PBXGroup;
			children = (
				6355FCCB2440ED2100224A7F /* Component */,
				63593294242729FD00ABB2A1 /* FZBaseVC.swift */,
				635932DD242CC53600ABB2A1 /* FZNetWorkManager.swift */,
				63CD86D82475AC30001DA4FF /* Toast.swift */,
			);
			path = Base;
			sourceTree = "<group>";
		};
		635932D9242CC12C00ABB2A1 /* vendors */ = {
			isa = PBXGroup;
			children = (
				63B2646524B70603005DF1C9 /* GameAudioPlayer */,
				6372A1642465D00C00F998E1 /* PennyPincher */,
				634E11B92444EE5E00175027 /* TNSlider */,
				6355FCC22440C54E00224A7F /* ZCycleView */,
				6359330C242F552C00ABB2A1 /* NKCache */,
				635932F9242DF2B700ABB2A1 /* ConfigureableTableVC */,
				635932DA242CC34500ABB2A1 /* Codextended */,
			);
			path = vendors;
			sourceTree = "<group>";
		};
		635932DA242CC34500ABB2A1 /* Codextended */ = {
			isa = PBXGroup;
			children = (
				635932DB242CC34500ABB2A1 /* Codextended.swift */,
			);
			path = Codextended;
			sourceTree = "<group>";
		};
		635932DF242D01A800ABB2A1 /* Network */ = {
			isa = PBXGroup;
			children = (
				635932F0242D1C7200ABB2A1 /* API */,
				635932E4242D01DE00ABB2A1 /* MoyaConfig.swift */,
				635932EA242D1BE700ABB2A1 /* NetWorkError.swift */,
				635932EC242D1C2A00ABB2A1 /* Response.swift */,
				635932EE242D1C5300ABB2A1 /* NetworkManager.swift */,
			);
			path = Network;
			sourceTree = "<group>";
		};
		635932E6242D0AAB00ABB2A1 /* model */ = {
			isa = PBXGroup;
			children = (
				************************ /* RecommendSearchModel.swift */,
				635932F3242D207100ABB2A1 /* SearchResModel.swift */,
				635932F5242D319A00ABB2A1 /* MomentModel.swift */,
			);
			path = model;
			sourceTree = "<group>";
		};
		635932F0242D1C7200ABB2A1 /* API */ = {
			isa = PBXGroup;
			children = (
				635932F1242D1CBA00ABB2A1 /* FindAPI.swift */,
				634014BF2457571B00961DE8 /* GameAPI.swift */,
			);
			path = API;
			sourceTree = "<group>";
		};
		635932F9242DF2B700ABB2A1 /* ConfigureableTableVC */ = {
			isa = PBXGroup;
			children = (
				635932FB242DF2E200ABB2A1 /* CellConfigurator.swift */,
				635932FA242DF2E100ABB2A1 /* ConfigurableTableViewController.h */,
				635932FD242DF2E300ABB2A1 /* ConfigurableTableViewController.swift */,
				635932FC242DF2E300ABB2A1 /* Updatable.swift */,
			);
			path = ConfigureableTableVC;
			sourceTree = "<group>";
		};
		63593301242DF3DA00ABB2A1 /* view */ = {
			isa = PBXGroup;
			children = (
				6359331A2434779600ABB2A1 /* homepageHeadView.swift */,
				6359331C2434A61800ABB2A1 /* otherUserHeadView.swift */,
				63593302242DF3EE00ABB2A1 /* DraftCell.swift */,
				6355FCB9243F953900224A7F /* MyLikedCell.swift */,
			);
			path = view;
			sourceTree = "<group>";
		};
		63593304242DF43B00ABB2A1 /* model */ = {
			isa = PBXGroup;
			children = (
				6355FCB7243F804A00224A7F /* MyLikedModel.swift */,
			);
			path = model;
			sourceTree = "<group>";
		};
		63593307242EF6DD00ABB2A1 /* PostMoment */ = {
			isa = PBXGroup;
			children = (
				63593308242EF6FD00ABB2A1 /* FZAddMusic.swift */,
			);
			path = PostMoment;
			sourceTree = "<group>";
		};
		6359330C242F552C00ABB2A1 /* NKCache */ = {
			isa = PBXGroup;
			children = (
				6359330E242F552C00ABB2A1 /* NKCacheManager.swift */,
			);
			path = NKCache;
			sourceTree = "<group>";
		};
		6372A1642465D00C00F998E1 /* PennyPincher */ = {
			isa = PBXGroup;
			children = (
				6372A19A246C49B800F998E1 /* gestures */,
				6372A1662465D00C00F998E1 /* ImportedGestures.swift */,
				6372A1672465D00C00F998E1 /* AndroidGesturesImporter.swift */,
				6372A1682465D00C00F998E1 /* PennyPincherGestureRecognizer.swift */,
				6372A1692465D00C00F998E1 /* PennyPincherTemplate.swift */,
				6372A16A2465D00C00F998E1 /* PennyPincher.swift */,
			);
			path = PennyPincher;
			sourceTree = "<group>";
		};
		63B2646524B70603005DF1C9 /* GameAudioPlayer */ = {
			isa = PBXGroup;
			children = (
				63B2646624B70603005DF1C9 /* GameAudioPlayer.swift */,
				63B2646724B70603005DF1C9 /* README.md */,
			);
			path = GameAudioPlayer;
			sourceTree = "<group>";
		};
		63D92907243AFEFB00943130 /* UIImageView */ = {
			isa = PBXGroup;
			children = (
				63D9290A243AFF0F00943130 /* UIImage+TColor.h */,
				63D92909243AFF0F00943130 /* UIImage+TColor.m */,
				63D9290B243AFF0F00943130 /* UIImageView+Web.h */,
				63D92908243AFF0F00943130 /* UIImageView+Web.m */,
			);
			path = UIImageView;
			sourceTree = "<group>";
		};
		63FDD8AA23BB338F00817917 /* Herz */ = {
			isa = PBXGroup;
			children = (
				63FDD8AB23BB33BB00817917 /* HQFlowView.h */,
				63FDD8AD23BB33BC00817917 /* HQFlowView.m */,
				63FDD8AC23BB33BB00817917 /* HQIndexBannerSubview.h */,
				63FDD8AE23BB33BC00817917 /* HQIndexBannerSubview.m */,
			);
			path = Herz;
			sourceTree = "<group>";
		};
		65168B3721E5F4CE002CF1D3 /* ImageClasses */ = {
			isa = PBXGroup;
			children = (
				65168B3A21E5F4CE002CF1D3 /* UIImage+Wechat.h */,
				65168B3821E5F4CE002CF1D3 /* UIImage+Wechat.m */,
			);
			path = ImageClasses;
			sourceTree = "<group>";
		};
		6530FBAA2184189D00166841 /* Login */ = {
			isa = PBXGroup;
			children = (
				************************ /* AccountLoginVC.h */,
				************************ /* AccountLoginVC.m */,
				6530FBAE2184189E00166841 /* View */,
				6530FBAB2184189E00166841 /* Model */,
				6581CA12219D176A00424564 /* LoginVC.h */,
				6581CA13219D176A00424564 /* LoginVC.m */,
				6581CA15219D18DE00424564 /* VerificationCodeVC.h */,
				6581CA16219D18DE00424564 /* VerificationCodeVC.m */,
				6581CA18219D190700424564 /* GenderVC.h */,
				6581CA19219D190700424564 /* GenderVC.m */,
				6581CA1B219D192D00424564 /* InformationVC.h */,
				6581CA1C219D192D00424564 /* InformationVC.m */,
				65DC0CCC21AAA114009C7673 /* HeziChouseOneVC.h */,
				65DC0CCD21AAA114009C7673 /* HeziChouseOneVC.m */,
				65DC0CD221AAA1AD009C7673 /* HeziChouseTwoVC.h */,
				65DC0CD321AAA1AD009C7673 /* HeziChouseTwoVC.m */,
				65DC0CD521AAA1C5009C7673 /* HeziChouseThreeVC.h */,
				65DC0CD621AAA1C5009C7673 /* HeziChouseThreeVC.m */,
				65DC0CE121ABE55E009C7673 /* HeziShareVC.h */,
				65DC0CE221ABE55E009C7673 /* HeziShareVC.m */,
				653D54EC21D4A98F0054B986 /* InvitationCodeVC.h */,
				653D54ED21D4A98F0054B986 /* InvitationCodeVC.m */,
				FCAD95E22A77975F001E2D3A /* XGAuthCode.h */,
				FCAD95E32A77975F001E2D3A /* XGAuthCode.m */,
				FCF3C3E82A7B46DC00599FA4 /* verifyViewController.h */,
				FCF3C3E92A7B46DC00599FA4 /* verifyViewController.m */,
			);
			path = Login;
			sourceTree = "<group>";
		};
		6530FBAB2184189E00166841 /* Model */ = {
			isa = PBXGroup;
			children = (
				65C0764221B11DC900F5EB1F /* GetUserinfoModel.h */,
				65C0764321B11DC900F5EB1F /* GetUserinfoModel.m */,
				658CCB7321ABF6F4003A93CC /* CommonCodeModel.h */,
				658CCB7421ABF6F4003A93CC /* CommonCodeModel.m */,
				6530FBB32184456700166841 /* ValidateCode.h */,
				6530FBB42184456700166841 /* ValidateCode.m */,
				6530FBB6218489FE00166841 /* Compute.h */,
				6530FBB7218489FE00166841 /* Compute.m */,
				658CCB7621AC43A0003A93CC /* UserUserinfoModel.h */,
				658CCB7721AC43A0003A93CC /* UserUserinfoModel.m */,
				658CCB7921AC4E1E003A93CC /* RateAttributeModel.h */,
				658CCB7A21AC4E1E003A93CC /* RateAttributeModel.m */,
				658CCB7C21AD27D5003A93CC /* RateCalculateModel.h */,
				658CCB7D21AD27D5003A93CC /* RateCalculateModel.m */,
				65C0763F21B0FDE500F5EB1F /* WXLoginModel.h */,
				65C0764021B0FDE500F5EB1F /* WXLoginModel.m */,
				650A088521C253AA0004A588 /* PaoPaoCollectionViewCell.h */,
				650A088621C253AA0004A588 /* PaoPaoCollectionViewCell.m */,
				653D54F221D4BEB20054B986 /* LoadInvitationCodeModel.h */,
				653D54F321D4BEB20054B986 /* LoadInvitationCodeModel.m */,
				653D54F521D4D2F40054B986 /* InvitationStatusModel.h */,
				653D54F621D4D2F40054B986 /* InvitationStatusModel.m */,
				FCF3C3EB2A7B7A1F00599FA4 /* verifyModel.h */,
				FCF3C3EC2A7B7A1F00599FA4 /* verifyModel.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		6530FBAE2184189E00166841 /* View */ = {
			isa = PBXGroup;
			children = (
				65DC0CD821AACD63009C7673 /* HeziChouseCollectionViewCell.h */,
				65DC0CD921AACD63009C7673 /* HeziChouseCollectionViewCell.m */,
				FCF3C3F12A7C959900599FA4 /* VerifyTableViewCell.h */,
				FCF3C3F22A7C959900599FA4 /* VerifyTableViewCell.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		6530FBB92185C2AA00166841 /* Framework */ = {
			isa = PBXGroup;
			children = (
				634014C42458AD8000961DE8 /* TLSSDK.framework */,
				696D73862297CA6300BEB77D /* libTalkingData.a */,
				696D73872297CA6300BEB77D /* TalkingData.h */,
				6359326523FEA70700ABB2A1 /* Bugly.framework */,
				656328C221D27F7E006FED9C /* QALSDK.framework */,
			);
			path = Framework;
			sourceTree = "<group>";
		};
		653136D4217F0C0A0008DD48 = {
			isa = PBXGroup;
			children = (
				653136DF217F0C0A0008DD48 /* FTHZ */,
				653136DE217F0C0A0008DD48 /* Products */,
				9253B029223C877A00798AE9 /* ProjectConfig */,
				6557453521D0BB3A00855BE2 /* FTHZ.entitlements */,
				D38E47FF6DF166262AFC23BF /* Frameworks */,
				369023B64E52C2DE1BC925A7 /* Pods */,
			);
			sourceTree = "<group>";
		};
		653136DE217F0C0A0008DD48 /* Products */ = {
			isa = PBXGroup;
			children = (
				653136DD217F0C0A0008DD48 /* 52hz.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		653136DF217F0C0A0008DD48 /* FTHZ */ = {
			isa = PBXGroup;
			children = (
				6359326C2423EFA700ABB2A1 /* SectionsSwift */,
				92D484B02262CEB700221A7C /* View */,
				9265281C225999C900A84B21 /* API */,
				92652813225994BE00A84B21 /* Definition */,
				925CAADD2250F2B100E19218 /* Extension */,
				925CAACB22508E8000E19218 /* Sections */,
				92444E8B224A79420092BCF8 /* Global */,
				9254FF39224918CE007D5569 /* Model */,
				6599806F2181975100FB7EEA /* Common */,
				653136E0217F0C0A0008DD48 /* AppDelegate.h */,
				653136E1217F0C0A0008DD48 /* AppDelegate.m */,
				FC0FB1462D8CFC2A0024E5B2 /* FTHZTabBarController.h */,
				FC0FB1472D8CFC2A0024E5B2 /* FTHZTabBarController.m */,
				65313715217F0FB90008DD48 /* Assets.xcassets */,
				65998077218198D600FB7EEA /* Constants.h */,
				6599807A2181993100FB7EEA /* Common_Test.h */,
				659980792181991600FB7EEA /* Common_AppStore.h */,
				65998076218198C200FB7EEA /* UrlLink.h */,
				65FF4CB321C36F58008FAAC4 /* LaunchScreen.storyboard */,
				653136EF217F0C0C0008DD48 /* main.m */,
			);
			path = FTHZ;
			sourceTree = "<group>";
		};
		6533696021DB30D0004E1782 /* XHInputView */ = {
			isa = PBXGroup;
			children = (
				6533696121DB30D0004E1782 /* XHInputView.h */,
				6533696221DB30D0004E1782 /* XHInputView.m */,
			);
			path = XHInputView;
			sourceTree = "<group>";
		};
		6534E60721C3C71F002DBEF6 /* Model */ = {
			isa = PBXGroup;
			children = (
				6534E60821C3C726002DBEF6 /* AffairFollowListModel.h */,
				6534E60921C3C726002DBEF6 /* AffairFollowListModel.m */,
				6557451721CCD06C00855BE2 /* DeleteDynamicModel.h */,
				6557451821CCD06C00855BE2 /* DeleteDynamicModel.m */,
				6557451A21CCD34D00855BE2 /* BlackUserModel.h */,
				6557451B21CCD34D00855BE2 /* BlackUserModel.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		653D54B521D383250054B986 /* ThridMsg */ = {
			isa = PBXGroup;
			children = (
				653D54B621D383440054B986 /* Class */,
			);
			path = ThridMsg;
			sourceTree = "<group>";
		};
		653D54B621D383440054B986 /* Class */ = {
			isa = PBXGroup;
			children = (
				653D54B721D383440054B986 /* LGAudioKit.h */,
				653D54B821D383440054B986 /* LGSoundPlayer */,
				653D54BB21D383440054B986 /* Resource */,
				653D54C621D383440054B986 /* LGSoundRecorder */,
				653D54C921D383440054B986 /* Vendor */,
			);
			path = Class;
			sourceTree = "<group>";
		};
		653D54B821D383440054B986 /* LGSoundPlayer */ = {
			isa = PBXGroup;
			children = (
				653D54B921D383440054B986 /* LGAudioPlayer.m */,
				653D54BA21D383440054B986 /* LGAudioPlayer.h */,
			);
			path = LGSoundPlayer;
			sourceTree = "<group>";
		};
		653D54BB21D383440054B986 /* Resource */ = {
			isa = PBXGroup;
			children = (
				653D54BC21D383440054B986 /* <EMAIL> */,
				653D54BD21D383440054B986 /* <EMAIL> */,
				653D54BE21D383440054B986 /* <EMAIL> */,
				653D54BF21D383440054B986 /* <EMAIL> */,
				653D54C021D383440054B986 /* <EMAIL> */,
				653D54C121D383440054B986 /* <EMAIL> */,
				653D54C221D383440054B986 /* <EMAIL> */,
				653D54C321D383440054B986 /* <EMAIL> */,
				653D54C421D383440054B986 /* <EMAIL> */,
				653D54C521D383440054B986 /* <EMAIL> */,
			);
			path = Resource;
			sourceTree = "<group>";
		};
		653D54C621D383440054B986 /* LGSoundRecorder */ = {
			isa = PBXGroup;
			children = (
				653D54C721D383440054B986 /* LGSoundRecorder.m */,
				653D54C821D383440054B986 /* LGSoundRecorder.h */,
			);
			path = LGSoundRecorder;
			sourceTree = "<group>";
		};
		653D54C921D383440054B986 /* Vendor */ = {
			isa = PBXGroup;
			children = (
				653D54CA21D383440054B986 /* AMR */,
			);
			path = Vendor;
			sourceTree = "<group>";
		};
		653D54CA21D383440054B986 /* AMR */ = {
			isa = PBXGroup;
			children = (
				653D54CB21D383440054B986 /* amrFileCodec.h */,
				653D54CC21D383440054B986 /* include */,
				653D54D321D383440054B986 /* lib */,
				653D54D621D383440054B986 /* amrFileCodec.m */,
			);
			path = AMR;
			sourceTree = "<group>";
		};
		653D54CC21D383440054B986 /* include */ = {
			isa = PBXGroup;
			children = (
				653D54CD21D383440054B986 /* opencore-amrwb */,
				653D54D021D383440054B986 /* opencore-amrnb */,
			);
			path = include;
			sourceTree = "<group>";
		};
		653D54CD21D383440054B986 /* opencore-amrwb */ = {
			isa = PBXGroup;
			children = (
				653D54CE21D383440054B986 /* if_rom.h */,
				653D54CF21D383440054B986 /* dec_if.h */,
			);
			path = "opencore-amrwb";
			sourceTree = "<group>";
		};
		653D54D021D383440054B986 /* opencore-amrnb */ = {
			isa = PBXGroup;
			children = (
				653D54D121D383440054B986 /* interf_dec.h */,
				653D54D221D383440054B986 /* interf_enc.h */,
			);
			path = "opencore-amrnb";
			sourceTree = "<group>";
		};
		653D54D321D383440054B986 /* lib */ = {
			isa = PBXGroup;
			children = (
				653D54D421D383440054B986 /* libopencore-amrwb.a */,
				653D54D521D383440054B986 /* libopencore-amrnb.a */,
			);
			path = lib;
			sourceTree = "<group>";
		};
		6581CA0D219C1D8A00424564 /* FontResource */ = {
			isa = PBXGroup;
			children = (
				650A087A21C0D67E0004A588 /* 52fuwu.txt */,
				650A087421C0C4EC0004A588 /* 52yinsi.txt */,
			);
			path = FontResource;
			sourceTree = "<group>";
		};
		6581CA1E219D819D00424564 /* Source */ = {
			isa = PBXGroup;
			children = (
				659B651E21A2A97C00DC13B8 /* SNSCodeCountdownButton.h */,
				659B651F21A2A97C00DC13B8 /* SNSCodeCountdownButton.m */,
				659B64DB21A280C600DC13B8 /* VTBBirthdayPicker.h */,
				659B64DA21A280C600DC13B8 /* VTBBirthdayPicker.m */,
				6581CA20219D819D00424564 /* KingIdentifyingView.h */,
				6581CA1F219D819D00424564 /* KingIdentifyingView.m */,
				653D54EF21D4AF4C0054B986 /* InvitationCodeView.h */,
				653D54F021D4AF4C0054B986 /* InvitationCodeView.m */,
			);
			path = Source;
			sourceTree = "<group>";
		};
		659980602181953F00FB7EEA /* Attention */ = {
			isa = PBXGroup;
			children = (
				6534E60721C3C71F002DBEF6 /* Model */,
				659980622181953F00FB7EEA /* View */,
				6598910E2181D8BD00BF1327 /* AttentionVC.h */,
				6598910F2181D8BD00BF1327 /* AttentionVC.m */,
				63DC11562437A79C00A1035A /* AttentionSegmentViewController.h */,
				63DC11572437A79C00A1035A /* AttentionSegmentViewController.m */,
				6547B55421A7A9FE005E7DA2 /* AttentionDetailVC.h */,
				6547B55521A7A9FE005E7DA2 /* AttentionDetailVC.m */,
				6516CE0F21A8205000703E14 /* AttentionLikeVC.h */,
				6516CE1021A8205000703E14 /* AttentionLikeVC.m */,
				6516CE1221A8207900703E14 /* AttentionCommentVC.h */,
				6516CE1321A8207900703E14 /* AttentionCommentVC.m */,
				928B564B226C38960035BE45 /* TopicVCViewController.h */,
				928B564C226C38960035BE45 /* TopicVCViewController.m */,
				928B564F226C44700035BE45 /* AttentionContainerViewController.h */,
				928B5650226C44700035BE45 /* AttentionContainerViewController.m */,
			);
			path = Attention;
			sourceTree = "<group>";
		};
		659980622181953F00FB7EEA /* View */ = {
			isa = PBXGroup;
			children = (
				FC0E17162D76DA6C008430CF /* OperationTableViewCell.h */,
				FC0E17172D76DA6C008430CF /* OperationTableViewCell.m */,
				63DC11502437A78600A1035A /* AttentionSegmentHeaderView.h */,
				63DC11522437A78600A1035A /* AttentionSegmentHeaderView.m */,
				63DC11512437A78600A1035A /* AttentionSegmentView.h */,
				63DC11532437A78600A1035A /* AttentionSegmentView.m */,
				652B107A218AAA2900D31B3F /* AttentionTableViewCell.h */,
				652B107B218AAA2900D31B3F /* AttentionTableViewCell.m */,
				6516CE1521A8212100703E14 /* AttentionLikeTableViewCell.h */,
				6516CE1621A8212100703E14 /* AttentionLikeTableViewCell.m */,
				6516CE1821A8213900703E14 /* AttentionCommentTableViewCell.h */,
				6516CE1921A8213900703E14 /* AttentionCommentTableViewCell.m */,
				6516CE1B21A8269100703E14 /* AttentionHeaderView.h */,
				6516CE1C21A8269100703E14 /* AttentionHeaderView.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		659980632181953F00FB7EEA /* Find */ = {
			isa = PBXGroup;
			children = (
				659980642181953F00FB7EEA /* Model */,
				659980652181953F00FB7EEA /* View */,
				6598910B2181D8A100BF1327 /* FindVC.h */,
				6598910C2181D8A100BF1327 /* FindVC.m */,
				6530FBCD2187FA3000166841 /* DynamicVC.h */,
				6530FBCE2187FA3000166841 /* DynamicVC.m */,
				652B1080218AD92300D31B3F /* DynamicDetailVC.h */,
				652B1081218AD92300D31B3F /* DynamicDetailVC.m */,
				6516CE1E21A83A7500703E14 /* DynamicLikeVC.h */,
				6516CE1F21A83A7500703E14 /* DynamicLikeVC.m */,
				6516CE2121A83A9600703E14 /* DynamicCommentVC.h */,
				6516CE2221A83A9600703E14 /* DynamicCommentVC.m */,
				65C0766821BC88F900F5EB1F /* WhaleVC.h */,
				65C0766921BC88F900F5EB1F /* WhaleVC.m */,
				63AFBAD423B7C743000251ED /* FTHZHeziVC.h */,
				63AFBAD523B7C743000251ED /* FTHZHeziVC.m */,
				63FDD8B323BB369000817917 /* FTHZHerzDetailVC.h */,
				63FDD8B423BB369000817917 /* FTHZHerzDetailVC.m */,
				6547B55721A7E176005E7DA2 /* WhaleDetailVC.h */,
				6547B55821A7E176005E7DA2 /* WhaleDetailVC.m */,
				65168B4621E7B5D8002CF1D3 /* ActivityWebVC.h */,
				65168B4721E7B5D8002CF1D3 /* ActivityWebVC.m */,
				656459D722006E110089660C /* TagAffairVC.h */,
				656459D822006E110089660C /* TagAffairVC.m */,
				65DC0C3C21A9868E009C7673 /* ReleaseDynamicVC.h */,
				65DC0C3D21A9868E009C7673 /* ReleaseDynamicVC.m */,
				921B651522282F0100D8E2DF /* FTHZInputMusicURLDialogController.h */,
				921B651622282F0100D8E2DF /* FTHZInputMusicURLDialogController.m */,
				8C2E412A22A763420045A50C /* FTHZStraitVC.h */,
				8C2E412B22A763420045A50C /* FTHZStraitVC.m */,
				630536A222A912D0005B3878 /* FTHZChannelListVC.h */,
				630536A322A912D0005B3878 /* FTHZChannelListVC.m */,
				630536A622AAC472005B3878 /* FTHZCreateChannelVC.h */,
				630536A722AAC472005B3878 /* FTHZCreateChannelVC.m */,
				630536AA22AF327D005B3878 /* FTHZChannelDetailVC.h */,
				630536AB22AF327D005B3878 /* FTHZChannelDetailVC.m */,
				8C9F1D7A22BBACC40047DC08 /* FTHZChannelVoiceReplyVC.h */,
				8C9F1D7B22BBACC40047DC08 /* FTHZChannelVoiceReplyVC.m */,
				8C9F1D8022BC7E750047DC08 /* FTHZChannelImgReplyVC.h */,
				8C9F1D8122BC7E750047DC08 /* FTHZChannelImgReplyVC.m */,
				FCC5B2212A6A5DBB001C65E5 /* CreateShudongVC.h */,
				FCC5B2222A6A5DBB001C65E5 /* CreateShudongVC.m */,
				FCA89E5E2A6E0CD200B79271 /* LiuyanVC.h */,
				FCA89E5F2A6E0CD200B79271 /* LiuyanVC.m */,
			);
			path = Find;
			sourceTree = "<group>";
		};
		659980642181953F00FB7EEA /* Model */ = {
			isa = PBXGroup;
			children = (
				FC0FB1572D8D3CA50024E5B2 /* DynamicTitleModel.h */,
				FC0FB1582D8D3CA50024E5B2 /* DynamicTitleModel.m */,
				FC0E17132D76D0F4008430CF /* OperationsModel.h */,
				FC0E17142D76D0F4008430CF /* OperationsModel.m */,
				65F685B321898F23003D37F6 /* AffairListModel.h */,
				65F685B421898F23003D37F6 /* AffairListModel.m */,
				652B1086218EA99D00D31B3F /* AffairDetailModel.h */,
				652B1087218EA99D00D31B3F /* AffairDetailModel.m */,
				65C0763521AECE5F00F5EB1F /* ContentModel.h */,
				65C0763621AECE5F00F5EB1F /* ContentModel.m */,
				65C0764521B3B4C600F5EB1F /* WhaleListModel.h */,
				65C0764621B3B4C600F5EB1F /* WhaleListModel.m */,
				63AFBAD823B7CF64000251ED /* FTHZHeziModel.h */,
				63AFBAD923B7CF64000251ED /* FTHZHeziModel.m */,
				65C0764821B434A600F5EB1F /* AffairLikesModel.h */,
				65C0764921B434A600F5EB1F /* AffairLikesModel.m */,
				65C0764E21B57A6300F5EB1F /* AffairCommentsModel.h */,
				65C0764F21B57A6300F5EB1F /* AffairCommentsModel.m */,
				65C0764B21B5276500F5EB1F /* DoLikeModel.h */,
				65C0764C21B5276500F5EB1F /* DoLikeModel.m */,
				65C0765121B57FEE00F5EB1F /* DoCommentModel.h */,
				65C0765221B57FEE00F5EB1F /* DoCommentModel.m */,
				650A086D21C0B6040004A588 /* UserAffairListModel.h */,
				650A086E21C0B6040004A588 /* UserAffairListModel.m */,
				650A087C21C0E0AC0004A588 /* OtherUserInfo.h */,
				650A087D21C0E0AC0004A588 /* OtherUserInfo.m */,
				6534E5F521C38247002DBEF6 /* AddAttentionModel.h */,
				6534E5F621C38247002DBEF6 /* AddAttentionModel.m */,
				6534E5F821C3825E002DBEF6 /* ReleaseAttentionModel.h */,
				6534E5F921C3825E002DBEF6 /* ReleaseAttentionModel.m */,
				65168B4321E7AC94002CF1D3 /* UserStatsModel.h */,
				65168B4421E7AC94002CF1D3 /* UserStatsModel.m */,
				65168B8721E7D32C002CF1D3 /* ChangeStatsModel.h */,
				65168B8821E7D32C002CF1D3 /* ChangeStatsModel.m */,
				656459D421FFE57A0089660C /* AffairTagmodel.h */,
				656459D521FFE57B0089660C /* AffairTagmodel.m */,
				656459DA220073510089660C /* AffairTagListModel.h */,
				656459DB220073510089660C /* AffairTagListModel.m */,
				656459DD22007EAF0089660C /* ClearBadgeModel.h */,
				656459DE22007EAF0089660C /* ClearBadgeModel.m */,
				92B1F4ED22280E26004ADD4A /* MusicInfoModel.h */,
				92B1F4EE22280E26004ADD4A /* MusicInfoModel.m */,
				8C2E412D22A7A7400045A50C /* FTHZStraitModel.h */,
				8C2E412E22A7A7400045A50C /* FTHZStraitModel.m */,
				8C9F1D7D22BBAEE70047DC08 /* FTHZChannelReplyModel.h */,
				8C9F1D7E22BBAEE70047DC08 /* FTHZChannelReplyModel.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		659980652181953F00FB7EEA /* View */ = {
			isa = PBXGroup;
			children = (
				63FDD8AA23BB338F00817917 /* Herz */,
				6530FBD3218858AB00166841 /* DynamicTableViewCell.h */,
				6530FBD4218858AB00166841 /* DynamicTableViewCell.m */,
				6533695421D9222F004E1782 /* DynamicSegmentView.h */,
				6533695521D9222F004E1782 /* DynamicSegmentView.m */,
				6533695721D9256A004E1782 /* DynamicSegmentHeaderView.h */,
				6533695821D9256A004E1782 /* DynamicSegmentHeaderView.m */,
				6533696421DC9B55004E1782 /* FindSegmentView.h */,
				6533696521DC9B55004E1782 /* FindSegmentView.m */,
				6533696721DC9BDB004E1782 /* FindSegmentHeaderView.h */,
				6533696821DC9BDB004E1782 /* FindSegmentHeaderView.m */,
				652B107D218ACC8B00D31B3F /* WhaleTableViewCell.h */,
				652B107E218ACC8B00D31B3F /* WhaleTableViewCell.m */,
				63FDD8B723BB773800817917 /* FTHZHerzListCell.h */,
				63FDD8B823BB773800817917 /* FTHZHerzListCell.m */,
				6516CE2421A83AB300703E14 /* DynamicHeaderView.h */,
				6516CE2521A83AB300703E14 /* DynamicHeaderView.m */,
				65C0765421B62BA400F5EB1F /* DyLikeTableViewCell.h */,
				65C0765521B62BA400F5EB1F /* DyLikeTableViewCell.m */,
				65C0765721B6517200F5EB1F /* DyCommentTableViewCell.h */,
				65C0765821B6517200F5EB1F /* DyCommentTableViewCell.m */,
				650A086521BFE31F0004A588 /* WhaleHeaderView.h */,
				650A086621BFE31F0004A588 /* WhaleHeaderView.m */,
				656459D121FF3D210089660C /* pageTageCollectionViewCell.h */,
				656459D221FF3D210089660C /* pageTageCollectionViewCell.m */,
				8C2E413122A7BDE70045A50C /* StraitListCell.h */,
				8C2E413222A7BDE70045A50C /* StraitListCell.m */,
				8C9F1D8522BCE4270047DC08 /* ChannelCommentCell.h */,
				8C9F1D8622BCE4270047DC08 /* ChannelCommentCell.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		659980662181953F00FB7EEA /* Message */ = {
			isa = PBXGroup;
			children = (
				653D54B521D383250054B986 /* ThridMsg */,
				659B9F8021C8E4C60040651F /* Model */,
				659B9F7D21C8E4C50040651F /* View */,
				659891112181D8CE00BF1327 /* MessageVC.h */,
				659891122181D8CE00BF1327 /* MessageVC.m */,
				657C9A9E21CA482E00681717 /* MessageDetailVC.h */,
				657C9A9F21CA482E00681717 /* MessageDetailVC.m */,
				6557453221CFC0D000855BE2 /* MessageDynaficDetailVC.h */,
				6557453321CFC0D000855BE2 /* MessageDynaficDetailVC.m */,
				657E9B7821CA180F009029E6 /* ReportVC.h */,
				657E9B7921CA180F009029E6 /* ReportVC.m */,
				FCAF83282A667D8500B787B6 /* ShudongDetailVC.h */,
				FCAF83292A667D8500B787B6 /* ShudongDetailVC.m */,
				FCA89E6A2A71035D00B79271 /* LiuyanDetailVC.h */,
				FCA89E6B2A71035D00B79271 /* LiuyanDetailVC.m */,
			);
			path = Message;
			sourceTree = "<group>";
		};
		659980692181953F00FB7EEA /* Mine */ = {
			isa = PBXGroup;
			children = (
				FC0FB1642D9553690024E5B2 /* FriendSelectionVC.h */,
				FC0FB1652D9553690024E5B2 /* FriendSelectionVC.m */,
				FCDD072E2D7EE8A5003B7FF4 /* VerifyCodeVC.h */,
				FCDD072F2D7EE8A5003B7FF4 /* VerifyCodeVC.m */,
				FC8C71E92D64700900651BD1 /* ChangePasswordVC.h */,
				FC8C71EA2D64700900651BD1 /* ChangePasswordVC.m */,
				6599806A2181953F00FB7EEA /* Model */,
				6599806B2181953F00FB7EEA /* View */,
				659891142181D8DB00BF1327 /* MineVC.h */,
				659891152181D8DB00BF1327 /* MineVC.m */,
				6581C9FE219A94AD00424564 /* SegmentViewController.h */,
				6581C9FF219A94AD00424564 /* SegmentViewController.m */,
				6581CA0A219A992500424564 /* MyDynamicVC.h */,
				6581CA0B219A992500424564 /* MyDynamicVC.m */,
				6581CA01219A960700424564 /* AttentionMeVC.h */,
				6581CA02219A960700424564 /* AttentionMeVC.m */,
				6581CA04219A962400424564 /* MyAttentionVC.h */,
				6581CA05219A962400424564 /* MyAttentionVC.m */,
				659B652A21A4FB4400DC13B8 /* SettingVC.h */,
				659B652B21A4FB4400DC13B8 /* SettingVC.m */,
				6557452021CCDFC900855BE2 /* BlackUserVC.h */,
				6557452121CCDFC900855BE2 /* BlackUserVC.m */,
				659B653021A5202B00DC13B8 /* UserInformationVC.h */,
				659B653121A5202B00DC13B8 /* UserInformationVC.m */,
				FC8C71EC2D6576B800651BD1 /* ChangeAccountVC.h */,
				FC8C71ED2D6576B800651BD1 /* ChangeAccountVC.m */,
				65C0766B21BCAA8900F5EB1F /* ChangeNameVC.h */,
				65C0766C21BCAA8900F5EB1F /* ChangeNameVC.m */,
				65C0766E21BCAAA800F5EB1F /* ChangeGenderVC.h */,
				65C0766F21BCAAA800F5EB1F /* ChangeGenderVC.m */,
				650A086221BF9D9A0004A588 /* ChangeTagVC.h */,
				650A086321BF9D9A0004A588 /* ChangeTagVC.m */,
				650A087F21C236FA0004A588 /* ChangeLikeTagVC.h */,
				650A088021C236FA0004A588 /* ChangeLikeTagVC.m */,
				650A088821C28CF60004A588 /* ChangeUserVC.h */,
				650A088921C28CF60004A588 /* ChangeUserVC.m */,
				650A088221C23E170004A588 /* ChangeSigelVC.h */,
				650A088321C23E170004A588 /* ChangeSigelVC.m */,
				656459BF21E89FFC0089660C /* WhalePageVC.h */,
				656459C021E89FFC0089660C /* WhalePageVC.m */,
				63AFBACE23A7843C000251ED /* FTHZInviteCodeVC.h */,
				63AFBACF23A7843C000251ED /* FTHZInviteCodeVC.m */,
				63AFBAE023B7EB62000251ED /* FTHZInviteShareVC.h */,
				63AFBAE123B7EB62000251ED /* FTHZInviteShareVC.m */,
			);
			path = Mine;
			sourceTree = "<group>";
		};
		6599806A2181953F00FB7EEA /* Model */ = {
			isa = PBXGroup;
			children = (
				FC0FB1672D9556F60024E5B2 /* SearchFriendsModel.h */,
				FC0FB1682D9556F60024E5B2 /* SearchFriendsModel.m */,
				FCDD07312D7EE8B0003B7FF4 /* VerifyCodeModel.h */,
				FCDD07322D7EE8B0003B7FF4 /* VerifyCodeModel.m */,
				FC8C71EF2D657A5500651BD1 /* ChangeAccountModel.h */,
				FC8C71F02D657A5500651BD1 /* ChangeAccountModel.m */,
				FC8C71E62D646FFC00651BD1 /* ChangePasswordModel.h */,
				FC8C71E72D646FFC00651BD1 /* ChangePasswordModel.m */,
				6547B54F21A6898C005E7DA2 /* CertificateModel.h */,
				6547B55021A6898C005E7DA2 /* CertificateModel.m */,
				6534E5FE21C39BB9002DBEF6 /* MyAttentionModel.h */,
				6534E5FF21C39BB9002DBEF6 /* MyAttentionModel.m */,
				656459CE21FC7E450089660C /* MyAttentionPageModel.h */,
				656459CF21FC7E450089660C /* MyAttentionPageModel.m */,
				6534E60121C39BC4002DBEF6 /* AttentionMeModel.h */,
				6534E60221C39BC4002DBEF6 /* AttentionMeModel.m */,
				656459CB21FC7E290089660C /* AttentionMePageModel.h */,
				656459CC21FC7E290089660C /* AttentionMePageModel.m */,
				657AC6A021CA54D500674FE2 /* IPStatusModel.h */,
				657AC6A121CA54D500674FE2 /* IPStatusModel.m */,
				657AC6A321CA54F600674FE2 /* POSTIPStatusModel.h */,
				657AC6A421CA54F600674FE2 /* POSTIPStatusModel.m */,
				6557452621CD172900855BE2 /* GetBlackUserModel.h */,
				6557452721CD172900855BE2 /* GetBlackUserModel.m */,
				6557452921CD22F000855BE2 /* DoOutBlackModel.h */,
				6557452A21CD22F000855BE2 /* DoOutBlackModel.m */,
				65168B3D21E63578002CF1D3 /* GetMessageNumberModel.h */,
				65168B3E21E63578002CF1D3 /* GetMessageNumberModel.m */,
				65168B4021E6F4CB002CF1D3 /* UserMessageNumberModel.h */,
				65168B4121E6F4CB002CF1D3 /* UserMessageNumberModel.m */,
				656459C221E9AA5B0089660C /* UserdeviceTokenModel.h */,
				656459C321E9AA5B0089660C /* UserdeviceTokenModel.m */,
				656459C521E9C0590089660C /* CommenPushModel.h */,
				656459C621E9C0590089660C /* CommenPushModel.m */,
				63AFBADC23B7D312000251ED /* FTHZInvitModel.h */,
				63AFBADD23B7D312000251ED /* FTHZInvitModel.m */,
				63593315242F621B00ABB2A1 /* FZDraftModel.h */,
				63593316242F621B00ABB2A1 /* FZDraftModel.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		6599806B2181953F00FB7EEA /* View */ = {
			isa = PBXGroup;
			children = (
				FC0E17232D7AC2C9008430CF /* WaveView.h */,
				FC0E17242D7AC2C9008430CF /* WaveView.m */,
				6581C9F5219A73DF00424564 /* SegmentView.h */,
				6581C9F6219A73DF00424564 /* SegmentView.m */,
				6581C9F8219A73F400424564 /* SegmentHeaderView.h */,
				6581C9F9219A73F400424564 /* SegmentHeaderView.m */,
				6581C9FC219A742C00424564 /* CenterTouchTableView.h */,
				6581C9FB219A742C00424564 /* CenterTouchTableView.m */,
				659B652121A3B64000DC13B8 /* UserHeaderView.h */,
				659B652221A3B64000DC13B8 /* UserHeaderView.m */,
				659B652721A43B0F00DC13B8 /* AttentionMeTableViewCell.h */,
				659B652821A43B0F00DC13B8 /* AttentionMeTableViewCell.m */,
				659B652421A430EC00DC13B8 /* MyAttentionTableViewCell.h */,
				659B652521A430EC00DC13B8 /* MyAttentionTableViewCell.m */,
				659B653321A5512200DC13B8 /* UserInformationTableViewCell.h */,
				659B653421A5512200DC13B8 /* UserInformationTableViewCell.m */,
				657AC6A621CB3EE100674FE2 /* SettingTableViewCell.h */,
				657AC6A721CB3EE100674FE2 /* SettingTableViewCell.m */,
				6557452321CD152100855BE2 /* BlackUserTableViewCell.h */,
				6557452421CD152100855BE2 /* BlackUserTableViewCell.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		6599806F2181975100FB7EEA /* Common */ = {
			isa = PBXGroup;
			children = (
				FC892CF42D6D52BF0048DFA4 /* Models */,
				927CC26B222B769F00654DE3 /* Components */,
				6581CA0D219C1D8A00424564 /* FontResource */,
				6530FBB92185C2AA00166841 /* Framework */,
				659980702181975100FB7EEA /* UI */,
				659980712181975100FB7EEA /* Category */,
				659980722181975100FB7EEA /* API */,
				659980732181975100FB7EEA /* Currency */,
			);
			path = Common;
			sourceTree = "<group>";
		};
		659980702181975100FB7EEA /* UI */ = {
			isa = PBXGroup;
			children = (
				92B5C651225441EE002ABA55 /* RefreshControl */,
				925CAADF225105F600E19218 /* ToolbarButton */,
				925CAAB8225068FF00E19218 /* FTHZUI */,
				924E5EE8222953860002C231 /* DialogPresentationController */,
				65168B3721E5F4CE002CF1D3 /* ImageClasses */,
				6533696021DB30D0004E1782 /* XHInputView */,
				65C0766221BC88E100F5EB1F /* DJStatusBarHUD */,
				65C0763821AF8B6F00F5EB1F /* RefreshCostom */,
				65C0763121AE8BD200F5EB1F /* HUD */,
				65DC0CDB21AAD886009C7673 /* UICollectionViewLeftAlignedLayout */,
				6581CA1E219D819D00424564 /* Source */,
				65F6863D2189A2F0003D37F6 /* ptotoBrowser */,
				659980902181AF6B00FB7EEA /* FlatButton */,
			);
			path = UI;
			sourceTree = "<group>";
		};
		659980712181975100FB7EEA /* Category */ = {
			isa = PBXGroup;
			children = (
				6599808B2181AAC000FB7EEA /* NSString+PJR.h */,
				6599808A2181AAC000FB7EEA /* NSString+PJR.m */,
				659890FF2181B7A600BF1327 /* NSDate+VTB.h */,
				659891002181B7A600BF1327 /* NSDate+VTB.m */,
				659891062181BB2D00BF1327 /* UIImage+TintColor.h */,
				659891052181BB2D00BF1327 /* UIImage+TintColor.m */,
				8C2E413722A7E28E0045A50C /* UIImageView+AnimationCompletion.h */,
				8C2E413822A7E28E0045A50C /* UIImageView+AnimationCompletion.m */,
			);
			path = Category;
			sourceTree = "<group>";
		};
		659980722181975100FB7EEA /* API */ = {
			isa = PBXGroup;
			children = (
				659980832181AA4300FB7EEA /* BaseJsonModel.h */,
				6599807E2181AA4200FB7EEA /* BaseJsonModel.m */,
				6599807B2181AA4200FB7EEA /* Http.h */,
				6599807D2181AA4200FB7EEA /* Http.m */,
				6599807F2181AA4200FB7EEA /* JNFHTTPManager.h */,
				659980802181AA4300FB7EEA /* JNFHTTPManager.m */,
				6599807C2181AA4200FB7EEA /* VitNetAPIClient.h */,
				659980842181AA4300FB7EEA /* VitNetAPIClient.m */,
			);
			path = API;
			sourceTree = "<group>";
		};
		659980732181975100FB7EEA /* Currency */ = {
			isa = PBXGroup;
			children = (
				659890FD2181B78500BF1327 /* NemoUtil.h */,
				659890FC2181B78500BF1327 /* NemoUtil.m */,
				659891022181BA3E00BF1327 /* CurrencyRootVC.h */,
				659891032181BA3E00BF1327 /* CurrencyRootVC.m */,
				659891082181C44A00BF1327 /* CurrencyNavVC.h */,
				659891092181C44A00BF1327 /* CurrencyNavVC.m */,
			);
			path = Currency;
			sourceTree = "<group>";
		};
		659980902181AF6B00FB7EEA /* FlatButton */ = {
			isa = PBXGroup;
			children = (
				659980922181AF6B00FB7EEA /* FlatButton.h */,
				659980932181AF6B00FB7EEA /* FlatButton.m */,
				659980912181AF6B00FB7EEA /* UITextView+YLTextView.h */,
				659980942181AF6B00FB7EEA /* UITextView+YLTextView.m */,
			);
			path = FlatButton;
			sourceTree = "<group>";
		};
		659B9F7D21C8E4C50040651F /* View */ = {
			isa = PBXGroup;
			children = (
				************************ /* LiuyanTimeSeparatorCell.h */,
				************************ /* LiuyanTimeSeparatorCell.m */,
				FC03DDA92E0E9E3C005469DD /* LiuyanDetailTableCell.h */,
				FC03DDAA2E0E9E3C005469DD /* LiuyanDetailTableCell.m */,
				************************ /* ShudongCardCell.h */,
				************************ /* ShudongCardCell.m */,
				FCF3B4E62D59A5B10050133E /* LiuyanDetailView.h */,
				FCF3B4E72D59A5B10050133E /* LiuyanDetailView.m */,
				659B9F7E21C8E4C50040651F /* MessageTableViewCell.h */,
				659B9F7F21C8E4C50040651F /* MessageTableViewCell.m */,
				657E9B7221C93537009029E6 /* MsgDetailTableViewCell.h */,
				657E9B7321C93537009029E6 /* MsgDetailTableViewCell.m */,
				6557452F21CF817600855BE2 /* MessageNotificationTableViewCell.h */,
				6557453021CF817600855BE2 /* MessageNotificationTableViewCell.m */,
				FC519AD52A68DFA000A066B2 /* ShudongTableViewCell.h */,
				************************ /* ShudongTableViewCell.m */,
				FCC5B21E2A6A25D3001C65E5 /* ShudongDetailView.h */,
				FCC5B21F2A6A25D3001C65E5 /* ShudongDetailView.m */,
				FCA89E672A6FC4C100B79271 /* LiuyanListCell.h */,
				FCA89E682A6FC4C100B79271 /* LiuyanListCell.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		659B9F8021C8E4C60040651F /* Model */ = {
			isa = PBXGroup;
			children = (
				************************ /* LiuyanTimeSeparatorModel.h */,
				************************ /* LiuyanTimeSeparatorModel.m */,
				************************ /* ClearNotificationsModel.h */,
				************************ /* ClearNotificationsModel.m */,
				653D54E621D38A260054B986 /* LGMessageModel.h */,
				653D54E721D38A270054B986 /* LGMessageModel.m */,
				659B9F8221C8E4C60040651F /* IMUserInfoModel.h */,
				659B9F8121C8E4C60040651F /* IMUserInfoModel.m */,
				657E9B7B21CA21B5009029E6 /* ReportModel.h */,
				657E9B7C21CA21B5009029E6 /* ReportModel.m */,
				6557452C21CF7CAC00855BE2 /* MessageNotificationModel.h */,
				6557452D21CF7CAC00855BE2 /* MessageNotificationModel.m */,
				653D54E921D461340054B986 /* MessageChangeStatsModel.h */,
				653D54EA21D461340054B986 /* MessageChangeStatsModel.m */,
				6533696D21DF36A9004E1782 /* IMNewAffairDetailModel.h */,
				6533696E21DF36A9004E1782 /* IMNewAffairDetailModel.m */,
				FC519AD22A67861600A066B2 /* ShudongModel.h */,
				************************ /* ShudongModel.m */,
				FCA89E612A6FB75600B79271 /* LiuyanModel.h */,
				FCA89E622A6FB75600B79271 /* LiuyanModel.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		65C0763121AE8BD200F5EB1F /* HUD */ = {
			isa = PBXGroup;
			children = (
				65C0763221AE8BD200F5EB1F /* HUD.h */,
				65C0763321AE8BD200F5EB1F /* HUD.m */,
			);
			path = HUD;
			sourceTree = "<group>";
		};
		65C0763821AF8B6F00F5EB1F /* RefreshCostom */ = {
			isa = PBXGroup;
			children = (
				657E9B7621C9F21C009029E6 /* MJChiBaoZiFooter.h */,
				657E9B7521C9F21C009029E6 /* MJChiBaoZiFooter.m */,
				65C0763921AF8B6F00F5EB1F /* MJChiBaoZiHeader.h */,
				65C0763A21AF8B6F00F5EB1F /* MJChiBaoZiHeader.m */,
			);
			path = RefreshCostom;
			sourceTree = "<group>";
		};
		65C0766221BC88E100F5EB1F /* DJStatusBarHUD */ = {
			isa = PBXGroup;
			children = (
				65C0766321BC88E100F5EB1F /* DJStatusBarHUD.h */,
				65C0766421BC88E100F5EB1F /* DJStatusBarHUD.bundle */,
				65C0766521BC88E100F5EB1F /* DJStatusBarHUD.m */,
			);
			path = DJStatusBarHUD;
			sourceTree = "<group>";
		};
		65DC0CDB21AAD886009C7673 /* UICollectionViewLeftAlignedLayout */ = {
			isa = PBXGroup;
			children = (
				65DC0CDC21AAD886009C7673 /* UICollectionViewLeftAlignedLayout.h */,
				65DC0CDD21AAD886009C7673 /* UICollectionViewLeftAlignedLayout.m */,
				65DC0CDE21AAD886009C7673 /* Info.plist */,
			);
			path = UICollectionViewLeftAlignedLayout;
			sourceTree = "<group>";
		};
		65F6863D2189A2F0003D37F6 /* ptotoBrowser */ = {
			isa = PBXGroup;
			children = (
				65F686492189A2F0003D37F6 /* HZPhotoBrowser.h */,
				65F686412189A2F0003D37F6 /* HZPhotoBrowser.m */,
				65F686422189A2F0003D37F6 /* HZPhotoBrowserView.h */,
				65F686482189A2F0003D37F6 /* HZPhotoBrowserView.m */,
				65F686452189A2F0003D37F6 /* HZWaitingView.h */,
				65F6863F2189A2F0003D37F6 /* HZWaitingView.m */,
				65F686502189A6EF003D37F6 /* HZPhotoBrowserConfig.h */,
				65F686472189A2F0003D37F6 /* HZPhotoBrowser.bundle */,
			);
			path = ptotoBrowser;
			sourceTree = "<group>";
		};
		920E326522523D7A000AC41D /* NSAttributedString */ = {
			isa = PBXGroup;
			children = (
				920E326622523D9A000AC41D /* NSAttributedString+BoundingRect.h */,
				920E326722523D9A000AC41D /* NSAttributedString+BoundingRect.m */,
			);
			path = NSAttributedString;
			sourceTree = "<group>";
		};
		9230686822531EBA0034DF02 /* PlayerView */ = {
			isa = PBXGroup;
			children = (
				92B1F4EA22264D80004ADD4A /* FTHZMusicPlayerView.h */,
				92B1F4EB22264D80004ADD4A /* FTHZMusicPlayerView.m */,
			);
			path = PlayerView;
			sourceTree = "<group>";
		};
		9230686922531EC80034DF02 /* PhotoGroup */ = {
			isa = PBXGroup;
			children = (
				************************ /* HZPhotoGroupOld.h */,
				************************ /* HZPhotoGroupOld.m */,
				FC7945132D5DA10400CA6A6C /* FTHZSimpleVideoPlayer.h */,
				************************ /* FTHZSimpleVideoPlayer.m */,
				65F6863E2189A2F0003D37F6 /* HZPhotoGroup.h */,
				65F686442189A2F0003D37F6 /* HZPhotoGroup.m */,
				63AFBAE823B99774000251ED /* HZPhotoSkipGroup.h */,
				63AFBAE923B99774000251ED /* HZPhotoSkipGroup.m */,
			);
			path = PhotoGroup;
			sourceTree = "<group>";
		};
		9230686A22531ED50034DF02 /* FeedCard */ = {
			isa = PBXGroup;
			children = (
				925CAAD22250900000E19218 /* FTHZFeedCardContentView.h */,
				925CAAD32250900000E19218 /* FTHZFeedCardContentView.m */,
				9230686B22531F050034DF02 /* FTHZFeedNormalCardView.h */,
				9230686C22531F050034DF02 /* FTHZFeedNormalCardView.m */,
			);
			path = FeedCard;
			sourceTree = "<group>";
		};
		9230686F2253937C0034DF02 /* OceanFeed */ = {
			isa = PBXGroup;
			children = (
				92306870225393A40034DF02 /* FTHZOceanFeedListViewController.h */,
				92306871225393A40034DF02 /* FTHZOceanFeedListViewController.m */,
			);
			path = OceanFeed;
			sourceTree = "<group>";
		};
		92444E8B224A79420092BCF8 /* Global */ = {
			isa = PBXGroup;
			children = (
				659980752181987200FB7EEA /* PrefixHeader.pch */,
				92444E87224A77380092BCF8 /* AppConfig.h */,
				92444E88224A77380092BCF8 /* AppConfig.m */,
				635932692418114500ABB2A1 /* FTHZ_Bridging_Header.h */,
				63593312242F615A00ABB2A1 /* FZKeyedArchiver.h */,
				63593313242F615A00ABB2A1 /* FZKeyedArchiver.m */,
			);
			path = Global;
			sourceTree = "<group>";
		};
		924E5EE8222953860002C231 /* DialogPresentationController */ = {
			isa = PBXGroup;
			children = (
				924E5EE9222953A20002C231 /* FTHZDialogPresentationController.h */,
				924E5EEA222953A20002C231 /* FTHZDialogPresentationController.m */,
			);
			path = DialogPresentationController;
			sourceTree = "<group>";
		};
		9253B029223C877A00798AE9 /* ProjectConfig */ = {
			isa = PBXGroup;
			children = (
				653136EE217F0C0C0008DD48 /* Info.plist */,
				9253B02B223C878D00798AE9 /* Dev */,
				9253B02A223C878700798AE9 /* Release */,
				9254FF3A2249D1D1007D5569 /* AppConfig.xcconfig */,
			);
			path = ProjectConfig;
			sourceTree = "<group>";
		};
		9253B02A223C878700798AE9 /* Release */ = {
			isa = PBXGroup;
			children = (
				9253B103223C882800798AE9 /* Config-Release-Debug.xcconfig */,
				9253B105223C88C600798AE9 /* Config-Release-Release.xcconfig */,
			);
			path = Release;
			sourceTree = "<group>";
		};
		9253B02B223C878D00798AE9 /* Dev */ = {
			isa = PBXGroup;
			children = (
				9253B102223C881F00798AE9 /* Config-Dev-Debug.xcconfig */,
				9253B104223C88BB00798AE9 /* Config-Dev-Release.xcconfig */,
			);
			path = Dev;
			sourceTree = "<group>";
		};
		9254FF3522491793007D5569 /* Network */ = {
			isa = PBXGroup;
			children = (
				FCB2CA052DD58A1500E4F222 /* FTHZRequestEncryptor.h */,
				FCB2CA062DD58A1500E4F222 /* FTHZRequestEncryptor.m */,
				926528212259B8B100A84B21 /* BizParser */,
				9254FF362249188D007D5569 /* FTHZNetworkManager.h */,
				9254FF372249188D007D5569 /* FTHZNetworkManager.m */,
			);
			path = Network;
			sourceTree = "<group>";
		};
		9254FF39224918CE007D5569 /* Model */ = {
			isa = PBXGroup;
			children = (
				928B5647226C0AF50035BE45 /* TopicModel.h */,
				928B5648226C0AF50035BE45 /* TopicModel.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		925CAAB8225068FF00E19218 /* FTHZUI */ = {
			isa = PBXGroup;
			children = (
				92F1DBEF22649FD0009C6737 /* NestListViewController */,
				925CAABE22506C7100E19218 /* ListViewController */,
				925CAABD22506C6400E19218 /* ViewController */,
			);
			path = FTHZUI;
			sourceTree = "<group>";
		};
		925CAABD22506C6400E19218 /* ViewController */ = {
			isa = PBXGroup;
			children = (
				925CAAB92250692C00E19218 /* FTHZViewController.h */,
				925CAABA2250692C00E19218 /* FTHZViewController.m */,
			);
			path = ViewController;
			sourceTree = "<group>";
		};
		925CAABE22506C7100E19218 /* ListViewController */ = {
			isa = PBXGroup;
			children = (
				925CAABF22506CBB00E19218 /* FTHZTableView.h */,
				925CAAC022506CBB00E19218 /* FTHZTableView.m */,
				925CAAC322506D4500E19218 /* FTHZListViewController.h */,
				925CAAC422506D4500E19218 /* FTHZListViewController.m */,
				925CAAC7225070F500E19218 /* FTHZListViewElement.h */,
				925CAAC8225070F500E19218 /* FTHZListViewElement.m */,
			);
			path = ListViewController;
			sourceTree = "<group>";
		};
		925CAACB22508E8000E19218 /* Sections */ = {
			isa = PBXGroup;
			children = (
				9265283B225A457B00A84B21 /* Business */,
				6530FBAA2184189D00166841 /* Login */,
				659980632181953F00FB7EEA /* Find */,
				659980602181953F00FB7EEA /* Attention */,
				659980662181953F00FB7EEA /* Message */,
				659980692181953F00FB7EEA /* Mine */,
				925CAAD122508FD400E19218 /* Feed */,
			);
			path = Sections;
			sourceTree = "<group>";
		};
		925CAACC22508E8600E19218 /* FeedList(Feed流) */ = {
			isa = PBXGroup;
			children = (
				9230686F2253937C0034DF02 /* OceanFeed */,
				925CAACD22508EA100E19218 /* FTHZFeedListViewController.h */,
				925CAACE22508EA100E19218 /* FTHZFeedListViewController.m */,
			);
			path = "FeedList(Feed流)";
			sourceTree = "<group>";
		};
		925CAAD122508FD400E19218 /* Feed */ = {
			isa = PBXGroup;
			children = (
				92D484AA2262CE3B00221A7C /* View */,
				925CAACC22508E8600E19218 /* FeedList(Feed流) */,
				92D484A52262CD4D00221A7C /* FeedDetail */,
			);
			path = Feed;
			sourceTree = "<group>";
		};
		925CAADD2250F2B100E19218 /* Extension */ = {
			isa = PBXGroup;
			children = (
				63D92907243AFEFB00943130 /* UIImageView */,
				92652849225B6FEA00A84B21 /* DispatchQueue */,
				92652844225AF92600A84B21 /* UIView */,
				920E326522523D7A000AC41D /* NSAttributedString */,
				925CAADE2250F2CB00E19218 /* UIViewLayout */,
			);
			path = Extension;
			sourceTree = "<group>";
		};
		925CAADE2250F2CB00E19218 /* UIViewLayout */ = {
			isa = PBXGroup;
			children = (
				659B64DE21A2826000DC13B8 /* UIView+Frame.h */,
				659B64DD21A2826000DC13B8 /* UIView+Frame.m */,
			);
			path = UIViewLayout;
			sourceTree = "<group>";
		};
		925CAADF225105F600E19218 /* ToolbarButton */ = {
			isa = PBXGroup;
			children = (
				925CAAE02251063E00E19218 /* FTHZToolbarButton.h */,
				925CAAE12251063E00E19218 /* FTHZToolbarButton.m */,
			);
			path = ToolbarButton;
			sourceTree = "<group>";
		};
		92652813225994BE00A84B21 /* Definition */ = {
			isa = PBXGroup;
			children = (
				92652814225994CE00A84B21 /* ErrorCode.h */,
				92652815225994CE00A84B21 /* ErrorCode.m */,
			);
			path = Definition;
			sourceTree = "<group>";
		};
		9265281C225999C900A84B21 /* API */ = {
			isa = PBXGroup;
			children = (
				928B5643226C061F0035BE45 /* FTHZNetworkTask+Topic.h */,
				9265281D22599A0A00A84B21 /* FTHZNetworkTask+OceanAPI.h */,
				9265281E22599A0A00A84B21 /* FTHZNetworkTask+OceanAPI.m */,
				9265282F2259FD5A00A84B21 /* FTHZNetworkTask+UserInfo.h */,
				926528302259FD5A00A84B21 /* FTHZNetworkTask+UserInfo.m */,
				92D4849C2261E5BA00221A7C /* FTHZNetworkTask+Feed.h */,
				92D4849D2261E5BA00221A7C /* FTHZNetworkTask+Feed.m */,
				928B5644226C061F0035BE45 /* FTHZNetworkTask+Topic.m */,
			);
			path = API;
			sourceTree = "<group>";
		};
		926528212259B8B100A84B21 /* BizParser */ = {
			isa = PBXGroup;
			children = (
				926528222259BAC300A84B21 /* FTHZUniversalBizParser.h */,
				926528232259BAC300A84B21 /* FTHZUniversalBizParser.m */,
				926528262259C4C300A84B21 /* FTHZListDataParser.h */,
				926528272259C4C300A84B21 /* FTHZListDataParser.m */,
				926528332259FEB500A84B21 /* FTHZObjectDataParser.h */,
				926528342259FEB500A84B21 /* FTHZObjectDataParser.m */,
			);
			path = BizParser;
			sourceTree = "<group>";
		};
		9265282A2259D17600A84B21 /* AccountManager */ = {
			isa = PBXGroup;
			children = (
				9265282B2259D18500A84B21 /* FTHZAccountManager.h */,
				9265282C2259D18500A84B21 /* FTHZAccountManager.m */,
			);
			path = AccountManager;
			sourceTree = "<group>";
		};
		9265283B225A457B00A84B21 /* Business */ = {
			isa = PBXGroup;
			children = (
				92652840225AD3F000A84B21 /* FTHZBusiness.h */,
				92652841225AD3F000A84B21 /* FTHZBusiness.m */,
				9265283C225A458600A84B21 /* LoginBusiness.h */,
				9265283D225A458600A84B21 /* LoginBusiness.m */,
				92D484982261E56E00221A7C /* FeedBusiness.h */,
				92D484992261E56E00221A7C /* FeedBusiness.m */,
			);
			path = Business;
			sourceTree = "<group>";
		};
		92652844225AF92600A84B21 /* UIView */ = {
			isa = PBXGroup;
			children = (
				92652845225AF98600A84B21 /* UIViewController+ViewHierarchy.h */,
				92652846225AF98600A84B21 /* UIViewController+ViewHierarchy.m */,
				926AF275225CCE4A003D0435 /* UIView+RAC.h */,
				926AF276225CCE4A003D0435 /* UIView+RAC.m */,
			);
			path = UIView;
			sourceTree = "<group>";
		};
		92652849225B6FEA00A84B21 /* DispatchQueue */ = {
			isa = PBXGroup;
			children = (
				9265284A225B700000A84B21 /* DispatchQueue+Mark.h */,
				9265284B225B700000A84B21 /* DispatchQueue+Mark.m */,
			);
			path = DispatchQueue;
			sourceTree = "<group>";
		};
		927CC26B222B769F00654DE3 /* Components */ = {
			isa = PBXGroup;
			children = (
				FCB2CA1A2DD70AEB00E4F222 /* TZImagePickerManager */,
				92D484A02262078800221A7C /* Coordinator */,
				9265282A2259D17600A84B21 /* AccountManager */,
				9254FF3522491793007D5569 /* Network */,
				92BE1536222EBAC700380B3D /* ConfigCenter */,
				92C4D0B2222E049B00A58687 /* LocationManager */,
				927CC26C222B76AC00654DE3 /* MusicPlayer */,
			);
			path = Components;
			sourceTree = "<group>";
		};
		927CC26C222B76AC00654DE3 /* MusicPlayer */ = {
			isa = PBXGroup;
			children = (
				927CC26D222B76C000654DE3 /* FTHZMusicPlayer.h */,
				927CC26E222B76C000654DE3 /* FTHZMusicPlayer.m */,
			);
			path = MusicPlayer;
			sourceTree = "<group>";
		};
		92B5C651225441EE002ABA55 /* RefreshControl */ = {
			isa = PBXGroup;
			children = (
				92B5C65222544216002ABA55 /* FTHZRefreshHeader.h */,
				92B5C65322544216002ABA55 /* FTHZRefreshHeader.m */,
				92B5C6562254AF5F002ABA55 /* FTHZRefreshFooter.h */,
				92B5C6572254AF5F002ABA55 /* FTHZRefreshFooter.m */,
			);
			path = RefreshControl;
			sourceTree = "<group>";
		};
		92BE1536222EBAC700380B3D /* ConfigCenter */ = {
			isa = PBXGroup;
			children = (
				92BE1537222EBAD900380B3D /* FTHZUserConfig.h */,
				92BE1538222EBAD900380B3D /* FTHZUserConfig.m */,
				92652837225A37D200A84B21 /* FTHZGlobalConfig.h */,
				92652838225A37D200A84B21 /* FTHZGlobalConfig.m */,
			);
			path = ConfigCenter;
			sourceTree = "<group>";
		};
		92C4D0B2222E049B00A58687 /* LocationManager */ = {
			isa = PBXGroup;
			children = (
				92C4D0B3222E04AE00A58687 /* FTHZLocationManager.h */,
				92C4D0B4222E04AE00A58687 /* FTHZLocationManager.m */,
			);
			path = LocationManager;
			sourceTree = "<group>";
		};
		92D484A02262078800221A7C /* Coordinator */ = {
			isa = PBXGroup;
			children = (
				92D484A12262079200221A7C /* FTHZCoordinator.h */,
				92D484A22262079200221A7C /* FTHZCoordinator.m */,
			);
			path = Coordinator;
			sourceTree = "<group>";
		};
		92D484A52262CD4D00221A7C /* FeedDetail */ = {
			isa = PBXGroup;
			children = (
				92D484A62262CD6C00221A7C /* FeedDetailViewController.h */,
				92D484A72262CD6C00221A7C /* FeedDetailViewController.m */,
			);
			path = FeedDetail;
			sourceTree = "<group>";
		};
		92D484AA2262CE3B00221A7C /* View */ = {
			isa = PBXGroup;
			children = (
				92D484B62262DF7300221A7C /* UserCommentRecordView */,
				92D484AB2262CE4F00221A7C /* UpvoteUserRecordView */,
				9230686822531EBA0034DF02 /* PlayerView */,
				9230686922531EC80034DF02 /* PhotoGroup */,
				9230686A22531ED50034DF02 /* FeedCard */,
			);
			path = View;
			sourceTree = "<group>";
		};
		92D484AB2262CE4F00221A7C /* UpvoteUserRecordView */ = {
			isa = PBXGroup;
			children = (
				92D484AC2262CE7300221A7C /* FTHZUpvoteUserRecordView.h */,
				92D484AD2262CE7300221A7C /* FTHZUpvoteUserRecordView.m */,
			);
			path = UpvoteUserRecordView;
			sourceTree = "<group>";
		};
		92D484B02262CEB700221A7C /* View */ = {
			isa = PBXGroup;
			children = (
				FC089B342E090916000DDB5D /* FTHZRadialMenuView.h */,
				FC089B352E090916000DDB5D /* FTHZRadialMenuView.m */,
				92F1DBFD2267F8D8009C6737 /* AlertDialog */,
				92F1DBF82266F993009C6737 /* PopMenu */,
				92D484B12262CEBF00221A7C /* Avatar */,
			);
			path = View;
			sourceTree = "<group>";
		};
		92D484B12262CEBF00221A7C /* Avatar */ = {
			isa = PBXGroup;
			children = (
				92D484B22262CED800221A7C /* FTHZAvatarView.h */,
				92D484B32262CED800221A7C /* FTHZAvatarView.m */,
			);
			path = Avatar;
			sourceTree = "<group>";
		};
		92D484B62262DF7300221A7C /* UserCommentRecordView */ = {
			isa = PBXGroup;
			children = (
				92D484B72262DFAD00221A7C /* UserTopLevelCommentRecordView.h */,
				92D484B82262DFAD00221A7C /* UserTopLevelCommentRecordView.m */,
			);
			path = UserCommentRecordView;
			sourceTree = "<group>";
		};
		92F1DBEF22649FD0009C6737 /* NestListViewController */ = {
			isa = PBXGroup;
			children = (
				92F1DBF42264B777009C6737 /* FTHZSegmentView.h */,
				92F1DBF52264B777009C6737 /* FTHZSegmentView.m */,
				92F1DBF022649FE1009C6737 /* FTHZNestSegmentListViewController.h */,
				92F1DBF122649FE1009C6737 /* FTHZNestSegmentListViewController.m */,
			);
			path = NestListViewController;
			sourceTree = "<group>";
		};
		92F1DBF82266F993009C6737 /* PopMenu */ = {
			isa = PBXGroup;
			children = (
				92F1DBF92266F9AE009C6737 /* FTHZPopMenuController.h */,
				92F1DBFA2266F9AE009C6737 /* FTHZPopMenuController.m */,
			);
			path = PopMenu;
			sourceTree = "<group>";
		};
		92F1DBFD2267F8D8009C6737 /* AlertDialog */ = {
			isa = PBXGroup;
			children = (
				92F1DBFE2267F8EC009C6737 /* FTHZAlertDialog.h */,
				92F1DBFF2267F8EC009C6737 /* FTHZAlertDialog.m */,
			);
			path = AlertDialog;
			sourceTree = "<group>";
		};
		D38E47FF6DF166262AFC23BF /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				6328EEE524B4232D009168A3 /* libsqlite3.0.tbd */,
				696D73902297CB2B00BEB77D /* libc++.tbd */,
				696D738E2297CB1200BEB77D /* Security.framework */,
				696D738C2297CB0900BEB77D /* CoreMotion.framework */,
				696D738A2297CAEB00BEB77D /* AdSupport.framework */,
				65168B8A21E84112002CF1D3 /* UserNotifications.framework */,
				653D54F821D8A5150054B986 /* CoreLocation.framework */,
				C5E9C60E21D26B0C0019D2AC /* AVFoundation.framework */,
				65C0765A21B6A83600F5EB1F /* libz.tbd */,
				6530FBCB2185C73200166841 /* libsqlite3.dylib */,
				6530FBC92185C6A200166841 /* libz.1.dylib */,
				6530FBC62185C60700166841 /* libc++.1.dylib */,
				6530FBC42185C3D800166841 /* SystemConfiguration.framework */,
				6530FBC22185C3C600166841 /* CoreTelephony.framework */,
				0AF71898A267C41AD9671FF2 /* Pods_FTHZ.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		FCB2CA1A2DD70AEB00E4F222 /* TZImagePickerManager */ = {
			isa = PBXGroup;
			children = (
				FCB2CA182DD70AEB00E4F222 /* TZImagePickerManager.h */,
				FCB2CA192DD70AEB00E4F222 /* TZImagePickerManager.m */,
			);
			path = TZImagePickerManager;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		653136DC217F0C0A0008DD48 /* FTHZ */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 65313709217F0C0C0008DD48 /* Build configuration list for PBXNativeTarget "FTHZ" */;
			buildPhases = (
				A111BF29D3E9F51A5EF2CF1A /* [CP] Check Pods Manifest.lock */,
				653136D9217F0C0A0008DD48 /* Sources */,
				653136DA217F0C0A0008DD48 /* Frameworks */,
				653136DB217F0C0A0008DD48 /* Resources */,
				634014C3245759BD00961DE8 /* Embed Frameworks */,
				BCEBB91CC55367EF8EF6EF70 /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				FC892CF42D6D52BF0048DFA4 /* Models */,
			);
			name = FTHZ;
			productName = FTHZ;
			productReference = 653136DD217F0C0A0008DD48 /* 52hz.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		653136D5217F0C0A0008DD48 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				CLASSPREFIX = FZ;
				LastUpgradeCheck = 1210;
				ORGANIZATIONNAME = 52hezi;
				TargetAttributes = {
					653136DC217F0C0A0008DD48 = {
						CreatedOnToolsVersion = 10.0;
						LastSwiftMigration = 1130;
						SystemCapabilities = {
							com.apple.BackgroundModes = {
								enabled = 1;
							};
							com.apple.Push = {
								enabled = 1;
							};
						};
					};
				};
			};
			buildConfigurationList = 653136D8217F0C0A0008DD48 /* Build configuration list for PBXProject "FTHZ" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 653136D4217F0C0A0008DD48;
			productRefGroup = 653136DE217F0C0A0008DD48 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				653136DC217F0C0A0008DD48 /* FTHZ */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		653136DB217F0C0A0008DD48 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				653D54DC21D383440054B986 /* <EMAIL> in Resources */,
				653D54D821D383440054B986 /* <EMAIL> in Resources */,
				653D54D921D383440054B986 /* <EMAIL> in Resources */,
				63B2646B24B70603005DF1C9 /* README.md in Resources */,
				653D54E021D383440054B986 /* <EMAIL> in Resources */,
				65FF4CB421C36F58008FAAC4 /* LaunchScreen.storyboard in Resources */,
				653D54E121D383450054B986 /* <EMAIL> in Resources */,
				65F6864E2189A2F0003D37F6 /* HZPhotoBrowser.bundle in Resources */,
				650A087521C0C4EC0004A588 /* 52yinsi.txt in Resources */,
				6372A19B246C49B800F998E1 /* gestures in Resources */,
				65C0766621BC88E100F5EB1F /* DJStatusBarHUD.bundle in Resources */,
				653D54DF21D383440054B986 /* <EMAIL> in Resources */,
				653D54DD21D383440054B986 /* <EMAIL> in Resources */,
				650A087B21C0D67F0004A588 /* 52fuwu.txt in Resources */,
				65313716217F0FB90008DD48 /* Assets.xcassets in Resources */,
				653D54DA21D383440054B986 /* <EMAIL> in Resources */,
				653D54DB21D383440054B986 /* <EMAIL> in Resources */,
				653D54DE21D383440054B986 /* <EMAIL> in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		A111BF29D3E9F51A5EF2CF1A /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-FTHZ-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		BCEBB91CC55367EF8EF6EF70 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-FTHZ/Pods-FTHZ-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-FTHZ/Pods-FTHZ-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-FTHZ/Pods-FTHZ-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		653136D9217F0C0A0008DD48 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				************************ /* RecommendSearchModel.swift in Sources */,
				925CAAC622506D4500E19218 /* FTHZListViewController.m in Sources */,
				6372A16D2465D00C00F998E1 /* AndroidGesturesImporter.swift in Sources */,
				6557451C21CCD34D00855BE2 /* BlackUserModel.m in Sources */,
				63AFBAE323B7EB62000251ED /* FTHZInviteShareVC.m in Sources */,
				925CAAD022508EA100E19218 /* FTHZFeedListViewController.m in Sources */,
				926528292259C4C300A84B21 /* FTHZListDataParser.m in Sources */,
				657AC6A221CA54D500674FE2 /* IPStatusModel.m in Sources */,
				6557452221CCDFC900855BE2 /* BlackUserVC.m in Sources */,
				FCF3C3F32A7C959900599FA4 /* VerifyTableViewCell.m in Sources */,
				656459CD21FC7E290089660C /* AttentionMePageModel.m in Sources */,
				6516CE2321A83A9600703E14 /* DynamicCommentVC.m in Sources */,
				928B564A226C0AF50035BE45 /* TopicModel.m in Sources */,
				928B5652226C44700035BE45 /* AttentionContainerViewController.m in Sources */,
				63593317242F621B00ABB2A1 /* FZDraftModel.m in Sources */,
				FC0E17182D76DA6C008430CF /* OperationTableViewCell.m in Sources */,
				635932EF242D1C5300ABB2A1 /* NetworkManager.swift in Sources */,
				6581CA00219A94AD00424564 /* SegmentViewController.m in Sources */,
				FCDD07332D7EE8B0003B7FF4 /* VerifyCodeModel.m in Sources */,
				659980962181AF6B00FB7EEA /* UITextView+YLTextView.m in Sources */,
				650A086721BFE31F0004A588 /* WhaleHeaderView.m in Sources */,
				634E11BF2444EE7400175027 /* TNSlider.swift in Sources */,
				659B653221A5202B00DC13B8 /* UserInformationVC.m in Sources */,
				650A088A21C28CF60004A588 /* ChangeUserVC.m in Sources */,
				63AFBAD023A7843C000251ED /* FTHZInviteCodeVC.m in Sources */,
				659891012181B7A600BF1327 /* NSDate+VTB.m in Sources */,
				9230686D22531F050034DF02 /* FTHZFeedNormalCardView.m in Sources */,
				9265283F225A458600A84B21 /* LoginBusiness.m in Sources */,
				65C0765021B57A6300F5EB1F /* AffairCommentsModel.m in Sources */,
				FC0FB1482D8CFC2A0024E5B2 /* FTHZTabBarController.m in Sources */,
				65F6864C2189A2F0003D37F6 /* HZPhotoGroup.m in Sources */,
				659B9F8321C8E4C60040651F /* MessageTableViewCell.m in Sources */,
				6359328A2425F12A00ABB2A1 /* FZConstants.swift in Sources */,
				FC0E17252D7AC2C9008430CF /* WaveView.m in Sources */,
				652B1082218AD92300D31B3F /* DynamicDetailVC.m in Sources */,
				************************ /* DescPopupView.swift in Sources */,
				63593314242F615A00ABB2A1 /* FZKeyedArchiver.m in Sources */,
				926528252259BAC300A84B21 /* FTHZUniversalBizParser.m in Sources */,
				6516CE1A21A8213900703E14 /* AttentionCommentTableViewCell.m in Sources */,
				FC8C71EE2D6576B800651BD1 /* ChangeAccountVC.m in Sources */,
				65C0765621B62BA400F5EB1F /* DyLikeTableViewCell.m in Sources */,
				FCF3C3ED2A7B7A1F00599FA4 /* verifyModel.m in Sources */,
				659B9F8421C8E4C60040651F /* IMUserInfoModel.m in Sources */,
				6359331D2434A61800ABB2A1 /* otherUserHeadView.swift in Sources */,
				635933192432531C00ABB2A1 /* FZMyHomePageVC.swift in Sources */,
				6355FCC72440C54E00224A7F /* ZCycleView.swift in Sources */,
				8C9F1D8322BC7E750047DC08 /* FTHZChannelImgReplyVC.m in Sources */,
				653D54F421D4BEB20054B986 /* LoadInvitationCodeModel.m in Sources */,
				9265284D225B700000A84B21 /* DispatchQueue+Mark.m in Sources */,
				FC0FB1692D9556F60024E5B2 /* SearchFriendsModel.m in Sources */,
				65F6864B2189A2F0003D37F6 /* HZPhotoBrowser.m in Sources */,
				6534E5F721C38247002DBEF6 /* AddAttentionModel.m in Sources */,
				635932ED242D1C2A00ABB2A1 /* Response.swift in Sources */,
				63FDD8B623BB369000817917 /* FTHZHerzDetailVC.m in Sources */,
				635932D5242A856D00ABB2A1 /* UIButton.swift in Sources */,
				6355FCC82440C54E00224A7F /* ZCycleViewCell.swift in Sources */,
				650A087E21C0E0AC0004A588 /* OtherUserInfo.m in Sources */,
				926AF278225CCE4A003D0435 /* UIView+RAC.m in Sources */,
				659B652C21A4FB4400DC13B8 /* SettingVC.m in Sources */,
				63D92913243E29C700943130 /* FZMomentLikeListVC.swift in Sources */,
				6557452E21CF7CAC00855BE2 /* MessageNotificationModel.m in Sources */,
				657C9AA021CA482E00681717 /* MessageDetailVC.m in Sources */,
				65168B4221E6F4CB002CF1D3 /* UserMessageNumberModel.m in Sources */,
				FC8C71E82D646FFC00651BD1 /* ChangePasswordModel.m in Sources */,
				6359331B2434779600ABB2A1 /* homepageHeadView.swift in Sources */,
				6313C15E24BDEB3100385E9C /* CALayer.swift in Sources */,
				65F685B521898F23003D37F6 /* AffairListModel.m in Sources */,
				925CAAE32251063E00E19218 /* FTHZToolbarButton.m in Sources */,
				6355FCBE243FA03300224A7F /* FZPlayVC.swift in Sources */,
				6547B55121A6898C005E7DA2 /* CertificateModel.m in Sources */,
				65DC0CD421AAA1AD009C7673 /* HeziChouseTwoVC.m in Sources */,
				FC0E17152D76D0F4008430CF /* OperationsModel.m in Sources */,
				92F1DBFC2266F9AE009C6737 /* FTHZPopMenuController.m in Sources */,
				92F1DBF72264B777009C6737 /* FTHZSegmentView.m in Sources */,
				657E9B7721C9F21C009029E6 /* MJChiBaoZiFooter.m in Sources */,
				653D54EE21D4A98F0054B986 /* InvitationCodeVC.m in Sources */,
				656459C421E9AA5B0089660C /* UserdeviceTokenModel.m in Sources */,
				925CAAC222506CBB00E19218 /* FTHZTableView.m in Sources */,
				6557453121CF817600855BE2 /* MessageNotificationTableViewCell.m in Sources */,
				6533696F21DF36A9004E1782 /* IMNewAffairDetailModel.m in Sources */,
				92D484BA2262DFAD00221A7C /* UserTopLevelCommentRecordView.m in Sources */,
				65F6864A2189A2F0003D37F6 /* HZWaitingView.m in Sources */,
				63D92904243A4D2700943130 /* momentDetailView.swift in Sources */,
				63FDD8BA23BB773800817917 /* FTHZHerzListCell.m in Sources */,
				6530FBCF2187FA3000166841 /* DynamicVC.m in Sources */,
				6516CE1721A8212100703E14 /* AttentionLikeTableViewCell.m in Sources */,
				925CAACA225070F500E19218 /* FTHZListViewElement.m in Sources */,
				653D54D721D383440054B986 /* LGAudioPlayer.m in Sources */,
				6557452B21CD22F000855BE2 /* DoOutBlackModel.m in Sources */,
				659891042181BA3E00BF1327 /* CurrencyRootVC.m in Sources */,
				659B652621A430EC00DC13B8 /* MyAttentionTableViewCell.m in Sources */,
				658CCB7E21AD27D5003A93CC /* RateCalculateModel.m in Sources */,
				635932FF242DF2E300ABB2A1 /* Updatable.swift in Sources */,
				92444E8A224A77380092BCF8 /* AppConfig.m in Sources */,
				FCB2CA072DD58A1500E4F222 /* FTHZRequestEncryptor.m in Sources */,
				8C9F1D7F22BBAEE70047DC08 /* FTHZChannelReplyModel.m in Sources */,
				65DC0CDF21AAD886009C7673 /* UICollectionViewLeftAlignedLayout.m in Sources */,
				FCA89E632A6FB75600B79271 /* LiuyanModel.m in Sources */,
				63B2646A24B70603005DF1C9 /* GameAudioPlayer.swift in Sources */,
				6359329224271D8900ABB2A1 /* UIFont.swift in Sources */,
				6581CA21219D819E00424564 /* KingIdentifyingView.m in Sources */,
				65168B3B21E5F4CF002CF1D3 /* UIImage+Wechat.m in Sources */,
				659B652021A2A97C00DC13B8 /* SNSCodeCountdownButton.m in Sources */,
				************************ /* FTHZVideoPlayerView.swift in Sources */,
				6328EEE024AB14AE009168A3 /* SwipeScrollView.swift in Sources */,
				6302B4F124371FCA00FCB8C4 /* SearchCell.swift in Sources */,
				************************ /* ShudongTableViewCell.m in Sources */,
				65C0763B21AF8B6F00F5EB1F /* MJChiBaoZiHeader.m in Sources */,
				************************ /* ShudongCardCell.m in Sources */,
				634E11B324434FA500175027 /* StraitCell.swift in Sources */,
				FC8C71EB2D64700900651BD1 /* ChangePasswordVC.m in Sources */,
				65DC0CE321ABE55E009C7673 /* HeziShareVC.m in Sources */,
				921B651722282F0100D8E2DF /* FTHZInputMusicURLDialogController.m in Sources */,
				6534E60021C39BB9002DBEF6 /* MyAttentionModel.m in Sources */,
				635932FE242DF2E300ABB2A1 /* CellConfigurator.swift in Sources */,
				6355FCD62441637200224A7F /* StriatNavLayout.swift in Sources */,
				6334870024477F990080D3F0 /* FZMessageVC.swift in Sources */,
				9265282022599A0A00A84B21 /* FTHZNetworkTask+OceanAPI.m in Sources */,
				FC0E171A2D77E984008430CF /* FZOperationDetailVC.swift in Sources */,
				656459D021FC7E450089660C /* MyAttentionPageModel.m in Sources */,
				65DC0CDA21AACD63009C7673 /* HeziChouseCollectionViewCell.m in Sources */,
				FCF3C3EA2A7B46DC00599FA4 /* verifyViewController.m in Sources */,
				6530FBB8218489FE00166841 /* Compute.m in Sources */,
				6359326E2423EFEB00ABB2A1 /* FZMineVC.swift in Sources */,
				65C0764721B3B4C600F5EB1F /* WhaleListModel.m in Sources */,
				6533695921D9256A004E1782 /* DynamicSegmentHeaderView.m in Sources */,
				63DC11582437A79C00A1035A /* AttentionSegmentViewController.m in Sources */,
				65168B3F21E63578002CF1D3 /* GetMessageNumberModel.m in Sources */,
				659980862181AA4300FB7EEA /* BaseJsonModel.m in Sources */,
				6372A16C2465D00C00F998E1 /* ImportedGestures.swift in Sources */,
				6533696321DB30D0004E1782 /* XHInputView.m in Sources */,
				92B1F4EC22264D80004ADD4A /* FTHZMusicPlayerView.m in Sources */,
				6355FCD02440EEF600224A7F /* FZCollectionLayout.swift in Sources */,
				65C0766721BC88E100F5EB1F /* DJStatusBarHUD.m in Sources */,
				65C0766D21BCAA8900F5EB1F /* ChangeNameVC.m in Sources */,
				92652848225AF98600A84B21 /* UIViewController+ViewHierarchy.m in Sources */,
				63D92918243EEFA000943130 /* FZMyLikeListVC.swift in Sources */,
				92B5C65522544216002ABA55 /* FTHZRefreshHeader.m in Sources */,
				650A088421C23E180004A588 /* ChangeSigelVC.m in Sources */,
				FCB2CA1B2DD70AEB00E4F222 /* TZImagePickerManager.m in Sources */,
				6530FBD5218858AB00166841 /* DynamicTableViewCell.m in Sources */,
				650A086421BF9D9A0004A588 /* ChangeTagVC.m in Sources */,
				635932972427481D00ABB2A1 /* UIImageView.swift in Sources */,
				63CD86D92475AC30001DA4FF /* Toast.swift in Sources */,
				653D54EB21D461340054B986 /* MessageChangeStatsModel.m in Sources */,
				65DC0C3E21A9868E009C7673 /* ReleaseDynamicVC.m in Sources */,
				6355FCBA243F953900224A7F /* MyLikedCell.swift in Sources */,
				635932F8242DCD2B00ABB2A1 /* FZDraftVC.swift in Sources */,
				65C0764A21B434A600F5EB1F /* AffairLikesModel.m in Sources */,
				63DC11552437A78600A1035A /* AttentionSegmentView.m in Sources */,
				6534E5FA21C3825E002DBEF6 /* ReleaseAttentionModel.m in Sources */,
				657AC6A821CB3EE100674FE2 /* SettingTableViewCell.m in Sources */,
				************************ /* FZFollowVC.swift in Sources */,
				928B564E226C38960035BE45 /* TopicVCViewController.m in Sources */,
				634E11A12441CDA600175027 /* LabelLayoutManager.swift in Sources */,
				6328EED4249BC5C2009168A3 /* FZGiftVC.swift in Sources */,
				FCA89E602A6E0CD200B79271 /* LiuyanVC.m in Sources */,
				650A086F21C0B6040004A588 /* UserAffairListModel.m in Sources */,
				630536A422A912D0005B3878 /* FTHZChannelListVC.m in Sources */,
				659B652921A43B0F00DC13B8 /* AttentionMeTableViewCell.m in Sources */,
				635932F2242D1CBA00ABB2A1 /* FindAPI.swift in Sources */,
				8C2E412C22A763420045A50C /* FTHZStraitVC.m in Sources */,
				65C0765321B57FEE00F5EB1F /* DoCommentModel.m in Sources */,
				63D92915243E2C2D00943130 /* LikeUserCell.swift in Sources */,
				92B1F4EF22280E26004ADD4A /* MusicInfoModel.m in Sources */,
				6359329C242750A700ABB2A1 /* UIView.swift in Sources */,
				6530FBB52184456700166841 /* ValidateCode.m in Sources */,
				656459DC220073510089660C /* AffairTagListModel.m in Sources */,
				65168B4521E7AC94002CF1D3 /* UserStatsModel.m in Sources */,
				92BE1539222EBAD900380B3D /* FTHZUserConfig.m in Sources */,
				657E9B7A21CA180F009029E6 /* ReportVC.m in Sources */,
				652B1088218EA99D00D31B3F /* AffairDetailModel.m in Sources */,
				6328EEDE24A27671009168A3 /* NSDate.swift in Sources */,
				6372A1702465D00C00F998E1 /* PennyPincher.swift in Sources */,
				FCAF832A2A667D8500B787B6 /* ShudongDetailVC.m in Sources */,
				6581CA0C219A992500424564 /* MyDynamicVC.m in Sources */,
				63593310242F552C00ABB2A1 /* NKCacheManager.swift in Sources */,
				659B653521A5512200DC13B8 /* UserInformationTableViewCell.m in Sources */,
				652B107F218ACC8B00D31B3F /* WhaleTableViewCell.m in Sources */,
				6557451921CCD06C00855BE2 /* DeleteDynamicModel.m in Sources */,
				6355FCB8243F804A00224A7F /* MyLikedModel.swift in Sources */,
				657E9B7421C93537009029E6 /* MsgDetailTableViewCell.m in Sources */,
				92D4849B2261E56E00221A7C /* FeedBusiness.m in Sources */,
				6533696621DC9B55004E1782 /* FindSegmentView.m in Sources */,
				FCF3B4E82D59A5B10050133E /* LiuyanDetailView.m in Sources */,
				63D9290C243AFF0F00943130 /* UIImageView+Web.m in Sources */,
				659891102181D8BD00BF1327 /* AttentionVC.m in Sources */,
				6598910A2181C44A00BF1327 /* CurrencyNavVC.m in Sources */,
				6581CA17219D18DE00424564 /* VerificationCodeVC.m in Sources */,
				634E11AF2442DFEF00175027 /* FZHerzVC.swift in Sources */,
				659B64DF21A2826000DC13B8 /* UIView+Frame.m in Sources */,
				6359329924274A5900ABB2A1 /* UIImage.swift in Sources */,
				6534E60321C39BC4002DBEF6 /* AttentionMeModel.m in Sources */,
				658CCB7B21AC4E1E003A93CC /* RateAttributeModel.m in Sources */,
				928B5646226C061F0035BE45 /* FTHZNetworkTask+Topic.m in Sources */,
				6557452821CD172900855BE2 /* GetBlackUserModel.m in Sources */,
				926528362259FEB500A84B21 /* FTHZObjectDataParser.m in Sources */,
				6516CE1121A8205000703E14 /* AttentionLikeVC.m in Sources */,
				925CAABC2250692C00E19218 /* FTHZViewController.m in Sources */,
				656459C721E9C0590089660C /* CommenPushModel.m in Sources */,
				63593295242729FD00ABB2A1 /* FZBaseVC.swift in Sources */,
				************************ /* FTHZSimpleVideoPlayer.m in Sources */,
				63D92906243ADD5B00943130 /* String.swift in Sources */,
				920E326922523D9A000AC41D /* NSAttributedString+BoundingRect.m in Sources */,
				92D484A42262079200221A7C /* FTHZCoordinator.m in Sources */,
				6355FCCA2440C54E00224A7F /* ZPageControl.swift in Sources */,
				659980852181AA4300FB7EEA /* Http.m in Sources */,
				650A088721C253AA0004A588 /* PaoPaoCollectionViewCell.m in Sources */,
				653D54F121D4AF4C0054B986 /* InvitationCodeView.m in Sources */,
				************************ /* AccountLoginVC.m in Sources */,
				63FDD8B023BB33BC00817917 /* HQFlowView.m in Sources */,
				************************ /* ClearNotificationsModel.m in Sources */,
				FCA89E692A6FC4C100B79271 /* LiuyanListCell.m in Sources */,
				************************ /* FZCocreationVC.swift in Sources */,
				924E5EEB222953A20002C231 /* FTHZDialogPresentationController.m in Sources */,
				65C0764121B0FDE600F5EB1F /* WXLoginModel.m in Sources */,
				635932DC242CC34500ABB2A1 /* Codextended.swift in Sources */,
				65DC0CCE21AAA114009C7673 /* HeziChouseOneVC.m in Sources */,
				6533696921DC9BDB004E1782 /* FindSegmentHeaderView.m in Sources */,
				6581C9F7219A73DF00424564 /* SegmentView.m in Sources */,
				63FDD8B223BB33BC00817917 /* HQIndexBannerSubview.m in Sources */,
				63D9290D243AFF0F00943130 /* UIImage+TColor.m in Sources */,
				656459D922006E110089660C /* TagAffairVC.m in Sources */,
				6557453421CFC0D000855BE2 /* MessageDynaficDetailVC.m in Sources */,
				6359330B242EFD5C00ABB2A1 /* CopyLabel.swift in Sources */,
				630536AC22AF327D005B3878 /* FTHZChannelDetailVC.m in Sources */,
				63593300242DF2E300ABB2A1 /* ConfigurableTableViewController.swift in Sources */,
				656459DF22007EAF0089660C /* ClearBadgeModel.m in Sources */,
				653D54E221D383450054B986 /* LGSoundRecorder.m in Sources */,
				92D4849F2261E5BA00221A7C /* FTHZNetworkTask+Feed.m in Sources */,
				************************ /* ShudongModel.m in Sources */,
				FC089B362E090916000DDB5D /* FTHZRadialMenuView.m in Sources */,
				6547B55921A7E176005E7DA2 /* WhaleDetailVC.m in Sources */,
				657AC6A521CA54F600674FE2 /* POSTIPStatusModel.m in Sources */,
				6534E60A21C3C726002DBEF6 /* AffairFollowListModel.m in Sources */,
				65168B4821E7B5D8002CF1D3 /* ActivityWebVC.m in Sources */,
				656459C121E89FFC0089660C /* WhalePageVC.m in Sources */,
				6581CA1A219D190700424564 /* GenderVC.m in Sources */,
				6355FCCE2440ED8A00224A7F /* FZCollectionMenu.swift in Sources */,
				63593309242EF6FD00ABB2A1 /* FZAddMusic.swift in Sources */,
				6359328F242703B200ABB2A1 /* FZFindVC.swift in Sources */,
				635932D8242BC7CD00ABB2A1 /* FZSearchVC.swift in Sources */,
				634E11AB2442480E00175027 /* TopicNavCell.swift in Sources */,
				8C2E412F22A7A7400045A50C /* FTHZStraitModel.m in Sources */,
				6328EED624A11A18009168A3 /* GradientLabel.swift in Sources */,
				6581CA14219D176A00424564 /* LoginVC.m in Sources */,
				6328EEAE248CE514009168A3 /* UINavigationController.swift in Sources */,
				652B107C218AAA2900D31B3F /* AttentionTableViewCell.m in Sources */,
				FC0FB1592D8D3CA50024E5B2 /* DynamicTitleModel.m in Sources */,
				92652843225AD3F000A84B21 /* FTHZBusiness.m in Sources */,
				9254FF382249188D007D5569 /* FTHZNetworkManager.m in Sources */,
				6372A16E2465D00C00F998E1 /* PennyPincherGestureRecognizer.swift in Sources */,
				8C2E413A22A7E28E0045A50C /* UIImageView+AnimationCompletion.m in Sources */,
				92652817225994CE00A84B21 /* ErrorCode.m in Sources */,
				6533695621D9222F004E1782 /* DynamicSegmentView.m in Sources */,
				659980952181AF6B00FB7EEA /* FlatButton.m in Sources */,
				65C0764421B11DC900F5EB1F /* GetUserinfoModel.m in Sources */,
				635932DE242CC53600ABB2A1 /* FZNetWorkManager.swift in Sources */,
				658CCB7521ABF6F4003A93CC /* CommonCodeModel.m in Sources */,
				634E11BE2444EE7400175027 /* TNTextLayer.swift in Sources */,
				659891072181BB2D00BF1327 /* UIImage+TintColor.m in Sources */,
				FC8C71F12D657A5500651BD1 /* ChangeAccountModel.m in Sources */,
				659B64DC21A280C600DC13B8 /* VTBBirthdayPicker.m in Sources */,
				65C0766A21BC88FA00F5EB1F /* WhaleVC.m in Sources */,
				653136F0217F0C0C0008DD48 /* main.m in Sources */,
				659B652321A3B64000DC13B8 /* UserHeaderView.m in Sources */,
				6516CE2621A83AB300703E14 /* DynamicHeaderView.m in Sources */,
				659890FE2181B78500BF1327 /* NemoUtil.m in Sources */,
				6359328D2425F19400ABB2A1 /* UIColor.swift in Sources */,
				659891162181D8DB00BF1327 /* MineVC.m in Sources */,
				63AFBAD723B7C743000251ED /* FTHZHeziVC.m in Sources */,
				FC0FB1662D9553690024E5B2 /* FriendSelectionVC.m in Sources */,
				92D484A92262CD6C00221A7C /* FeedDetailViewController.m in Sources */,
				634E11AD24424FA900175027 /* TopicNavLayout.swift in Sources */,
				65C0767021BCAAA800F5EB1F /* ChangeGenderVC.m in Sources */,
				9265282E2259D18500A84B21 /* FTHZAccountManager.m in Sources */,
				65C0763421AE8BD200F5EB1F /* HUD.m in Sources */,
				634E11C02444EE7400175027 /* TNTrackLayer.swift in Sources */,
				6396D2FB244BF2F500B7B2EE /* (null) in Sources */,
				63AB043024D0A61D00BA3BE6 /* MaskTriangleView.swift in Sources */,
				634E11A32441CDDE00175027 /* LabelWithLineSpace.swift in Sources */,
				6516CE2021A83A7500703E14 /* DynamicLikeVC.m in Sources */,
				8C9F1D7C22BBACC40047DC08 /* FTHZChannelVoiceReplyVC.m in Sources */,
				653D54E521D383450054B986 /* amrFileCodec.m in Sources */,
				92B5C6592254AF5F002ABA55 /* FTHZRefreshFooter.m in Sources */,
				6599808C2181AAC000FB7EEA /* NSString+PJR.m in Sources */,
				6581CA03219A960700424564 /* AttentionMeVC.m in Sources */,
				635932F4242D207100ABB2A1 /* SearchResModel.swift in Sources */,
				659980872181AA4300FB7EEA /* JNFHTTPManager.m in Sources */,
				634014C02457571B00961DE8 /* GameAPI.swift in Sources */,
				635932F6242D319A00ABB2A1 /* MomentModel.swift in Sources */,
				65C0763721AECE5F00F5EB1F /* ContentModel.m in Sources */,
				9265283A225A37D200A84B21 /* FTHZGlobalConfig.m in Sources */,
				8C9F1D8822BCE4270047DC08 /* ChannelCommentCell.m in Sources */,
				653136E2217F0C0A0008DD48 /* AppDelegate.m in Sources */,
				65C0764D21B5276500F5EB1F /* DoLikeModel.m in Sources */,
				6547B55621A7A9FE005E7DA2 /* AttentionDetailVC.m in Sources */,
				635932E5242D01DE00ABB2A1 /* MoyaConfig.swift in Sources */,
				65168B8921E7D32C002CF1D3 /* ChangeStatsModel.m in Sources */,
				FCC5B2232A6A5DBB001C65E5 /* CreateShudongVC.m in Sources */,
				FCA89E6C2A71035D00B79271 /* LiuyanDetailVC.m in Sources */,
				925CAAD52250900000E19218 /* FTHZFeedCardContentView.m in Sources */,
				63593303242DF3EE00ABB2A1 /* DraftCell.swift in Sources */,
				92306872225393A40034DF02 /* FTHZOceanFeedListViewController.m in Sources */,
				656459D621FFE57B0089660C /* AffairTagmodel.m in Sources */,
				6372A16F2465D00C00F998E1 /* PennyPincherTemplate.swift in Sources */,
				926528322259FD5A00A84B21 /* FTHZNetworkTask+UserInfo.m in Sources */,
				63AFBADF23B7D312000251ED /* FTHZInvitModel.m in Sources */,
				FCDD07302D7EE8A5003B7FF4 /* VerifyCodeVC.m in Sources */,
				6581C9FD219A742C00424564 /* CenterTouchTableView.m in Sources */,
				************************ /* LiuyanTimeSeparatorModel.m in Sources */,
				92F1DC012267F8EC009C6737 /* FTHZAlertDialog.m in Sources */,
				657E9B7D21CA21B5009029E6 /* ReportModel.m in Sources */,
				6355FCD2244162FF00224A7F /* FZStraitVC.swift in Sources */,
				630536A822AAC472005B3878 /* FTHZCreateChannelVC.m in Sources */,
				65F6864F2189A2F0003D37F6 /* HZPhotoBrowserView.m in Sources */,
				637AE944244BE52E005ABD3B /* FZAttributeString.swift in Sources */,
				FCC5B2202A6A25D3001C65E5 /* ShudongDetailView.m in Sources */,
				656459D321FF3D210089660C /* pageTageCollectionViewCell.m in Sources */,
				650A088121C236FA0004A588 /* ChangeLikeTagVC.m in Sources */,
				6355FCC92440C54E00224A7F /* ZCycleLayout.swift in Sources */,
				634E11C12444EE7400175027 /* TNConstants.swift in Sources */,
				653D54F721D4D2F40054B986 /* InvitationStatusModel.m in Sources */,
				659980892181AA4300FB7EEA /* VitNetAPIClient.m in Sources */,
				634E11B62444D82E00175027 /* TouchAreaButton.swift in Sources */,
				6334B5722439EB13001C4F35 /* FZMomentVC.swift in Sources */,
				FC0E171C2D7822FB008430CF /* OperationDetailView.swift in Sources */,
				92F1DBF322649FE1009C6737 /* FTHZNestSegmentListViewController.m in Sources */,
				92D484B52262CED800221A7C /* FTHZAvatarView.m in Sources */,
				6557452521CD152100855BE2 /* BlackUserTableViewCell.m in Sources */,
				92C4D0B5222E04AE00A58687 /* FTHZLocationManager.m in Sources */,
				6581CA06219A962400424564 /* MyAttentionVC.m in Sources */,
				************************ /* HZPhotoGroupOld.m in Sources */,
				635932EB242D1BE700ABB2A1 /* NetWorkError.swift in Sources */,
				6581CA1D219D192D00424564 /* InformationVC.m in Sources */,
				FC0FB15F2D8D65770024E5B2 /* FZDynamicWebVC.swift in Sources */,
				6516CE1421A8207900703E14 /* AttentionCommentVC.m in Sources */,
				658CCB7821AC43A0003A93CC /* UserUserinfoModel.m in Sources */,
				FC03DDAB2E0E9E3C005469DD /* LiuyanDetailTableCell.m in Sources */,
				6302B4F32437399800FCB8C4 /* UILabel.swift in Sources */,
				63DC11542437A78600A1035A /* AttentionSegmentHeaderView.m in Sources */,
				6372A19D246DBB5F00F998E1 /* CGFloat.swift in Sources */,
				634E11A92442473B00175027 /* FZTopicVC.swift in Sources */,
				634E11B1244305A000175027 /* HerzCardView.swift in Sources */,
				63AFBADB23B7CF64000251ED /* FTHZHeziModel.m in Sources */,
				6581C9FA219A73F400424564 /* SegmentHeaderView.m in Sources */,
				6516CE1D21A8269100703E14 /* AttentionHeaderView.m in Sources */,
				927CC26F222B76C000654DE3 /* FTHZMusicPlayer.m in Sources */,
				************************ /* LiuyanTimeSeparatorCell.m in Sources */,
				653D54E821D38A270054B986 /* LGMessageModel.m in Sources */,
				92D484AF2262CE7300221A7C /* FTHZUpvoteUserRecordView.m in Sources */,
				65DC0CD721AAA1C5009C7673 /* HeziChouseThreeVC.m in Sources */,
				65C0765921B6517200F5EB1F /* DyCommentTableViewCell.m in Sources */,
				FCAD95E42A77975F001E2D3A /* XGAuthCode.m in Sources */,
				6355FCD82441642300224A7F /* StraitNavCell.swift in Sources */,
				6598910D2181D8A100BF1327 /* FindVC.m in Sources */,
				63AFBAEB23B99774000251ED /* HZPhotoSkipGroup.m in Sources */,
				8C2E413322A7BDE80045A50C /* StraitListCell.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		65313707217F0C0C0008DD48 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9254FF3A2249D1D1007D5569 /* AppConfig.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		65313708217F0C0C0008DD48 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9254FF3A2249D1D1007D5569 /* AppConfig.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		6531370A217F0C0C0008DD48 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9253B103223C882800798AE9 /* Config-Release-Debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CLANG_ENABLE_MODULES = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CODE_SIGN_ENTITLEMENTS = "$(SRCROOT)/FTHZ.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 20201026;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = 4FMP9SP953;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/FTHZ/Common/Framework",
				);
				INFOPLIST_FILE = "$(SRCROOT)/ProjectConfig/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/FTHZ/Sections/Message/ThridMsg/Class/Vendor/AMR/lib",
					"$(PROJECT_DIR)/FTHZ",
					"$(PROJECT_DIR)/FTHZ/Common/Framework",
				);
				MARKETING_VERSION = 2.0.0;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = "com.lindexi-gd.sp.FTHZ";
				PRODUCT_NAME = 52hz;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = FTHZ/Global/FTHZ_Bridging_Header.h;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		6531370B217F0C0C0008DD48 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9253B105223C88C600798AE9 /* Config-Release-Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CLANG_ENABLE_MODULES = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CODE_SIGN_ENTITLEMENTS = "$(SRCROOT)/FTHZ.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 20201026;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = 4FMP9SP953;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/FTHZ/Common/Framework",
				);
				INFOPLIST_FILE = "$(SRCROOT)/ProjectConfig/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/FTHZ/Sections/Message/ThridMsg/Class/Vendor/AMR/lib",
					"$(PROJECT_DIR)/FTHZ",
					"$(PROJECT_DIR)/FTHZ/Common/Framework",
				);
				MARKETING_VERSION = 2.0.0;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_BUNDLE_IDENTIFIER = "com.lindexi-gd.sp.FTHZ";
				PRODUCT_NAME = 52hz;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = FTHZ/Global/FTHZ_Bridging_Header.h;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		653136D8217F0C0A0008DD48 /* Build configuration list for PBXProject "FTHZ" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				65313707217F0C0C0008DD48 /* Debug */,
				65313708217F0C0C0008DD48 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		65313709217F0C0C0008DD48 /* Build configuration list for PBXNativeTarget "FTHZ" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6531370A217F0C0C0008DD48 /* Debug */,
				6531370B217F0C0C0008DD48 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 653136D5217F0C0A0008DD48 /* Project object */;
}
